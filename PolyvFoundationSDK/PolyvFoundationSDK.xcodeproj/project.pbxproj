// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXAggregateTarget section */
		22E42BD12193DC66007CE190 /* PolyvFoundationSDKBuild */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 22E42BD42193DC66007CE190 /* Build configuration list for PBXAggregateTarget "PolyvFoundationSDKBuild" */;
			buildPhases = (
				22E42BD52193DC6B007CE190 /* ShellScript */,
			);
			dependencies = (
			);
			name = PolyvFoundationSDKBuild;
			productName = PolyvFoundationSDKBuild;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		002E89BE23B21502006F08D4 /* PLVFQosLogReporter.h in Headers */ = {isa = PBXBuildFile; fileRef = 002E89BC23B21502006F08D4 /* PLVFQosLogReporter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		002E89BF23B21502006F08D4 /* PLVFQosLogReporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 002E89BD23B21502006F08D4 /* PLVFQosLogReporter.m */; };
		002E89C223B215AC006F08D4 /* PLVFQosLogModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 002E89C023B215AC006F08D4 /* PLVFQosLogModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		002E89C323B215AC006F08D4 /* PLVFQosLogModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 002E89C123B215AC006F08D4 /* PLVFQosLogModel.m */; };
		00466F8223B063BF00F968DC /* PLVFViewLogReporter.h in Headers */ = {isa = PBXBuildFile; fileRef = 00466F8023B063BF00F968DC /* PLVFViewLogReporter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00466F8323B063BF00F968DC /* PLVFViewLogReporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 00466F8123B063BF00F968DC /* PLVFViewLogReporter.m */; };
		00466F8623B06A4A00F968DC /* PLVFViewLogModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 00466F8423B06A4A00F968DC /* PLVFViewLogModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00466F8723B06A4A00F968DC /* PLVFViewLogModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 00466F8523B06A4A00F968DC /* PLVFViewLogModel.m */; };
		004D45F723A092FE001905D7 /* PLVFPPTErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 004D45F523A092FE001905D7 /* PLVFPPTErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		004D45F823A092FE001905D7 /* PLVFPPTErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 004D45F623A092FE001905D7 /* PLVFPPTErrorCodeGenerator.m */; };
		004D460623A21860001905D7 /* PLVFErrorBaseCodeDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 004D460423A21860001905D7 /* PLVFErrorBaseCodeDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
		004D460723A21860001905D7 /* PLVFErrorBaseCodeDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 004D460523A21860001905D7 /* PLVFErrorBaseCodeDefine.m */; };
		004D461023A24949001905D7 /* PLVFPlayErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 004D460E23A24949001905D7 /* PLVFPlayErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		004D461123A24949001905D7 /* PLVFPlayErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 004D460F23A24949001905D7 /* PLVFPlayErrorCodeGenerator.m */; };
		004D461423A24A24001905D7 /* PLVFDownloadErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 004D461223A24A24001905D7 /* PLVFDownloadErrorCodeGenerator.m */; };
		004D461523A24A24001905D7 /* PLVFDownloadErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 004D461323A24A24001905D7 /* PLVFDownloadErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		004D461823A24A8A001905D7 /* PLVFUploadErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 004D461623A24A8A001905D7 /* PLVFUploadErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		004D461923A24A8A001905D7 /* PLVFUploadErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 004D461723A24A8A001905D7 /* PLVFUploadErrorCodeGenerator.m */; };
		004D461C23A24ADA001905D7 /* PLVFRecordErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 004D461A23A24ADA001905D7 /* PLVFRecordErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		004D461D23A24ADA001905D7 /* PLVFRecordErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 004D461B23A24ADA001905D7 /* PLVFRecordErrorCodeGenerator.m */; };
		004D462023A24B2D001905D7 /* PLVFRtmpErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 004D461E23A24B2D001905D7 /* PLVFRtmpErrorCodeGenerator.m */; };
		004D462123A24B2D001905D7 /* PLVFRtmpErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 004D461F23A24B2D001905D7 /* PLVFRtmpErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		004D462423A24B61001905D7 /* PLVFChatErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 004D462223A24B61001905D7 /* PLVFChatErrorCodeGenerator.m */; };
		004D462523A24B61001905D7 /* PLVFChatErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 004D462323A24B61001905D7 /* PLVFChatErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		004D462823A24B8A001905D7 /* PLVFLinkErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 004D462623A24B89001905D7 /* PLVFLinkErrorCodeGenerator.m */; };
		004D462923A24B8A001905D7 /* PLVFLinkErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 004D462723A24B89001905D7 /* PLVFLinkErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		005063F925E3AD15007A3277 /* PLVViewFrameUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 005063F725E3AD15007A3277 /* PLVViewFrameUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		005063FA25E3AD15007A3277 /* PLVViewFrameUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 005063F825E3AD15007A3277 /* PLVViewFrameUtil.m */; };
		00727A0025CCDC72001CE398 /* PLVFWKWebViewJavascriptBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 007279FA25CCDC72001CE398 /* PLVFWKWebViewJavascriptBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00727A0125CCDC72001CE398 /* PLVFWebViewJavascriptBridge_JS.m in Sources */ = {isa = PBXBuildFile; fileRef = 007279FB25CCDC72001CE398 /* PLVFWebViewJavascriptBridge_JS.m */; };
		00727A0225CCDC72001CE398 /* PLVFWebViewJavascriptBridgeBase.m in Sources */ = {isa = PBXBuildFile; fileRef = 007279FC25CCDC72001CE398 /* PLVFWebViewJavascriptBridgeBase.m */; };
		00727A0325CCDC72001CE398 /* PLVFWebViewJavascriptBridge_JS.h in Headers */ = {isa = PBXBuildFile; fileRef = 007279FD25CCDC72001CE398 /* PLVFWebViewJavascriptBridge_JS.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00727A0425CCDC72001CE398 /* PLVFWKWebViewJavascriptBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 007279FE25CCDC72001CE398 /* PLVFWKWebViewJavascriptBridge.m */; };
		00727A0525CCDC72001CE398 /* PLVFWebViewJavascriptBridgeBase.h in Headers */ = {isa = PBXBuildFile; fileRef = 007279FF25CCDC72001CE398 /* PLVFWebViewJavascriptBridgeBase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		007EDC9723FE6600007D911B /* PLVFConsoleLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 007EDC9523FE6600007D911B /* PLVFConsoleLogger.m */; };
		007EDC9923FE6623007D911B /* PLVFConsoleLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = 007EDC9623FE6600007D911B /* PLVFConsoleLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0090E87723CC663300A00C5C /* PLVFHttpErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 0090E87523CC663300A00C5C /* PLVFHttpErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0090E87823CC663300A00C5C /* PLVFHttpErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 0090E87623CC663300A00C5C /* PLVFHttpErrorCodeGenerator.m */; };
		0090E87B23CC669800A00C5C /* PLVFInteractionErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 0090E87923CC669800A00C5C /* PLVFInteractionErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0090E87C23CC669800A00C5C /* PLVFInteractionErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 0090E87A23CC669800A00C5C /* PLVFInteractionErrorCodeGenerator.m */; };
		0090E87F23CF232900A00C5C /* PLVFInitErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 0090E87D23CF232900A00C5C /* PLVFInitErrorCodeGenerator.m */; };
		0090E88023CF232900A00C5C /* PLVFInitErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 0090E87E23CF232900A00C5C /* PLVFInitErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		009F5AB32398DB0700ADFE25 /* PLVFLogModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 009F5AB12398DB0700ADFE25 /* PLVFLogModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		009F5AB42398DB0700ADFE25 /* PLVFLogModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 009F5AB22398DB0700ADFE25 /* PLVFLogModel.m */; };
		009F5AB72398E2D000ADFE25 /* PLVFELogReporter.h in Headers */ = {isa = PBXBuildFile; fileRef = 009F5AB52398E2D000ADFE25 /* PLVFELogReporter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		009F5AB82398E2D000ADFE25 /* PLVFELogReporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 009F5AB62398E2D000ADFE25 /* PLVFELogReporter.m */; };
		009F5ABB239913B200ADFE25 /* PLVFFileUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 009F5AB9239913B200ADFE25 /* PLVFFileUtil.m */; };
		009F5ABC239913B200ADFE25 /* PLVFFileUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 009F5ABA239913B200ADFE25 /* PLVFFileUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00A4C7FA234F27930054CF38 /* PLVColorUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 00A4C7F6234F27930054CF38 /* PLVColorUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00A4C7FB234F27930054CF38 /* PLVColorUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 00A4C7F7234F27930054CF38 /* PLVColorUtil.m */; };
		00BD536B238E706D002BD784 /* PLVFBaseLogReporter.h in Headers */ = {isa = PBXBuildFile; fileRef = 00BD5369238E706D002BD784 /* PLVFBaseLogReporter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00BD536C238E706D002BD784 /* PLVFBaseLogReporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 00BD536A238E706D002BD784 /* PLVFBaseLogReporter.m */; };
		00BD5373238FB34B002BD784 /* PLVFELogModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 00BD5371238FB34B002BD784 /* PLVFELogModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00BD5374238FB34B002BD784 /* PLVFELogModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 00BD5372238FB34B002BD784 /* PLVFELogModel.m */; };
		00BD537F2390B54E002BD784 /* PLVFUserAgentBuilder.h in Headers */ = {isa = PBXBuildFile; fileRef = 00BD537D2390B54E002BD784 /* PLVFUserAgentBuilder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00BD53802390B54E002BD784 /* PLVFUserAgentBuilder.m in Sources */ = {isa = PBXBuildFile; fileRef = 00BD537E2390B54E002BD784 /* PLVFUserAgentBuilder.m */; };
		00BD53872390CC2A002BD784 /* PLVFLogReporter.h in Headers */ = {isa = PBXBuildFile; fileRef = 00BD53852390CC2A002BD784 /* PLVFLogReporter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00BD53882390CC2A002BD784 /* PLVFLogReporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 00BD53862390CC2A002BD784 /* PLVFLogReporter.m */; };
		00BD538C23910C6F002BD784 /* PLVFJsonConverter.h in Headers */ = {isa = PBXBuildFile; fileRef = 00BD538A23910C6F002BD784 /* PLVFJsonConverter.h */; };
		00BD538D23910C6F002BD784 /* PLVFJsonConverter.m in Sources */ = {isa = PBXBuildFile; fileRef = 00BD538B23910C6F002BD784 /* PLVFJsonConverter.m */; };
		00F5E1C622E062CC00850840 /* PLVSafeModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 00F5E1C422E062CC00850840 /* PLVSafeModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00F5E1C722E062CC00850840 /* PLVSafeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 00F5E1C522E062CC00850840 /* PLVSafeModel.m */; };
		00F5E1CB22E0765600850840 /* PLVProgressHUD.m in Sources */ = {isa = PBXBuildFile; fileRef = 00F5E1C922E0765600850840 /* PLVProgressHUD.m */; };
		00F5E1CC22E0765600850840 /* PLVProgressHUD.h in Headers */ = {isa = PBXBuildFile; fileRef = 00F5E1CA22E0765600850840 /* PLVProgressHUD.h */; settings = {ATTRIBUTES = (Public, ); }; };
		217826AD2A13339100A363D7 /* PLVFTrackLogReporter.h in Headers */ = {isa = PBXBuildFile; fileRef = 217826AB2A13339100A363D7 /* PLVFTrackLogReporter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		217826AE2A13339100A363D7 /* PLVFTrackLogReporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 217826AC2A13339100A363D7 /* PLVFTrackLogReporter.m */; };
		21934BBD25982B8000153945 /* PLVMulticastDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 21934BBB25982B8000153945 /* PLVMulticastDelegate.m */; };
		21934BBE25982B8000153945 /* PLVMulticastDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 21934BBC25982B8000153945 /* PLVMulticastDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		21958B862A131AEF00A03524 /* PLVFTrackLogModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 21958B842A131AEF00A03524 /* PLVFTrackLogModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		21958B872A131AEF00A03524 /* PLVFTrackLogModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 21958B852A131AEF00A03524 /* PLVFTrackLogModel.m */; };
		22142FA121130A8500062C6E /* PLVFoundationSDK.h in Headers */ = {isa = PBXBuildFile; fileRef = 22142F9F21130A8500062C6E /* PLVFoundationSDK.h */; settings = {ATTRIBUTES = (Public, ); }; };
		22142FB321130FDC00062C6E /* PLVDataUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 22142FA921130FDC00062C6E /* PLVDataUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		22142FB521130FDC00062C6E /* PLVDataUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 22142FAB21130FDC00062C6E /* PLVDataUtil.m */; };
		225FF4F421182D9C000110F3 /* PLVNetworkError.h in Headers */ = {isa = PBXBuildFile; fileRef = 225FF4F221182D9C000110F3 /* PLVNetworkError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		225FF4F521182D9C000110F3 /* PLVNetworkError.m in Sources */ = {isa = PBXBuildFile; fileRef = 225FF4F321182D9C000110F3 /* PLVNetworkError.m */; };
		2299AA4D2114275E00BE675D /* PLVFNetworkUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 2299AA4B2114275E00BE675D /* PLVFNetworkUtil.m */; };
		2299AA4E2114275E00BE675D /* PLVFNetworkUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 2299AA4C2114275E00BE675D /* PLVFNetworkUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		22D92D2621413134000FAF43 /* PLVConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 22D92D2421413134000FAF43 /* PLVConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3659FE9A2B049A9B00895F12 /* PLVFSignConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 3659FE982B049A9B00895F12 /* PLVFSignConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3659FE9B2B049A9B00895F12 /* PLVFSignConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 3659FE992B049A9B00895F12 /* PLVFSignConfig.m */; };
		36733E2D2B314CB600262BB8 /* PLVSM2CryptoUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 36733E2B2B314CB600262BB8 /* PLVSM2CryptoUtil.h */; };
		36733E2E2B314CB600262BB8 /* PLVSM2CryptoUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 36733E2C2B314CB600262BB8 /* PLVSM2CryptoUtil.m */; };
		36D19EE92AA84CBC00BB51C3 /* PLVFDI18NUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 36D19EE72AA84CBC00BB51C3 /* PLVFDI18NUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		36D19EEA2AA84CBC00BB51C3 /* PLVFDI18NUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 36D19EE82AA84CBC00BB51C3 /* PLVFDI18NUtil.m */; };
		36F60C9B2B05A38300600E53 /* PLVSM2Util.h in Headers */ = {isa = PBXBuildFile; fileRef = 36F60C992B05A38300600E53 /* PLVSM2Util.h */; settings = {ATTRIBUTES = (Public, ); }; };
		36F60C9C2B05A38300600E53 /* PLVSM2Util.m in Sources */ = {isa = PBXBuildFile; fileRef = 36F60C9A2B05A38300600E53 /* PLVSM2Util.m */; };
		491F05AA9B145FBE6CD4FD58 /* Pods_PLVFoundationSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9AC1E7DF2014E56AC991DC72 /* Pods_PLVFoundationSDK.framework */; };
		6355466024D3B4F500EF1CE1 /* PLVFWeakProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 6355465E24D3B4F500EF1CE1 /* PLVFWeakProxy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6355466124D3B4F500EF1CE1 /* PLVFWeakProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 6355465F24D3B4F500EF1CE1 /* PLVFWeakProxy.m */; };
		652C701C29D5800000EF3DA5 /* PLVFLogan.h in Headers */ = {isa = PBXBuildFile; fileRef = 652C701B29D5800000EF3DA5 /* PLVFLogan.h */; settings = {ATTRIBUTES = (Public, ); }; };
		652C701E29D5800D00EF3DA5 /* PLVFLogan.m in Sources */ = {isa = PBXBuildFile; fileRef = 652C701D29D5800D00EF3DA5 /* PLVFLogan.m */; };
		6560F5D328D1BDDF00D5DFA4 /* PLVKeyUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 6560F5D128D1BDDF00D5DFA4 /* PLVKeyUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6560F5D428D1BDDF00D5DFA4 /* PLVKeyUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 6560F5D228D1BDDF00D5DFA4 /* PLVKeyUtil.m */; };
		67019B9723BDEBA600D3D043 /* PLVFSocketErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = 67019B9523BDEBA600D3D043 /* PLVFSocketErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		67019B9823BDEBA600D3D043 /* PLVFSocketErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = 67019B9623BDEBA600D3D043 /* PLVFSocketErrorCodeGenerator.m */; };
		672B33C323161137009B187B /* PLVFdUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = 672B33C123161137009B187B /* PLVFdUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		672B33C423161137009B187B /* PLVFdUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 672B33C223161137009B187B /* PLVFdUtil.m */; };
		674F1C4A22E58F5C0081D670 /* PLVAuthorizationManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 674F1C4822E58F5C0081D670 /* PLVAuthorizationManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		674F1C4B22E58F5C0081D670 /* PLVAuthorizationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 674F1C4922E58F5C0081D670 /* PLVAuthorizationManager.m */; };
		67BC2FC6246AA838005DE58D /* PLVReachability.h in Headers */ = {isa = PBXBuildFile; fileRef = 67BC2FC4246AA838005DE58D /* PLVReachability.h */; settings = {ATTRIBUTES = (Public, ); }; };
		67BC2FC7246AA838005DE58D /* PLVReachability.m in Sources */ = {isa = PBXBuildFile; fileRef = 67BC2FC5246AA838005DE58D /* PLVReachability.m */; };
		67CE6C5F2451A784004CFF5D /* PLVJSBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 67CE6C5D2451A784004CFF5D /* PLVJSBridge.m */; };
		67CE6C602451A784004CFF5D /* PLVJSBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 67CE6C5E2451A784004CFF5D /* PLVJSBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8F75BC562680989700D327AA /* PLVNetworkAccessibility.h in Headers */ = {isa = PBXBuildFile; fileRef = 8F75BC542680989700D327AA /* PLVNetworkAccessibility.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8F75BC572680989700D327AA /* PLVNetworkAccessibility.m in Sources */ = {isa = PBXBuildFile; fileRef = 8F75BC552680989700D327AA /* PLVNetworkAccessibility.m */; };
		C5D1AA4CDD101AE1A358C8ED /* Pods_PLVFoundationSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5F2B725B57A76353CC5D3CA4 /* Pods_PLVFoundationSDK.framework */; };
		FA46E4CA2797A69F00B6F36C /* PLVImageUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = FA46E4C82797A69F00B6F36C /* PLVImageUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA46E4CB2797A69F00B6F36C /* PLVImageUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = FA46E4C92797A69F00B6F36C /* PLVImageUtil.m */; };
		FA46E4CE2797B4B500B6F36C /* PLVGLTexture.h in Headers */ = {isa = PBXBuildFile; fileRef = FA46E4CC2797B4B500B6F36C /* PLVGLTexture.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA46E4CF2797B4B500B6F36C /* PLVGLTexture.m in Sources */ = {isa = PBXBuildFile; fileRef = FA46E4CD2797B4B500B6F36C /* PLVGLTexture.m */; };
		FA688A0F284CBC400099AEF7 /* PLVFBeautyErrorCodeGenerator.h in Headers */ = {isa = PBXBuildFile; fileRef = FA688A0D284CBC400099AEF7 /* PLVFBeautyErrorCodeGenerator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA688A10284CBC400099AEF7 /* PLVFBeautyErrorCodeGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = FA688A0E284CBC400099AEF7 /* PLVFBeautyErrorCodeGenerator.m */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		002E89BC23B21502006F08D4 /* PLVFQosLogReporter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFQosLogReporter.h; sourceTree = "<group>"; };
		002E89BD23B21502006F08D4 /* PLVFQosLogReporter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFQosLogReporter.m; sourceTree = "<group>"; };
		002E89C023B215AC006F08D4 /* PLVFQosLogModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFQosLogModel.h; sourceTree = "<group>"; };
		002E89C123B215AC006F08D4 /* PLVFQosLogModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFQosLogModel.m; sourceTree = "<group>"; };
		00466F8023B063BF00F968DC /* PLVFViewLogReporter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFViewLogReporter.h; sourceTree = "<group>"; };
		00466F8123B063BF00F968DC /* PLVFViewLogReporter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFViewLogReporter.m; sourceTree = "<group>"; };
		00466F8423B06A4A00F968DC /* PLVFViewLogModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFViewLogModel.h; sourceTree = "<group>"; };
		00466F8523B06A4A00F968DC /* PLVFViewLogModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFViewLogModel.m; sourceTree = "<group>"; };
		004D45F523A092FE001905D7 /* PLVFPPTErrorCodeGenerator.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFPPTErrorCodeGenerator.h; sourceTree = "<group>"; };
		004D45F623A092FE001905D7 /* PLVFPPTErrorCodeGenerator.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFPPTErrorCodeGenerator.m; sourceTree = "<group>"; };
		004D460423A21860001905D7 /* PLVFErrorBaseCodeDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFErrorBaseCodeDefine.h; sourceTree = "<group>"; };
		004D460523A21860001905D7 /* PLVFErrorBaseCodeDefine.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFErrorBaseCodeDefine.m; sourceTree = "<group>"; };
		004D460E23A24949001905D7 /* PLVFPlayErrorCodeGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFPlayErrorCodeGenerator.h; sourceTree = "<group>"; };
		004D460F23A24949001905D7 /* PLVFPlayErrorCodeGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFPlayErrorCodeGenerator.m; sourceTree = "<group>"; };
		004D461223A24A24001905D7 /* PLVFDownloadErrorCodeGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFDownloadErrorCodeGenerator.m; sourceTree = "<group>"; };
		004D461323A24A24001905D7 /* PLVFDownloadErrorCodeGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFDownloadErrorCodeGenerator.h; sourceTree = "<group>"; };
		004D461623A24A8A001905D7 /* PLVFUploadErrorCodeGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFUploadErrorCodeGenerator.h; sourceTree = "<group>"; };
		004D461723A24A8A001905D7 /* PLVFUploadErrorCodeGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFUploadErrorCodeGenerator.m; sourceTree = "<group>"; };
		004D461A23A24ADA001905D7 /* PLVFRecordErrorCodeGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFRecordErrorCodeGenerator.h; sourceTree = "<group>"; };
		004D461B23A24ADA001905D7 /* PLVFRecordErrorCodeGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFRecordErrorCodeGenerator.m; sourceTree = "<group>"; };
		004D461E23A24B2D001905D7 /* PLVFRtmpErrorCodeGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFRtmpErrorCodeGenerator.m; sourceTree = "<group>"; };
		004D461F23A24B2D001905D7 /* PLVFRtmpErrorCodeGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFRtmpErrorCodeGenerator.h; sourceTree = "<group>"; };
		004D462223A24B61001905D7 /* PLVFChatErrorCodeGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFChatErrorCodeGenerator.m; sourceTree = "<group>"; };
		004D462323A24B61001905D7 /* PLVFChatErrorCodeGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFChatErrorCodeGenerator.h; sourceTree = "<group>"; };
		004D462623A24B89001905D7 /* PLVFLinkErrorCodeGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFLinkErrorCodeGenerator.m; sourceTree = "<group>"; };
		004D462723A24B89001905D7 /* PLVFLinkErrorCodeGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFLinkErrorCodeGenerator.h; sourceTree = "<group>"; };
		005063F725E3AD15007A3277 /* PLVViewFrameUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVViewFrameUtil.h; sourceTree = "<group>"; };
		005063F825E3AD15007A3277 /* PLVViewFrameUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVViewFrameUtil.m; sourceTree = "<group>"; };
		007279FA25CCDC72001CE398 /* PLVFWKWebViewJavascriptBridge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFWKWebViewJavascriptBridge.h; sourceTree = "<group>"; };
		007279FB25CCDC72001CE398 /* PLVFWebViewJavascriptBridge_JS.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFWebViewJavascriptBridge_JS.m; sourceTree = "<group>"; };
		007279FC25CCDC72001CE398 /* PLVFWebViewJavascriptBridgeBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFWebViewJavascriptBridgeBase.m; sourceTree = "<group>"; };
		007279FD25CCDC72001CE398 /* PLVFWebViewJavascriptBridge_JS.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFWebViewJavascriptBridge_JS.h; sourceTree = "<group>"; };
		007279FE25CCDC72001CE398 /* PLVFWKWebViewJavascriptBridge.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFWKWebViewJavascriptBridge.m; sourceTree = "<group>"; };
		007279FF25CCDC72001CE398 /* PLVFWebViewJavascriptBridgeBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFWebViewJavascriptBridgeBase.h; sourceTree = "<group>"; };
		007EDC9523FE6600007D911B /* PLVFConsoleLogger.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFConsoleLogger.m; sourceTree = "<group>"; };
		007EDC9623FE6600007D911B /* PLVFConsoleLogger.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFConsoleLogger.h; sourceTree = "<group>"; };
		0090E87523CC663300A00C5C /* PLVFHttpErrorCodeGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFHttpErrorCodeGenerator.h; sourceTree = "<group>"; };
		0090E87623CC663300A00C5C /* PLVFHttpErrorCodeGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFHttpErrorCodeGenerator.m; sourceTree = "<group>"; };
		0090E87923CC669800A00C5C /* PLVFInteractionErrorCodeGenerator.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFInteractionErrorCodeGenerator.h; sourceTree = "<group>"; };
		0090E87A23CC669800A00C5C /* PLVFInteractionErrorCodeGenerator.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFInteractionErrorCodeGenerator.m; sourceTree = "<group>"; };
		0090E87D23CF232900A00C5C /* PLVFInitErrorCodeGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFInitErrorCodeGenerator.m; sourceTree = "<group>"; };
		0090E87E23CF232900A00C5C /* PLVFInitErrorCodeGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFInitErrorCodeGenerator.h; sourceTree = "<group>"; };
		009F5AB12398DB0700ADFE25 /* PLVFLogModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFLogModel.h; sourceTree = "<group>"; };
		009F5AB22398DB0700ADFE25 /* PLVFLogModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFLogModel.m; sourceTree = "<group>"; };
		009F5AB52398E2D000ADFE25 /* PLVFELogReporter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFELogReporter.h; sourceTree = "<group>"; };
		009F5AB62398E2D000ADFE25 /* PLVFELogReporter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFELogReporter.m; sourceTree = "<group>"; };
		009F5AB9239913B200ADFE25 /* PLVFFileUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFFileUtil.m; sourceTree = "<group>"; };
		009F5ABA239913B200ADFE25 /* PLVFFileUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFFileUtil.h; sourceTree = "<group>"; };
		00A2508C2395202D00A85257 /* PLVFLogReporterInternal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFLogReporterInternal.h; sourceTree = "<group>"; };
		00A4C7F6234F27930054CF38 /* PLVColorUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVColorUtil.h; sourceTree = "<group>"; };
		00A4C7F7234F27930054CF38 /* PLVColorUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVColorUtil.m; sourceTree = "<group>"; };
		00BD5369238E706D002BD784 /* PLVFBaseLogReporter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFBaseLogReporter.h; sourceTree = "<group>"; };
		00BD536A238E706D002BD784 /* PLVFBaseLogReporter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFBaseLogReporter.m; sourceTree = "<group>"; };
		00BD5371238FB34B002BD784 /* PLVFELogModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFELogModel.h; sourceTree = "<group>"; };
		00BD5372238FB34B002BD784 /* PLVFELogModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFELogModel.m; sourceTree = "<group>"; };
		00BD537D2390B54E002BD784 /* PLVFUserAgentBuilder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = PLVFUserAgentBuilder.h; path = ../PLVFUserAgentBuilder.h; sourceTree = "<group>"; };
		00BD537E2390B54E002BD784 /* PLVFUserAgentBuilder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = PLVFUserAgentBuilder.m; path = ../PLVFUserAgentBuilder.m; sourceTree = "<group>"; };
		00BD53852390CC2A002BD784 /* PLVFLogReporter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFLogReporter.h; sourceTree = "<group>"; };
		00BD53862390CC2A002BD784 /* PLVFLogReporter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFLogReporter.m; sourceTree = "<group>"; };
		00BD538A23910C6F002BD784 /* PLVFJsonConverter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFJsonConverter.h; sourceTree = "<group>"; };
		00BD538B23910C6F002BD784 /* PLVFJsonConverter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFJsonConverter.m; sourceTree = "<group>"; };
		00F5E1C422E062CC00850840 /* PLVSafeModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVSafeModel.h; sourceTree = "<group>"; };
		00F5E1C522E062CC00850840 /* PLVSafeModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVSafeModel.m; sourceTree = "<group>"; };
		00F5E1C922E0765600850840 /* PLVProgressHUD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVProgressHUD.m; sourceTree = "<group>"; };
		00F5E1CA22E0765600850840 /* PLVProgressHUD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVProgressHUD.h; sourceTree = "<group>"; };
		217826AB2A13339100A363D7 /* PLVFTrackLogReporter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFTrackLogReporter.h; sourceTree = "<group>"; };
		217826AC2A13339100A363D7 /* PLVFTrackLogReporter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFTrackLogReporter.m; sourceTree = "<group>"; };
		21934BBB25982B8000153945 /* PLVMulticastDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVMulticastDelegate.m; sourceTree = "<group>"; };
		21934BBC25982B8000153945 /* PLVMulticastDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVMulticastDelegate.h; sourceTree = "<group>"; };
		21958B842A131AEF00A03524 /* PLVFTrackLogModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFTrackLogModel.h; sourceTree = "<group>"; };
		21958B852A131AEF00A03524 /* PLVFTrackLogModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFTrackLogModel.m; sourceTree = "<group>"; };
		22142F9C21130A8500062C6E /* PLVFoundationSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = PLVFoundationSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		22142F9F21130A8500062C6E /* PLVFoundationSDK.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFoundationSDK.h; sourceTree = "<group>"; };
		22142FA021130A8500062C6E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		22142FA921130FDC00062C6E /* PLVDataUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVDataUtil.h; sourceTree = "<group>"; };
		22142FAB21130FDC00062C6E /* PLVDataUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVDataUtil.m; sourceTree = "<group>"; };
		225FF4F221182D9C000110F3 /* PLVNetworkError.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVNetworkError.h; sourceTree = "<group>"; };
		225FF4F321182D9C000110F3 /* PLVNetworkError.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVNetworkError.m; sourceTree = "<group>"; };
		2299AA4B2114275E00BE675D /* PLVFNetworkUtil.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFNetworkUtil.m; sourceTree = "<group>"; };
		2299AA4C2114275E00BE675D /* PLVFNetworkUtil.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFNetworkUtil.h; sourceTree = "<group>"; };
		22D92D2421413134000FAF43 /* PLVConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVConfig.h; sourceTree = "<group>"; };
		3659FE982B049A9B00895F12 /* PLVFSignConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFSignConfig.h; sourceTree = "<group>"; };
		3659FE992B049A9B00895F12 /* PLVFSignConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFSignConfig.m; sourceTree = "<group>"; };
		36733E2B2B314CB600262BB8 /* PLVSM2CryptoUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVSM2CryptoUtil.h; sourceTree = "<group>"; };
		36733E2C2B314CB600262BB8 /* PLVSM2CryptoUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVSM2CryptoUtil.m; sourceTree = "<group>"; };
		36D19EE72AA84CBC00BB51C3 /* PLVFDI18NUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFDI18NUtil.h; sourceTree = "<group>"; };
		36D19EE82AA84CBC00BB51C3 /* PLVFDI18NUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFDI18NUtil.m; sourceTree = "<group>"; };
		36F60C992B05A38300600E53 /* PLVSM2Util.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVSM2Util.h; sourceTree = "<group>"; };
		36F60C9A2B05A38300600E53 /* PLVSM2Util.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVSM2Util.m; sourceTree = "<group>"; };
		5F2B725B57A76353CC5D3CA4 /* Pods_PLVFoundationSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_PLVFoundationSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6355465E24D3B4F500EF1CE1 /* PLVFWeakProxy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFWeakProxy.h; sourceTree = "<group>"; };
		6355465F24D3B4F500EF1CE1 /* PLVFWeakProxy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFWeakProxy.m; sourceTree = "<group>"; };
		652C701B29D5800000EF3DA5 /* PLVFLogan.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFLogan.h; sourceTree = "<group>"; };
		652C701D29D5800D00EF3DA5 /* PLVFLogan.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFLogan.m; sourceTree = "<group>"; };
		6560F5D128D1BDDF00D5DFA4 /* PLVKeyUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVKeyUtil.h; sourceTree = "<group>"; };
		6560F5D228D1BDDF00D5DFA4 /* PLVKeyUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVKeyUtil.m; sourceTree = "<group>"; };
		67019B9523BDEBA600D3D043 /* PLVFSocketErrorCodeGenerator.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFSocketErrorCodeGenerator.h; sourceTree = "<group>"; };
		67019B9623BDEBA600D3D043 /* PLVFSocketErrorCodeGenerator.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFSocketErrorCodeGenerator.m; sourceTree = "<group>"; };
		672B33C123161137009B187B /* PLVFdUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFdUtil.h; sourceTree = "<group>"; };
		672B33C223161137009B187B /* PLVFdUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFdUtil.m; sourceTree = "<group>"; };
		674F1C4822E58F5C0081D670 /* PLVAuthorizationManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVAuthorizationManager.h; sourceTree = "<group>"; };
		674F1C4922E58F5C0081D670 /* PLVAuthorizationManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVAuthorizationManager.m; sourceTree = "<group>"; };
		67BC2FC4246AA838005DE58D /* PLVReachability.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVReachability.h; sourceTree = "<group>"; };
		67BC2FC5246AA838005DE58D /* PLVReachability.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVReachability.m; sourceTree = "<group>"; };
		67BC2FC8246AAEEA005DE58D /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		67CE6C5D2451A784004CFF5D /* PLVJSBridge.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVJSBridge.m; sourceTree = "<group>"; };
		67CE6C5E2451A784004CFF5D /* PLVJSBridge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVJSBridge.h; sourceTree = "<group>"; };
		70E828BD74F767034178AC50 /* Pods-business-PLVFoundationSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-PLVFoundationSDK.release.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-PLVFoundationSDK/Pods-business-PLVFoundationSDK.release.xcconfig"; sourceTree = "<group>"; };
		7C84414CAAA0CACEF5DAF272 /* Pods-business-PLVFoundationSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-PLVFoundationSDK.debug.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-PLVFoundationSDK/Pods-business-PLVFoundationSDK.debug.xcconfig"; sourceTree = "<group>"; };
		8F75BC542680989700D327AA /* PLVNetworkAccessibility.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVNetworkAccessibility.h; sourceTree = "<group>"; };
		8F75BC552680989700D327AA /* PLVNetworkAccessibility.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVNetworkAccessibility.m; sourceTree = "<group>"; };
		90B5D7D5C9A0F04440509A75 /* Pods-PLVFoundationSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PLVFoundationSDK.debug.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-PLVFoundationSDK/Pods-PLVFoundationSDK.debug.xcconfig"; sourceTree = "<group>"; };
		B4492BAF6E3487CFCCBD22A6 /* Pods-PLVFoundationSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PLVFoundationSDK.release.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-PLVFoundationSDK/Pods-PLVFoundationSDK.release.xcconfig"; sourceTree = "<group>"; };
		D7B119F981ECF46E4A2AF9DA /* Pods-PolyvFoundationSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PolyvFoundationSDK.debug.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-PolyvFoundationSDK/Pods-PolyvFoundationSDK.debug.xcconfig"; sourceTree = "<group>"; };
		F3AE8AA93868797BF3C35412 /* Pods-PolyvFoundationSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PolyvFoundationSDK.release.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-PolyvFoundationSDK/Pods-PolyvFoundationSDK.release.xcconfig"; sourceTree = "<group>"; };
		FA46E4C82797A69F00B6F36C /* PLVImageUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVImageUtil.h; sourceTree = "<group>"; };
		FA46E4C92797A69F00B6F36C /* PLVImageUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVImageUtil.m; sourceTree = "<group>"; };
		FA46E4CC2797B4B500B6F36C /* PLVGLTexture.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVGLTexture.h; sourceTree = "<group>"; };
		FA46E4CD2797B4B500B6F36C /* PLVGLTexture.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVGLTexture.m; sourceTree = "<group>"; };
		FA688A0D284CBC400099AEF7 /* PLVFBeautyErrorCodeGenerator.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFBeautyErrorCodeGenerator.h; sourceTree = "<group>"; };
		FA688A0E284CBC400099AEF7 /* PLVFBeautyErrorCodeGenerator.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFBeautyErrorCodeGenerator.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		22142F9821130A8500062C6E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C5D1AA4CDD101AE1A358C8ED /* Pods_PLVFoundationSDK.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		004D45F023A09102001905D7 /* ErrorCode */ = {
			isa = PBXGroup;
			children = (
				004D460423A21860001905D7 /* PLVFErrorBaseCodeDefine.h */,
				004D460523A21860001905D7 /* PLVFErrorBaseCodeDefine.m */,
				004D460E23A24949001905D7 /* PLVFPlayErrorCodeGenerator.h */,
				004D460F23A24949001905D7 /* PLVFPlayErrorCodeGenerator.m */,
				004D461323A24A24001905D7 /* PLVFDownloadErrorCodeGenerator.h */,
				004D461223A24A24001905D7 /* PLVFDownloadErrorCodeGenerator.m */,
				004D461623A24A8A001905D7 /* PLVFUploadErrorCodeGenerator.h */,
				004D461723A24A8A001905D7 /* PLVFUploadErrorCodeGenerator.m */,
				004D461A23A24ADA001905D7 /* PLVFRecordErrorCodeGenerator.h */,
				004D461B23A24ADA001905D7 /* PLVFRecordErrorCodeGenerator.m */,
				004D461F23A24B2D001905D7 /* PLVFRtmpErrorCodeGenerator.h */,
				004D461E23A24B2D001905D7 /* PLVFRtmpErrorCodeGenerator.m */,
				004D462323A24B61001905D7 /* PLVFChatErrorCodeGenerator.h */,
				004D462223A24B61001905D7 /* PLVFChatErrorCodeGenerator.m */,
				004D462723A24B89001905D7 /* PLVFLinkErrorCodeGenerator.h */,
				004D462623A24B89001905D7 /* PLVFLinkErrorCodeGenerator.m */,
				004D45F523A092FE001905D7 /* PLVFPPTErrorCodeGenerator.h */,
				004D45F623A092FE001905D7 /* PLVFPPTErrorCodeGenerator.m */,
				0090E87E23CF232900A00C5C /* PLVFInitErrorCodeGenerator.h */,
				0090E87D23CF232900A00C5C /* PLVFInitErrorCodeGenerator.m */,
				67019B9523BDEBA600D3D043 /* PLVFSocketErrorCodeGenerator.h */,
				67019B9623BDEBA600D3D043 /* PLVFSocketErrorCodeGenerator.m */,
				0090E87523CC663300A00C5C /* PLVFHttpErrorCodeGenerator.h */,
				0090E87623CC663300A00C5C /* PLVFHttpErrorCodeGenerator.m */,
				0090E87923CC669800A00C5C /* PLVFInteractionErrorCodeGenerator.h */,
				0090E87A23CC669800A00C5C /* PLVFInteractionErrorCodeGenerator.m */,
				FA688A0D284CBC400099AEF7 /* PLVFBeautyErrorCodeGenerator.h */,
				FA688A0E284CBC400099AEF7 /* PLVFBeautyErrorCodeGenerator.m */,
			);
			path = ErrorCode;
			sourceTree = "<group>";
		};
		007279F925CCDC72001CE398 /* WebViewJavascriptBridge */ = {
			isa = PBXGroup;
			children = (
				007279FD25CCDC72001CE398 /* PLVFWebViewJavascriptBridge_JS.h */,
				007279FB25CCDC72001CE398 /* PLVFWebViewJavascriptBridge_JS.m */,
				007279FA25CCDC72001CE398 /* PLVFWKWebViewJavascriptBridge.h */,
				007279FE25CCDC72001CE398 /* PLVFWKWebViewJavascriptBridge.m */,
				007279FF25CCDC72001CE398 /* PLVFWebViewJavascriptBridgeBase.h */,
				007279FC25CCDC72001CE398 /* PLVFWebViewJavascriptBridgeBase.m */,
			);
			path = WebViewJavascriptBridge;
			sourceTree = "<group>";
		};
		007EDC9423FE6600007D911B /* ConsoleLog */ = {
			isa = PBXGroup;
			children = (
				007EDC9623FE6600007D911B /* PLVFConsoleLogger.h */,
				007EDC9523FE6600007D911B /* PLVFConsoleLogger.m */,
			);
			path = ConsoleLog;
			sourceTree = "<group>";
		};
		00BD53812390CBC5002BD784 /* LogReporter */ = {
			isa = PBXGroup;
			children = (
				00BD538923910C3D002BD784 /* Utils */,
				00BD53842390CC0E002BD784 /* Model */,
				00A2508C2395202D00A85257 /* PLVFLogReporterInternal.h */,
				00BD5369238E706D002BD784 /* PLVFBaseLogReporter.h */,
				00BD536A238E706D002BD784 /* PLVFBaseLogReporter.m */,
				00BD53852390CC2A002BD784 /* PLVFLogReporter.h */,
				00BD53862390CC2A002BD784 /* PLVFLogReporter.m */,
				009F5AB52398E2D000ADFE25 /* PLVFELogReporter.h */,
				009F5AB62398E2D000ADFE25 /* PLVFELogReporter.m */,
				00466F8023B063BF00F968DC /* PLVFViewLogReporter.h */,
				00466F8123B063BF00F968DC /* PLVFViewLogReporter.m */,
				002E89BC23B21502006F08D4 /* PLVFQosLogReporter.h */,
				002E89BD23B21502006F08D4 /* PLVFQosLogReporter.m */,
				217826AB2A13339100A363D7 /* PLVFTrackLogReporter.h */,
				217826AC2A13339100A363D7 /* PLVFTrackLogReporter.m */,
			);
			path = LogReporter;
			sourceTree = "<group>";
		};
		00BD53842390CC0E002BD784 /* Model */ = {
			isa = PBXGroup;
			children = (
				009F5AB12398DB0700ADFE25 /* PLVFLogModel.h */,
				009F5AB22398DB0700ADFE25 /* PLVFLogModel.m */,
				00BD5371238FB34B002BD784 /* PLVFELogModel.h */,
				00BD5372238FB34B002BD784 /* PLVFELogModel.m */,
				00466F8423B06A4A00F968DC /* PLVFViewLogModel.h */,
				00466F8523B06A4A00F968DC /* PLVFViewLogModel.m */,
				002E89C023B215AC006F08D4 /* PLVFQosLogModel.h */,
				002E89C123B215AC006F08D4 /* PLVFQosLogModel.m */,
				21958B842A131AEF00A03524 /* PLVFTrackLogModel.h */,
				21958B852A131AEF00A03524 /* PLVFTrackLogModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		00BD538923910C3D002BD784 /* Utils */ = {
			isa = PBXGroup;
			children = (
				00BD537D2390B54E002BD784 /* PLVFUserAgentBuilder.h */,
				00BD537E2390B54E002BD784 /* PLVFUserAgentBuilder.m */,
				00BD538A23910C6F002BD784 /* PLVFJsonConverter.h */,
				00BD538B23910C6F002BD784 /* PLVFJsonConverter.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		00F5E1C322E062B800850840 /* Model */ = {
			isa = PBXGroup;
			children = (
				00F5E1C422E062CC00850840 /* PLVSafeModel.h */,
				00F5E1C522E062CC00850840 /* PLVSafeModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		00F5E1C822E0764300850840 /* ProgressHUD */ = {
			isa = PBXGroup;
			children = (
				00F5E1CA22E0765600850840 /* PLVProgressHUD.h */,
				00F5E1C922E0765600850840 /* PLVProgressHUD.m */,
			);
			path = ProgressHUD;
			sourceTree = "<group>";
		};
		21934BBA25982B8000153945 /* Multicast */ = {
			isa = PBXGroup;
			children = (
				21934BBC25982B8000153945 /* PLVMulticastDelegate.h */,
				21934BBB25982B8000153945 /* PLVMulticastDelegate.m */,
			);
			path = Multicast;
			sourceTree = "<group>";
		};
		22142F9221130A8500062C6E = {
			isa = PBXGroup;
			children = (
				22142F9F21130A8500062C6E /* PLVFoundationSDK.h */,
				22142F9E21130A8500062C6E /* PolyvFoundationSDK */,
				22142F9D21130A8500062C6E /* Products */,
				F843BC3E1661EB5277B56391 /* Pods */,
				69EEAAD2EB16DAA43C5C6DEA /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		22142F9D21130A8500062C6E /* Products */ = {
			isa = PBXGroup;
			children = (
				22142F9C21130A8500062C6E /* PLVFoundationSDK.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		22142F9E21130A8500062C6E /* PolyvFoundationSDK */ = {
			isa = PBXGroup;
			children = (
				22D92D2421413134000FAF43 /* PLVConfig.h */,
				21934BBA25982B8000153945 /* Multicast */,
				22142FA721130FDC00062C6E /* Utils */,
				004D45F023A09102001905D7 /* ErrorCode */,
				00BD53812390CBC5002BD784 /* LogReporter */,
				652C701A29D57FEB00EF3DA5 /* Logan */,
				22142FAF21130FDC00062C6E /* Network */,
				007EDC9423FE6600007D911B /* ConsoleLog */,
				671ED4AD24D26615008D2976 /* Library */,
				671ED49A24D260F2008D2976 /* ThirdPart */,
				67BC2FC8246AAEEA005DE58D /* README.md */,
				22142FA021130A8500062C6E /* Info.plist */,
			);
			path = PolyvFoundationSDK;
			sourceTree = "<group>";
		};
		22142FA721130FDC00062C6E /* Utils */ = {
			isa = PBXGroup;
			children = (
				672B33C123161137009B187B /* PLVFdUtil.h */,
				672B33C223161137009B187B /* PLVFdUtil.m */,
				22142FA921130FDC00062C6E /* PLVDataUtil.h */,
				22142FAB21130FDC00062C6E /* PLVDataUtil.m */,
				00A4C7F6234F27930054CF38 /* PLVColorUtil.h */,
				00A4C7F7234F27930054CF38 /* PLVColorUtil.m */,
				009F5ABA239913B200ADFE25 /* PLVFFileUtil.h */,
				009F5AB9239913B200ADFE25 /* PLVFFileUtil.m */,
				6355465E24D3B4F500EF1CE1 /* PLVFWeakProxy.h */,
				6355465F24D3B4F500EF1CE1 /* PLVFWeakProxy.m */,
				005063F725E3AD15007A3277 /* PLVViewFrameUtil.h */,
				005063F825E3AD15007A3277 /* PLVViewFrameUtil.m */,
				8F75BC542680989700D327AA /* PLVNetworkAccessibility.h */,
				8F75BC552680989700D327AA /* PLVNetworkAccessibility.m */,
				FA46E4C82797A69F00B6F36C /* PLVImageUtil.h */,
				FA46E4C92797A69F00B6F36C /* PLVImageUtil.m */,
				FA46E4CC2797B4B500B6F36C /* PLVGLTexture.h */,
				FA46E4CD2797B4B500B6F36C /* PLVGLTexture.m */,
				6560F5D128D1BDDF00D5DFA4 /* PLVKeyUtil.h */,
				6560F5D228D1BDDF00D5DFA4 /* PLVKeyUtil.m */,
				3659FE982B049A9B00895F12 /* PLVFSignConfig.h */,
				3659FE992B049A9B00895F12 /* PLVFSignConfig.m */,
				36F60C992B05A38300600E53 /* PLVSM2Util.h */,
				36F60C9A2B05A38300600E53 /* PLVSM2Util.m */,
				36733E2B2B314CB600262BB8 /* PLVSM2CryptoUtil.h */,
				36733E2C2B314CB600262BB8 /* PLVSM2CryptoUtil.m */,
				36D19EE72AA84CBC00BB51C3 /* PLVFDI18NUtil.h */,
				36D19EE82AA84CBC00BB51C3 /* PLVFDI18NUtil.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		22142FAF21130FDC00062C6E /* Network */ = {
			isa = PBXGroup;
			children = (
				225FF4F221182D9C000110F3 /* PLVNetworkError.h */,
				225FF4F321182D9C000110F3 /* PLVNetworkError.m */,
				2299AA4C2114275E00BE675D /* PLVFNetworkUtil.h */,
				2299AA4B2114275E00BE675D /* PLVFNetworkUtil.m */,
			);
			path = Network;
			sourceTree = "<group>";
		};
		652C701A29D57FEB00EF3DA5 /* Logan */ = {
			isa = PBXGroup;
			children = (
				652C701B29D5800000EF3DA5 /* PLVFLogan.h */,
				652C701D29D5800D00EF3DA5 /* PLVFLogan.m */,
			);
			path = Logan;
			sourceTree = "<group>";
		};
		671ED49A24D260F2008D2976 /* ThirdPart */ = {
			isa = PBXGroup;
			children = (
				007279F925CCDC72001CE398 /* WebViewJavascriptBridge */,
				00F5E1C822E0764300850840 /* ProgressHUD */,
				67BC2FC3246AA810005DE58D /* Reachability */,
			);
			path = ThirdPart;
			sourceTree = "<group>";
		};
		671ED4AD24D26615008D2976 /* Library */ = {
			isa = PBXGroup;
			children = (
				00F5E1C322E062B800850840 /* Model */,
				67CE6C5C2451A753004CFF5D /* PLVJSBridge */,
				674F1C4722E58F5C0081D670 /* PLVAuthorizationManager */,
			);
			path = Library;
			sourceTree = "<group>";
		};
		674F1C4722E58F5C0081D670 /* PLVAuthorizationManager */ = {
			isa = PBXGroup;
			children = (
				674F1C4822E58F5C0081D670 /* PLVAuthorizationManager.h */,
				674F1C4922E58F5C0081D670 /* PLVAuthorizationManager.m */,
			);
			path = PLVAuthorizationManager;
			sourceTree = "<group>";
		};
		67BC2FC3246AA810005DE58D /* Reachability */ = {
			isa = PBXGroup;
			children = (
				67BC2FC4246AA838005DE58D /* PLVReachability.h */,
				67BC2FC5246AA838005DE58D /* PLVReachability.m */,
			);
			path = Reachability;
			sourceTree = "<group>";
		};
		67CE6C5C2451A753004CFF5D /* PLVJSBridge */ = {
			isa = PBXGroup;
			children = (
				67CE6C5E2451A784004CFF5D /* PLVJSBridge.h */,
				67CE6C5D2451A784004CFF5D /* PLVJSBridge.m */,
			);
			path = PLVJSBridge;
			sourceTree = "<group>";
		};
		69EEAAD2EB16DAA43C5C6DEA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				5F2B725B57A76353CC5D3CA4 /* Pods_PLVFoundationSDK.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F843BC3E1661EB5277B56391 /* Pods */ = {
			isa = PBXGroup;
			children = (
				D7B119F981ECF46E4A2AF9DA /* Pods-PolyvFoundationSDK.debug.xcconfig */,
				F3AE8AA93868797BF3C35412 /* Pods-PolyvFoundationSDK.release.xcconfig */,
				90B5D7D5C9A0F04440509A75 /* Pods-PLVFoundationSDK.debug.xcconfig */,
				B4492BAF6E3487CFCCBD22A6 /* Pods-PLVFoundationSDK.release.xcconfig */,
				7C84414CAAA0CACEF5DAF272 /* Pods-business-PLVFoundationSDK.debug.xcconfig */,
				70E828BD74F767034178AC50 /* Pods-business-PLVFoundationSDK.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		22142F9921130A8500062C6E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				004D460623A21860001905D7 /* PLVFErrorBaseCodeDefine.h in Headers */,
				22142FA121130A8500062C6E /* PLVFoundationSDK.h in Headers */,
				00466F8223B063BF00F968DC /* PLVFViewLogReporter.h in Headers */,
				004D462523A24B61001905D7 /* PLVFChatErrorCodeGenerator.h in Headers */,
				00F5E1C622E062CC00850840 /* PLVSafeModel.h in Headers */,
				002E89C223B215AC006F08D4 /* PLVFQosLogModel.h in Headers */,
				004D461523A24A24001905D7 /* PLVFDownloadErrorCodeGenerator.h in Headers */,
				004D461C23A24ADA001905D7 /* PLVFRecordErrorCodeGenerator.h in Headers */,
				005063F925E3AD15007A3277 /* PLVViewFrameUtil.h in Headers */,
				00F5E1CC22E0765600850840 /* PLVProgressHUD.h in Headers */,
				00466F8623B06A4A00F968DC /* PLVFViewLogModel.h in Headers */,
				217826AD2A13339100A363D7 /* PLVFTrackLogReporter.h in Headers */,
				0090E87B23CC669800A00C5C /* PLVFInteractionErrorCodeGenerator.h in Headers */,
				22D92D2621413134000FAF43 /* PLVConfig.h in Headers */,
				00727A0325CCDC72001CE398 /* PLVFWebViewJavascriptBridge_JS.h in Headers */,
				00727A0525CCDC72001CE398 /* PLVFWebViewJavascriptBridgeBase.h in Headers */,
				21958B862A131AEF00A03524 /* PLVFTrackLogModel.h in Headers */,
				21934BBE25982B8000153945 /* PLVMulticastDelegate.h in Headers */,
				22142FB321130FDC00062C6E /* PLVDataUtil.h in Headers */,
				FA46E4CA2797A69F00B6F36C /* PLVImageUtil.h in Headers */,
				FA46E4CE2797B4B500B6F36C /* PLVGLTexture.h in Headers */,
				36F60C9B2B05A38300600E53 /* PLVSM2Util.h in Headers */,
				3659FE9A2B049A9B00895F12 /* PLVFSignConfig.h in Headers */,
				225FF4F421182D9C000110F3 /* PLVNetworkError.h in Headers */,
				2299AA4E2114275E00BE675D /* PLVFNetworkUtil.h in Headers */,
				004D462923A24B8A001905D7 /* PLVFLinkErrorCodeGenerator.h in Headers */,
				00BD536B238E706D002BD784 /* PLVFBaseLogReporter.h in Headers */,
				00727A0025CCDC72001CE398 /* PLVFWKWebViewJavascriptBridge.h in Headers */,
				009F5AB72398E2D000ADFE25 /* PLVFELogReporter.h in Headers */,
				00BD53872390CC2A002BD784 /* PLVFLogReporter.h in Headers */,
				009F5AB32398DB0700ADFE25 /* PLVFLogModel.h in Headers */,
				004D462123A24B2D001905D7 /* PLVFRtmpErrorCodeGenerator.h in Headers */,
				6560F5D328D1BDDF00D5DFA4 /* PLVKeyUtil.h in Headers */,
				002E89BE23B21502006F08D4 /* PLVFQosLogReporter.h in Headers */,
				00BD5373238FB34B002BD784 /* PLVFELogModel.h in Headers */,
				36D19EE92AA84CBC00BB51C3 /* PLVFDI18NUtil.h in Headers */,
				00BD537F2390B54E002BD784 /* PLVFUserAgentBuilder.h in Headers */,
				004D45F723A092FE001905D7 /* PLVFPPTErrorCodeGenerator.h in Headers */,
				36733E2D2B314CB600262BB8 /* PLVSM2CryptoUtil.h in Headers */,
				0090E88023CF232900A00C5C /* PLVFInitErrorCodeGenerator.h in Headers */,
				004D461023A24949001905D7 /* PLVFPlayErrorCodeGenerator.h in Headers */,
				6355466024D3B4F500EF1CE1 /* PLVFWeakProxy.h in Headers */,
				652C701C29D5800000EF3DA5 /* PLVFLogan.h in Headers */,
				00A4C7FA234F27930054CF38 /* PLVColorUtil.h in Headers */,
				674F1C4A22E58F5C0081D670 /* PLVAuthorizationManager.h in Headers */,
				0090E87723CC663300A00C5C /* PLVFHttpErrorCodeGenerator.h in Headers */,
				009F5ABC239913B200ADFE25 /* PLVFFileUtil.h in Headers */,
				67019B9723BDEBA600D3D043 /* PLVFSocketErrorCodeGenerator.h in Headers */,
				004D461823A24A8A001905D7 /* PLVFUploadErrorCodeGenerator.h in Headers */,
				00BD538C23910C6F002BD784 /* PLVFJsonConverter.h in Headers */,
				007EDC9923FE6623007D911B /* PLVFConsoleLogger.h in Headers */,
				8F75BC562680989700D327AA /* PLVNetworkAccessibility.h in Headers */,
				67CE6C602451A784004CFF5D /* PLVJSBridge.h in Headers */,
				67BC2FC6246AA838005DE58D /* PLVReachability.h in Headers */,
				FA688A0F284CBC400099AEF7 /* PLVFBeautyErrorCodeGenerator.h in Headers */,
				672B33C323161137009B187B /* PLVFdUtil.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		22142F9B21130A8500062C6E /* PLVFoundationSDK */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 22142FA421130A8500062C6E /* Build configuration list for PBXNativeTarget "PLVFoundationSDK" */;
			buildPhases = (
				41982845ED78E88BD688BAD6 /* [CP] Check Pods Manifest.lock */,
				22142F9721130A8500062C6E /* Sources */,
				22142F9821130A8500062C6E /* Frameworks */,
				22142F9921130A8500062C6E /* Headers */,
				22142F9A21130A8500062C6E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PLVFoundationSDK;
			productName = PolyvFoundationSDK;
			productReference = 22142F9C21130A8500062C6E /* PLVFoundationSDK.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		22142F9321130A8500062C6E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1010;
				ORGANIZATIONNAME = PLV;
				TargetAttributes = {
					22142F9B21130A8500062C6E = {
						CreatedOnToolsVersion = 9.4.1;
						ProvisioningStyle = Automatic;
					};
					22E42BD12193DC66007CE190 = {
						CreatedOnToolsVersion = 10.0;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 22142F9621130A8500062C6E /* Build configuration list for PBXProject "PolyvFoundationSDK" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
			);
			mainGroup = 22142F9221130A8500062C6E;
			productRefGroup = 22142F9D21130A8500062C6E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				22142F9B21130A8500062C6E /* PLVFoundationSDK */,
				22E42BD12193DC66007CE190 /* PolyvFoundationSDKBuild */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		22142F9A21130A8500062C6E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		22E42BD52193DC6B007CE190 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/sh\n#要build的target名\nTARGET_NAME=${PROJECT_NAME}\nif [[ $1 ]]\nthen\nTARGET_NAME=$1\nfi\n\nUNIVERSAL_OUTPUT_FOLDER=\"${SRCROOT}/Products/\"\n\n#创建输出目录，并删除之前的framework文件\nmkdir -p \"${UNIVERSAL_OUTPUT_FOLDER}\"\nrm -rf \"${UNIVERSAL_OUTPUT_FOLDER}/${TARGET_NAME}.framework\"\n\n#分别编译模拟器和真机的Framework\nxcodebuild -target \"${TARGET_NAME}\" ONLY_ACTIVE_ARCH=NO -configuration ${CONFIGURATION} -sdk iphoneos BUILD_DIR=\"${BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\" clean build\nxcodebuild -target \"${TARGET_NAME}\" ONLY_ACTIVE_ARCH=NO -configuration ${CONFIGURATION} -sdk iphonesimulator BUILD_DIR=\"${BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\" clean build\n\n#拷贝framework到univer目录\ncp -R \"${BUILD_DIR}/${CONFIGURATION}-iphoneos/${TARGET_NAME}.framework\" \"${UNIVERSAL_OUTPUT_FOLDER}\"\n\n#合并framework，输出最终的framework到build目录\nlipo -create -output \"${UNIVERSAL_OUTPUT_FOLDER}/${TARGET_NAME}.framework/${TARGET_NAME}\" \"${BUILD_DIR}/${CONFIGURATION}-iphonesimulator/${TARGET_NAME}.framework/${TARGET_NAME}\" \"${BUILD_DIR}/${CONFIGURATION}-iphoneos/${TARGET_NAME}.framework/${TARGET_NAME}\"\n\n#删除编译之后生成的无关的配置文件\ndir_path=\"${UNIVERSAL_OUTPUT_FOLDER}/${TARGET_NAME}.framework/\"\nfor file in ls $dir_path\ndo\nif [[ ${file} =~ \".xcconfig\" ]]\nthen\nrm -f \"${dir_path}/${file}\"\nfi\ndone\n#判断build文件夹是否存在，存在则删除\nif [ -d \"${SRCROOT}/build\" ]\nthen\nrm -rf \"${SRCROOT}/build\"\nfi\nrm -rf \"${BUILD_DIR}/${CONFIGURATION}-iphonesimulator\" \"${BUILD_DIR}/${CONFIGURATION}-iphoneos\"\n#打开合并后的文件夹\nopen \"${UNIVERSAL_OUTPUT_FOLDER}\"\n";
		};
		41982845ED78E88BD688BAD6 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-PLVFoundationSDK-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		22142F9721130A8500062C6E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00BD53802390B54E002BD784 /* PLVFUserAgentBuilder.m in Sources */,
				36F60C9C2B05A38300600E53 /* PLVSM2Util.m in Sources */,
				004D462823A24B8A001905D7 /* PLVFLinkErrorCodeGenerator.m in Sources */,
				00727A0425CCDC72001CE398 /* PLVFWKWebViewJavascriptBridge.m in Sources */,
				004D45F823A092FE001905D7 /* PLVFPPTErrorCodeGenerator.m in Sources */,
				00BD538D23910C6F002BD784 /* PLVFJsonConverter.m in Sources */,
				00A4C7FB234F27930054CF38 /* PLVColorUtil.m in Sources */,
				652C701E29D5800D00EF3DA5 /* PLVFLogan.m in Sources */,
				674F1C4B22E58F5C0081D670 /* PLVAuthorizationManager.m in Sources */,
				6560F5D428D1BDDF00D5DFA4 /* PLVKeyUtil.m in Sources */,
				004D461423A24A24001905D7 /* PLVFDownloadErrorCodeGenerator.m in Sources */,
				004D462423A24B61001905D7 /* PLVFChatErrorCodeGenerator.m in Sources */,
				00BD536C238E706D002BD784 /* PLVFBaseLogReporter.m in Sources */,
				FA46E4CF2797B4B500B6F36C /* PLVGLTexture.m in Sources */,
				005063FA25E3AD15007A3277 /* PLVViewFrameUtil.m in Sources */,
				007EDC9723FE6600007D911B /* PLVFConsoleLogger.m in Sources */,
				00727A0225CCDC72001CE398 /* PLVFWebViewJavascriptBridgeBase.m in Sources */,
				22142FB521130FDC00062C6E /* PLVDataUtil.m in Sources */,
				00727A0125CCDC72001CE398 /* PLVFWebViewJavascriptBridge_JS.m in Sources */,
				67BC2FC7246AA838005DE58D /* PLVReachability.m in Sources */,
				FA688A10284CBC400099AEF7 /* PLVFBeautyErrorCodeGenerator.m in Sources */,
				2299AA4D2114275E00BE675D /* PLVFNetworkUtil.m in Sources */,
				36D19EEA2AA84CBC00BB51C3 /* PLVFDI18NUtil.m in Sources */,
				004D461D23A24ADA001905D7 /* PLVFRecordErrorCodeGenerator.m in Sources */,
				00466F8323B063BF00F968DC /* PLVFViewLogReporter.m in Sources */,
				21934BBD25982B8000153945 /* PLVMulticastDelegate.m in Sources */,
				67019B9823BDEBA600D3D043 /* PLVFSocketErrorCodeGenerator.m in Sources */,
				36733E2E2B314CB600262BB8 /* PLVSM2CryptoUtil.m in Sources */,
				0090E87C23CC669800A00C5C /* PLVFInteractionErrorCodeGenerator.m in Sources */,
				6355466124D3B4F500EF1CE1 /* PLVFWeakProxy.m in Sources */,
				004D461123A24949001905D7 /* PLVFPlayErrorCodeGenerator.m in Sources */,
				0090E87F23CF232900A00C5C /* PLVFInitErrorCodeGenerator.m in Sources */,
				00466F8723B06A4A00F968DC /* PLVFViewLogModel.m in Sources */,
				009F5AB82398E2D000ADFE25 /* PLVFELogReporter.m in Sources */,
				21958B872A131AEF00A03524 /* PLVFTrackLogModel.m in Sources */,
				002E89BF23B21502006F08D4 /* PLVFQosLogReporter.m in Sources */,
				FA46E4CB2797A69F00B6F36C /* PLVImageUtil.m in Sources */,
				004D461923A24A8A001905D7 /* PLVFUploadErrorCodeGenerator.m in Sources */,
				3659FE9B2B049A9B00895F12 /* PLVFSignConfig.m in Sources */,
				00BD53882390CC2A002BD784 /* PLVFLogReporter.m in Sources */,
				002E89C323B215AC006F08D4 /* PLVFQosLogModel.m in Sources */,
				225FF4F521182D9C000110F3 /* PLVNetworkError.m in Sources */,
				009F5ABB239913B200ADFE25 /* PLVFFileUtil.m in Sources */,
				8F75BC572680989700D327AA /* PLVNetworkAccessibility.m in Sources */,
				00BD5374238FB34B002BD784 /* PLVFELogModel.m in Sources */,
				004D460723A21860001905D7 /* PLVFErrorBaseCodeDefine.m in Sources */,
				00F5E1C722E062CC00850840 /* PLVSafeModel.m in Sources */,
				0090E87823CC663300A00C5C /* PLVFHttpErrorCodeGenerator.m in Sources */,
				217826AE2A13339100A363D7 /* PLVFTrackLogReporter.m in Sources */,
				672B33C423161137009B187B /* PLVFdUtil.m in Sources */,
				009F5AB42398DB0700ADFE25 /* PLVFLogModel.m in Sources */,
				004D462023A24B2D001905D7 /* PLVFRtmpErrorCodeGenerator.m in Sources */,
				67CE6C5F2451A784004CFF5D /* PLVJSBridge.m in Sources */,
				00F5E1CB22E0765600850840 /* PLVProgressHUD.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		22142FA221130A8500062C6E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.4;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		22142FA321130A8500062C6E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		22142FA521130A8500062C6E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 90B5D7D5C9A0F04440509A75 /* Pods-PLVFoundationSDK.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				HEADER_SEARCH_PATHS = "";
				INFOPLIST_FILE = PolyvFoundationSDK/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/../../Frameworks";
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.1.0;
				OTHER_CFLAGS = "-fembed-bitcode";
				PRODUCT_BUNDLE_IDENTIFIER = polyv.PolyvFoundationSDK;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		22142FA621130A8500062C6E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B4492BAF6E3487CFCCBD22A6 /* Pods-PLVFoundationSDK.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				HEADER_SEARCH_PATHS = "";
				INFOPLIST_FILE = PolyvFoundationSDK/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/../../Frameworks";
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.1.0;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = "-fembed-bitcode";
				PRODUCT_BUNDLE_IDENTIFIER = polyv.PolyvFoundationSDK;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		22E42BD22193DC66007CE190 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 8BMZBNN3Q5;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		22E42BD32193DC66007CE190 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 8BMZBNN3Q5;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		22142F9621130A8500062C6E /* Build configuration list for PBXProject "PolyvFoundationSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22142FA221130A8500062C6E /* Debug */,
				22142FA321130A8500062C6E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		22142FA421130A8500062C6E /* Build configuration list for PBXNativeTarget "PLVFoundationSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22142FA521130A8500062C6E /* Debug */,
				22142FA621130A8500062C6E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		22E42BD42193DC66007CE190 /* Build configuration list for PBXAggregateTarget "PolyvFoundationSDKBuild" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22E42BD22193DC66007CE190 /* Debug */,
				22E42BD32193DC66007CE190 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 22142F9321130A8500062C6E /* Project object */;
}
