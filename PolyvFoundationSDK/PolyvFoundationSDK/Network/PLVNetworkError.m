//
//  PLVNetworkError.m
//  PLVFoundationSDK
//
//  Created by zykhbl on 2018/8/6.
//  Copyright © 2018年 PLV. All rights reserved.
//

#import "PLVNetworkError.h"
#import "PLVFDI18NUtil.h"

@implementation PLVNetworkError

+ (NSString*)descWithCode:(NSInteger)code {
    switch (code) {
        case PLVNetworkErrorCodeParamsInvalid:{
            return PLVFDLocalizableString(@"参数无效/非法");
        }
        case PLVNetworkErrorCodeResponseFail: {
            return PLVFDLocalizableString(@"服务器请求失败异常，响应非200");
        }
        case PLVNetworkErrorCodeJsonDecodeFail: {
            return PLVFDLocalizableString(@"JSON解析失败");
        }
        case PLVNetworkErrorCodeEncodeJsonDecodeFail: {
            return PLVFDLocalizableString(@"加密JSON解析失败");
        }
        case PLVNetworkErrorCodeDecodeFail: {
            return PLVFDLocalizableString(@"解码失败（异常）");
        }
        case PLVNetworkErrorCodeLoginFail: {
            return PLVFDLocalizableString(@"登录失败（登录接口）");
        }
        case PLVNetworkErrorCodeSettingFail: {
            return PLVFDLocalizableString(@"设置失败");
        }
        case PLVNetworkErrorCodeRequestFail: {
            return PLVFDLocalizableString(@"请求失败(解析JSON数据中code非200)");
        }
        default: {
            return PLVFDLocalizableString(@"未知错误");
        }
    }
}

@end
