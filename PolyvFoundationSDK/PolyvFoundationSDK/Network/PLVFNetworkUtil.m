//
//  PLVFNetworkUtil.m
//  PLVLiveSDK
//
//  Created by ftao on 26/10/2017.
//  Copyright © 2017 PLV. All rights reserved.
//

#import "PLVFNetworkUtil.h"
#import "PLVDataUtil.h"
#import "PLVKeyUtil.h"
#import "PLVSM2Util.h"
#import "PLVFSignConfig.h"
#import "PLVFConsoleLogger.h"

#define FoundationSDKErrorDomain @"polyv.foundationSDK"
@implementation PLVFNetworkUtil

+ (void)initialize {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        // increase the number of simultaneous persistent connections per host, in case connection timeout
        [NSURLSession sharedSession].configuration.HTTPMaximumConnectionsPerHost = 10;
    });
}

+ (NSString*)paramStr:(NSDictionary *)params {
    NSArray *keys = params.allKeys;
    //??点播VOD时是否必须对参数排序??
//    keys = [keys sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
//        return [obj1 compare:obj2 options:NSCaseInsensitiveSearch];
//    }];
    
    NSMutableString *paramStr = [NSMutableString string];
    for (NSString *key in keys) {
        [paramStr appendFormat:@"%@=%@&", key, params[key]];
    }
    return [paramStr substringToIndex:paramStr.length - 1];
}

+ (NSMutableURLRequest *)requestWithURLString:(NSString *)url params:(NSDictionary *)params httpMethod:(NSString *)httpMethod timeoutInterval:(NSTimeInterval)timeoutInterval userAgent:(NSString *)userAgent {
    NSMutableURLRequest *request = [PLVFNetworkUtil requestWithURLString:url params:params httpMethod:httpMethod timeoutInterval:timeoutInterval userAgent:userAgent headerParams:nil];
    return request;
}

+ (NSMutableURLRequest *)requestWithURLString:(NSString *)url params:(NSDictionary *)params httpMethod:(NSString *)httpMethod timeoutInterval:(NSTimeInterval)timeoutInterval userAgent:(NSString *)userAgent headerParams:(NSDictionary * _Nullable)headerParams {
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] init];
    if (params && params.count > 0) {
        NSString *parameters = [PLVFNetworkUtil paramStr:params];
        if ([httpMethod isEqualToString:PLV_HM_GET] || [httpMethod isEqualToString:PLV_HM_DELETE]) {
            if ([url containsString:@"?"]) {
                url = [url stringByAppendingFormat:@"&%@", parameters];
            } else {
                url = [url stringByAppendingFormat:@"?%@", parameters];
            }
        } else if ([httpMethod isEqualToString:PLV_HM_POST]) {//set http body.
            [request setHTTPBody:[parameters dataUsingEncoding:NSUTF8StringEncoding]];
        }
    }
    
    if (headerParams && headerParams.count > 0) {
        NSArray *keys = headerParams.allKeys;
        for (NSString *key in keys) {
            NSString *value = headerParams[key];
            [request setValue:value forHTTPHeaderField:key];
        }
    }
    
    [request setHTTPMethod:httpMethod];
    [request setURL:[NSURL URLWithString:url]];
    [request setTimeoutInterval:timeoutInterval];
    [request setCachePolicy:NSURLRequestReloadIgnoringCacheData];
    [request setValue:userAgent forHTTPHeaderField:@"User-Agent"];
    
    return request;
}

+ (NSMutableURLRequest *)jsonBodyPostRequestWithURLString:(NSString *)url
                                              paramString:(NSString *)paramString
                                          timeoutInterval:(NSTimeInterval)timeoutInterval
                                                userAgent:(NSString *)userAgent {
    NSMutableURLRequest *request = [PLVFNetworkUtil jsonBodyPostRequestWithURLString:url paramString:paramString timeoutInterval:timeoutInterval userAgent:userAgent headerParams:nil];
    return request;
}

+ (NSMutableURLRequest *)jsonBodyPostRequestWithURLString:(NSString *)url
                                              paramString:(NSString *)paramString
                                          timeoutInterval:(NSTimeInterval)timeoutInterval
                                                userAgent:(NSString *)userAgent
                                             headerParams:(NSDictionary * _Nullable)headerParams {
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] init];
    if (paramString && [paramString isKindOfClass:[NSString class]] && paramString.length > 0) {
        [request setHTTPBody:[paramString dataUsingEncoding:NSUTF8StringEncoding]];
    }
    
    if (headerParams && headerParams.count > 0) {
        NSArray *keys = headerParams.allKeys;
        for (NSString *key in keys) {
            NSString *value = headerParams[key];
            [request setValue:value forHTTPHeaderField:key];
        }
    }
    
    [request setHTTPMethod:PLV_HM_POST];
    [request setURL:[NSURL URLWithString:url]];
    [request setTimeoutInterval:timeoutInterval];
    [request setCachePolicy:NSURLRequestReloadIgnoringCacheData];
    [request setValue:userAgent forHTTPHeaderField:@"User-Agent"];
    
    return request;
}

+ (void)request:(NSURLRequest *)request
        success:(void (^)(id responseObject))success
        failure:(void (^)(NSError *error))failure {
    NSString *httpMethod = request.HTTPMethod;
    NSString *logMessage = [NSString stringWithFormat:@"%@ %@", httpMethod, request.URL.absoluteString];
    if ([httpMethod isEqualToString:PLV_HM_POST]) {
        NSString *bodyString = [[NSString alloc] initWithData:request.HTTPBody encoding:NSUTF8StringEncoding];
        PLVF_NORMAL_LOG_DEBUG(@"Network", @"--> %@ %@", logMessage, bodyString);
    } else {
        PLVF_NORMAL_LOG_DEBUG(@"Network", @"--> %@", logMessage);
    }
    [[[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
        
        dispatch_async(dispatch_get_main_queue(), ^{//切入主线程执行
            if (!error && [httpResponse statusCode] == 200) { // Request success
                PLVF_NORMAL_LOG_DEBUG(@"Network", @"<-- %ld %@", (long)httpResponse.statusCode, logMessage);
                if (success) {
                    NSError *jsonError = nil;
                    id jsonObj = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&jsonError];
                    if (jsonError) {// 返回字符串 body 内容
                        NSString *str = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
                        NSString *content = [str stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
                        success(content);
                    } else {
                        success(jsonObj);
                    }
                }
            } else {
                if (error) { // 网络异常
                    PLVF_NORMAL_LOG_ERROR(@"Network", @"<-- error:%@ %@", error.localizedDescription, logMessage);
                    if (failure) {
                        failure(error);
                    }
                } else { // 服务器异常，httpResponse的statusCode非200
                    NSString *description = [NSString stringWithFormat:@"HTTP status code %ld", (long)httpResponse.statusCode];
                    NSError *jsonError = nil;
                    id jsonObj = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&jsonError];
                    if (!jsonError && jsonObj && [jsonObj isKindOfClass:[NSDictionary class]]) {
                        NSDictionary *jsonDict = (NSDictionary *)jsonObj;
                        if (jsonDict[@"message"]) {
                            description = [description stringByAppendingFormat:@"（message:%@）", jsonDict[@"message"]];
                        }
                    }
                    NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeResponseFail desc:description];
                    PLVF_NORMAL_LOG_ERROR(@"Network", @"<-- error:%@ %@", error.localizedDescription, logMessage);
                    if (failure) {
                        failure(error);
                    }
                }
            }
        });
    }] resume];
}

+ (void)requestData:(NSURLRequest *)request completion:(void (^)(NSData * _Nullable data, NSURLResponse * _Nullable response))completion fail:(void (^)(NSError *error))fail {
    NSString *httpMethod = request.HTTPMethod;
    NSString *logMessage = [NSString stringWithFormat:@"%@ %@", httpMethod, request.URL.absoluteString];
    if ([httpMethod isEqualToString:PLV_HM_POST]) {
        NSString *bodyString = [[NSString alloc] initWithData:request.HTTPBody encoding:NSUTF8StringEncoding];
        PLVF_NORMAL_LOG_DEBUG(@"Network", @"--> %@ %@", logMessage, bodyString);
    } else {
        PLVF_NORMAL_LOG_DEBUG(@"Network", @"--> %@", logMessage);
    }
    [[[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
        
        dispatch_async(dispatch_get_main_queue(), ^{//切入主线程执行
            if (error) {//1.网络异常，NSError不做处理，原地上抛
                PLVF_NORMAL_LOG_ERROR(@"Network", @"<-- error:%@ %@", error.localizedDescription, logMessage);
                if (fail) {
                    fail(error);
                }
            } else if ([httpResponse statusCode] != 200) {//2.服务器异常，NSURLResponse的statusCode非200，生成服务器异常PLVNetworkError，上抛
                NSError *jsonError = nil;
                id jsonObj = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&jsonError];
                NSString *description;
                if (jsonError) {
                    description = jsonError.localizedDescription;
                } else if (jsonObj != nil && [jsonObj isKindOfClass:[NSDictionary class]]) {
                    NSDictionary *dict = (NSDictionary *)jsonObj;
                    description = [NSString stringWithFormat:@"message:%@, statusCode: %ld", dict[@"message"], (long)httpResponse.statusCode];
                } else {
                    description = [NSString stringWithFormat:@"statusCode: %ld", (long)httpResponse.statusCode];
                }
                PLVF_NORMAL_LOG_ERROR(@"Network", @"<-- error:%@ %@", description, logMessage);
                if (fail) {
                    [PLVFNetworkUtil fail:fail errorCode:PLVNetworkErrorCodeResponseFail desc:description];
                }
            } else {//3.Request success.
                PLVF_NORMAL_LOG_DEBUG(@"Network", @"<-- 200 %@", logMessage);
                if (completion) {
                    completion(data, response);
                }
            }
        });
    }] resume];
}

+ (void)requestNetwork:(NSURLRequest *)request completion:(void (^)(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont))completion fail:(void (^)(NSError *error))fail {
    [self requestData:request completion:^(NSData * _Nullable data, NSURLResponse * _Nullable response) {
        NSError *jsonError = nil;
        id jsonObj = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&jsonError];
        if (jsonError) {//3.1 返回字符串 body 内容
            NSString *str = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
            NSString *content = [str stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
            if (completion) {
                completion(nil, nil, content);
            }
        } else {
            if ([jsonObj isKindOfClass:[NSDictionary class]]) {//3.2 返回字典 body 内容
                NSDictionary *dict = (NSDictionary *)jsonObj;
                if (completion) {
                    NSDictionary *responseDict = [self verifyDecryptDataInDictionary:dict];
                    completion(responseDict, nil, nil);
                }
            } else {//3.3 返回数组 body 内容
                NSArray *arr = (NSArray *)jsonObj;
                if (completion) {
                    completion(nil, arr, nil);
                }
            }
        }
    } fail:fail];
}

+ (void)requestDictionary:(NSURLRequest *)request completion:(void (^)(NSDictionary *responseDict))completion fail:(void (^)(NSError *error))fail {
    [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
        if (responseDict) {
            if (completion) {
                completion(responseDict);
            }
        } else if (responseArr) {
            [PLVFNetworkUtil fail:fail errorCode:PLVNetworkErrorCodeDecodeFail desc:[responseArr description]];
        } else {
            [PLVFNetworkUtil fail:fail errorCode:PLVNetworkErrorCodeDecodeFail desc:responseCont];
        }
    } fail:fail];
}

+ (void)requestArray:(NSURLRequest *)request completion:(void (^)(NSArray *responseArr))completion fail:(void (^)(NSError *error))fail {
    [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
        if (responseArr) {
            if (completion) {
                completion(responseArr);
            }
        } else if (responseDict) {
            [PLVFNetworkUtil fail:fail errorCode:PLVNetworkErrorCodeDecodeFail desc:[responseDict description]];
        } else {
            [PLVFNetworkUtil fail:fail errorCode:PLVNetworkErrorCodeDecodeFail desc:responseCont];
        }
    } fail:fail];
}

+ (void)requestString:(NSURLRequest *)request completion:(void (^)(NSString *responseCont))completion fail:(void (^)(NSError *error))fail {
    [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
        if (responseCont) {
            if (completion) {
                completion(responseCont);
            }
        } else if (responseDict) {
            [PLVFNetworkUtil fail:fail errorCode:PLVNetworkErrorCodeDecodeFail desc:[responseDict description]];
        } else {
            [PLVFNetworkUtil fail:fail errorCode:PLVNetworkErrorCodeDecodeFail desc:[responseArr description]];
        }
    } fail:fail];
}

+ (void)requestFileWithRequest:(NSURLRequest *)request toFilePath:(NSString *)toFilePath completion:(void (^)(NSString * _Nonnull filePath))completion fail:(void (^)(NSError * _Nonnull))fail {
    NSString *httpMethod = request.HTTPMethod;
    NSString *logMessage = [NSString stringWithFormat:@"%@ %@", httpMethod, request.URL.absoluteString];
    if ([httpMethod isEqualToString:PLV_HM_POST]) {
        NSString *bodyString = [[NSString alloc] initWithData:request.HTTPBody encoding:NSUTF8StringEncoding];
        PLVF_NORMAL_LOG_DEBUG(@"Network", @"--> %@ %@", logMessage, bodyString);
    } else {
        PLVF_NORMAL_LOG_DEBUG(@"Network", @"--> %@", logMessage);
    }
    [[[NSURLSession sharedSession] downloadTaskWithRequest:request completionHandler:^(NSURL * _Nullable location, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
        if (error) {//1.网络异常，NSError不做处理，原地上抛
            PLVF_NORMAL_LOG_ERROR(@"Network", @"<-- error:%@ %@", error.localizedDescription, logMessage)
            if (fail) {
                fail(error);
            }
        } else if ([httpResponse statusCode] != 200) {//2.服务器异常，NSURLResponse的statusCode非200，生成服务器异常PLVNetworkError，上抛
            PLVF_NORMAL_LOG_ERROR(@"Network", @"<-- error:%ld %@", (long)httpResponse.statusCode, logMessage);
            if (fail) {
                [PLVFNetworkUtil fail:fail errorCode:PLVNetworkErrorCodeResponseFail desc:[NSString stringWithFormat:@"statusCode: %ld", (long)httpResponse.statusCode]];
            }
        } else {//3.Request success.
            PLVF_NORMAL_LOG_ERROR(@"Network", @"<-- 200 %@",logMessage);
            if (completion) { // 下载成功后，只能在回调的线程处理文件。切换线程后临时文件将会被删除，copy文件时会报260错误。
                BOOL copy = [[NSFileManager defaultManager] copyItemAtPath:location.path toPath:toFilePath error:&error];
                if (copy) {
                    completion(toFilePath);
                } else {
                    fail ? fail(error) : nil;
                }
            }
        }
    }] resume];
}

+ (void)fail:(void (^)(NSError *))fail errorCode:(PLVNetworkErrorCode)errorCode desc:(NSString*)desc {
    if (fail) {
        NSError *error = [self failWitErrorCode:errorCode desc:desc];
        fail(error);
    }
}

+ (NSError *)failWitErrorCode:(PLVNetworkErrorCode)errorCode desc:(NSString *)desc {
    if (desc == nil) {
        desc = [PLVNetworkError descWithCode:errorCode];
    }
    NSDictionary *userInfo = [NSDictionary dictionaryWithObject:desc forKey:NSLocalizedDescriptionKey];
    return [PLVNetworkError errorWithDomain:FoundationSDKErrorDomain code:errorCode userInfo:userInfo];
}

/// 响应字段包含encryption且值为true代表data字段被加密
+ (BOOL)dataNeedDecrypt:(NSDictionary *)responseDict {
    if (responseDict && [responseDict isKindOfClass:[NSDictionary class]] && [responseDict count] > 0) {
        return [[responseDict objectForKey:@"encryption"] boolValue];
    } else {
        return NO;
    }
}

/// 输入响应数据，判断响应数据是否需要解密，如果需要，返回解密后的数据，不需要，则原数据返回
+ (NSDictionary *)verifyDecryptDataInDictionary:(NSDictionary *)responseDict {
    if (![self dataNeedDecrypt:responseDict]) {
        return responseDict;
    }
    id data = [responseDict objectForKey:@"data"];
    id decryptedData = [self parseDecryptData:data];
    if (decryptedData) {
        NSMutableDictionary *resultDict = [[NSMutableDictionary alloc] initWithDictionary:responseDict];
        [resultDict setObject:decryptedData forKey:@"data"];
        return [resultDict copy];
    }
    
    return responseDict;
}

+ (id)parseDecryptData:(id)data {
    NSString *encryptedString = (NSString *)data;
    if (!encryptedString || ![encryptedString isKindOfClass:[NSString class]] || encryptedString.length == 0) {
        return nil;
    }
    
    id decryptedData = nil;
    if ([PLVFSignConfig sharedInstance].encryptType == PLVEncryptType_SM2) {
        /// 外部密钥解码
        NSString *decryptedSM2String = [PLVSM2Util formatDecryptString:encryptedString privateKey:[PLVFSignConfig sharedInstance].clientSM2PrivateKey];

        /// 内部密钥解码
        if (!decryptedSM2String || decryptedSM2String.length == 0) {
            decryptedSM2String = [PLVSM2Util formatDecryptString:encryptedString privateKey:[PLVKeyUtil getApiUtilsPrivateKey]];
        }
        
        if (decryptedSM2String && decryptedSM2String.length > 0) {
            decryptedData = [decryptedSM2String dataUsingEncoding:NSUTF8StringEncoding];
        }
    } else {
        NSData *decodeData = [[NSData alloc] initWithBase64EncodedString:encryptedString options:NSDataBase64DecodingIgnoreUnknownCharacters];
        if (decodeData) {
            /// AES 解密
            decryptedData = [PLVDataUtil AES128DecryptData:decodeData withKey:[PLVKeyUtil getUtilsKey] iv:[PLVKeyUtil getUtilsIv]];
        }
    }

    if (decryptedData) {
        id dataDecryptedObject = [NSJSONSerialization JSONObjectWithData:decryptedData
                                                                 options:NSJSONReadingMutableContainers
                                                                   error:nil];
        if (!dataDecryptedObject) {
            dataDecryptedObject = [[NSString alloc] initWithData:decryptedData encoding:NSUTF8StringEncoding];
        }
        
        return dataDecryptedObject;
    }
    
    return nil;
}

@end
