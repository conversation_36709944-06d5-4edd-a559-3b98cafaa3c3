//
//  PLVFSignConfig.m
//  PLVFoundationSDK
//
//  Created by Saky<PERSON> on 2023/11/15.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVFSignConfig.h"

@implementation PLVFSignConfig

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static PLVFSignConfig *signConfig = nil;
    dispatch_once(&onceToken, ^{
        signConfig = [[self alloc] init];
    });
    return signConfig;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _encryptType = PLVEncryptType_AES;
    }
    return self;
}

- (void)setSM2EncryptTypeWithServerSM2PublicKey:(NSString *)serverSM2PublicKey clientSM2PrivateKey:(NSString *)clientSM2PrivateKey {
    _encryptType = PLVEncryptType_SM2;
    _serverSM2PublicKey = serverSM2PublicKey;
    _clientSM2PrivateKey = clientSM2PrivateKey;
}

@end
