//
//  PLVViewFrameUtil.m
//  PLVFoundationSDK
//
//  Created by <PERSON> on 2021/1/29.
//  Copyright © 2021 PLV. All rights reserved.
//  UIView frame工具类

#import "PLVViewFrameUtil.h"

@implementation PLVViewFrameUtil

+ (CGFloat)getTop:(UIView *)view {
    return view.frame.origin.y;
}

+ (void)setTop:(CGFloat)top view:(UIView *)view {
    CGRect frame = view.frame;
    frame.origin.y = top;
    view.frame = frame;
}

+ (CGFloat)getBottom:(UIView *)view {
    return UIViewGetTop(view) + UIViewGetHeight(view);
}

+ (void)setBottom:(CGFloat)bottom view:(UIView *)view {
    UIViewSetTop(view, bottom - UIViewGetHeight(view));
}

+ (CGFloat)getLeft:(UIView *)view {
    return view.frame.origin.x;
}

+ (void)setLeft:(CGFloat)left view:(UIView *)view {
    CGRect frame = view.frame;
    frame.origin.x = left;
    view.frame = frame;
}

+ (CGFloat)getRight:(UIView *)view {
    return [self getLeft:view] + [self getWidth:view];
}

+ (void)setRight:(CGFloat)right view:(UIView *)view {
    [self setLeft:right - [self getWidth:view] view:view];
}

+ (CGFloat)getWidth:(UIView *)view {
    return view.frame.size.width;
}

+ (void)setPlvWidth:(CGFloat)width view:(UIView *)view {
    CGRect frame = view.frame;
    frame.size.width = width;
    view.frame = frame;
}

+ (CGFloat)getHeight:(UIView *)view {
    return view.frame.size.height;
}

+ (void)setHeight:(CGFloat)height view:(UIView *)view {
    CGRect frame = view.frame;
    frame.size.height = height;
    view.frame = frame;
}

@end
