//
//  PLVSM2Util.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2023/11/14.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVSM2Util.h"

#define PLVSM2CryptoUtilCLS NSClassFromString(@"PLVSM2CryptoUtil")

@implementation PLVSM2Util

+ (NSArray<NSString *> *)createKeyPair {
    return [PLVSM2CryptoUtilCLS createKeyPair];
}

+ (void)setEllipticCurveType:(int)curveType {
    [PLVSM2CryptoUtilCLS setEllipticCurveType:curveType];
}

+ (NSString *)formatEncryptString:(NSString *)plainString publicKey:(NSString *)publicKey {
    return [PLVSM2CryptoUtilCLS formatEncryptString:plainString publicKey:publicKey];
}

+ (nullable NSString *)encryptString:(NSString *)plainString publicKey:(NSString *)publicKey {
    return [PLVSM2CryptoUtilCLS encryptString:plainString publicKey:publicKey];
}

+ (nullable NSString *)formatDecryptString:(NSString *)ciphertext privateKey:(NSString *)privateKey {
    return [PLVSM2CryptoUtilCLS formatDecryptString:ciphertext privateKey:privateKey];
}

+ (nullable NSString *)decryptString:(NSString *)ciphertext privateKey:(NSString *)privateKey {
    return [PLVSM2CryptoUtilCLS formatDecryptString:ciphertext privateKey:privateKey];
}

+ (nullable NSString *)convertC1C2C3ToC1C3C2:(NSString *)ciphertext hasPrefix:(BOOL)hasPrefix {
    return [PLVSM2CryptoUtilCLS convertC1C2C3ToC1C3C2:ciphertext hasPrefix:hasPrefix];
}

+ (nullable NSString *)convertC1C3C2ToC1C2C3:(NSString *)ciphertext hasPrefix:(BOOL)hasPrefix {
    return [PLVSM2CryptoUtilCLS convertC1C3C2ToC1C2C3:ciphertext hasPrefix:hasPrefix];
}

+ (nullable NSString *)asn1EncodeWithC1C3C2:(NSString *)c1c3c2Hex {
    return [PLVSM2CryptoUtilCLS asn1EncodeWithC1C3C2:c1c3c2Hex];
}

+ (nullable NSString *)asn1DecodeToC1C3C2:(NSString *)asn1Hex {
    return [PLVSM2CryptoUtilCLS asn1DecodeToC1C3C2:asn1Hex];
}

+ (nullable NSArray<NSString *> *)asn1DecodeToC1C3C2Array:(NSString *)asn1Hex {
    return [PLVSM2CryptoUtilCLS asn1DecodeToC1C3C2Array:asn1Hex];
}

@end
