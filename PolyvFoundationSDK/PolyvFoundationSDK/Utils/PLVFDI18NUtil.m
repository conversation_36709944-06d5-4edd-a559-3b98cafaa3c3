//
//  PLVFDI18NUtil.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2023/9/6.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVFDI18NUtil.h"
#import "PLVFdUtil.h"

@interface PLVFDI18NUtil ()

@property (nonatomic, strong) NSBundle *localizableBundle;

@end

@implementation PLVFDI18NUtil

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static PLVFDI18NUtil *config = nil;
    dispatch_once(&onceToken, ^{
        config = [[PLVFDI18NUtil alloc] init];
    });
    return config;
}

- (void)setPreferredLanguage:(NSString *)preferredLanguage {
    _preferredLanguage = preferredLanguage;
}

- (void)setupLocalizableBundle:(NSBundle *)localizableBundle {
    _localizableBundle = localizableBundle;
}

+ (NSString *)plv_localizedStringForKey:(NSString *)key value:(nullable NSString *)value {
    if (![PLVFdUtil checkStringUseable:key]) {
        return key;
    }
    
    NSBundle *localizableBundle = [PLVFDI18NUtil sharedInstance].localizableBundle;
    if (!localizableBundle) {
        value = [[NSBundle mainBundle] localizedStringForKey:key value:value table:nil];
        return value;
    }
    
    NSString *language = [self plv_localizableLanguageKey];
    NSBundle *bundle = [NSBundle bundleWithPath:[localizableBundle pathForResource:language ofType:@"lproj"]];
    value = [bundle localizedStringForKey:key value:value table:nil];
    return value;
}

+ (NSString *)plv_localizableLanguageKey {
    NSString *language = [PLVFDI18NUtil sharedInstance].preferredLanguage;
    // 如果配置中没有配置语言
    if (!language) {
        // 默认跟随系统
        language = [NSLocale preferredLanguages].firstObject;
    }
    if ([language hasPrefix:@"en"]) {
        language = @"en";
    } else if ([language hasPrefix:@"zh"]) {
        language = @"zh-Hans"; // 简体中文
    } else {
        language = @"en";
    }
    return language;
}

@end
