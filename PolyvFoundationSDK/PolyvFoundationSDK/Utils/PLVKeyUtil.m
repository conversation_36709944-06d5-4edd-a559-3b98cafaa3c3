//
//  PLVKeyUtil.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON> on 2022/9/14.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVKeyUtil.h"
#import "PLVFFileUtil.h"
#import "PLVFdUtil.h"

@implementation PLVKeyUtil

#pragma mark - key工具类常量

NSString *const ALI_DNS_AUTH_KEY = @"aliDnsAuthKey";

NSString *const ALI_DNS_AUTH_IV = @"aliDnsAuthIv";

NSString *const LINK_MIC_AUTH_KEY = @"linkMicAuthKey";

NSString *const LINK_MIC_AUTH_IV = @"linkMicAuthIv";

NSString *const UTILS_AUTH_KEY = @"utilsAuthKey";

NSString *const UTILS_AUTH_IV = @"utilsAuthIv";

NSString *const CHANNEL_INFO_AUTH_KEY = @"channelInfoAuthKey";

NSString *const CHANNEL_INFO_AUTH_IV = @"channelInfoAuthIv";

NSString *const API_UTILS_AUTH_KEY = @"apiUtilsAuthKey";

NSString *const API_UTILS_AUTH_IV = @"apiUtilsAuthIv";

NSString *const API_UTILS_AUTH_PUBLIC_KEY = @"apiUtilsSM2PublicKey";

NSString *const API_UTILS_AUTH_PRIVATE_KEY = @"apiUtilsSM2PrivateKey";

NSString *const API_LOG_UTILS_KEY = @"apiLogUtilsKey";

#pragma mark - Public Methods

+ (void)setUp {
    NSDictionary *paramDict = @{
        ALI_DNS_AUTH_KEY : @"cG9seXZsaXZl",
        ALI_DNS_AUTH_IV : @"MTExMTAwMDA=",
        LINK_MIC_AUTH_KEY :@"cG9seXZsaXZl",
        LINK_MIC_AUTH_IV :@"MTExMTAwMDA=",
        UTILS_AUTH_KEY : @"UG9seXZBcGk=",
        UTILS_AUTH_IV : @"UG9seXZMaXZl",
        CHANNEL_INFO_AUTH_KEY : @"cG9seXZsaXZl",
        CHANNEL_INFO_AUTH_IV : @"MTExMTAwMDA=",
        API_UTILS_AUTH_KEY : @"S0xmTzdabmY=",
        API_UTILS_AUTH_IV : @"VlRSZTdTbWQ=",
        API_UTILS_AUTH_PUBLIC_KEY : @"MDQ1QjgxNTVFOTc0N0MyODNBQ0Y4NDI4OTVEMzA0OEU2Q0E5NDNDQzQ1NEFGQTZBOUE0NTJCNTkwM0NDMzBGQ0Uy",
        API_UTILS_AUTH_PRIVATE_KEY : @"MDBCMzE5NjE4QjRCMUJBRUI4Q0M2NzY1RDg5MDNDMDVB",
        API_LOG_UTILS_KEY : @"cG9seXZfc2RrXw=="
    };
    NSData *data = [NSJSONSerialization dataWithJSONObject:paramDict options:NSJSONWritingPrettyPrinted error:nil];
    BOOL success = [PLVFFileUtil writeData:data atPath:[self filePathAndName]];
    if (success) {
        NSURL *fileURL = [NSURL fileURLWithPath:[self filePathAndName]];
        NSError *error = nil;
        [fileURL setResourceValue:@(YES) forKey:NSURLIsExcludedFromBackupKey error:&error];
        if (error) {
            NSLog(@"PLVKeyUtil set up failed, error: %@", error);
        }
    }
}

+ (NSString *)getAliKey {
    return [self getKey:ALI_DNS_AUTH_KEY];
}

+ (NSString *)getAliIv {
    return [self getKey:ALI_DNS_AUTH_IV];
}

+ (NSString *)getLinkMicKey {
    return [self getKey:LINK_MIC_AUTH_KEY];
}

+ (NSString *)getLinkMicIv {
    return [self getKey:LINK_MIC_AUTH_IV];
}

+ (NSString *)getUtilsKey {
    return [self getKey:UTILS_AUTH_KEY];
}

+ (NSString *)getUtilsIv {
    return [self getKey:UTILS_AUTH_IV];
}

+ (NSString *)getChannelInfoKey {
    return [self getKey:CHANNEL_INFO_AUTH_KEY];
}

+ (NSString *)getChannelInfoIv {
    return [self getKey:CHANNEL_INFO_AUTH_IV];
}

+ (NSString *)getApiUtilsKey {
    return [self getKey:API_UTILS_AUTH_KEY];
}

+ (NSString *)getApiUtilsIv {
    return [self getKey:API_UTILS_AUTH_IV];
}

+ (NSString *)getApiUtilsPublicKey {
    return [self getKey:API_UTILS_AUTH_PUBLIC_KEY];
}

+ (NSString *)getApiUtilsPrivateKey {
    return [self getKey:API_UTILS_AUTH_PRIVATE_KEY];
}

+ (NSString *)getApiLogUtilsKey {
    return [self getKey:API_LOG_UTILS_KEY];
}

#pragma mark - Private Methods

+ (NSString *)getKey:(NSString *)key {
    NSString *keyString = [NSString stringWithFormat:@"%@%@",[self base64DecodedString:[self getCacheString:key]],[self base64DecodedString:[self getString:key]]];
    return keyString;
}

+ (NSString *)getString:(NSString *)key {
    if (!key || ![key isKindOfClass:NSString.class] || key.length == 0) {
        return @"";
    } else if ([key isEqualToString:ALI_DNS_AUTH_KEY]) {
        return @"NzY1NDMyMQ==";
    } else if ([key isEqualToString:ALI_DNS_AUTH_IV]) {
        return @"MTExMTAwMTE=";
    } else if ([key isEqualToString:LINK_MIC_AUTH_KEY]) {
        return @"U0RLQXV0aA==";
    } else if ([key isEqualToString:LINK_MIC_AUTH_IV]) {
        return @"MTExMTAwMDA=";
    } else if ([key isEqualToString:UTILS_AUTH_KEY]) {
        return @"UmVzcG9uc2U=";
    } else if ([key isEqualToString:UTILS_AUTH_IV]) {
        return @"RW5jcnlwdA==";
    } else if ([key isEqualToString:CHANNEL_INFO_AUTH_KEY]) {
        return @"ODc2NTQzMg==";
    } else if ([key isEqualToString:CHANNEL_INFO_AUTH_IV]) {
        return @"MTExMTAwMTA=";
    } else if ([key isEqualToString:API_UTILS_AUTH_KEY]) {
        return @"UUM4akl2RVg=";
    } else if ([key isEqualToString:API_UTILS_AUTH_IV]) {
        return @"bGxSc0o3Q2I=";
    } else if ([key isEqualToString:API_UTILS_AUTH_PUBLIC_KEY]) {
        return @"NzhBNkY4OEY2MDJFREY4MDNEQUI0MEJENDVFMzVGNjYwNjc2QkY2OEZEN0NDQkUwRjZBRDMyMzg4MTM2Q0QwRA==";
    } else if ([key isEqualToString:API_UTILS_AUTH_PRIVATE_KEY]) {
        return @"MEIxNkQ5RDlGQ0QxNjMyNzFCQjZGMUI4OEY2NTdBMjhF";
    } else if ([key isEqualToString:API_LOG_UTILS_KEY]) {
        return @"YXBpX2lubm9y";
    } else {
        return @"";
    }
}

+ (NSString *)getCacheString:(NSString *)key {
    if (!key || ![key isKindOfClass:NSString.class] || key.length == 0) {
        return @"";
    } else {
        NSData *data =[PLVFFileUtil dataAtPath:[self filePathAndName]];
        NSString *cacheString;
        if (data) {
            NSDictionary *paramDict = [NSJSONSerialization JSONObjectWithData:data options:0 error:nil];
            cacheString = PLV_SafeStringForDictKey(paramDict, key);
        }
        return cacheString ? cacheString : @"";
    }
}

+ (NSString *)base64DecodedString:(NSString *)string {
    if (!string || ![string isKindOfClass:NSString.class] || string.length == 0) {
        return @"";
    } else {
        NSData *data = [[NSData alloc]initWithBase64EncodedString:string options:0];
        return [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
    }
}

+ (NSString *)filePathAndName {
    return [NSString stringWithFormat:@"%@/PLVUtils/Fd/KUtils.txt", [PLVFFileUtil documentPath]];
}

@end
