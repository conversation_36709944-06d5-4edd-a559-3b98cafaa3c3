//
//  PLVFFileUtil.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/4.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFFileUtil.h"

@implementation PLVFFileUtil

+ (NSString *)homePath {
    return NSHomeDirectory();
}

+ (NSString *)documentPath {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    return paths[0];
}

+ (NSString *)libraryPath {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES);
    return paths[0];
}

+ (NSString *)cachesPath {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    return paths[0];
}

+ (NSString *)tempPath {
    return NSTemporaryDirectory();
}

+ (BOOL)createFileAtPath:(NSString *)filePath {
    if (filePath.length == 0) {
        return NO;
    }
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if ([fileManager fileExistsAtPath:filePath]) {
        return YES;
    }
    
    NSString *dirPath = [filePath stringByDeletingLastPathComponent];
    BOOL isSuccess = [fileManager createDirectoryAtPath:dirPath withIntermediateDirectories:YES attributes:nil error:nil];
    if (!isSuccess) {
        return NO;
    }
    isSuccess = [fileManager createFileAtPath:filePath contents:nil attributes:nil];
    return isSuccess;
}

+ (BOOL)deleteFileAtPath:(NSString *)filePath {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if (![fileManager fileExistsAtPath:filePath]) {
        return YES;
    }
    BOOL isSuccess = [fileManager removeItemAtPath:filePath error:nil];
    return isSuccess;
}

+ (BOOL)writeData:(NSData *)data atPath:(NSString *)filePath {
    if (filePath.length==0) {
        return NO;
    }
    BOOL result = [self createFileAtPath:filePath];
    if (!result) {
        return NO;
    }
    result = [data writeToFile:filePath atomically:YES];
    return result;
}

+ (NSData *)dataAtPath:(NSString *)filePath {
    NSFileHandle *handle = [NSFileHandle fileHandleForReadingAtPath:filePath];
    NSData *fileData = [handle readDataToEndOfFile];
    [handle closeFile];
    return fileData;
}

@end
