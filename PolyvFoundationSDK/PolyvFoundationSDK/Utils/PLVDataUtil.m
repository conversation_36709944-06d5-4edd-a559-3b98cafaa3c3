//
//  PLVDataUtil.m
//  PLVNetworkSDK
//
//  Created by zykhbl on 2018/7/30.
//  Copyright © 2018年 PLV. All rights reserved.
//

#import "PLVDataUtil.h"
#import "PLVFdUtil.h"
#import "PLVFConsoleLogger.h"

#import <CommonCrypto/CommonDigest.h>
#import <CommonCrypto/CommonCryptor.h>

@interface NSData (AES)

- (NSData *)AES128operation:(CCOperation)operation key:(NSString *)key iv:(NSString *)iv;

- (NSData *)AES256operation:(CCOperation)operation key:(NSString *)key iv:(NSString *)iv;

@end

@implementation NSData (AES)

- (NSData *)AES128operation:(CCOperation)operation key:(NSString *)key iv:(NSString *)iv {
    char keyPtr[kCCKeySizeAES128 + 1];
    bzero(keyPtr, sizeof(keyPtr));
    [key getCString:keyPtr maxLength:sizeof(keyPtr) encoding:NSUTF8StringEncoding];
    
    // IV
    char ivPtr[kCCBlockSizeAES128 + 1];
    memset(ivPtr, 0, sizeof(ivPtr));
    [iv getCString:ivPtr maxLength:sizeof(ivPtr) encoding:NSUTF8StringEncoding];
    
    size_t bufferSize = [self length] + kCCBlockSizeAES128;
    void *buffer = malloc(bufferSize);
    size_t numBytesEncrypted = 0;
    
    CCCryptorStatus cryptorStatus = CCCrypt(operation, kCCAlgorithmAES128, kCCOptionPKCS7Padding, keyPtr, kCCKeySizeAES128, ivPtr, [self bytes], [self length], buffer, bufferSize, &numBytesEncrypted);
    
    if(cryptorStatus == kCCSuccess) {
        PLVF_NORMAL_LOG_DEBUG(@"Util",@"Decryption Success.");
        return [NSData dataWithBytesNoCopy:buffer length:numBytesEncrypted];
    } else {
        PLVF_NORMAL_LOG_ERROR(@"Util",@"Decryption Error.");
    }
    
    free(buffer);
    return nil;
}

- (NSData *)AES256operation:(CCOperation)operation key:(NSString *)key iv:(NSString *)iv {
    char keyPtr[kCCKeySizeAES256 + 1];
    bzero(keyPtr, sizeof(keyPtr));
    [key getCString:keyPtr maxLength:sizeof(keyPtr) encoding:NSUTF8StringEncoding];
    
    // IV
    char ivPtr[kCCKeySizeAES128 + 1];
    memset(ivPtr, 0, sizeof(ivPtr));
    [iv getCString:ivPtr maxLength:sizeof(ivPtr) encoding:NSUTF8StringEncoding];
    
    size_t bufferSize = [self length] + kCCBlockSizeAES128;
    void *buffer = malloc(bufferSize);
    size_t numBytesEncrypted = 0;
    
    CCCryptorStatus cryptorStatus = CCCrypt(operation, kCCAlgorithmAES128, kCCOptionPKCS7Padding, keyPtr, kCCKeySizeAES256, ivPtr, [self bytes], [self length], buffer, bufferSize, &numBytesEncrypted);
    
    if (cryptorStatus == kCCSuccess) {
        PLVF_NORMAL_LOG_DEBUG(@"Util",@"Decryption Success.");
        return [NSData dataWithBytesNoCopy:buffer length:numBytesEncrypted];
    } else {
        PLVF_NORMAL_LOG_ERROR(@"Util",@"Decryption Error.");
    }
    
    free(buffer);
    return nil;
}

@end


@implementation PLVDataUtil

#pragma mark - MD5
/// MD5 加密方法
+ (NSString *)md5HexDigest:(NSString *)input {
    if (![PLVFdUtil checkStringUseable:input]) {
        return @"";
    }
    const char* str = [input UTF8String];
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    CC_MD5(str, (CC_LONG)strlen(str), result);
    
    NSMutableString *ret = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
    for(int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [ret appendFormat:@"%02x", result[i]];
    }
    return ret;
}

+ (NSString *)md5OfFile:(NSString *)filePath {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if ([fileManager fileExistsAtPath:filePath]) {
        NSData *data = [NSData dataWithContentsOfFile:filePath];
        unsigned char digest[CC_MD5_DIGEST_LENGTH];
        CC_MD5(data.bytes, (CC_LONG)data.length, digest);
        NSMutableString *md5 = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
        for( int i = 0; i < CC_MD5_DIGEST_LENGTH; i++ ) {
            [md5 appendFormat:@"%02x", digest[i]];
        }
        return md5;
    }
    return @"";
}

#pragma mark - 二进制/十六进制
/// 二进制转十六进制
+ (NSString *)hexStringFromData:(NSData *)data {
    Byte *bytes = (Byte *)[data bytes];
    // 下面是Byte 转换为16进制。
    NSString *hexStr = @"";
    for(int i = 0; i < [data length]; i++) {
        NSString *newHexStr = [NSString stringWithFormat:@"%x", bytes[i] & 0xff]; //16进制数
        newHexStr = [newHexStr uppercaseString];
        if([newHexStr length] == 1) {
            newHexStr = [NSString stringWithFormat:@"0%@", newHexStr];
        }
        hexStr = [hexStr stringByAppendingString:newHexStr];
    }
    return hexStr;
}

/// 十六进制字符串转二进制
+ (NSData *)dataForHexString:(NSString *)hexString {
    if (hexString == nil) {
        return nil;
    }
    const char *ch = [[hexString lowercaseString] cStringUsingEncoding:NSUTF8StringEncoding];
    NSMutableData *data = [NSMutableData data];
    while (*ch) {
        if (*ch == ' ') {
            continue;
        }
        char byte = 0;
        if ('0' <= *ch && *ch <= '9') {
            byte = *ch - '0';
        } else if ('a' <= *ch && *ch <= 'f') {
            byte = *ch - 'a' + 10;
        } else if ('A' <= *ch && *ch <= 'F') {
            byte = *ch - 'A' + 10;
        }
        ch++;
        byte = byte << 4;
        if (*ch) {
            if ('0' <= *ch && *ch <= '9') {
                byte += *ch - '0';
            } else if ('a' <= *ch && *ch <= 'f') {
                byte += *ch - 'a' + 10;
            } else if('A' <= *ch && *ch <= 'F'){
                byte += *ch - 'A' + 10;
            }
            ch++;
        }
        [data appendBytes:&byte length:1];
    }
    return data;
}

#pragma mark - AES 128
/// AES 128 加密
+ (NSData *)AES128EncryptData:(NSData *)data withKey:(NSString *)key iv:(NSString *)iv {
    return [data AES128operation:kCCEncrypt key:key iv:iv];
}

/// AES 128 解密
+ (NSData *)AES128DecryptData:(NSData *)data withKey:(NSString *)key iv:(NSString *)iv {
    return [data AES128operation:kCCDecrypt key:key iv:iv];
}

#pragma mark - AES 256
/// AES 256 加密
+ (NSData *)AES256EncryptData:(NSData *)data withKey:(NSString *)key iv:(NSString *)iv {
    return [data AES256operation:kCCEncrypt key:key iv:iv];
}

/// AES 256 解密
+ (NSData *)AES256DecryptData:(NSData *)data withKey:(NSString *)key iv:(NSString *)iv {
    return [data AES256operation:kCCDecrypt key:key iv:iv];
}

#pragma mark - URL SafeBase64
/**
 普通字符的URL safeBase64 编码
 @discussion 因为标准 Base64 编码中包含有+,/,=这些不安全的URL字符串，所以要进行换字符
 */
+ (NSString *)safeUrlBase64Encode:(NSString *)string {
    if (![PLVFdUtil checkStringUseable:string]) {
        return @"";
    }
    
    NSData *date = [string dataUsingEncoding:NSUTF8StringEncoding];
    NSString *base64Str = [date base64EncodedStringWithOptions:NSDataBase64Encoding64CharacterLineLength];
    //NSString * base64Str = [GTMBase64 stringByEncodingData:data];
    /*
     '+' -> '-'、'/' -> '_'、'=' -> ''
     */
    NSMutableString * safeBase64Str = [[NSMutableString alloc] initWithString:base64Str];
    safeBase64Str = (NSMutableString *)[safeBase64Str stringByReplacingOccurrencesOfString:@"+" withString:@"-"];
    safeBase64Str = (NSMutableString *)[safeBase64Str stringByReplacingOccurrencesOfString:@"/" withString:@"_"];
    safeBase64Str = (NSMutableString *)[safeBase64Str stringByReplacingOccurrencesOfString:@"=" withString:@""];
    //NSLog(@"safeBase64编码：%@", safeBase64Str);
    safeBase64Str = (NSMutableString *)[safeBase64Str stringByReplacingOccurrencesOfString:@"\n" withString:@""];
    safeBase64Str = (NSMutableString *)[safeBase64Str stringByReplacingOccurrencesOfString:@"\r" withString:@""];
    
    return safeBase64Str;
}

/**
 URL safeBase64 字符解码
 @discussion 将saveBase64编码中的"-"，"_"字符串转换成"+"，"/"，字符串长度余4倍的位补"="
 */
+ (NSData *)safeUrlBase64Decode:(NSString *)safeUrlbase64Str {
    /*
     '-' -> '+'、 '_' -> '/'、不足4倍长度，补'='
     */
    NSMutableString * base64Str = [[NSMutableString alloc] initWithString:safeUrlbase64Str];
    base64Str = (NSMutableString * )[base64Str stringByReplacingOccurrencesOfString:@"-" withString:@"+"];
    base64Str = (NSMutableString * )[base64Str stringByReplacingOccurrencesOfString:@"_" withString:@"/"];
    NSInteger mod4 = base64Str.length % 4;
    if(mod4 > 0)
        [base64Str appendString:[@"====" substringToIndex:(4 - mod4)]];
    //NSLog(@"Base64原文：%@", base64Str);
    
    //return [GTMBase64 decodeData:[base64Str dataUsingEncoding:NSUTF8StringEncoding]];
    return [[NSData alloc] initWithBase64EncodedString:base64Str options:NSDataBase64DecodingIgnoreUnknownCharacters];
}

/// Base64 编码
+ (NSString *)base64String:(NSString *)inputString {
    if (![PLVFdUtil checkStringUseable:inputString]) {
        return @"";
    }
    
    NSData *data = [inputString dataUsingEncoding:NSUTF8StringEncoding];
    NSString *base64String = [data base64EncodedStringWithOptions:0];
    return base64String;
}

/// url safe Base64 编码
+ (NSString *)urlSafeBase64String:(NSString *)inputString {
    if (![PLVFdUtil checkStringUseable:inputString]) {
        return @"";
    }
    
    NSData *data = [inputString dataUsingEncoding:NSUTF8StringEncoding];
    NSString *base64String = [data base64EncodedStringWithOptions:0];
    base64String = [base64String stringByReplacingOccurrencesOfString:@"/" withString:@"_"];
    base64String = [base64String stringByReplacingOccurrencesOfString:@"+" withString:@"-"];
    return base64String;
}

/// SHA1 加密
+ (NSString *)sha1String:(NSString *)inputString {
    if (![PLVFdUtil checkStringUseable:inputString]) {
        return @"";
    }
    /*这两句容易造成 、中文字符串转data时会造成数据丢失
    const char *cstr = [inputString cStringUsingEncoding:NSUTF8StringEncoding];
    NSData *data = [NSData dataWithBytes:cstr length:inputString.length];
    */
    NSData *data = [inputString dataUsingEncoding:NSUTF8StringEncoding];
    //使用对应的CC_SHA1,CC_SHA256,CC_SHA384,CC_SHA512的长度分别是20,32,48,64
    uint8_t digest[CC_SHA1_DIGEST_LENGTH];
    //使用对应的CC_SHA256,CC_SHA384,CC_SHA512
    CC_SHA1(data.bytes, (CC_LONG)data.length, digest);
    
    NSMutableString *output = [NSMutableString stringWithCapacity:CC_SHA1_DIGEST_LENGTH * 2];
    
    for(int i = 0; i < CC_SHA1_DIGEST_LENGTH; i++)
        [output appendFormat:@"%02x", digest[i]];
    
    return output;
}

/// SHA256 加密
+ (NSString *)sha256String:(NSString *)inputString {
    if (![PLVFdUtil checkStringUseable:inputString]) {
        return @"";
    }
    
    NSData *data = [inputString dataUsingEncoding:NSUTF8StringEncoding];
    
    uint8_t digest[CC_SHA256_DIGEST_LENGTH] = {0};
    CC_SHA256(data.bytes, (CC_LONG)data.length, digest);
    NSData *out = [NSData dataWithBytes:digest length:CC_SHA256_DIGEST_LENGTH];
    const unsigned *hashBytes = [out bytes];
    NSString *hash = [NSString stringWithFormat:@"%08x%08x%08x%08x%08x%08x%08x%08x",
    ntohl(hashBytes[0]), ntohl(hashBytes[1]), ntohl(hashBytes[2]),
    ntohl(hashBytes[3]), ntohl(hashBytes[4]), ntohl(hashBytes[5]),
    ntohl(hashBytes[6]), ntohl(hashBytes[7])];
    return hash;
}

/// AES-128 加密，不使用加密向量
+ (NSData *)AES128EncryptedDataWithKey:(NSString *)key data:(NSData *)data {
    return [self AES128EncryptedDataWithKey:key iv:nil data:data];
}

/// AES-128 解密，不使用加密向量
+ (NSData *)AES128DecryptedDataWithKey:(NSString *)key data:(NSData *)data {
    return [self AES128DecryptedDataWithKey:key iv:nil data:data];
}

/// AES-128 加密
+ (NSData *)AES128EncryptedDataWithKey:(NSString *)key iv:(NSString *)iv data:(NSData *)data {
    return [self AES128Operation:kCCEncrypt key:[key dataUsingEncoding:NSUTF8StringEncoding] iv:[iv dataUsingEncoding:NSUTF8StringEncoding] data:data];
}

/// AES-128 解密
+ (NSData *)AES128DecryptedDataWithKey:(NSString *)key iv:(NSString *)iv data:(NSData *)data {
    return [self AES128Operation:kCCDecrypt key:[key dataUsingEncoding:NSUTF8StringEncoding] iv:[iv dataUsingEncoding:NSUTF8StringEncoding] data:data];
}

+ (NSData *)AES128Operation:(CCOperation)operation key:(NSData *)keyData iv:(NSData *)ivData data:(NSData *)contentData {
    size_t bufferSize = contentData.length + kCCBlockSizeAES128;
    void *buffer = malloc(bufferSize);
    size_t numBytesEncrypted = 0;
    
    CCCryptorStatus cryptorStatus = CCCrypt(operation, kCCAlgorithmAES128, kCCOptionPKCS7Padding, keyData.bytes, kCCKeySizeAES128, ivData.bytes, contentData.bytes, contentData.length, buffer, bufferSize, &numBytesEncrypted);
    
    if (cryptorStatus == kCCSuccess) {
        return [NSData dataWithBytesNoCopy:buffer length:numBytesEncrypted];
    }
    
    free(buffer);
    return nil;
}

/// 十六进制字符串转NSData
+ (NSData *)dataWithHexString:(NSString *)hexString {
    if (!hexString || hexString.length == 0) {
        return nil;
    }
    NSMutableData *hexData = [[NSMutableData alloc] initWithCapacity:8];
    NSRange range;
    if (hexString.length % 2 == 0){
        range = NSMakeRange(0, 2);
    } else range = NSMakeRange(0, 1);
    for (NSInteger i = range.location; i < hexString.length; i += 2) {
        unsigned int anInt;
        NSString *hexCharStr = [hexString substringWithRange:range];
        if (!hexCharStr.length) return nil;
        NSScanner *scanner = [NSScanner scannerWithString:hexCharStr];
        [scanner scanHexInt:&anInt];
        NSData *entity = [[NSData alloc] initWithBytes:&anInt length:1];
        [hexData appendData:entity];
        range.location += range.length;
        range.length = 2;
    }
    return hexData;
}

#pragma mark - 数据格式转换

+ (NSString *)jsonStringWithJSONObject:(id)jsonObject {
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:jsonObject options:NSJSONWritingPrettyPrinted error:&error];
    NSMutableString *mutStr = nil;
    if (jsonData) {
        NSString *jsonString = [[NSString alloc]initWithData:jsonData encoding:NSUTF8StringEncoding];
        mutStr = [NSMutableString stringWithString:jsonString];
    }
    return mutStr ? [mutStr copy] : nil;
}

+ (id)dictionaryWithJsonString:(NSString *)jsonString {
    if (jsonString == nil) {
        return nil;
    }
    
    if (![jsonString isKindOfClass:[NSString class]]) {
        return jsonString;
    }
    
    NSError *error;
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData
                                                    options:NSJSONReadingMutableContainers
                                                      error:&error];
    return (jsonObject && !error) ? jsonObject : nil;
}

@end
