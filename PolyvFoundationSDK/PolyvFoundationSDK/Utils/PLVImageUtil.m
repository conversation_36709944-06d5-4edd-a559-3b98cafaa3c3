//
//  PLVImageUtil.m
//  PLVFoundationSDK
//
//  Created by junotang on 2022/1/19.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVImageUtil.h"
#import "PLVFConsoleLogger.h"
#import <Accelerate/Accelerate.h>

static const int TEXTURE_CACHE_NUM = 3;

@implementation PLVPixelBufferInfo
@end

@implementation PLVBuffer
@end

@interface PLVImageUtil () {
    int                             _textureIndex;
    NSMutableArray<id<PLVGLTexture>> *_inputTextures;
    NSMutableArray<id<PLVGLTexture>> *_outputTextures;
    CVPixelBufferRef                _cachedPixelBuffer;
    BOOL                            _useCacheTexture;
    CVOpenGLESTextureCacheRef       _textureCache;
}
@property (nonatomic, readonly) CVOpenGLESTextureCacheRef textureCache;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSValue *> *pixelBufferPoolDict;

@end

@implementation PLVImageUtil

#pragma mark - [ Life Period ]
- (instancetype)init
{
    self = [super init];
    if (self) {
        _textureIndex = 0;
        _inputTextures = [NSMutableArray arrayWithCapacity:TEXTURE_CACHE_NUM];
        _outputTextures = [NSMutableArray arrayWithCapacity:TEXTURE_CACHE_NUM];
        _textureCache = nil;
        _useCacheTexture = YES;
    }
    return self;
}

- (void)dealloc
{
    if (_textureCache) {
        CVOpenGLESTextureCacheFlush(_textureCache, 0);
        CFRelease(_textureCache);
        _textureCache = nil;
    }
    for (id<PLVGLTexture> texture in _inputTextures) {
        [texture destroy];
    }
    [_inputTextures removeAllObjects];
    for (id<PLVGLTexture> texture in _outputTextures) {
        [texture destroy];
    }
    [_outputTextures removeAllObjects];
    
    if (_cachedPixelBuffer != nil) {
        CVPixelBufferRelease(_cachedPixelBuffer);
    }
    for (NSValue *value in self.pixelBufferPoolDict.allValues) {
        CVPixelBufferPoolRef pool = [value pointerValue];
        CVPixelBufferPoolRelease(pool);
    }
}

#pragma mark - [ Public Methods ]
- (PLVPixelBufferInfo *)getCVPixelBufferInfo:(CVPixelBufferRef)pixelBuffer {
    int bytesPerRow = (int) CVPixelBufferGetBytesPerRow(pixelBuffer);
    int width = (int) CVPixelBufferGetWidth(pixelBuffer);
    int height = (int) CVPixelBufferGetHeight(pixelBuffer);
    size_t iTop, iBottom, iLeft, iRight;
    CVPixelBufferGetExtendedPixels(pixelBuffer, &iLeft, &iRight, &iTop, &iBottom);
    width = width + (int) iLeft + (int) iRight;
    height = height + (int) iTop + (int) iBottom;
    bytesPerRow = bytesPerRow + (int) iLeft + (int) iRight;
    
    PLVPixelBufferInfo *info = [PLVPixelBufferInfo new];
    info.format = [self getCVPixelBufferFormat:pixelBuffer];
    info.width = width;
    info.height = height;
    info.bytesPerRow = bytesPerRow;
    return info;
}

- (PLVFormatType)getCVPixelBufferFormat:(CVPixelBufferRef)pixelBuffer {
    OSType type = CVPixelBufferGetPixelFormatType(pixelBuffer);
    return [self getFormatForOSType:type];
}

- (PLVFormatType)getFormatForOSType:(OSType)type {
    switch (type) {
        case kCVPixelFormatType_32BGRA:
            return PLVFormatType_BGRA;
        case kCVPixelFormatType_32RGBA:
            return PLVFormatType_RGBA;
        case kCVPixelFormatType_420YpCbCr8BiPlanarFullRange:
            return PLVFormatType_YUV420F;
        case kCVPixelFormatType_420YpCbCr8BiPlanarVideoRange:
            return PLVFormatType_YUV420V;
        case kCVPixelFormatType_420YpCbCr8Planar:
            return PLVFormatType_YUVY420;
        default:
            return PLVFormatType_UNKNOW;
            break;
    }
}

- (OSType)getOsType:(PLVFormatType)format {
    switch (format) {
        case PLVFormatType_RGBA:
            return kCVPixelFormatType_32RGBA;
        case PLVFormatType_BGRA:
            return kCVPixelFormatType_32BGRA;
        case PLVFormatType_YUV420F:
            return kCVPixelFormatType_420YpCbCr8BiPlanarFullRange;
        case PLVFormatType_YUV420V:
            return kCVPixelFormatType_420YpCbCr8BiPlanarVideoRange;
        case PLVFormatType_YUVY420:
            return kCVPixelFormatType_420YpCbCr8Planar;
        default:
            return kCVPixelFormatType_32BGRA;
            break;
    }
}

- (CVPixelBufferRef)transforCVPixelBufferToCVPixelBuffer:(CVPixelBufferRef)pixelBuffer outputFormat:(PLVFormatType)outputFormat {
    if ([self getCVPixelBufferFormat:pixelBuffer] == outputFormat) {
        return pixelBuffer;
    }
    PLVBuffer *inputBuffer = [self plv_getBufferFromCVPixelBuffer:pixelBuffer];
    CVPixelBufferRef outputPixelBuffer = [self plv_createCVPixelBufferWithWidth:inputBuffer.width height:inputBuffer.height format:outputFormat];
    if (!outputPixelBuffer) {
        return nil;
    }
    CVPixelBufferLockBaseAddress(outputPixelBuffer, 0);
    PLVBuffer *outputBuffer = [self plv_getBufferFromCVPixelBuffer:outputPixelBuffer];
    BOOL result = [self transforBufferToBuffer:inputBuffer outputBuffer:outputBuffer];
    CVPixelBufferUnlockBaseAddress(outputPixelBuffer, 0);
    if (result) {
        return outputPixelBuffer;
    }
    return nil;
}

- (BOOL)transforBufferToBuffer:(PLVBuffer *)inputBuffer outputBuffer:(PLVBuffer *)outputBuffer {
    if ([self plv_isYuv420:outputBuffer.format]) {
        if ([self plv_isRgba:inputBuffer.format]) {
            vImage_Buffer rgbaBuffer;
            rgbaBuffer.data = inputBuffer.buffer;
            rgbaBuffer.width = inputBuffer.width;
            rgbaBuffer.height = inputBuffer.height;
            rgbaBuffer.rowBytes = inputBuffer.bytesPerRow;
            vImage_Buffer yBuffer;
            yBuffer.data = outputBuffer.yBuffer;
            yBuffer.width = outputBuffer.yWidth;
            yBuffer.height = outputBuffer.yHeight;
            yBuffer.rowBytes = outputBuffer.yBytesPerRow;
            vImage_Buffer uvBuffer;
            uvBuffer.data = outputBuffer.uvBuffer;
            uvBuffer.width = outputBuffer.uvWidth;
            uvBuffer.height = outputBuffer.uvHeight;
            uvBuffer.rowBytes = outputBuffer.uvBytesPerRow;
            BOOL result = [self plv_convertRgbaToYuv:&rgbaBuffer yBuffer:&yBuffer yuBuffer:&uvBuffer inputFormat:inputBuffer.format outputFormat:outputBuffer.format];
            return result;
        }
    } else if ([self plv_isRgba:outputBuffer.format]) {
        if ([self plv_isRgba:inputBuffer.format]) {
            vImage_Buffer rgbaBuffer;
            rgbaBuffer.data = inputBuffer.buffer;
            rgbaBuffer.width = inputBuffer.width;
            rgbaBuffer.height = inputBuffer.height;
            rgbaBuffer.rowBytes = inputBuffer.bytesPerRow;
            vImage_Buffer bgraBuffer;
            bgraBuffer.data = outputBuffer.buffer;
            bgraBuffer.width = outputBuffer.width;
            bgraBuffer.height = outputBuffer.height;
            bgraBuffer.rowBytes = outputBuffer.bytesPerRow;
            BOOL result = [self plv_convertRgbaToBgra:&rgbaBuffer outputBuffer:&bgraBuffer inputFormat:inputBuffer.format outputFormat:outputBuffer.format];
            return result;
        } else if ([self plv_isYuv420:inputBuffer.format]) {
            vImage_Buffer yBuffer;
            yBuffer.data = inputBuffer.yBuffer;
            yBuffer.width = inputBuffer.yWidth;
            yBuffer.height = inputBuffer.yHeight;
            yBuffer.rowBytes = inputBuffer.yBytesPerRow;
            vImage_Buffer uvBuffer;
            uvBuffer.data = inputBuffer.uvBuffer;
            uvBuffer.width = inputBuffer.uvWidth;
            uvBuffer.height = inputBuffer.uvHeight;
            uvBuffer.rowBytes = inputBuffer.uvBytesPerRow;
            vImage_Buffer bgraBuffer;
            bgraBuffer.data = outputBuffer.buffer;
            bgraBuffer.width = outputBuffer.width;
            bgraBuffer.height = outputBuffer.height;
            bgraBuffer.rowBytes = outputBuffer.bytesPerRow;
            BOOL result = [self plv_convertYuvToRgba:&yBuffer yvBuffer:&uvBuffer rgbaBuffer:&bgraBuffer inputFormat:inputBuffer.format outputFormat:outputBuffer.format];
            return result;
        } else if ([self plv_isYuv420Planar:inputBuffer.format]) {
            vImage_Buffer yBuffer;
            yBuffer.data = inputBuffer.yBuffer;
            yBuffer.width = inputBuffer.yWidth;
            yBuffer.height = inputBuffer.yHeight;
            yBuffer.rowBytes = inputBuffer.yBytesPerRow;
            vImage_Buffer uBuffer;
            uBuffer.data = inputBuffer.uBuffer;
            uBuffer.width = inputBuffer.uvWidth;
            uBuffer.height = inputBuffer.uvHeight;
            uBuffer.rowBytes = inputBuffer.uBytesPerRow;
            vImage_Buffer vBuffer;
            vBuffer.data = inputBuffer.vBuffer;
            vBuffer.width = inputBuffer.uvWidth;
            vBuffer.height = inputBuffer.uvHeight;
            vBuffer.rowBytes = inputBuffer.vBytesPerRow;
            vImage_Buffer bgraBuffer;
            bgraBuffer.data = outputBuffer.buffer;
            bgraBuffer.width = outputBuffer.width;
            bgraBuffer.height = outputBuffer.height;
            bgraBuffer.rowBytes = outputBuffer.bytesPerRow;
            BOOL result = [self plv_convertYuvToRgba:&yBuffer uBuffer:&uBuffer vBuffer:&vBuffer rgbaBuffer:&bgraBuffer inputFormat:inputBuffer.format outputFormat:outputBuffer.format];
            return result;
        }
    } else if ([self plv_isYuv420Planar:outputBuffer.format]) {
        if ([self plv_isRgba:inputBuffer.format]) {
            vImage_Buffer rgbaBuffer;
            rgbaBuffer.data = inputBuffer.buffer;
            rgbaBuffer.width = inputBuffer.width;
            rgbaBuffer.height = inputBuffer.height;
            rgbaBuffer.rowBytes = inputBuffer.bytesPerRow;
            vImage_Buffer yBuffer;
            yBuffer.data = outputBuffer.yBuffer;
            yBuffer.width = outputBuffer.yWidth;
            yBuffer.height = outputBuffer.yHeight;
            yBuffer.rowBytes = outputBuffer.yBytesPerRow;
            vImage_Buffer uBuffer;
            uBuffer.data = outputBuffer.uBuffer;
            uBuffer.width = outputBuffer.uvWidth;
            uBuffer.height = outputBuffer.uvHeight;
            uBuffer.rowBytes = outputBuffer.uBytesPerRow;
            vImage_Buffer vBuffer;
            vBuffer.data = outputBuffer.vBuffer;
            vBuffer.width = outputBuffer.uvWidth;
            vBuffer.height = outputBuffer.uvHeight;
            vBuffer.rowBytes = outputBuffer.vBytesPerRow;

            BOOL result = [self plv_convertRgbaToYuv:&rgbaBuffer yBuffer:&yBuffer uBuffer:&uBuffer vBuffer:&vBuffer inputFormat:inputBuffer.format outputFormat:outputBuffer.format];
            return result;
        }
    }
    return NO;
}

- (id<PLVGLTexture>)transforCVPixelBufferToTexture:(CVPixelBufferRef)pixelBuffer {
    PLVPixelBufferInfo *info = [self getCVPixelBufferInfo:pixelBuffer];
    if (info.format != PLVFormatType_BGRA) {
        pixelBuffer = [self transforCVPixelBufferToCVPixelBuffer:pixelBuffer outputFormat:PLVFormatType_BGRA];
        PLVF_NORMAL_LOG_WARN(@"Util",@"this method only supports BRGA format CVPixelBuffer, convert it to BGRA CVPixelBuffer internal");
    }
    
    if (_useCacheTexture) {
        _textureIndex = (_textureIndex + 1) % TEXTURE_CACHE_NUM;
    } else {
        _textureIndex = 0;
    }
    
    while (_textureIndex >= _inputTextures.count) {
        [_inputTextures addObject:[[PLVPixelBufferGLTexture alloc] initWithTextureCache:self.textureCache]];
    }
    
    id<PLVGLTexture> texture = _inputTextures[_textureIndex];
    if (texture.type != PLV_PIXEL_BUFFER_TEXTURE) {
        [texture destroy];
        texture = [[PLVPixelBufferGLTexture alloc] initWithCVPixelBuffer:pixelBuffer textureCache:self.textureCache];
        _inputTextures[_textureIndex] = texture;
    } else {
        [(PLVPixelBufferGLTexture *)texture update:pixelBuffer];
    }
    
    return texture;
}

- (PLVPixelBufferGLTexture *)getOutputPixelBufferGLTextureWithWidth:(int)width height:(int)height format:(PLVFormatType)format {
    if (format != PLVFormatType_BGRA) {
        PLVF_NORMAL_LOG_WARN(@"Util",@"this method only supports BE_BRGA format, please use BE_BGRA");
        return nil;
    }
    
    while (_textureIndex >= _outputTextures.count) {
        [_outputTextures addObject:[[PLVPixelBufferGLTexture alloc] initWithTextureCache:self.textureCache]];
    }
    
    id<PLVGLTexture> _outputTexture = _outputTextures[_textureIndex];
    if (!_outputTexture || _outputTexture.type != PLV_PIXEL_BUFFER_TEXTURE) {
        if (_outputTexture) {
            [_outputTexture destroy];
        }
        _outputTexture = [[PLVPixelBufferGLTexture alloc] initWithWidth:width height:height textureCache:self.textureCache];
    }
    
    [_outputTexture updateWidth:width height:height];
    
    if (_useCacheTexture) {
        // If use pipeline, return last output texture if we can.
        // To resolve problems like size changed between two continuous frames
        int lastTextureIndex = (_textureIndex + TEXTURE_CACHE_NUM - 1) % TEXTURE_CACHE_NUM;
        if (_outputTextures.count > lastTextureIndex && _outputTextures[lastTextureIndex].available) {
            _outputTexture = _outputTextures[lastTextureIndex];
        }
    }
    return _outputTexture.available ? _outputTexture : nil;
}

-(void)fillPixelBuffer:(CVPixelBufferRef)pixelBuffer withInputPixelBuffer:(CVPixelBufferRef)inputPixelBuffer {
      size_t width = CVPixelBufferGetWidth(pixelBuffer);
      size_t height = CVPixelBufferGetHeight(pixelBuffer);
      CVPixelBufferLockBaseAddress(pixelBuffer, 0);
      CVPixelBufferLockBaseAddress(inputPixelBuffer, 0);
      size_t yStride = CVPixelBufferGetWidthOfPlane(pixelBuffer, 0);
      unsigned char *yPlane = (unsigned char *)CVPixelBufferGetBaseAddressOfPlane(pixelBuffer, 0);
      unsigned char *yOutputPlane = (unsigned char *)CVPixelBufferGetBaseAddressOfPlane(inputPixelBuffer, 0);
      for (int i = 0, k = 0; i < height; i ++) {
        for (int j = 0; j < width; j ++) {
          yPlane[j + i * yStride] = yOutputPlane[k++];
        }
      }
      size_t uvStride = CVPixelBufferGetWidthOfPlane(pixelBuffer, 1);
      unsigned char *uvPlane = (unsigned char *)CVPixelBufferGetBaseAddressOfPlane(pixelBuffer, 1);
      unsigned char *uvOutputPlane = (unsigned char *)CVPixelBufferGetBaseAddressOfPlane(inputPixelBuffer, 1);
      for (int i = 0, k = 0; i < height; i ++) {
        for (int j = 0; j < width / 2; j ++) {
          uvPlane[j + i * uvStride] = uvOutputPlane[k++];
        }
      }
      CVPixelBufferUnlockBaseAddress(pixelBuffer, 0);
      CVPixelBufferUnlockBaseAddress(inputPixelBuffer, 0);
}

-(void)fillY420PixelBuffer:(CVPixelBufferRef)pixelBuffer withInputPixelBuffer:(CVPixelBufferRef)inputPixelBuffer {
    size_t height = CVPixelBufferGetHeight(inputPixelBuffer);
    size_t bytesPerRow = CVPixelBufferGetBytesPerRow(inputPixelBuffer);
    
    CVPixelBufferLockBaseAddress(pixelBuffer, 0);
    CVPixelBufferLockBaseAddress(inputPixelBuffer, 0);
    
    uint8_t *baseAddress = CVPixelBufferGetBaseAddress(inputPixelBuffer);
    uint8_t *copyBaseAddress = CVPixelBufferGetBaseAddress(pixelBuffer);
    memcpy(copyBaseAddress, baseAddress, height * bytesPerRow);
    
    CVPixelBufferUnlockBaseAddress(pixelBuffer, 0);
    CVPixelBufferUnlockBaseAddress(inputPixelBuffer, 0);
}

- (CVPixelBufferRef)copyI420BufferToNv12:(size_t)width height:(size_t)height yData:(char *)yData uData:(char *)uData vData:(char *)vData yStride:(size_t)yStride uStride:(size_t)uStride vStride:(size_t)vStride uvBufferLength:(size_t)uvBufferLength uvBuffer:(char *)uvBuffer {
    size_t uvStride = uStride;
    
    char* yBuffer = yData;
    char* uBuffer = uData;
    char* vBuffer = vData;
    
    for (size_t uv = 0, u = 0; uv < uvBufferLength; uv += 2, u++) {
        // swtich the location of U、V，to NV12
        uvBuffer[uv] = uBuffer[u];
        uvBuffer[uv+1] = vBuffer[u];
    }
    @autoreleasepool {
        void * planeBaseAddress[2] = {yBuffer, uvBuffer};
        size_t planeWidth[2] = {width, width / 2};
        size_t planeHeight[2] = {height, height / 2};
        size_t planeBytesPerRow[2] = {yStride, uvStride * 2};
        CVPixelBufferRef pixelBuffer = NULL;
        CVReturn result = CVPixelBufferCreateWithPlanarBytes(kCFAllocatorDefault,
                                                             width, height,
                                                             kCVPixelFormatType_420YpCbCr8BiPlanarVideoRange,
                                                             NULL, 0,
                                                             2, planeBaseAddress, planeWidth, planeHeight, planeBytesPerRow,
                                                             NULL, NULL, NULL,
                                                             &pixelBuffer);
        if (result != kCVReturnSuccess) {
            PLVF_NORMAL_LOG_ERROR(@"Util",@"Unable to create cvpixelbuffer %d", result);
        }
        
        return pixelBuffer;
    }
}

- (void)changePixelBufferRef:(CVPixelBufferRef)pixelBuffer toYUVBuffer:(int)width height:(int)height yData:(char *)yData uData:(char *)uData vData:(char *)vData yStride:(int)yStride uStride:(int)uStride vStride:(int)vStride {
    CVPixelBufferLockBaseAddress(pixelBuffer, 0);
    unsigned char *yDestPlane = (unsigned char *)CVPixelBufferGetBaseAddressOfPlane(pixelBuffer, 0);
    int yBytesPerRow = (int)CVPixelBufferGetBytesPerRowOfPlane(pixelBuffer, 0);
    for (int i = 0; i < height; i ++) {
       for (int j = 0; j < width; j ++) {
           yData[j + i * yStride] = yDestPlane[j + i*yBytesPerRow];
       }
    }
    unsigned char *uvDestPlane = (unsigned char *)CVPixelBufferGetBaseAddressOfPlane(pixelBuffer, 1);
    int uvBytesPerRow = (int)CVPixelBufferGetBytesPerRowOfPlane(pixelBuffer, 1);
    for (int i = 0; i < height / 2; i ++) {
       for (int j = 0; j < width / 2; j ++) {
           uData[j + i * uStride] = uvDestPlane[j * 2 + i * uvBytesPerRow];
           vData[j + i * vStride] = uvDestPlane[j * 2 + i * uvBytesPerRow + 1];
       }
    }
    
    CVPixelBufferUnlockBaseAddress(pixelBuffer, 0);
}

+ (UIImage *)imageFromUIView:(UIView *)view {
    if (@available(iOS 17.0, *)) {
        UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:view.bounds.size];
        UIImage *image = [renderer imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull ref) {
            [view.layer renderInContext:ref.CGContext];
        }];
        return image;
    } else {
        UIGraphicsBeginImageContext(view.bounds.size);
        CGContextRef ctxRef = UIGraphicsGetCurrentContext();
        [view.layer renderInContext:ctxRef];
        UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
        return image;
    }
}

+ (UIImage *)imageFromUIView:(UIView *)view opaque:(BOOL)opaque scale:(CGFloat)scale {
    if (@available(iOS 17.0, *)) {
        UIGraphicsImageRendererFormat *format = [UIGraphicsImageRendererFormat preferredFormat];
        format.opaque = opaque;
        format.scale = scale;
        UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:view.bounds.size format:format];
        UIImage *image = [renderer imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull ref) {
            [view.layer renderInContext:ref.CGContext];
        }];
        
        return image;
    } else {
        UIGraphicsBeginImageContextWithOptions(view.bounds.size, opaque, scale);
        CGContextRef ctxRef = UIGraphicsGetCurrentContext();
        [view.layer renderInContext:ctxRef];
        UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
        
        return image;
    }
}

+ (NSData *)compressImage:(UIImage *)image mbValue:(double)mbValue {
    // 1.第一轮压缩压缩比采用1.0
    NSData *data = UIImageJPEGRepresentation(image, 1.0);
    double firstMBValue = data.length / (1024.0 * 1024.0);
    if (firstMBValue <= mbValue) {
        return data;
    }
    
    // 2.如果第一轮压缩无法达到目标大小，再进行第二轮压缩
    // 2.1 根据算法计算出第二轮压缩的压缩比quality
    float quality = [self compressionQualityFromDataLength:firstMBValue toDataLength:mbValue];
    
    // 2.2 根据计算出了的压缩比quality，进行第二轮压缩，第二轮压缩不管最终数据多少，都会返回
    UIImage *firstProcessImage = [UIImage imageWithData:data];
    data = UIImageJPEGRepresentation(firstProcessImage, quality);
    return data;
}

+ (NSData *)compressImageWithSingleCompression:(UIImage *)image {
    // 只压缩一次，质量比为0.8
    return UIImageJPEGRepresentation(image, 0.8);
}

+ (float)compressionQualityFromDataLength:(double)originMBValue toDataLength:(double)processMBValue {
    if (processMBValue - 0.091 * originMBValue <= 0) {
        return 0.2;
    }
    
    float quality = 106.847 - (6.188 * originMBValue) / (processMBValue - 0.091 * originMBValue);
    return MIN(MAX(0, quality), 1);
}

#pragma mark - [ getter && Setter ]
- (NSMutableDictionary<NSString *,NSValue *> *)pixelBufferPoolDict {
    if (_pixelBufferPoolDict == nil) {
        _pixelBufferPoolDict = [NSMutableDictionary dictionary];
    }
    return _pixelBufferPoolDict;
}

- (void)setUseCachedTexture:(BOOL)useCache {
    _useCacheTexture = useCache;
    if (!useCache) {
        _textureIndex = 0;
    }
}

- (CVOpenGLESTextureCacheRef)textureCache {
    if (!_textureCache) {
        EAGLContext *context = [EAGLContext currentContext];
        CVReturn ret = CVOpenGLESTextureCacheCreate(kCFAllocatorDefault, NULL, context, NULL, &_textureCache);
        if (ret != kCVReturnSuccess) {
            PLVF_NORMAL_LOG_ERROR(@"Util",@"create CVOpenGLESTextureCacheRef fail: %d", ret);
        }
    }
    return _textureCache;
}

#pragma mark - [ Private Methods ]
/// 数据格式 Yuv 到 Rgba
- (BOOL)plv_convertYuvToRgba:(vImage_Buffer *)yBuffer
                    yvBuffer:(vImage_Buffer *)uvBuffer
                  rgbaBuffer:(vImage_Buffer *)rgbaBuffer
                 inputFormat:(PLVFormatType)inputFormat
                outputFormat:(PLVFormatType)outputFormat {
    if (![self plv_isYuv420:inputFormat] || ![self plv_isRgba:outputFormat]) {
        return NO;
    }
    
    uint8_t map[4] = {1, 2, 3, 0};
    [self plv_permuteMap:map format:outputFormat];
    vImage_YpCbCrPixelRange pixelRange;
    [self plv_yuvPixelRange:&pixelRange format:inputFormat];
    
    vImageARGBType argbType = kvImageARGB8888;
    vImageYpCbCrType yuvType = kvImage420Yp8_CbCr8;
    vImage_YpCbCrToARGB conversionInfo;
    vImage_Flags flags = kvImageNoFlags;
    
    vImage_Error error = vImageConvert_YpCbCrToARGB_GenerateConversion(kvImage_YpCbCrToARGBMatrix_ITU_R_601_4, &pixelRange, &conversionInfo, yuvType, argbType, flags);
    if (error != kvImageNoError) {
        PLVF_NORMAL_LOG_ERROR(@"Util",@"vImageConvert_YpCbCrToARGB_GenerateConversion error: %ld", error);
        return NO;
    }
    
    error = vImageConvert_420Yp8_CbCr8ToARGB8888(yBuffer, uvBuffer, rgbaBuffer, &conversionInfo, map, 255, flags);
    if (error != kvImageNoError) {
        PLVF_NORMAL_LOG_ERROR(@"Util",@"vImageConvert_420Yp8_CbCr8ToARGB8888 error: %ld", error);
        return NO;
    }
    
    return YES;
}

- (BOOL)plv_convertYuvToRgba:(vImage_Buffer *)yBuffer uBuffer:(vImage_Buffer *)uBuffer vBuffer:(vImage_Buffer *)vBuffer rgbaBuffer:(vImage_Buffer *)rgbaBuffer inputFormat:(PLVFormatType)inputFormat outputFormat:(PLVFormatType)outputFormat {
    if (![self plv_isYuv420Planar:inputFormat] || ![self plv_isRgba:outputFormat]) {
        return NO;
    }
    
    uint8_t map[4] = {1, 2, 3, 0};
    [self plv_permuteMap:map format:outputFormat];
    vImage_YpCbCrPixelRange pixelRange;
    [self plv_yuvPixelRange:&pixelRange format:inputFormat];
    
    vImageARGBType argbType = kvImageARGB8888;
    vImageYpCbCrType yuvType = kvImage420Yp8_Cb8_Cr8;
    vImage_YpCbCrToARGB conversionInfo;
    vImage_Flags flags = kvImageNoFlags;
    
    vImage_Error error = vImageConvert_YpCbCrToARGB_GenerateConversion(kvImage_YpCbCrToARGBMatrix_ITU_R_601_4, &pixelRange, &conversionInfo, yuvType, argbType, flags);
    if (error != kvImageNoError) {
        NSLog(@"vImageConvert_YpCbCrToARGB_GenerateConversion error: %ld", error);
        return NO;
    }
    
    error = vImageConvert_420Yp8_Cb8_Cr8ToARGB8888(yBuffer, uBuffer, vBuffer, rgbaBuffer, &conversionInfo, map, 255, flags);
    if (error != kvImageNoError) {
        NSLog(@"vImageConvert_420Yp8_Cb8_Cr8ToARGB8888 error: %ld", error);
        return NO;
    }
    
    return YES;
}

/// 数据格式 Rgba 到 Bgra
- (BOOL)plv_convertRgbaToBgra:(vImage_Buffer *)inputBuffer outputBuffer:(vImage_Buffer *)outputBuffer inputFormat:(PLVFormatType)inputFormat outputFormat:(PLVFormatType)outputFormat {
    if (![self plv_isRgba:inputFormat] || ![self plv_isRgba:outputFormat]) {
        return NO;
    }
    uint8_t map[4] = {0, 1, 2, 3};
    [self plv_permuteMap:map format:inputFormat];
    [self plv_permuteMap:map format:outputFormat];
    vImage_Error error = vImagePermuteChannels_ARGB8888(inputBuffer, outputBuffer, map, kvImageNoFlags);
    if (error != kvImageNoError) {
        PLVF_NORMAL_LOG_ERROR(@"Util",@"be_transforRgbaToRgba error: %ld", error);
    }
    return error == kvImageNoError;
}

/// 数据格式 Rgba 到 Yuv
- (BOOL)plv_convertRgbaToYuv:(vImage_Buffer *)inputBuffer
                     yBuffer:(vImage_Buffer *)yBuffer
                    yuBuffer:(vImage_Buffer *)uvBuffer
                 inputFormat:(PLVFormatType)inputFormat
                outputFormat:(PLVFormatType)outputFormat {
    if (![self plv_isRgba:inputFormat] || ![self plv_isYuv420:outputFormat]) {
        return NO;
    }
    uint8_t map[4] = {1, 2, 3, 0};
    [self plv_permuteMap:map format:inputFormat];
    vImage_YpCbCrPixelRange pixelRange;
    [self plv_yuvPixelRange:&pixelRange format:outputFormat];
    
    vImageARGBType argbType = kvImageARGB8888;
    vImageYpCbCrType yuvType = kvImage420Yp8_CbCr8;
    vImage_ARGBToYpCbCr conversionInfo;
    vImage_Flags flags = kvImageNoFlags;
    
    vImage_Error error = vImageConvert_ARGBToYpCbCr_GenerateConversion(kvImage_ARGBToYpCbCrMatrix_ITU_R_601_4, &pixelRange, &conversionInfo, argbType, yuvType, flags);
    if (error != kvImageNoError) {
        PLVF_NORMAL_LOG_ERROR(@"Util",@"vImageConvert_ARGBToYpCbCr_GenerateConversion error: %ld", error);
        return NO;
    }
    
    error = vImageConvert_ARGB8888To420Yp8_CbCr8(inputBuffer, yBuffer, uvBuffer, &conversionInfo, map, flags);
    if (error != kvImageNoError) {
        PLVF_NORMAL_LOG_ERROR(@"Util",@"vImageConvert_ARGB8888To420Yp8_CbCr8 error: %ld", error);
        return NO;
    }
    
    return YES;
}

- (BOOL)plv_convertRgbaToYuv:(vImage_Buffer *)inputBuffer
                    yBuffer:(vImage_Buffer *)yBuffer
                    uBuffer:(vImage_Buffer *)uBuffer
                    vBuffer:(vImage_Buffer *)vBuffer
                inputFormat:(PLVFormatType)inputFormat
               outputFormat:(PLVFormatType)outputFormat {
    if (![self plv_isRgba:inputFormat] || ![self plv_isYuv420Planar:outputFormat]) {
        return NO;
    }
    uint8_t map[4] = {1, 2, 3, 0};
    [self plv_permuteMap:map format:inputFormat];
    vImage_YpCbCrPixelRange pixelRange;
    [self plv_yuvPixelRange:&pixelRange format:outputFormat];
    
    vImageARGBType argbType = kvImageARGB8888;
    vImageYpCbCrType yuvType = kvImage420Yp8_Cb8_Cr8;
    vImage_ARGBToYpCbCr conversionInfo;
    vImage_Flags flags = kvImageNoFlags;
    
    vImage_Error error = vImageConvert_ARGBToYpCbCr_GenerateConversion(kvImage_ARGBToYpCbCrMatrix_ITU_R_601_4, &pixelRange, &conversionInfo, argbType, yuvType, flags);
    if (error != kvImageNoError) {
        NSLog(@"vImageConvert_ARGBToYpCbCr_GenerateConversion error: %ld", error);
        return NO;
    }
    
    error = vImageConvert_ARGB8888To420Yp8_Cb8_Cr8(inputBuffer, yBuffer, uBuffer, vBuffer, &conversionInfo, map, flags);
    if (error != kvImageNoError) {
        NSLog(@"vImageConvert_ARGB8888To420Yp8_Cb8_Cr8 error: %ld", error);
        return NO;
    }
    
    return YES;
}

/// 生成 CVPixelBufferRef
- (CVPixelBufferRef)plv_createCVPixelBufferWithWidth:(int)width height:(int)height format:(PLVFormatType)format {
    if (_cachedPixelBuffer != nil) {
        PLVPixelBufferInfo *info = [self getCVPixelBufferInfo:_cachedPixelBuffer];
        if (info.format == format && info.width == width && info.height == height) {
            return _cachedPixelBuffer;
        } else {
            CVBufferRelease(_cachedPixelBuffer);
        }
    }
    
    CVPixelBufferRef pixelBuffer = [self plv_createPixelBufferFromPool:[self getOsType:format] heigth:height width:width];
    _cachedPixelBuffer = pixelBuffer;
    return pixelBuffer;
}

/// 从缓存池中生成 CVPixelBufferRef
- (CVPixelBufferRef)plv_createPixelBufferFromPool:(OSType)type heigth:(int)height width:(int)width {
    NSString* key = [NSString stringWithFormat:@"%u_%d_%d", (unsigned int)type, height, width];
    CVPixelBufferPoolRef pixelBufferPool = NULL;
    NSValue *bufferPoolAddress = [self.pixelBufferPoolDict objectForKey:key];
    
    if (!bufferPoolAddress) {
        pixelBufferPool = [self plv_createPixelBufferPool:type heigth:height width:width];
        bufferPoolAddress = [NSValue valueWithPointer:pixelBufferPool];
        [self.pixelBufferPoolDict setValue:bufferPoolAddress forKey:key];
    }else {
        pixelBufferPool = [bufferPoolAddress pointerValue];
    }
    
    CVPixelBufferRef buffer = NULL;
    CVReturn ret = CVPixelBufferPoolCreatePixelBuffer(kCFAllocatorDefault, pixelBufferPool, &buffer);
    if (ret != kCVReturnSuccess) {
        PLVF_NORMAL_LOG_ERROR(@"Util",@"CVPixelBufferCreate error: %d", ret);
        if (ret == kCVReturnInvalidPixelFormat) {
            PLVF_NORMAL_LOG_ERROR(@"Util",@"only format BGRA and YUV420 can be used");
        }
    }
    return buffer;
}

/// 从缓存池中提取 CVPixelBufferRef
- (CVPixelBufferPoolRef)plv_createPixelBufferPool:(OSType)type heigth:(int)height width:(int)width {
    CVPixelBufferPoolRef pool = NULL;
    NSMutableDictionary* attributes = [NSMutableDictionary dictionary];
    
    [attributes setObject:[NSNumber numberWithBool:YES] forKey:(NSString*)kCVPixelBufferOpenGLCompatibilityKey];
    [attributes setObject:[NSNumber numberWithInt:type] forKey:(NSString*)kCVPixelBufferPixelFormatTypeKey];
    [attributes setObject:[NSNumber numberWithInt:width] forKey: (NSString*)kCVPixelBufferWidthKey];
    [attributes setObject:[NSNumber numberWithInt:height] forKey: (NSString*)kCVPixelBufferHeightKey];
    [attributes setObject:@(16) forKey:(NSString*)kCVPixelBufferBytesPerRowAlignmentKey];
    [attributes setObject:[NSDictionary dictionary] forKey:(NSString*)kCVPixelBufferIOSurfacePropertiesKey];
        
    CVReturn ret = CVPixelBufferPoolCreate(kCFAllocatorDefault, NULL, (__bridge CFDictionaryRef)attributes, &pool);
    
    if (ret != kCVReturnSuccess){
        PLVF_NORMAL_LOG_ERROR(@"Util",@"Create pixbuffer pool failed %d", ret);
        return NULL;
    }
    
    CVPixelBufferRef buffer;
    ret = CVPixelBufferPoolCreatePixelBuffer(NULL, pool, &buffer);
    if (ret != kCVReturnSuccess){
        PLVF_NORMAL_LOG_ERROR(@"Util",@"Create pixbuffer from pixelbuffer pool failed %d", ret);
        return NULL;
    }
    
    return pool;
}

/// 从 CVPixelBufferRef 提取信息到 PLVBuffer
- (PLVBuffer *)plv_getBufferFromCVPixelBuffer:(CVPixelBufferRef)pixelBuffer {
    PLVBuffer *buffer = [[PLVBuffer alloc] init];
    PLVPixelBufferInfo *info = [self getCVPixelBufferInfo:pixelBuffer];
    buffer.width = info.width;
    buffer.height = info.height;
    buffer.format = info.format;
    
    CVPixelBufferLockBaseAddress(pixelBuffer, 0);
    if ([self plv_isRgba:info.format]) {
        buffer.buffer = (unsigned char *)CVPixelBufferGetBaseAddress(pixelBuffer);
        buffer.bytesPerRow = (int)CVPixelBufferGetBytesPerRow(pixelBuffer);
    } else if ([self plv_isYuv420:info.format]) {
        buffer.yBuffer = (unsigned char *)CVPixelBufferGetBaseAddressOfPlane(pixelBuffer, 0);
        buffer.yBytesPerRow = (int)CVPixelBufferGetBytesPerRowOfPlane(pixelBuffer, 0);
        buffer.uvBuffer = (unsigned char *)CVPixelBufferGetBaseAddressOfPlane(pixelBuffer, 1);
        buffer.uvBytesPerRow = (int)CVPixelBufferGetBytesPerRowOfPlane(pixelBuffer, 1);
        
        buffer.yWidth = (int)CVPixelBufferGetWidthOfPlane(pixelBuffer, 0);
        buffer.yHeight = (int)CVPixelBufferGetHeightOfPlane(pixelBuffer, 0);
        buffer.uvWidth = (int)CVPixelBufferGetWidthOfPlane(pixelBuffer, 1);
        buffer.uvHeight = (int)CVPixelBufferGetHeightOfPlane(pixelBuffer, 1);
    } else if ([self plv_isYuv420Planar:info.format]) {
        buffer.yBuffer = (unsigned char *)CVPixelBufferGetBaseAddressOfPlane(pixelBuffer, 0);
        buffer.yBytesPerRow = (int)CVPixelBufferGetBytesPerRowOfPlane(pixelBuffer, 0);
        buffer.uBuffer = (unsigned char *)CVPixelBufferGetBaseAddressOfPlane(pixelBuffer, 1);
        buffer.uBytesPerRow = (int)CVPixelBufferGetBytesPerRowOfPlane(pixelBuffer, 1);
        buffer.vBuffer = (unsigned char *)CVPixelBufferGetBaseAddressOfPlane(pixelBuffer, 2);
        buffer.vBytesPerRow = (int)CVPixelBufferGetBytesPerRowOfPlane(pixelBuffer, 2);
        
        buffer.yWidth = (int)CVPixelBufferGetWidthOfPlane(pixelBuffer, 0);
        buffer.yHeight = (int)CVPixelBufferGetHeightOfPlane(pixelBuffer, 0);
        buffer.uvWidth = (int)CVPixelBufferGetWidthOfPlane(pixelBuffer, 1);
        buffer.uvHeight = (int)CVPixelBufferGetHeightOfPlane(pixelBuffer, 1);

    }
    CVPixelBufferUnlockBaseAddress(pixelBuffer, 0);
    
    return buffer;
}

- (BOOL)plv_isRgba:(PLVFormatType)format {
    return format == PLVFormatType_RGBA || format == PLVFormatType_BGRA;
}

- (BOOL)plv_isYuv420Planar:(PLVFormatType)format {
    return format == PLVFormatType_YUVY420;
}

- (BOOL)plv_isYuv420:(PLVFormatType)format {
    return format == PLVFormatType_YUV420F || format == PLVFormatType_YUV420V;
}

- (void)plv_permuteMap:(uint8_t *)map format:(PLVFormatType)format {
    int r = map[0], g = map[1], b = map[2], a = map[3];
    switch (format) {
        case PLVFormatType_RGBA:
            map[0] = r;
            map[1] = g;
            map[2] = b;
            map[3] = a;
            break;
        case PLVFormatType_BGRA:
            map[0] = b;
            map[1] = g;
            map[2] = r;
            map[3] = a;
        default:
            break;
    }
}

- (void)plv_yuvPixelRange:(vImage_YpCbCrPixelRange *)pixelRange format:(PLVFormatType)format {
    switch (format) {
        case PLVFormatType_YUV420F:
            pixelRange->Yp_bias = 0;
            pixelRange->CbCr_bias = 128;
            pixelRange->YpRangeMax = 255;
            pixelRange->CbCrRangeMax = 255;
            pixelRange->YpMax = 255;
            pixelRange->YpMin = 0;
            pixelRange->CbCrMax = 255;
            pixelRange->CbCrMin = 0;
            break;
        case PLVFormatType_YUV420V:
            pixelRange->Yp_bias = 16;
            pixelRange->CbCr_bias = 128;
            pixelRange->YpRangeMax = 235;
            pixelRange->CbCrRangeMax = 240;
            pixelRange->YpMax = 235;
            pixelRange->YpMin = 16;
            pixelRange->CbCrMax = 240;
            pixelRange->CbCrMin = 16;
            break;
        case PLVFormatType_YUVY420:
            pixelRange->Yp_bias = 16;
            pixelRange->CbCr_bias = 128;
            pixelRange->YpRangeMax = 235;
            pixelRange->CbCrRangeMax = 240;
            pixelRange->YpMax = 235;
            pixelRange->YpMin = 16;
            pixelRange->CbCrMax = 240;
            pixelRange->CbCrMin = 16;
            break;
        default:
            break;
    }
}

@end
