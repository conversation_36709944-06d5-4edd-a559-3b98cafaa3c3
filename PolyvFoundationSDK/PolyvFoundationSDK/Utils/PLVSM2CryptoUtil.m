//
//  PLVSM2CryptoUtil.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2023/12/19.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVSM2CryptoUtil.h"
#if __has_include(<openssl/sm2.h>)
    #import <openssl/sm2.h>
    #import <openssl/bn.h>
    #import <openssl/evp.h>
    #import <openssl/asn1t.h>
    #define PLVLOPENSSL_SM2
#endif

#ifdef PLVLOPENSSL_SM2

typedef struct SM2_Ciphertext_st_1 SM2_Ciphertext_1;
DECLARE_ASN1_FUNCTIONS(SM2_Ciphertext_1)

struct SM2_Ciphertext_st_1 {
    BIGNUM *C1x;
    BIGNUM *C1y;
    ASN1_OCTET_STRING *C3;
    ASN1_OCTET_STRING *C2;
};

ASN1_SEQUENCE(SM2_Ciphertext_1) = {
    ASN1_SIMPLE(SM2_Ciphertext_1, C1x, BIGNUM),
    ASN1_SIMPLE(SM2_Ciphertext_1, C1y, BIGNUM),
    ASN1_SIMPLE(SM2_Ciphertext_1, C3, ASN1_OCTET_STRING),
    ASN1_SIMPLE(SM2_Ciphertext_1, C2, ASN1_OCTET_STRING),
} ASN1_SEQUENCE_END(SM2_Ciphertext_1)

IMPLEMENT_ASN1_FUNCTIONS(SM2_Ciphertext_1)

// 默认椭圆曲线类型 NID_sm2
static int kDefaultEllipticCurveType = NID_sm2;

#endif

@implementation PLVSM2CryptoUtil
#ifdef PLVLOPENSSL_SM2

// OpenSSL 1.1.1 以上版本支持国密
+ (void)initialize {
    if (self == [PLVSM2CryptoUtil class]) {
        if (OPENSSL_VERSION_NUMBER < 0x1010100fL) {
            NSLog(@"OpenSSL 当前版本：%s", OPENSSL_VERSION_TEXT);
            NSAssert(NO, @"OpenSSL 版本低于 1.1.1，不支持国密");
        }
    }
}

#pragma mark - 椭圆曲线类型
+ (int)ellipticCurveType {
    return kDefaultEllipticCurveType;
}

+ (void)setEllipticCurveType:(int)curveType {
    kDefaultEllipticCurveType = curveType;
}

#pragma mark - 创建公私钥对
+ (NSArray<NSString *> *)createKeyPair {
    NSArray<NSString *> *keyArray = @[@"", @""];
    EC_GROUP *group = EC_GROUP_new_by_curve_name(kDefaultEllipticCurveType); // 椭圆曲线
    EC_KEY *key = NULL; // 密钥对
    do {
        key = EC_KEY_new();
        if (!EC_KEY_set_group(key, group)) {
            break;
        }
        if (!EC_KEY_generate_key(key)) {
            break;
        }
        const EC_POINT *pub_key = EC_KEY_get0_public_key(key);
        const BIGNUM *pri_key = EC_KEY_get0_private_key(key);

        char *hex_pub = EC_POINT_point2hex(group, pub_key, EC_KEY_get_conv_form(key), NULL);
        char *hex_pri = BN_bn2hex(pri_key);
        
        NSString *pubHex = [NSString stringWithCString:hex_pub encoding:NSUTF8StringEncoding];
        NSString *priHex = [NSString stringWithCString:hex_pri encoding:NSUTF8StringEncoding];
        if (pubHex.length > 0 && priHex.length > 0) {
            NSString *priHexWithPadding = [self bnToHexPadding:priHex];
            keyArray = @[pubHex, priHexWithPadding];
        }
        OPENSSL_free(hex_pub);
        OPENSSL_free(hex_pri);
    } while (NO);
    
    if (group != NULL) EC_GROUP_free(group);
    EC_KEY_free(key);
    
    return keyArray;
}

#pragma mark - SM2 加密
+ (NSString *)formatEncryptString:(NSString *)plainString publicKey:(NSString *)publicKey {
    // SM2 加密
    NSString *encryptedStr = [self encryptString:plainString publicKey:publicKey];
    if (!encryptedStr || encryptedStr.length == 0 ) {
        return nil;
    }
    
    // ASN1 解码
    NSString *c1c3c2Result = [self asn1DecodeToC1C3C2:encryptedStr];
    if (!c1c3c2Result || c1c3c2Result.length == 0 ) {
        return nil;
    }
    
    // C1C3C2 => C1C2C3
    NSString *c1c2c3Result = [self convertC1C3C2ToC1C2C3:c1c3c2Result hasPrefix:NO];
    if (c1c2c3Result && ![c1c2c3Result hasPrefix:@"04"]) {
        c1c2c3Result = [NSString stringWithFormat:@"04%@", c1c2c3Result];
    }
    
    return c1c2c3Result;
}

// 加密普通格式明文字符串
+ (nullable NSString *)encryptString:(NSString *)plainString publicKey:(NSString *)publicKey {
    if (plainString.length == 0 || plainString.length == 0) {
        return nil;
    }
    NSData *plainData = [plainString dataUsingEncoding:NSUTF8StringEncoding];
    NSData *cipherData = [self enData:plainData hexPubKey:publicKey];
    
    NSString *encryptedStr = [PLVSM2CryptoUtil dataToHex:cipherData];
    return encryptedStr;
}

+ (nullable NSData *)enData:(NSData *)plainData hexPubKey:(NSString *)hexPubKey {
    uint8_t *plain_bytes = (uint8_t *)plainData.bytes; // 明文
    const char *public_key = hexPubKey.UTF8String; // 公钥
    size_t msg_len = plainData.length; // 明文长度
    
    const EVP_MD *digest = EVP_sm3(); // 摘要算法
    EC_GROUP *group = EC_GROUP_new_by_curve_name(kDefaultEllipticCurveType); // 椭圆曲线
    EC_KEY *key = NULL; // 密钥对
    EC_POINT *pub_point = NULL; // 坐标
    uint8_t *ctext = NULL; // 密文
    NSData *cipherData = nil; // 密文
    do {
        key = EC_KEY_new();
        if (!EC_KEY_set_group(key, group)) {
             break;
        }
        
        pub_point = EC_POINT_new(group);
        EC_POINT_hex2point(group, public_key, pub_point, NULL);
        if (!EC_KEY_set_public_key(key, pub_point)) {
            break;
        }
        
        size_t ctext_len = 0;
        if (!sm2_ciphertext_size(key, digest, msg_len, &ctext_len)) {
            break;
        }
        
        ctext = (uint8_t *)OPENSSL_zalloc(ctext_len);
        if (!sm2_encrypt(key, digest, plain_bytes, msg_len, ctext, &ctext_len)) {
            break;
        }
        cipherData = [NSData dataWithBytes:ctext length:ctext_len];
    } while (NO);
    
    if (group != NULL) EC_GROUP_free(group);
    EC_POINT_free(pub_point);
    OPENSSL_free(ctext);
    EC_KEY_free(key);
    
    return cipherData;
}

#pragma mark - SM2 解密
+ (nullable NSString *)formatDecryptString:(NSString *)ciphertext privateKey:(NSString *)privateKey {
    if (ciphertext.length == 0 || privateKey.length == 0) {
        return nil;
    }
    
    NSString *ciphertextC1C3C2 = [PLVSM2CryptoUtil convertC1C2C3ToC1C3C2:ciphertext hasPrefix:YES];
    if (!ciphertextC1C3C2 || ciphertextC1C3C2 == 0) {
        return nil;
    }
    
    // asn1 编码
    NSString *asn1DecodeString = [PLVSM2CryptoUtil asn1EncodeWithC1C3C2:ciphertextC1C3C2];
    // sm2 解码
    NSString *decryptedString = [PLVSM2CryptoUtil decryptString:asn1DecodeString privateKey:privateKey];
    
    return decryptedString;
}

// 解密密文，返回普通字符串
+ (nullable NSString *)decryptString:(NSString *)ciphertext privateKey:(NSString *)privateKey {
    if (ciphertext.length == 0 || privateKey.length == 0) {
        return nil;
    }
    
    NSData *cipherData = [PLVSM2CryptoUtil hexToData:ciphertext];
    NSData *plainData = [self deData:cipherData hexPriKey:privateKey];
    
    NSString *decryptedStr = [[NSString alloc]initWithData:plainData encoding:NSUTF8StringEncoding];
    return decryptedStr;
}

+ (nullable NSData *)deData:(NSData *)cipherData hexPriKey:(NSString *)hexPriKey {
    uint8_t *cipher_bytes = (uint8_t *)cipherData.bytes; // 明文
    const char *private_key = hexPriKey.UTF8String; // 私钥
    size_t ctext_len = cipherData.length;
    
    const EVP_MD *digest = EVP_sm3(); // 摘要算法
    EC_GROUP *group = EC_GROUP_new_by_curve_name(kDefaultEllipticCurveType); // 椭圆曲线
    BIGNUM *pri_big_num = NULL; // 私钥
    EC_KEY *key = NULL; // 密钥对
    EC_POINT *pub_point = NULL; // 坐标
    uint8_t *plaintext = NULL; // 明文
    NSData *plainData = nil; // 明文
    
    do {
        if (!BN_hex2bn(&pri_big_num, private_key)) {
            break;
        }
        key = EC_KEY_new();
        if (!EC_KEY_set_group(key, group)) {
            break;
        }
        if (!EC_KEY_set_private_key(key, pri_big_num)) {
            break;
        }
        
        size_t ptext_len = 0;
        if (!sm2_plaintext_size(cipher_bytes, ctext_len, &ptext_len)) {
            break;
        }
        
        plaintext = (uint8_t *)OPENSSL_zalloc(ptext_len);
        if (!sm2_decrypt(key, digest, cipher_bytes, ctext_len, plaintext, &ptext_len)) {
            break;
        }
        plainData = [NSData dataWithBytes:plaintext length:ptext_len];
    } while (NO);
    
    if (group != NULL) EC_GROUP_free(group);
    EC_POINT_free(pub_point);
    OPENSSL_free(plaintext);
    BN_free(pri_big_num);
    EC_KEY_free(key);
    
    return plainData;
}

#pragma mark - 密文格式转换
// C1C2C3 顺序的 Hex 格式密文转为 C1C3C2 顺序
+ (nullable NSString *)convertC1C2C3ToC1C3C2:(NSString *)ciphertext hasPrefix:(BOOL)hasPrefix {
    NSString *cipherHex = ciphertext;
    if (hasPrefix == YES && cipherHex.length > 2) {
        cipherHex = [cipherHex substringFromIndex:2];
    }
    if (cipherHex.length < 192) {
        return nil;
    }
    NSString *c1 = [cipherHex substringToIndex:128];
    NSString *c3 = [cipherHex substringFromIndex:(cipherHex.length - 64)];
    NSString *c2 = [cipherHex substringWithRange:NSMakeRange(128, cipherHex.length - c1.length - c3.length)];
    NSString *c1c3c2 = [NSString stringWithFormat:@"%@%@%@", c1, c3, c2];
    return c1c3c2;
}

// C1C3C2 顺序的 Hex 格式密文转为 C1C2C3 顺序
+ (nullable NSString *)convertC1C3C2ToC1C2C3:(NSString *)ciphertext hasPrefix:(BOOL)hasPrefix {
    NSString *cipherHex = ciphertext;
    if (hasPrefix == YES && cipherHex.length > 2) {
        cipherHex = [cipherHex substringFromIndex:2];
    }
    if (cipherHex.length < 192) {
        return nil;
    }
    NSString *c1 = [cipherHex substringToIndex:128];
    NSString *c3 = [cipherHex substringWithRange:NSMakeRange(128, 64)];
    NSString *c2 = [cipherHex substringFromIndex:192];
    NSString *c1c2c3 = [NSString stringWithFormat:@"%@%@%@", c1, c2, c3];
    return c1c2c3;
}

#pragma mark - ASN1 编码
+ (nullable NSString *)asn1EncodeWithC1C3C2:(NSString *)c1c3c2Hex{
    if (c1c3c2Hex.length <= 192) {
        return nil;
    }
    NSString *upperEnText = c1c3c2Hex.uppercaseString;
    NSString *c1Hex = [upperEnText substringWithRange:NSMakeRange(0, 128)];
    NSString *c3Hex = [upperEnText substringWithRange:NSMakeRange(128, 64)];
    NSString *c2Hex = [upperEnText substringFromIndex:192];
    
    NSData *c3Data = [PLVSM2CryptoUtil hexToData:c3Hex];
    NSData *c2Data = [PLVSM2CryptoUtil hexToData:c2Hex];
    if (c3Data.length == 0 || c2Data.length == 0) {
        return nil;
    }
    
    NSData *asn1Data = [self asn1EnC1Hex:c1Hex c3Data:c3Data c2Data:c2Data];
    if (asn1Data.length == 0) {
        return nil;
    }
    
    NSString *asn1Str = [PLVSM2CryptoUtil dataToHex:asn1Data];

    return asn1Str;
}

+ (NSData *)asn1EnC1Hex:(NSString *)c1 c3Data:(NSData *)c3 c2Data:(NSData *)c2{
    if (c1.length == 0 || c3.length == 0 || c2.length == 0) {
        return nil;
    }

    NSUInteger c1_len = c1.length;
    const char *c1_x_hex = [c1 substringWithRange:NSMakeRange(0, c1_len/2)].UTF8String;
    const char *c1_y_hex = [c1 substringWithRange:NSMakeRange(c1_len/2, c1_len/2)].UTF8String;
    uint8_t *c3_text = (uint8_t *)c3.bytes;
    size_t c3_len = c3.length;
    uint8_t *c2_text = (uint8_t *)c2.bytes;
    size_t c2_len = c2.length;
    
    // ASN1 编码后存储数据的结构体
    struct SM2_Ciphertext_st_1 ctext_st;
    ctext_st.C2 = NULL;
    ctext_st.C3 = NULL;
    BIGNUM *x1 = NULL;
    BIGNUM *y1 = NULL;
    NSData *asn1Data = nil;
    do {
        if (!BN_hex2bn(&x1, c1_x_hex)) {
            break;
        }
        if (!BN_hex2bn(&y1, c1_y_hex)) {
            break;
        }
        ctext_st.C1x = x1;
        ctext_st.C1y = y1;
        ctext_st.C3 = ASN1_OCTET_STRING_new();
        ctext_st.C2 = ASN1_OCTET_STRING_new();
        if (ctext_st.C3 == NULL || ctext_st.C2 == NULL) {
            break;
        }
        if (!ASN1_OCTET_STRING_set(ctext_st.C3, (uint8_t *)c3_text, (int)c3_len)
            || !ASN1_OCTET_STRING_set(ctext_st.C2, (uint8_t *)c2_text, (int)c2_len)) {
            break;
        }
        uint8_t *asn1_buf = NULL; // 编码
        int asn1_len = i2d_SM2_Ciphertext_1(&ctext_st, &asn1_buf);
        /* Ensure cast to size_t is safe */
        if (asn1_len < 0 || !asn1_buf) {
            break;
        }
        asn1Data = [NSData dataWithBytes:asn1_buf length:asn1_len];
        free(asn1_buf); // 释放 buf
    } while (NO);
    
    ASN1_OCTET_STRING_free(ctext_st.C2);
    ASN1_OCTET_STRING_free(ctext_st.C3);
    BN_free(x1);
    BN_free(y1);
    
    return asn1Data;
}

#pragma mark - ASN1 解码
+ (NSArray<NSData *> *)asn1DeToC1C3C2Data:(NSData *)asn1Data{
    long asn1_ctext_len = asn1Data.length; // ASN1格式密文原文长度
    const uint8_t *asn1_ctext = (uint8_t *)asn1Data.bytes;
    
    const EVP_MD *digest = EVP_sm3(); // 摘要算法
    struct SM2_Ciphertext_st_1 *sm2_st = NULL;
    sm2_st = d2i_SM2_Ciphertext_1(NULL, &asn1_ctext, asn1_ctext_len);
    // C1
    char *c1x_text = BN_bn2hex(sm2_st->C1x);
    char *c1y_text = BN_bn2hex(sm2_st->C1y);
    NSString *c1xStr = [NSString stringWithCString:c1x_text encoding:NSUTF8StringEncoding];
    NSString *c1yStr = [NSString stringWithCString:c1y_text encoding:NSUTF8StringEncoding];
    // 如果转 Hex 不足 64 位前面补 0
    NSString *paddingC1X = [self bnToHexPadding:c1xStr];
    NSString *paddingC1Y = [self bnToHexPadding:c1yStr];
    NSString *c1Hex = [NSString stringWithFormat:@"%@%@", paddingC1X, paddingC1Y];
    NSData *c1Data = [PLVSM2CryptoUtil hexToData:c1Hex];
    // C3
    const int c3_len = EVP_MD_size(digest);
    NSData *c3Data = [NSData dataWithBytes:sm2_st->C3->data length:c3_len];
    // C2
    int c2_len = sm2_st->C2->length;
    NSData *c2Data = [NSData dataWithBytes:sm2_st->C2->data length:c2_len];
    
    OPENSSL_free(c1x_text);
    OPENSSL_free(c1y_text);
    SM2_Ciphertext_1_free(sm2_st);
    
    if (!c1Data || !c3Data || !c2Data) {
        return nil;
    }

    return @[c1Data, c3Data, c2Data];
}

+ (nullable NSString *)asn1DecodeToC1C3C2:(NSString *)asn1Hex{
    NSArray<NSString *> *c1c3c2 = [self asn1DecodeToC1C3C2Array:asn1Hex];
    if (c1c3c2.count != 3) {
        return nil;
    }
    
    NSString *c1c3c2Hex = [NSString stringWithFormat:@"%@%@%@", c1c3c2[0], c1c3c2[1], c1c3c2[2]];
    return c1c3c2Hex;
}

+ (nullable NSArray<NSString *> *)asn1DecodeToC1C3C2Array:(NSString *)asn1Hex{
    if (asn1Hex.length == 0) {
        return nil;
    }
    NSData *asn1Data = [PLVSM2CryptoUtil hexToData:asn1Hex];
    if (asn1Data.length == 0) {
        return nil;
    }
    NSArray<NSData *> *decodedArray = [self asn1DeToC1C3C2Data:asn1Data];
    if (decodedArray.count != 3) {
        return nil;
    }
    NSString *c1Hex = [PLVSM2CryptoUtil dataToHex:decodedArray[0]];
    NSString *c3Hex = [PLVSM2CryptoUtil dataToHex:decodedArray[1]];
    NSString *c2Hex = [PLVSM2CryptoUtil dataToHex:decodedArray[2]];
    
    if (c1Hex.length == 0 || c3Hex.length == 0 || c2Hex.length == 0) {
        return nil;
    }
    
    return @[c1Hex, c3Hex, c2Hex];
}

/// BIGNUM 转 Hex 时，不足 64 位前面补 0
/// @param orginHex 原 Hex 字符串
+ (NSString *)bnToHexPadding:(NSString *)orginHex{
    if (orginHex.length == 0 || orginHex.length >= 64) {
        return orginHex;
    }
    static NSString *paddingZero = @"0000000000000000000000000000000000000000000000000000000000000000";
    NSString *padding = [paddingZero substringToIndex:(64 - orginHex.length)];
    return [NSString stringWithFormat:@"%@%@", padding, orginHex];
}


#pragma mark - Hex 编码

+ (nullable NSString *)stringToHex:(NSString *)str{
    if (!str || str.length == 0) {
        return nil;
    }
    
    NSData *strData = [str dataUsingEncoding:NSUTF8StringEncoding];
    NSString *tmpHex = [self dataToHex:strData];
    
    return tmpHex;
}

+ (nullable NSString *)dataToHex:(NSData *)data{
    if (!data || data.length == 0) {
        return nil;
    }
        
    char *tmp = OPENSSL_buf2hexstr((uint8_t *)data.bytes, data.length);
    NSString *tmpHex = [NSString stringWithCString:tmp encoding:NSUTF8StringEncoding];
    tmpHex = [tmpHex stringByReplacingOccurrencesOfString:@":" withString:@""];
    OPENSSL_free(tmp);
    
    return tmpHex;
}

#pragma mark - Hex 解码

+ (nullable NSString *)hexToString:(NSString *)hexStr{
    if (!hexStr || hexStr.length < 2) {
        return nil;
    }
    
    NSData *bufData = [self hexToData:hexStr];
    NSString *bufStr = [[NSString alloc]initWithData:bufData encoding:NSUTF8StringEncoding];
    
    return bufStr;
}

+ (nullable NSData *)hexToData:(NSString *)hexStr{
    if (!hexStr || hexStr.length < 2) {
        return nil;
    }
    
    long buf_len = 0;
    uint8_t *tmp_buf = OPENSSL_hexstr2buf(hexStr.UTF8String, &buf_len);
    NSData *tmpData = [NSData dataWithBytes:tmp_buf length:buf_len];
    OPENSSL_free(tmp_buf);
    
    return tmpData;
}

#else

#pragma mark - NO_OPENSSL

+ (NSArray<NSString *> *)createKeyPair {
    return nil;
}

+ (void)setEllipticCurveType:(int)curveType {
    
}

+ (NSString *)formatEncryptString:(NSString *)plainString publicKey:(NSString *)publicKey {
    return nil;
}

+ (nullable NSString *)encryptString:(NSString *)plainString publicKey:(NSString *)publicKey {
    return nil;
}

+ (nullable NSString *)formatDecryptString:(NSString *)ciphertext privateKey:(NSString *)privateKey {
    return nil;
}

+ (nullable NSString *)decryptString:(NSString *)ciphertext privateKey:(NSString *)privateKey {
    return nil;
}

+ (nullable NSString *)convertC1C2C3ToC1C3C2:(NSString *)ciphertext hasPrefix:(BOOL)hasPrefix {
    return nil;
}

+ (nullable NSString *)convertC1C3C2ToC1C2C3:(NSString *)ciphertext hasPrefix:(BOOL)hasPrefix {
    return nil;
}

+ (nullable NSString *)asn1EncodeWithC1C3C2:(NSString *)c1c3c2Hex {
    return nil;
}

+ (nullable NSString *)asn1DecodeToC1C3C2:(NSString *)asn1Hex {
    return nil;
}

+ (nullable NSArray<NSString *> *)asn1DecodeToC1C3C2Array:(NSString *)asn1Hex {
    return nil;
}

#endif
@end
