<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>FMWK</string>
	<key>CFBundleShortVersionString</key>
	<string>1.25.1</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>NSPrincipalClass</key>
	<string></string>
	<key>PLVFoundationAuthorizationManagerSDK</key>
	<array>
		<string>PLVAuthorizationManager</string>
	</array>
	<key>PLVFoundationBaseUtilsSDK</key>
	<array>
		<string>PLVFdUtil</string>
		<string>PLVDataUtil</string>
		<string>PLVColorUtil</string>
		<string>PLVFFileUtil</string>
		<string>PLVFWeakProxy</string>
		<string>PLVViewFrameUtil</string>
		<string>PLVMulticastDelegate</string>
		<string>PLVImageUtil</string>
		<string>PLVGLTexture</string>
		<string>PLVKeyUtil</string>
		<string>PLVFDI18NUtil</string>
		<string>PLVFSignConfig</string>
		<string>PLVSM2Util</string>
	</array>
	<key>PLVFoundationConsoleLoggerSDK</key>
	<array>
		<string>PLVFConsoleLogger</string>
	</array>
	<key>PLVFoundationCryptoUtilsSDK</key>
	<array>
		<string>PLVSM2CryptoUtil</string>
	</array>
	<key>PLVFoundationErrorCodeSDK</key>
	<array>
		<string>PLVFErrorBaseCodeDefine</string>
		<string>PLVFPlayErrorCodeGenerator</string>
		<string>PLVFDownloadErrorCodeGenerator</string>
		<string>PLVFUploadErrorCodeGenerator</string>
		<string>PLVFRecordErrorCodeGenerator</string>
		<string>PLVFRtmpErrorCodeGenerator</string>
		<string>PLVFChatErrorCodeGenerator</string>
		<string>PLVFLinkErrorCodeGenerator</string>
		<string>PLVFPPTErrorCodeGenerator</string>
		<string>PLVFInitErrorCodeGenerator</string>
		<string>PLVFSocketErrorCodeGenerator</string>
		<string>PLVFHttpErrorCodeGenerator</string>
		<string>PLVFInteractionErrorCodeGenerator</string>
		<string>PLVFBeautyErrorCodeGenerator</string>
	</array>
	<key>PLVFoundationJSBridgeSDK</key>
	<array>
		<string>PLVJSBridge</string>
	</array>
	<key>PLVFoundationLogReporterSDK</key>
	<array>
		<string>PLVFUserAgentBuilder</string>
		<string>PLVFJsonConverter</string>
		<string>PLVFLogModel</string>
		<string>PLVFELogModel</string>
		<string>PLVFViewLogModel</string>
		<string>PLVFQosLogModel</string>
		<string>PLVFTrackLogModel</string>
		<string>PLVFBaseLogReporter</string>
		<string>PLVFLogReporter</string>
		<string>PLVFELogReporter</string>
		<string>PLVFViewLogReporter</string>
		<string>PLVFQosLogReporter</string>
		<string>PLVFTrackLogReporter</string>
	</array>
	<key>PLVFoundationLoganSDK</key>
	<array>
		<string>PLVFLogan</string>
	</array>
	<key>PLVFoundationNetworkUtilsSDK</key>
	<array>
		<string>PLVNetworkError</string>
		<string>PLVFNetworkUtil</string>
		<string>PLVNetworkAccessibility</string>
	</array>
	<key>PLVFoundationProgressHUDSDK</key>
	<array>
		<string>PLVProgressHUD</string>
	</array>
	<key>PLVFoundationReachabilitySDK</key>
	<array>
		<string>PLVReachability</string>
	</array>
	<key>PLVFoundationSDKSubModulesList</key>
	<array>
		<string>PLVFoundationBaseUtilsSDK</string>
		<string>PLVFoundationNetworkUtilsSDK</string>
		<string>PLVFoundationErrorCodeSDK</string>
		<string>PLVFoundationLogReporterSDK</string>
		<string>PLVFoundationConsoleLoggerSDK</string>
		<string>PLVFoundationSafeModelSDK</string>
		<string>PLVFoundationJSBridgeSDK</string>
		<string>PLVFoundationAuthorizationManagerSDK</string>
		<string>PLVFoundationWebviewBridgeSDK</string>
		<string>PLVFoundationProgressHUDSDK</string>
		<string>PLVFoundationReachabilitySDK</string>
		<string>PLVFoundationCryptoUtilsSDK</string>
		<string>PLVFoundationLoganSDK</string>
	</array>
	<key>PLVFoundationSafeModelSDK</key>
	<array>
		<string>PLVSafeModel</string>
	</array>
	<key>PLVFoundationWebviewBridgeSDK</key>
	<array>
		<string>PLVFWebViewJavascriptBridge_JS</string>
		<string>PLVFWKWebViewJavascriptBridge</string>
		<string>PLVFWebViewJavascriptBridgeBase</string>
	</array>
</dict>
</plist>
