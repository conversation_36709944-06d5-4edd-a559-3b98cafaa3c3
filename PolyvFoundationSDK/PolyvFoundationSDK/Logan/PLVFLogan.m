//
//  PLVFLogan.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON> on 2023/2/21.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVFLogan.h"
#import <sys/time.h>
#include <sys/mount.h>
#include <PLVCLogan/clogan_core.h>
#import "PLVFFileUtil.h"
#import "PLVFdUtil.h"
#import <objc/runtime.h>

#if TARGET_OS_IPHONE
#import <UIKit/UIKit.h>
#else
#import <Cocoa/Cocoa.h>
#endif

BOOL PLVLOGANUSEASL = NO;
BOOL PLVLoganHadInit = NO;
BOOL PLVLOGANDISABLE = NO;
NSData *__PLV_AES_KEY;
NSData *__PLV_AES_IV;
uint64_t __plv_max_file;
uint32_t __plv_max_reversed_date;


@interface Logan : NSObject {
    NSTimeInterval _PLVLastCheckFreeSpace;
}
@property (nonatomic, copy) NSString *lastLogDate;
@property (nonatomic, copy) NSString *lastLogCurrentTime;
@property (nonatomic, copy) NSString *lastLogChannelId;
@property (nonatomic, copy) NSString *lastSdkVersion;

#if OS_OBJECT_USE_OBJC
@property (nonatomic, strong) dispatch_queue_t loganQueue;
#else
@property (nonatomic, assign) dispatch_queue_t loganQueue;
#endif

+ (instancetype)logan;

- (void)writeLog:(NSString *)log logType:(NSUInteger)type;
- (void)clearLogs;
+ (NSDictionary *)allFilesInfo;
+ (NSDictionary *)fileKeyInfoWithTimeStamp:(NSString * _Nonnull)timeStamp;
+ (NSString *)currentDate;
+ (NSString *)currentTime;
+ (NSString *)logInfoFilePath:(NSString *)date;
+ (NSString *)logInfoFilePathWithTimestamp:(NSString *)timeStamp;
+ (NSString *)logInfoIndexFilePath;
+ (NSString *)getDateStringWithTimeString:(NSString *)timeString;
+ (void)deleteLoganFile:(NSString *)name;
- (void)flush;
- (void)filePathForTimeStamp:(NSString *)date block:(PLVLoganFilePathBlock)filePathBlock;
- (void)OpenNewCLib;
+ (void)uploadFileToServer:(NSString *)urlStr date:(NSString *)date appId:(NSString *)appId unionId:(NSString *)unionId deviceId:(NSString *)deviceId resultBlock:(PLVLoganUploadResultBlock)resultBlock;
@end

void PLVLoganInit(NSData *_Nonnull aes_key16, NSData *_Nonnull aes_iv16, uint64_t max_file) {
    /// init 方法里面也要加内容吧 加区分直播间的方法 这里的话应该写模糊一点 或者fileName？
    __PLV_AES_KEY = aes_key16;
    __PLV_AES_IV = aes_iv16;
    __plv_max_file = max_file;
    if (__plv_max_reversed_date == 0) {
        __plv_max_reversed_date = 7;
    }
    PLVLoganHadInit = YES;
}

void PLVLoganSetMaxReversedDate(int max_reversed_date) {
    if (max_reversed_date > 0) {
        __plv_max_reversed_date = max_reversed_date;
    }
}

void PLVLoganSetSdkVersion(NSString  * _Nullable sdkVersion) {
    if ([PLVFdUtil checkStringUseable:sdkVersion]) {
        NSString *logMessage = [NSString stringWithFormat:@"[Logan]PLVFLogan init : %@ !",sdkVersion];
        PLVLogan(2, logMessage);
        PLVLoganKeyInfo(@"sdkVersion", sdkVersion);
        if (PLVLoganHadInit) {
            [Logan logan].lastSdkVersion = sdkVersion;
        }
    }
}
void PLVLogan(NSUInteger type, NSString *_Nonnull log) {
    if (PLVLoganHadInit) {
        [[Logan logan] writeLog:log logType:type];
    }
}

void PLVLoganKeyInfoWithTimestamp (NSString * _Nullable key, NSString * _Nullable info, NSString * _Nullable timestamp) {
    if (![PLVFdUtil checkStringUseable:info]) {
        return;
    }
    NSString *currentKeyInfoTime = [PLVFdUtil checkStringUseable:timestamp] ? timestamp : [Logan logan].lastLogCurrentTime;
    
    // 子map
    // mediaIdList、viewerIdList、ipList、sdkVersion、uploadState、fileName
    NSString *keyString = key;
    if (![PLVFdUtil checkStringUseable:key]) {
        keyString = @"customExpand";
    }
    
    NSString *filePath = [Logan logInfoFilePathWithTimestamp:currentKeyInfoTime];
    NSData *fileData = [PLVFFileUtil dataAtPath:filePath];
    NSDictionary *infoDictionary;
    NSMutableDictionary *infoMuDictionary = [NSMutableDictionary dictionary];
    
    if (fileData) {
        infoDictionary = [NSJSONSerialization JSONObjectWithData:fileData options:0 error:nil];
    }
    
    NSArray *keyInfoArray = [infoDictionary valueForKey:keyString];
    NSMutableArray * keyInfoMuArray;
    if ([PLVFdUtil checkArrayUseable:keyInfoArray]) {
        keyInfoMuArray = [NSMutableArray arrayWithArray:keyInfoArray];
        if (![keyInfoArray containsObject:info]) {
            [keyInfoMuArray addObject:info];
        }
    } else {
        keyInfoMuArray = [NSMutableArray arrayWithObject:info];
    }
    infoMuDictionary = [NSMutableDictionary dictionaryWithDictionary:infoDictionary];
    [infoMuDictionary setValue:keyInfoMuArray forKey:keyString];
    fileData = [NSJSONSerialization dataWithJSONObject:infoMuDictionary options:0 error:nil];
    [PLVFFileUtil writeData:fileData atPath:filePath];
    
    // 记录到总文件
    
    NSString *indexPath = [Logan logInfoIndexFilePath];
    NSData *indexData = [PLVFFileUtil dataAtPath:indexPath];
    NSDictionary *indexDict;
    NSMutableDictionary *indexMuDict = [NSMutableDictionary dictionary];

    if (indexData) {
        indexDict = [NSJSONSerialization JSONObjectWithData:indexData options:0 error:nil];
    }
    if ([PLVFdUtil checkDictionaryUseable:indexDict]) {
        indexMuDict = [NSMutableDictionary dictionaryWithDictionary:indexDict];
    }
    [indexMuDict setValue:infoMuDictionary forKey:currentKeyInfoTime];
    indexData = [NSJSONSerialization dataWithJSONObject:indexMuDict options:0 error:nil];
    [PLVFFileUtil writeData:indexData atPath:indexPath];
}

void PLVLoganKeyInfo (NSString * _Nullable key, NSString * _Nullable info) {
    PLVLoganKeyInfoWithTimestamp(key, info, nil);
}

void PLVLoganUseASL(BOOL b) {
    PLVLOGANUSEASL = b;
}

void PLVLoganDisable(BOOL b) {
    PLVLOGANDISABLE = b;
}

void PVLoganPrintClibLog(BOOL b) {
    clogan_debug(!!b);
}

void PLVLoganClearLog(NSString * _Nonnull timeStamp) {
    NSString *fileName = [NSString stringWithFormat:@"PLViOSLogFile_%@_%@",[Logan getDateStringWithTimeString:timeStamp],timeStamp];
    [Logan deleteLoganFile:fileName];
}

void PLLVLoganClearAllLogs(void) {
    [[Logan logan] clearLogs];
}

NSDictionary *_Nullable PLVLoganAllFilesInfo(void) {
    return [Logan allFilesInfo];
}

NSDictionary *_Nullable PLVLoganFileKeyInfo(NSString * _Nonnull timeStamp) {
    return [Logan fileKeyInfoWithTimeStamp:timeStamp];
}

void PLVLoganUploadFilePath(NSString *_Nonnull timestamp, PLVLoganFilePathBlock _Nonnull filePathBlock) {
    [[Logan logan] filePathForTimeStamp:timestamp block:filePathBlock];
}

void PLVLoganUpload(NSString * _Nonnull url, NSString * _Nonnull timestamp,NSString * _Nullable appId, NSString *_Nullable unionId,NSString *_Nullable deviceId, PLVLoganUploadResultBlock _Nullable resultBlock){
    ///上传方法后面再改
    [Logan uploadFileToServer:url date:timestamp appId:appId unionId:unionId deviceId:deviceId resultBlock:resultBlock];
}

void PLVLoganFlush(void) {
    /// 立即写到文件，切换时场景时应该将
    if(PLVLoganHadInit) {
        [[Logan logan] flush];
    }
}

NSString *_Nonnull PLVLoganTodaysDate(void) {
    return [Logan currentDate];
}

NSString *_Nonnull PLVLoganTodaysTime(void) {
    return [Logan logan].lastLogCurrentTime;
}


@implementation Logan
+ (instancetype)logan {
    static Logan *instance = nil;
    static dispatch_once_t pred;
    dispatch_once(&pred, ^{
        instance = [[Logan alloc] init];
    });
    return instance;
}

- (nonnull instancetype)init {
    if (self = [super init]) {
        _loganQueue = dispatch_queue_create("com.plv.logan", DISPATCH_QUEUE_SERIAL);
        dispatch_async(self.loganQueue, ^{
            [self initAndOpenCLib];
            [self addNotification];
            [Logan deleteOutdatedFiles];
        });
    }
    return self;
}

- (void)initAndOpenCLib {
    NSAssert(__PLV_AES_KEY, @"aes_key is nil!!!,Please use PLVLoganInit() to set the key.");
    NSAssert(__PLV_AES_IV, @"aes_iv is nil!!!,Please use PLVLoganInit() to set the iv.");
    const char *path = [Logan loganLogDirectory].UTF8String;
    
    const char *aeskey = (const char *)[__PLV_AES_KEY bytes];
    const char *aesiv = (const char *)[__PLV_AES_IV bytes];
    clogan_init(path, path, (int)__plv_max_file, aeskey, aesiv);
    NSString *today = self.lastLogCurrentTime;
    NSString *fileName = [Logan logFileName:today];
    clogan_open((char *)fileName.UTF8String);
    NSString *logMessage = [NSString stringWithFormat:@"[Logan]PLVFLogan init : %@ !",self.lastSdkVersion];
    PLVLogan(2, logMessage);
    PLVLoganKeyInfo(@"sdkVersion", self.lastSdkVersion);
    clogan_flush();
    __PLV_AES_KEY = nil;
    __PLV_AES_IV = nil;
}

- (void)OpenNewCLib {
    clogan_flush();
    self.lastLogCurrentTime = [Logan currentTime];
    NSString *fileName = [Logan logFileName:self.lastLogCurrentTime];
    clogan_open((char *)fileName.UTF8String);
    NSString *logMessage = [NSString stringWithFormat:@"[Logan]PLVFLogan init : %@ !",self.lastSdkVersion];
    PLVLogan(2, logMessage);
    PLVLoganKeyInfo(@"sdkVersion", self.lastSdkVersion);
    clogan_flush();
}

- (void)writeLog:(NSString *)log logType:(NSUInteger)type {
    if (log.length == 0) {
        return;
    }
    
    if (PLVLOGANDISABLE) {
        return;
    }
    
    NSTimeInterval localTime = [[NSDate date] timeIntervalSince1970];
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:localTime];
    NSDateFormatter * formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"MMdd HH:mm:ss:SSS";
    NSString *modifiedString = [self filterAndReplaceWithLog:log];
    NSString *currentLog = [NSString stringWithFormat:@"%@%@", [formatter stringFromDate:date], modifiedString];
    NSString *threadName = [[NSThread currentThread] name];
    NSInteger threadNum = 1;
    BOOL threadIsMain = [[NSThread currentThread] isMainThread];
    if (!threadIsMain) {
        threadNum = [self getThreadNum];
    }
    char *threadNameC = threadName ? (char *)threadName.UTF8String : "";
    if (PLVLOGANUSEASL) {
        [self printfLog:log type:type];
    }
    
    if (![self hasFreeSpece]) {
        return;
    }
    
    dispatch_async(self.loganQueue, ^{
        NSString *today = [Logan currentDate];
        NSString *todayTime = [Logan currentTime];
        NSString *fileName = [Logan logFileName:todayTime];
        if (self.lastLogDate && ![self.lastLogDate isEqualToString:today]) {
            // 日期变化，立即写入日志文件
            clogan_flush();
            clogan_open((char *)fileName.UTF8String);
            
            // 复制上一个日志关键信息
            [Logan copyKeyInfoFrom:self.lastLogCurrentTime to:todayTime];
            self.lastLogCurrentTime = todayTime;
            PLVLoganKeyInfo(@"sdkVersion", self.lastSdkVersion);
        }
        self.lastLogDate = today;
        clogan_write((int)type, (char *)currentLog.UTF8String, (long long)localTime, threadNameC, (long long)threadNum, (int)threadIsMain);
    });
}

- (void)flush {
    dispatch_async(self.loganQueue, ^{
        [self flushInQueue];
    });
}

- (void)flushInQueue {
    clogan_flush();
}

- (void)clearLogs {
    dispatch_async(self.loganQueue, ^{
        NSArray *array = [Logan localFilesArray];
        NSError *error = nil;
        BOOL ret;
        for (NSString *name in array) {
            NSString *path = [[Logan loganLogDirectory] stringByAppendingPathComponent:name];
            ret = [[NSFileManager defaultManager] removeItemAtPath:path error:&error];
        }
        
        NSString *indexPath = [Logan logInfoIndexFilePath];
        ret = [[NSFileManager defaultManager] removeItemAtPath:indexPath error:&error];
        
        NSArray *infoArray = [Logan localInfoFilesArray];
        for (NSString *name in infoArray) {
            NSString *path = [[Logan loganLogInfoDirectory] stringByAppendingPathComponent:name];
            ret = [[NSFileManager defaultManager] removeItemAtPath:path error:&error];
        }
    });
}

- (BOOL)hasFreeSpece {
    NSTimeInterval now = [[NSDate date] timeIntervalSince1970];
    if (now > (_PLVLastCheckFreeSpace + 60)) {
        _PLVLastCheckFreeSpace = now;
            // 每隔至少1分钟，检查一下剩余空间
        long long freeDiskSpace = [self freeDiskSpaceInBytes];
        if (freeDiskSpace <= 5 * 1024 * 1024) {
                // 剩余空间不足5m时，不再写入
            return NO;
        }
    }
    return YES;
}

- (long long)freeDiskSpaceInBytes {
    struct statfs buf;
    long long freespace = -1;
    if (statfs("/var", &buf) >= 0) {
        freespace = (long long)(buf.f_bsize * buf.f_bfree);
    }
    return freespace;
}

- (NSInteger)getThreadNum {
    NSString *description = [[NSThread currentThread] description];
    NSRange beginRange = [description rangeOfString:@"{"];
    NSRange endRange = [description rangeOfString:@"}"];
    
    if (beginRange.location == NSNotFound || endRange.location == NSNotFound) return -1;
    
    NSInteger length = endRange.location - beginRange.location - 1;
    if (length < 1) {
        return -1;
    }
    
    NSRange keyRange = NSMakeRange(beginRange.location + 1, length);
    
    if (keyRange.location == NSNotFound) {
        return -1;
    }
    
    if (description.length > (keyRange.location + keyRange.length)) {
        NSString *keyPairs = [description substringWithRange:keyRange];
        NSArray *keyValuePairs = [keyPairs componentsSeparatedByString:@","];
        for (NSString *keyValuePair in keyValuePairs) {
            NSArray *components = [keyValuePair componentsSeparatedByString:@"="];
            if (components.count) {
                NSString *key = components[0];
                key = [key stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
                if (([key isEqualToString:@"num"] || [key isEqualToString:@"number"]) && components.count > 1) {
                    return [components[1] integerValue];
                }
            }
        }
    }
    return -1;
}

- (void)printfLog:(NSString *)log type:(NSUInteger)type {
    static time_t dtime = -1;
    if (dtime == -1) {
        time_t tm;
        time(&tm);
        struct tm *t_tm;
        t_tm = localtime(&tm);
        dtime = t_tm->tm_gmtoff;
    }
    struct timeval time;
    gettimeofday(&time, NULL);
    int secOfDay = (time.tv_sec + dtime) % (3600 * 24);
    int hour = secOfDay / 3600;
    int minute = secOfDay % 3600 / 60;
    int second = secOfDay % 60;
    int millis = time.tv_usec / 1000;
    NSString *str = [[NSString alloc] initWithFormat:@"%02d:%02d:%02d.%03d [%lu] %@\n", hour, minute, second, millis, (unsigned long)type, log];
    const char *buf = [str cStringUsingEncoding:NSUTF8StringEncoding];
    printf("%s", buf);
}

// 过滤和替换日志记录
- (NSString*)filterAndReplaceWithLog:(NSString *)log {
    if ([log rangeOfString:@"\"EVENT\":\"SPEAK\""].location != NSNotFound) {
        return @"SPEAK";
    } else if ([log rangeOfString:@"\"EVENT\":\"CHAT_IMG\""].location != NSNotFound) {
        return @"CHAT_IMG";
    } else if ([log rangeOfString:@"\"EVENT\":\"LOGIN\""].location != NSNotFound) {
        return @"LOGIN";
    } else if ([log rangeOfString:@"\"EVENT\":\"LOGOUT\""].location != NSNotFound) {
        return @"LOGOUT";
    } else if ([log rangeOfString:@"\"EVENT\":\"LIKES\""].location != NSNotFound) {
        return @"LIKES";
    } else if ([log rangeOfString:@"\"EVENT\":\"onSliceControl\""].location != NSNotFound) {
        return @"onSliceControl";
    } else if ([log rangeOfString:@"\"EVENT\":\"onSliceDraw\""].location != NSNotFound) {
        return @"onSliceDraw";
    } else if ([log rangeOfString:@"\"EVENT\":\"CUSTOMER_MESSAGE\""].location != NSNotFound) {
        return @"CUSTOMER_MESSAGE";
    } else if ([log rangeOfString:@"\"EVENT\":\"REWARD\""].location != NSNotFound) {
        return @"REWARD";
    }
    return log;
}

#pragma mark - notification
- (void)addNotification {
    // App Extension
    if ( [[[NSBundle mainBundle] bundlePath] hasSuffix:@".appex"] ) {
        return ;
    }
#if TARGET_OS_IPHONE
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillEnterForeground) name:UIApplicationWillEnterForegroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appDidEnterBackground) name:UIApplicationDidEnterBackgroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillTerminate) name:UIApplicationWillTerminateNotification object:nil];
#else
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillEnterForeground) name:NSApplicationWillBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appDidEnterBackground) name:NSApplicationDidResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillTerminate) name:NSApplicationWillTerminateNotification object:nil];
#endif

}

- (void)appWillResignActive {
    [self flush];
}

- (void)appDidEnterBackground {
    [self flush];
}

- (void)appWillEnterForeground {
    [self flush];
}

- (void)appWillTerminate {
    [self flush];
}

- (void)filePathForTimeStamp:(NSString *)timestamp block:(PLVLoganFilePathBlock)filePathBlock {
    NSString *uploadFilePath = nil;
    NSString *filePath = nil;
    if (timestamp.length) {
        NSArray *allFiles = [Logan localFilesArray];
        NSString *fileName = [NSString stringWithFormat:@"PLViOSLogFile_%@_%@",[Logan getDateStringWithTimeString:timestamp],timestamp];
        if ([allFiles containsObject:fileName]) {
            filePath = [Logan logFilePath:timestamp];
            if ([[NSFileManager defaultManager] fileExistsAtPath:filePath]) {
                uploadFilePath = filePath;
            }
        }
    }
    
    if (uploadFilePath.length) {
        if ([timestamp isEqualToString:self.lastLogCurrentTime]) {
            [self OpenNewCLib];
        }
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        filePathBlock(uploadFilePath);
    });
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

+ (NSDictionary *)allFilesInfo {
    NSArray *allFiles = [Logan localFilesArray];
    NSString *dateFormatString = @"yyyyMMdd";
    NSMutableDictionary *infoDic = [NSMutableDictionary new];
    for (NSString *file in allFiles) {
        if ([file pathExtension].length > 0 || file.length != (dateFormatString.length + 28)) {
            continue;
        }
        NSString *dateString = [file substringWithRange:NSMakeRange(15 + dateFormatString.length, 13)];
        unsigned long long gzFileSize = [Logan fileSizeAtPath:[self logFilePath:dateString]];
        NSString *size = [NSString stringWithFormat:@"%llu", gzFileSize];
        [infoDic setObject:size forKey:dateString];
    }
    return infoDic;
}

+ (NSDictionary *)fileKeyInfoWithTimeStamp:(NSString * _Nonnull)timeStamp {
    NSString *indexPath = [Logan logInfoIndexFilePath];
    NSData *indexData = [PLVFFileUtil dataAtPath:indexPath];
    NSDictionary *indexDict;
    if (indexData) {
        indexDict = [NSJSONSerialization JSONObjectWithData:indexData options:0 error:nil];
    }
    return PLV_SafeDictionaryForDictKey(indexDict, timeStamp);
}

#pragma mark - file

+ (void)uploadFileToServer:(NSString *)urlStr date:(NSString *)date appId:(NSString *)appId unionId:(NSString *)unionId deviceId:(NSString *)deviceId resultBlock:(PLVLoganUploadResultBlock)resultBlock {
    PLVLoganUploadFilePath(date, ^(NSString *_Nullable filePatch) {
        if (filePatch == nil) {
            if(resultBlock){
                dispatch_async(dispatch_get_main_queue(), ^{
                    NSError * error = [NSError errorWithDomain:@"come.plv.logan.error" code:-100 userInfo:@{@"info" : [NSString stringWithFormat:@"can't find file of %@",date]}];
                    resultBlock(nil,nil,error);
                });
            }
            return;
        }
        NSURL *url = [NSURL URLWithString:urlStr];
        NSMutableURLRequest *req = [[NSMutableURLRequest alloc] initWithURL:url cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:60];
        [req setHTTPMethod:@"POST"];
        [req addValue:@"binary/octet-stream" forHTTPHeaderField:@"Content-Type"];
        if(appId.length >0){
            [req addValue:appId forHTTPHeaderField:@"appId"];
        }
        if(unionId.length >0){
            [req addValue:unionId forHTTPHeaderField:@"unionId"];
        }
        NSString *bundleVersion = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleVersion"];
        if (bundleVersion.length > 0) {
            [req addValue:bundleVersion forHTTPHeaderField:@"bundleVersion"];
        }
        
        if(deviceId.length >0){
            [req addValue:deviceId forHTTPHeaderField:@"deviceId"];
        }
        [req addValue:[[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"] forHTTPHeaderField:@"appVersion"];
        [req addValue:@"2" forHTTPHeaderField:@"platform"];
        [req addValue:date forHTTPHeaderField:@"fileDate"];
        
        NSURL *fileUrl = [NSURL fileURLWithPath:filePatch];
        NSURLSessionUploadTask *task = [[NSURLSession sharedSession] uploadTaskWithRequest:req fromFile:fileUrl completionHandler:^(NSData *_Nullable data, NSURLResponse *_Nullable response, NSError *_Nullable error) {
            if(resultBlock){
                dispatch_async(dispatch_get_main_queue(), ^{
                    resultBlock(data,response,error);
                });
            }
        }];
        [task resume];
    });
}

+ (void)deleteOutdatedFiles {
    NSArray *allFiles = [Logan localFilesArray];
    __block NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    NSString *dateFormatString = @"yyyyMMdd";
    [formatter setDateFormat:dateFormatString];
    [allFiles enumerateObjectsUsingBlock:^(NSString *_Nonnull dateStr, NSUInteger idx, BOOL *_Nonnull stop) {
            // 检查后缀名
        if ([dateStr pathExtension].length > 0) {
            [self deleteLoganFile:dateStr];
            return;
        }
        
            // 检查文件名长度
        if (dateStr.length != (dateFormatString.length + 28)) {
            [self deleteLoganFile:dateStr];
            return;
        }
            // 文件名转化为日期
        NSString *currentDateStr = [dateStr substringWithRange:NSMakeRange(14, dateFormatString.length)];
        NSDate *date = [formatter dateFromString:currentDateStr];
        NSString *todayStr = [Logan currentDate];
        NSDate *todayDate = [formatter dateFromString:todayStr];
        if (!date || [self getDaysFrom:date To:todayDate] >= __plv_max_reversed_date) {
                // 删除过期文件
            [self deleteLoganFile:dateStr];
        }

        NSString *dateString = [dateStr substringWithRange:NSMakeRange(15 + dateFormatString.length, 13)];
        unsigned long long gzFileSize = [Logan fileSizeAtPath:[self logFilePath:dateString]];
        if (gzFileSize == 0){
            // 删除空文件
        [self deleteLoganFile:dateStr];
        }
        
        // 删除已完成上传的文件
        NSArray *uploadState = PLV_SafeArraryForDictKey(PLVLoganFileKeyInfo(dateString), @"uploadState");
        if ([PLVFdUtil checkArrayUseable:uploadState] && [uploadState containsObject:@"success"]) {
            [self deleteLoganFile:dateStr];
        }
    }];
}

+ (void)deleteLoganFile:(NSString *)name {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    [fileManager removeItemAtPath:[[self loganLogDirectory] stringByAppendingPathComponent:name] error:nil];
    [fileManager removeItemAtPath:[self logInfoFilePath:name] error:nil];
    [Logan resetIndexFile];
}

+ (void)resetIndexFile {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    [fileManager removeItemAtPath:[self logInfoIndexFilePath] error:nil];
    
    NSArray *allInfoFiles = [Logan localInfoFilesArray];
    NSMutableDictionary *indexDict = [[NSMutableDictionary alloc] init];
    for (NSString *name in allInfoFiles) {
        NSString *dateFormatString = @"yyyyMMdd";
        if ([name pathExtension].length > 0 || name.length != (dateFormatString.length + 28)) {
            continue;
        }
        NSString *dateString = [name substringWithRange:NSMakeRange(15 + dateFormatString.length, 13)];
        NSString *filePath = [Logan logInfoFilePathWithTimestamp:dateString];
        NSData *fileData = [PLVFFileUtil dataAtPath:filePath];
        NSMutableDictionary *infoDictionary = [[NSMutableDictionary alloc] init];
        if (fileData) {
            infoDictionary = [NSJSONSerialization JSONObjectWithData:fileData options:0 error:nil];
            [indexDict setValue:infoDictionary forKey:dateString];
        }
    }
    if (indexDict.count > 0) {
        NSData *indexData = [NSJSONSerialization dataWithJSONObject:indexDict options:0 error:nil];
        [PLVFFileUtil writeData:indexData atPath:[self logInfoIndexFilePath]];
    }
}

+ (NSInteger)getDaysFrom:(NSDate *)serverDate To:(NSDate *)endDate {
    NSCalendar *gregorian = [[NSCalendar alloc]
                             initWithCalendarIdentifier:NSCalendarIdentifierGregorian];
    
    NSDate *fromDate;
    NSDate *toDate;
    [gregorian rangeOfUnit:NSCalendarUnitDay startDate:&fromDate interval:NULL forDate:serverDate];
    [gregorian rangeOfUnit:NSCalendarUnitDay startDate:&toDate interval:NULL forDate:endDate];
    NSDateComponents *dayComponents = [gregorian components:NSCalendarUnitDay fromDate:fromDate toDate:toDate options:0];
    return dayComponents.day;
}

+ (NSString *)uploadFilePath:(NSString *)date {
    return [[self loganLogDirectory] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.temp", date]];
}
+ (NSString *)logFilePath:(NSString *)date {
    return [[Logan loganLogDirectory] stringByAppendingPathComponent:[Logan logFileName:date]];
}

+ (NSString *)logFileName:(NSString *)date {
    NSString *currentDateString = [self getDateStringWithTimeString:date] ? : @"";
    return [NSString stringWithFormat:@"PLViOSLogFile_%@_%@", currentDateString, date];
}

+ (unsigned long long)fileSizeAtPath:(NSString *)filePath {
    if (filePath.length == 0) {
        return 0;
    }
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL isExist = [fileManager fileExistsAtPath:filePath];
    if (isExist) {
        return [[fileManager attributesOfItemAtPath:filePath error:nil] fileSize];
    } else {
        return 0;
    }
}

+ (NSArray *)localFilesArray {
    return [[[[NSFileManager defaultManager] contentsOfDirectoryAtPath:[self loganLogDirectory] error:nil] filteredArrayUsingPredicate:[NSPredicate predicateWithFormat:@"SELF CONTAINS[cd] 'PLViOSLogFile_'"]] sortedArrayUsingSelector:@selector(compare:)]; //[c]不区分大小写 , [d]不区分发音符号即没有重音符号 , [cd]既不区分大小写，也不区分发音符号。
}

+ (NSArray *)localInfoFilesArray {
    return [[[[NSFileManager defaultManager] contentsOfDirectoryAtPath:[[Logan loganLogInfoDirectory] stringByAppendingPathComponent:@"/contain"] error:nil] filteredArrayUsingPredicate:[NSPredicate predicateWithFormat:@"SELF CONTAINS[cd] 'PLViOSLogFile_'"]] sortedArrayUsingSelector:@selector(compare:)]; //[c]不区分大小写 , [d]不区分发音符号即没有重音符号 , [cd]既不区分大小写，也不区分发音符号。
}

+ (NSString *)currentDate {
    NSString *key = @"LOGAN_CURRENTDATE";
    NSMutableDictionary *dictionary = [[NSThread currentThread] threadDictionary];
    NSDateFormatter *dateFormatter = [dictionary objectForKey:key];
    if (!dateFormatter) {
        dateFormatter = [[NSDateFormatter alloc] init];
        [dictionary setObject:dateFormatter forKey:key];
        [dateFormatter setLocale:[NSLocale localeWithLocaleIdentifier:@"en_US_POSIX"]];
        [dateFormatter setDateFormat:@"yyyyMMdd"];
        [dictionary setObject:dateFormatter forKey:key];
    }
    return [dateFormatter stringFromDate:[NSDate new]];
}

+ (NSString *)currentTime {
    return [PLVFdUtil curTimeStamp];
}

+ (NSString *)getDateStringWithTimeString:(NSString *)timeString {
    NSTimeInterval time = [timeString doubleValue] / 1000;
    NSDate *detailDate = [NSDate dateWithTimeIntervalSince1970:time];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyyMMdd"];
    NSString *currentDateString = [dateFormatter stringFromDate:detailDate];
    return currentDateString;
}

+ (NSString *)loganLogDirectory {
    static NSString *dir = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        dir = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0] stringByAppendingPathComponent:@"PLVLog"];//修改本地路径
    });
    return dir;
}

- (NSString *)lastLogCurrentTime {
    if (!_lastLogCurrentTime) {
        _lastLogCurrentTime = [Logan currentTime];
    }
    return _lastLogCurrentTime;
}

#pragma mark - key information
+ (NSString *)loganLogInfoDirectory {
    static NSString *dir = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        dir = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0] stringByAppendingPathComponent:@"PLVLog/info"];//日志信息文件路径,参考git设计
    });
    return dir;
}

+ (NSString *)logInfoFilePath:(NSString *)date {
    return [[Logan loganLogInfoDirectory] stringByAppendingPathComponent:[NSString stringWithFormat:@"/contain/%@" ,date]];
}

+ (NSString *)logInfoFilePathWithTimestamp:(NSString *)timeStamp {
    return [[Logan loganLogInfoDirectory] stringByAppendingPathComponent:[NSString stringWithFormat:@"/contain/%@" ,[Logan logFileName:timeStamp]]];
}

+ (NSString *)logInfoIndexFilePath {
    return [[Logan loganLogInfoDirectory] stringByAppendingPathComponent:@"/index/index"];
}

// 复制上一个日志的关键信息
+ (void)copyKeyInfoFrom:(NSString *)timeStamp to:(NSString *)otherTimeStamp {
    NSDictionary *infoDictionary = PLVLoganFileKeyInfo(timeStamp);
    if ([PLVFdUtil checkDictionaryUseable:infoDictionary]) {
        NSData *fileData = [NSJSONSerialization dataWithJSONObject:infoDictionary options:0 error:nil];
        NSString *filePath = [Logan logInfoFilePathWithTimestamp:otherTimeStamp];
        [PLVFFileUtil writeData:fileData atPath:filePath];
        
        // 记录到总文件
        NSString *indexPath = [Logan logInfoIndexFilePath];
        NSData *indexData = [PLVFFileUtil dataAtPath:indexPath];
        NSDictionary *indexDict;
        NSMutableDictionary *indexMuDict = [NSMutableDictionary dictionary];

        if (indexData) {
            indexDict = [NSJSONSerialization JSONObjectWithData:indexData options:0 error:nil];
        }
        if ([PLVFdUtil checkDictionaryUseable:indexDict]) {
            indexMuDict = [NSMutableDictionary dictionaryWithDictionary:indexDict];
        }
        [indexMuDict setValue:infoDictionary forKey:otherTimeStamp];
        indexData = [NSJSONSerialization dataWithJSONObject:indexMuDict options:0 error:nil];
        [PLVFFileUtil writeData:indexData atPath:indexPath];
    }
}

@end

