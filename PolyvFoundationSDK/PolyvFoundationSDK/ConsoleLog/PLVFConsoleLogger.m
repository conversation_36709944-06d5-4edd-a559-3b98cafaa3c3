//
//  PLVFConsoleLogger.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/2/20.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVFConsoleLogger.h"
#import "PLVFLogan.h"
#import "PLVKeyUtil.h"

@interface PLVFConsoleLogger ()

@end

@implementation PLVFConsoleLogger

#pragma mark - Public

+ (instancetype)defaultLogger {
    static PLVFConsoleLogger *logger = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        logger = [[PLVFConsoleLogger alloc] init];
    });
    return logger;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        NSData *keydata = [[PLVKeyUtil getApiUtilsKey] dataUsingEncoding:NSUTF8StringEncoding];
        NSData *ivdata = [[PLVKeyUtil getApiUtilsIv] dataUsingEncoding:NSUTF8StringEncoding];
        uint64_t file_max = 10 * 1024 * 1024;
        // logan init，incoming 16-bit key，16-bit iv，largest written to the file size(byte)
        PLVLoganInit(keydata, ivdata, file_max);
    }
    return self;
}

- (void)logWithModule:(NSString * _Nullable)module type:(PLVFConsoleLogType)type format:(NSString *)frmt, ... {
    if (!frmt) {
        frmt = @"";
        return;
    }
    
    va_list args;
    va_start(args, frmt);
    NSString *message = [[NSString alloc] initWithFormat:frmt arguments:args];
    va_end(args);

    NSString *loganModule = [self stringWithModule:module];
    // 替换和过滤message
    NSString *log = [self replaceWithLog:message];
    if (![self filterWithModule:loganModule log:log]) {
        return;
    }
    PLVLogan([self changeToLoganType:type], [NSString stringWithFormat:@"%@%@", loganModule, log]);
    PLVLoganFlush();
    
    if (!(type & self.logLevel)) {
        return;
    }
    
    NSLog(@"%@%@%@", (module ? [NSString stringWithFormat:@"[%@]", module] : @""), [self stringWithLogType:type], message);
}

- (void)logWithModule:(NSString *)module type:(PLVFConsoleLogType)type print:(BOOL)print format:(NSString *)frmt, ... {
    
    if (!frmt) {
        frmt = @"";
        return;
    }
    
    va_list args;
    va_start(args, frmt);
    NSString *message = [[NSString alloc] initWithFormat:frmt arguments:args];
//    NSLog(frmt, args);
    va_end(args);

    NSString *loganModule = [self stringWithModule:module];
    // 替换和过滤message
    NSString *log = [self replaceWithLog:message];
    if (![self filterWithModule:loganModule log:log]) {
        return;
    }
    PLVLogan([self changeToLoganType:type], [NSString stringWithFormat:@"%@%@", loganModule, log]);
    PLVLoganFlush();
    
    if (!(type & self.logLevel)) {
        return;
    }
    
    if (!print) {
        return;
    }
    
    NSLog(@"%@%@%@", (module ? [NSString stringWithFormat:@"[%@]", module] : @""), [self stringWithLogType:type], message);
}

- (void)logWithKey:(NSString *)key info:(NSString *)info {
    PLVLoganKeyInfo(key, info);
}
#pragma mark - Private

- (NSString *)stringWithLogType:(PLVFConsoleLogType)type {
    switch (type) {
        case PLVFConsoleLogTypeERROR:
            return @"[ERROR]";
        case PLVFConsoleLogTypeWARN:
            return @"[WARN]";
        case PLVFConsoleLogTypeDEBUG:
            return @"[DEBUG]";
        case PLVFConsoleLogTypeINFO:
            return @"[INFO]";
        default:
            return @"";
    }
}

// 简化module打印
- (NSString *)stringWithModule:(NSString *)module {
    if (!module) {
        return @"";
    }
    
    NSError *error = NULL;
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"(JS|SOCKET|PPT|Interact|Player|Room|Chatroom|Streamer|Download|Network|LinkMic|Verbose|Logan)"
                                                                           options:NSRegularExpressionCaseInsensitive
                                                                             error:&error];
    if (error) {
        NSLog(@"Regular expression error %@", error);
        return nil;
    }
    
    NSTextCheckingResult *match = [regex firstMatchInString:module options:0 range:NSMakeRange(0, [module length])];
    NSString *matchSting =  [NSString stringWithFormat:@"[%@]",module];
    if (match) {
        matchSting = [NSString stringWithFormat:@"[%@]",[module substringWithRange:[match rangeAtIndex:0]]];
    }
    
    return matchSting;
}

- (NSUInteger)changeToLoganType:(PLVFConsoleLogType)type {
    NSUInteger loganType = 2; // 安卓端2表示Verbose
    switch (type) {
        case PLVFConsoleLogTypeERROR:
            loganType  = 6;
            break;;
        case PLVFConsoleLogTypeWARN:
            loganType = 5;
            break;
        case PLVFConsoleLogTypeDEBUG:
            loganType = 3;
            break;
        case PLVFConsoleLogTypeINFO:
            loganType = 4;
            break;
        default:
            break;
    }
    return loganType;
}

//替换打印日志
- (NSString*)replaceWithLog:(NSString *)log {
    if ([log rangeOfString:@"\"EVENT\":\"SPEAK\""].location != NSNotFound) {
        return @"SPEAK";
    } else if ([log rangeOfString:@"\"EVENT\":\"CHAT_IMG\""].location != NSNotFound) {
        return @"CHAT_IMG";
    } else if ([log rangeOfString:@"\"EVENT\":\"LOGIN\""].location != NSNotFound) {
        return @"LOGIN";
    } else if ([log rangeOfString:@"\"EVENT\":\"LOGOUT\""].location != NSNotFound) {
        return @"LOGOUT";
    } else if ([log rangeOfString:@"\"EVENT\":\"LIKES\""].location != NSNotFound) {
        return @"LIKES";
    } else if ([log rangeOfString:@"\"EVENT\":\"onSliceControl\""].location != NSNotFound) {
        return @"onSliceControl";
    } else if ([log rangeOfString:@"\"EVENT\":\"onSliceDraw\""].location != NSNotFound) {
        return @"onSliceDraw";
    } else if ([log rangeOfString:@"\"EVENT\":\"CUSTOMER_MESSAGE\""].location != NSNotFound) {
        return @"CUSTOMER_MESSAGE";
    } else if ([log rangeOfString:@"\"EVENT\":\"REWARD\""].location != NSNotFound) {
        return @"REWARD";
    }
    return log;
}

//过滤不需要打印的消息
- (BOOL)filterWithModule:(NSString *)module log:(NSString *)log {
    if ([module rangeOfString:@"SOCKET"].location != NSNotFound &&
        ([log isEqualToString:@"SPEAK"] || [log isEqualToString:@"LOGIN"] || [log isEqualToString:@"LOGOUT"] || [log isEqualToString:@"LIKES"] || [log isEqualToString:@"CUSTOMER_MESSAGE"] || [log isEqualToString:@"REWARD"] || [log isEqualToString:@"CHAT_IMG"])) {
        return NO;
    } else if ([module rangeOfString:@"JS"].location != NSNotFound && [log isEqualToString:@"CUSTOMER_MESSAGE"]) {
        return NO;
    }
    return YES;
}

@end
