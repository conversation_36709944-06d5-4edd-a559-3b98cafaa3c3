//
//  PLVFELogReporter.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/5.
//  Copyright © 2019 PLV. All rights reserved.
//

#define PLVFLOGREPORTER_PROTECTED_ACCESS

#import "PLVFELogReporter.h"
#import "PLVFLogReporterInternal.h"
#import "PLVFJsonConverter.h"
#import "PLVFdUtil.h"
#import "PLVDataUtil.h"
#import "PLVFELogModel.h"
#import "PLVFUserAgentBuilder.h"

@interface PLVFELogReporter ()

/// 日志发送http地址域名
@property (nonatomic, strong) NSString *baseUrl;
/// 日志发送http路径
@property (nonatomic, strong) NSString *path;
/// ltype 字段：点播 1、直播 50、开播 62、互动学堂 71
@property (nonatomic, assign) NSInteger ltype;

/// 其他属性
@property (nonatomic, copy) NSString * _Nullable channelId;
@property (nonatomic, copy) NSString * _Nullable userId;
@property (nonatomic, copy) NSString * _Nullable vid;


@end

@implementation PLVFELogReporter

#pragma mark - Getter & Setter

- (void)setProductType:(PLVFLogProductType)productType {
    [super setProductType:productType];
    self.path = @"";
    if (productType == PLVProductTypeVod) {
        self.baseUrl = @"https://elog.polyv.net/v4/vod/save-elog";
        self.ltype = 1;
    } else {
        self.baseUrl = @"https://elog.polyv.net/v4/live/save-elog";
        if (productType == PLVProductTypeLive) {
            self.ltype = 50;
        } else if (productType == PLVProductTypeStreamer) {
            self.ltype = 62;
        } else if (productType == PLVProductTypeHiClass) {
            self.ltype = 71;
        } else {
            self.baseUrl = @"";
        }
    }
}

#pragma mark - Register

- (void)registerWithUserId:(NSString *)userId secretkey:(NSString *)secretkey {
    [self registerWithUserId:userId];
}

- (void)registerWithUserId:(NSString *)userId {
    if (self.productType != PLVProductTypeVod) {
        return;
    }
    
    self.userId = userId;
    self.vid = nil;
    
    self.identifier = self.userId;
}

- (void)registerWithChannelId:(NSString *)channelId appId:(NSString *)appId appSecret:(NSString *)appSecret userId:(NSString *)userId {
    [self registerWithChannelId:channelId userId:userId];
}

- (void)registerWithChannelId:(NSString *)channelId userId:(NSString *)userId {
    if (self.productType != PLVProductTypeLive) {
        return;
    }
    
    self.channelId = channelId;
    self.userId = userId;
    self.vid = nil;
    
    self.identifier = self.userId;
}

- (void)registerWithChannelId:(NSString *)channelId appId:(NSString *)appId appSecret:(NSString *)appSecret userId:(NSString *)userId vId:(NSString *)vid {
    [self registerWithChannelId:channelId userId:userId vId:vid];
}

- (void)registerWithChannelId:(NSString *)channelId userId:(NSString *)userId vId:(NSString *)vid {
    if (self.productType != PLVProductTypeLive) {
        return;
    }
    
    self.channelId = channelId;
    self.userId = userId;
    self.vid = vid;
    
    self.identifier = self.userId;
}

- (void)registerWithChannelId:(NSString *)channelId {
    if (self.productType != PLVProductTypeStreamer && self.productType != PLVProductTypeHiClass) {
        return;
    }
    
    self.channelId = channelId;
    self.userId = nil;
    self.vid = nil;
    
    self.identifier = self.channelId;
}

- (void)unregister {
    self.identifier = nil;
}

#pragma mark - Template Model Generater

- (PLVFELogModel *)generateTemplateModel {
    PLVFELogModel *elogModel = [[PLVFELogModel alloc] init];
    elogModel.channelId = self.channelId;
    elogModel.log = [self generateElogLogModel];
    return elogModel;
}

- (PLVELogLogModel *)generateElogLogModel {
    PLVELogLogModel *log = [[PLVELogLogModel alloc] init];
    log.channelId2 = self.channelId;
    log.userId2 = self.userId;
    log.version2 = [[PLVFUserAgentBuilder sharedBuilder] formatVersion];
    
    log.time = [PLVFdUtil curTimeStamp];
    log.platform2 = [PLVFUserAgentBuilder getDeviceNameAndVersion];
    log.deviceType = [PLVFUserAgentBuilder sharedBuilder].deviceType;
    log.project = [PLVFUserAgentBuilder sharedBuilder].project;
    log.framework = [PLVFUserAgentBuilder sharedBuilder].framework;
    log.videoId = self.vid;
    
    PLVELogLogFileModel *logFile = [[PLVELogLogFileModel alloc] init];
    log.logFile = logFile;
    
    return log;
}

#pragma mark - Patch Log Override

/// 将批量日志的数据模型数组转换为网络请求
- (NSMutableURLRequest *)getPatchLogRequestWithModels:(NSArray *)models {
    if ([models count] == 0) {
        return nil;
    }
    
    PLVFELogModel *logModel = (PLVFELogModel *)models[0];
    if (![logModel isKindOfClass:[PLVFELogModel class]]) {
        return nil;
    }
    
    if ([models count] > 1) {
        logModel = [self combineModels:models];
    }
    
    NSMutableURLRequest *request = [self startReport:logModel];
    return request;
}

#pragma mark - Real Time Log Override

/// 将实时日志的数据模型转换为网络请求
- (NSMutableURLRequest *)getLogRequestWithModel:(PLVFLogModel *)model {
    if (model == nil) {
        return nil;
    }
    
    PLVFELogModel *logModel = (PLVFELogModel *)model;
    if (![logModel isKindOfClass:[PLVFELogModel class]]) {
        return nil;
    }
    
    NSMutableURLRequest *request = [self startReport:logModel];
    return request;
}

#pragma mark - Patch Log Cache Override

/// 批量日志缓存文件存储路径
- (NSString *)cacheFilePath {
    return [NSString stringWithFormat:@"Elog/%@", [PLVFUserAgentBuilder sharedBuilder].project];
}

/// 批量日志缓存存储文件名
- (NSString *)cacheFileName {
    if (self.userId || self.channelId) {
        return [NSString stringWithFormat:@"log_%@.txt", self.identifier];
    } else {
        return nil;
    }
}

/// 将日志的数据模型转换为可直接写入缓存的json字符串
- (NSString *)convertModelsToJsonString:(NSArray *)array {
    PLVFELogModel *logModel = (PLVFELogModel *)array[0];
    if (![logModel isKindOfClass:[PLVFELogModel class]]) {
        return nil;
    }
    
    if ([array count] > 1) {
        logModel = [self combineModels:array];
    }
    
    NSString *log = [PLVFJsonConverter jsonStringFromModelArray:logModel.logs];
    return log;
}

/// 将缓存的json字符串转换为日志的数据模型
- (NSArray *)convertJsonStringToModels:(NSArray *)array {
    if (array == nil || [array count] == 0) {
        return nil;
    }
    
    NSMutableArray *muArray = [[NSMutableArray alloc] initWithCapacity:[array count]];
    for (NSDictionary *dict in array) {
        if ([dict isKindOfClass:[NSDictionary class]]) {
            PLVELogLogModel *log = [[PLVELogLogModel alloc] initWithDictionary:dict];
            PLVFELogModel *eModel = [self generateTemplateModel];
            eModel.log = log;
            [muArray addObject:eModel];
        }
    }
    return [muArray copy];
}

#pragma mark - Utils

/// 把数组里面所有 model 的 log 字段进行拼接，放入最后一条 model 的 logs 字段
- (PLVFELogModel *)combineModels:(NSArray *)array {
    if ([array count] == 0) {
        return nil;
    }
    
    NSMutableArray *muArray = [[NSMutableArray alloc] init];
    for (PLVFELogModel *elogModel in array) {
        [muArray addObject:elogModel.log];
    }
    PLVFELogModel *elogModel = [array lastObject];
    elogModel.logs = [muArray copy];
    return elogModel;
}

- (NSData *)httpBodyWithDictionary:(NSDictionary *)dict {
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dict options:0 error:0];
    if (self.requestXbody) {
        NSString *xbodyString = [PLVDataUtil jsonStringWithJSONObject:dict];
        if ([PLVFSignConfig sharedInstance].encryptType == PLVEncryptType_SM2) {
            xbodyString = [PLVSM2Util formatEncryptString:xbodyString publicKey:[PLVKeyUtil getApiUtilsPublicKey]];
        } else {
            xbodyString = [[PLVDataUtil AES128EncryptData:jsonData withKey:[PLVKeyUtil getApiUtilsKey] iv:[PLVKeyUtil getApiUtilsIv]] base64EncodedStringWithOptions:0];
        }
        if (xbodyString && xbodyString.length > 0) {
            xbodyString = [NSString stringWithFormat:@"xbody=%@",xbodyString];
            NSString *charactersToEscape = @"+";
            NSCharacterSet *allowedCharacters = [[NSCharacterSet characterSetWithCharactersInString:charactersToEscape] invertedSet];
            xbodyString = [xbodyString stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
            jsonData = [xbodyString dataUsingEncoding:NSUTF8StringEncoding];
        }
    }
    return jsonData;
}

#pragma mark - Request Generater

/// 将日志的数据模型数组转换为网络请求
- (NSMutableURLRequest *)startReport:(PLVFELogModel *)model {
    if (model.log == nil && (model.logs == nil || [model.logs count] == 0)) {
        return nil;
    }
    
    NSMutableURLRequest *request = nil;
    if (self.productType == PLVProductTypeVod) {
        request = [self convertVodModelToRequest:model];
    } else if (self.productType == PLVProductTypeLive || self.productType == PLVProductTypeStreamer || self.productType == PLVProductTypeHiClass) {
        request = [self convertLiveModelToRequest:model];
    }
    
    return request;
}

/// 将点播的ELog数据模型转换为网络请求
- (NSMutableURLRequest *)convertVodModelToRequest:(PLVFELogModel *)model {
    if (model.userId == nil) {
        return nil;
    }
    
    NSString *urlString = [NSString stringWithFormat:@"%@%@%@", self.baseUrl, model.userId, self.path];
    
    NSString *log;
    if(model.logs) {
        log = [PLVFJsonConverter jsonStringFromModelArray:model.logs];
    } else {
        log = [PLVFJsonConverter jsonStringFromModel:model.log];
    }
    
    NSString *timestamp = [PLVFdUtil curTimeStamp];
    NSString *sign = [self vodSignWithUserId:model.userId timeStamp:timestamp];
    if (sign == nil) {
        return nil;
    }
    
    NSMutableDictionary *muParam = [[NSMutableDictionary alloc] init];
    muParam[@"ptime"] = timestamp;
    muParam[@"sign"] = sign;
    muParam[@"log"] = log;
    muParam[@"ltype"] = @(self.ltype);
    
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] initWithURL:[NSURL URLWithString:urlString]];
    NSData *jsonData = [self httpBodyWithDictionary:muParam];
    request.HTTPMethod = @"POST";
    request.HTTPBody = jsonData;
    if (self.requestXbody &&
        [PLVFSignConfig sharedInstance].encryptType == PLVEncryptType_SM2) {
        [request setValue:@"2" forHTTPHeaderField:@"x-e-type"];
    }
    
    return request;
}

/// 将直播的ELog数据模型转换为网络请求
- (NSMutableURLRequest *)convertLiveModelToRequest:(PLVFELogModel *)model {
    if (model.channelId == nil) {
        return nil;
    }
        
    NSString *urlString = [NSString stringWithFormat:@"%@", self.baseUrl];
    
    NSString *log;
    if(model.logs) {
        log = [PLVFJsonConverter jsonStringFromModelArray:model.logs];
    } else {
        log = [PLVFJsonConverter jsonStringFromModel:model.log];
    }
        
    NSString *timestamp = [PLVFdUtil curTimeStamp];
    NSString *sign = [self liveSignWithChannelId:model.channelId timeStamp:timestamp];
    if (sign == nil) {
        return nil;
    }
    
    NSMutableDictionary *muParam = [[NSMutableDictionary alloc] init];
    muParam[@"timestamp"] = timestamp;
    muParam[@"sign"] = sign;
    muParam[@"log"] = log;
    muParam[@"ltype"] = @(self.ltype);
    muParam[@"channelId"] = model.channelId;
    
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] initWithURL:[NSURL URLWithString:urlString]];
    request.HTTPMethod = @"POST";
    if (self.requestXbody) {
        NSData *jsonData = [self httpBodyWithDictionary:muParam];
        if ([PLVFSignConfig sharedInstance].encryptType == PLVEncryptType_SM2) {
            [request setValue:@"2" forHTTPHeaderField:@"x-e-type"];
        } else {
            [request setValue:@"1" forHTTPHeaderField:@"x-e-type"];
        }
        [request setHTTPBody:jsonData];
    } else {
        NSString *parameters = [PLVFNetworkUtil paramStr:muParam];
        [request setHTTPBody:[parameters dataUsingEncoding:NSUTF8StringEncoding]];
    }
    
    return request;
}

#pragma mark - Sign Generater

/// 点播的网络请求签名
- (NSString *)vodSignWithUserId:(NSString *)userId timeStamp:(NSString *)timeStamp {
    NSString *sign = [NSString stringWithFormat:@"polyv_sdk_api_innorltype%zdtimestamp%@userId%@polyv_sdk_api_innor", self.ltype, timeStamp, userId];
    NSString *md5Sign = [PLVDataUtil md5HexDigest:sign];
    return [md5Sign uppercaseString];
}

/// 直播的网络请求签名
- (NSString *)liveSignWithChannelId:(NSString *)channelId timeStamp:(NSString *)timeStamp {
    NSString *sign = [NSString stringWithFormat:@"polyv_sdk_api_innorchannelId%@ltype%zdtimestamp%@polyv_sdk_api_innor", channelId, self.ltype, timeStamp];
    NSString *md5Sign = [PLVDataUtil md5HexDigest:sign];
    return [md5Sign uppercaseString];
}

@end
