//
//  PLVFQosLogReporter.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/24.
//  Copyright © 2019 PLV. All rights reserved.
//

#define PLVFLOGREPORTER_PROTECTED_ACCESS

#import "PLVFQosLogReporter.h"
#import "PLVFLogReporterInternal.h"
#import "PLVFJsonConverter.h"
#import "PLVFdUtil.h"
#import "PLVFQosLogModel.h"

@interface PLVFQosLogReporter ()

/// 日志发送http地址域名
@property (nonatomic, strong) NSString * _Nonnull baseUrl;
/// 日志发送http路径
@property (nonatomic, strong) NSString * _Nonnull path;

@end

@implementation PLVFQosLogReporter

#pragma mark - Getter & Setter

- (void)setProductType:(PLVFLogProductType)productType {
    [super setProductType:productType];
    
    if (productType == PLVProductTypeVod) {
        self.baseUrl = @"https://prtas.videocc.net";
        self.path = @"/qos";
    } else if (productType == PLVProductTypeLive) {
        self.baseUrl = @"https://rtas.videocc.net";
        self.path = @"/qos";
    }
}

#pragma mark - Real Time Log Override

/// 将实时日志的数据模型转换为网络请求
- (NSMutableURLRequest *)getLogRequestWithModel:(PLVFQosLogModel *)model {
    if (model == nil) {
        return nil;
    }
    
    PLVFQosLogModel *logModel = (PLVFQosLogModel *)model;
    if (![logModel isKindOfClass:[PLVFQosLogModel class]]) {
        return nil;
    }
    
    if (model.pid == nil || model.uid == nil || model.type == nil) {
        return nil;
    }
    
    if (self.productType != PLVProductTypeVod && self.productType != PLVProductTypeLive) {
        return nil;
    }
    
    if (self.productType == PLVProductTypeLive && model.cid == nil) {
        return nil;
    }
    
    if (!self.baseUrl || !self.path) {
        return nil;
    }
    
    NSString *paramString = [PLVFJsonConverter paramJsonFromModel:model];
    NSString *urlString = [NSString stringWithFormat:@"%@%@?%@", self.baseUrl, self.path, paramString];
    if (self.requestXbody &&
        [PLVFSignConfig sharedInstance].encryptType == PLVEncryptType_SM2) {
        urlString = [NSString stringWithFormat:@"%@&eType=2", urlString];
    }
    
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] initWithURL:[NSURL URLWithString:urlString]];
    request.HTTPMethod = @"GET";
    return request;
}

@end
