//
//  PLVFTrackLogModel.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/5/16.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVFTrackLogModel.h"
#import <objc/runtime.h>

@implementation PLVFTrackLogModel

@end

@implementation PLVETrackDataParamModel

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        unsigned int outCount;

        //获取类中的所有成员属性
        objc_property_t *arrPropertys = class_copyPropertyList([self class], &outCount);

        for (NSInteger i = 0; i < outCount; i ++) {
            objc_property_t property = arrPropertys[i];

            //获取属性名字符串
            //model中的属性名
            NSString *propertyName = [NSString stringWithUTF8String:property_getName(property)];
            id propertyValue = dict[propertyName];

            if (propertyValue != nil) {
                [self setValue:propertyValue forKey:propertyName];
            }
        }

        free(arrPropertys);
    }
    return self;
}

@end
