//
//  PLVFViewLogModel.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/23.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFViewLogModel.h"
#import "PLVDataUtil.h"

@implementation PLVFViewLogModel

- (void)setSid:(NSString *)sid {
    if (sid) {
        _sid = [PLVDataUtil safeUrlBase64Encode:sid];
    }
}

- (void)setViewerAvatar:(NSString *)viewerAvatar {
    if (viewerAvatar) {
        _viewerAvatar = [PLVDataUtil safeUrlBase64Encode:viewerAvatar];
    }
}

- (void)setHref:(NSString *)href {
    if (href) {
        _href = [PLVDataUtil safeUrlBase64Encode:href];
    }
}

- (void)setSession_id:(NSString *)session_id {
    if (session_id) {
        _session_id = [PLVDataUtil safeUrlBase64Encode:session_id];
    }
}

- (void)setParam3:(NSString *)param3 {
    if (param3) {
        _param3 = [PLVDataUtil safeUrlBase64Encode:param3];
    }
}

- (void)setKey1:(NSString *)key1 {
    if (key1) {
        _key1 = [PLVDataUtil safeUrlBase64Encode:key1];
    }
}

- (void)setKey2:(NSString *)key2 {
    if (key2) {
        _key2 = [PLVDataUtil safeUrlBase64Encode:key2];
    }
}

- (void)setKey3:(NSString *)key3 {
    if (key3) {
        _key3 = [PLVDataUtil safeUrlBase64Encode:key3];
    }
}

- (void)setUted:(NSString *)uted {
    if (uted) {
        _uted = [PLVDataUtil safeUrlBase64Encode:uted];
    }
}

@end
