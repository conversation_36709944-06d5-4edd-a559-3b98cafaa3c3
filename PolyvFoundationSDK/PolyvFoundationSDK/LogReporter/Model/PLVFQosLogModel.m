//
//  PLVFQosLogModel.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/24.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFQosLogModel.h"

@implementation PLVFQosLogModel

- (void)setParam3:(NSString *)param3 {
    if (param3) {
        _param3 = [PLVDataUtil safeUrlBase64Encode:param3];
    }
}

- (void)setUri:(NSString *)uri {
    if (uri) {
        _uri = [PLVDataUtil safeUrlBase64Encode:uri];
    }
}

- (void)setStatus:(NSString *)status {
    if (status) {
        _status = [PLVDataUtil safeUrlBase64Encode:status];
    }
}

- (void)setErrorcode:(NSString *)errorcode {
    if (errorcode) {
        _errorcode = [PLVDataUtil safeUrlBase64Encode:errorcode];
    }
}

- (void)setErrormsg:(NSString *)errormsg {
    if (errormsg) {
        _errormsg = [PLVDataUtil safeUrlBase64Encode:errormsg];
    }
}

- (void)setUrl:(NSString *)url {
    if (url) {
        _url = [PLVDataUtil safeUrlBase64Encode:url];
    }
}

- (void)setRequest_uri:(NSString *)request_uri {
    if (request_uri) {
        _request_uri = [PLVDataUtil safeUrlBase64Encode:request_uri];
    }
}

@end
