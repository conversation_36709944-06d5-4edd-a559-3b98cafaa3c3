//
//  PLVFELogModel.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/11/28.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFELogModel.h"
#import <objc/runtime.h>

@implementation PLVFELogModel

@end

@implementation PLVELogLogModel

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        unsigned int outCount;

        //获取类中的所有成员属性
        objc_property_t *arrPropertys = class_copyPropertyList([self class], &outCount);

        for (NSInteger i = 0; i < outCount; i ++) {
            objc_property_t property = arrPropertys[i];

            //获取属性名字符串
            //model中的属性名
            NSString *propertyName = [NSString stringWithUTF8String:property_getName(property)];
            id propertyValue = dict[propertyName];

            if (propertyValue != nil) {
                if ([propertyName isEqualToString:@"logFile"]) {
                    PLVELogLogFileModel *logFile = [[PLVELogLogFileModel alloc] initWithDictionary:propertyValue];
                    [self setValue:logFile forKey:propertyName];
                } else {
                    [self setValue:propertyValue forKey:propertyName];
                }
            }
        }

        free(arrPropertys);
    }
    return self;
}

- (void)setVersion2:(NSString *)version2 {
    if (version2) {
        _version2 = [version2 stringByReplacingOccurrencesOfString:@"+" withString:@"%2B"];
    }
}

@end

@implementation PLVELogLogFileModel

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        unsigned int outCount;

        //获取类中的所有成员属性
        objc_property_t *arrPropertys = class_copyPropertyList([self class], &outCount);

        for (NSInteger i = 0; i < outCount; i ++) {
            objc_property_t property = arrPropertys[i];

            //获取属性名字符串
            //model中的属性名
            NSString *propertyName = [NSString stringWithUTF8String:property_getName(property)];
            id propertyValue = dict[propertyName];

            if (propertyValue != nil) {
                if ([propertyName isEqualToString:@"playerParam"] && [propertyValue isKindOfClass:[NSArray class]]) {
                    NSMutableArray *muArray = [[NSMutableArray alloc] init];
                    for (NSDictionary *dict in propertyValue) {
                        PLVELogLogFilePlayerPramaModel *playerPrama = [[PLVELogLogFilePlayerPramaModel alloc] initWithDictionary:dict];
                        [muArray addObject:playerPrama];
                    }
                    [self setValue:[muArray copy] forKey:propertyName];
                } else {
                    [self setValue:propertyValue forKey:propertyName];
                }
            }
        }

        free(arrPropertys);
    }
    return self;
}

@end

@implementation PLVELogLogFilePlayerPramaModel

- (instancetype)initWithDictionary:(NSDictionary *)dict {
    self = [super init];
    if (self) {
        unsigned int outCount;

        //获取类中的所有成员属性
        objc_property_t *arrPropertys = class_copyPropertyList([self class], &outCount);

        for (NSInteger i = 0; i < outCount; i ++) {
            objc_property_t property = arrPropertys[i];

            //获取属性名字符串
            //model中的属性名
            NSString *propertyName = [NSString stringWithUTF8String:property_getName(property)];
            id propertyValue = dict[propertyName];

            if (propertyValue != nil) {
                [self setValue:propertyValue forKey:propertyName];
            }
        }

        free(arrPropertys);
    }
    return self;
}

@end
