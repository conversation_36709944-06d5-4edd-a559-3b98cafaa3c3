//
//  PLVFJsonConverter.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/11/29.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFJsonConverter.h"
#import <objc/runtime.h>

@implementation PLVFJsonConverter

#pragma mark - Public

+ (NSString *)jsonStringFromModelArray:(NSArray *)array {
    NSMutableString *muString = [[NSMutableString alloc] init];
    for (NSObject *object in array) {
        NSString *jsonString = [self jsonStringFromModel:object];
        [muString appendString:jsonString];
        [muString appendString:@","];
    }
    
    NSString *resultString = @"";
    if (muString.length > 0) {
        resultString = [muString substringToIndex:muString.length - 1];
    }
    return [NSString stringWithFormat:@"[%@]", resultString];
}

+ (NSString *)jsonStringFromModel:(NSObject *)object {
    NSDictionary *dict = [self dicFromObject:object];
    
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dict options:NSJSONWritingPrettyPrinted error:nil];
    if (jsonData){
        NSString *jsonStr = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        return jsonStr;
    }
    
    return nil;
}

+ (NSString *)paramJsonFromModel:(NSObject *)object {
    NSDictionary *dict = [self dicFromObject:object];
    NSMutableString *muString = [[NSMutableString alloc] init];
    BOOL hasKey = NO;
    for (NSString *key in [dict allKeys]) {
        NSString *string = [NSString stringWithFormat:@"%@=%@&", key, dict[key]];
        [muString appendString:string];
        hasKey = YES;
    }
    NSString *result = nil;
    if (hasKey) {
        result = [muString substringToIndex:muString.length - 1];
    }
    return result;
}

//将 model 转化为字典
+ (NSDictionary *)dicFromObject:(NSObject *)object {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    
    unsigned int count;
    objc_property_t *propertyList = class_copyPropertyList([object class], &count);
    
    for (int i = 0; i < count; i++) {
        objc_property_t property = propertyList[i];
        const char *cName = property_getName(property);
        NSString *name = [NSString stringWithUTF8String:cName];
        NSObject *value = [object valueForKey:name];// valueForKey 返回的数字和字符串都是对象
        
        if ([value isKindOfClass:[NSString class]] || [value isKindOfClass:[NSNumber class]]) { // string , bool, int ,NSinteger
            [dic setObject:value forKey:name];
        } else if ([value isKindOfClass:[NSArray class]] || [value isKindOfClass:[NSDictionary class]]) { // 数组或字典
            [dic setObject:[self arrayOrDicWithObject:(NSArray*)value] forKey:name];
        } else if (value == nil || value == [NSNull null]) {
            continue;
        } else { //model
            [dic setObject:[self dicFromObject:value] forKey:name];
        }
    }
    
    free(propertyList);
    return [dic copy];
}

//将可能存在 model 数组转化为普通数组
+ (id)arrayOrDicWithObject:(id)origin {
    if ([origin isKindOfClass:[NSArray class]]) { //数组
        NSMutableArray *array = [NSMutableArray array];
        for (NSObject *object in origin) {
            if ([object isKindOfClass:[NSString class]] || [object isKindOfClass:[NSNumber class]]) { // string , bool, int ,NSinteger
                [array addObject:object];
            } else if ([object isKindOfClass:[NSArray class]] || [object isKindOfClass:[NSDictionary class]]) { // 数组或字典
                [array addObject:[self arrayOrDicWithObject:(NSArray *)object]];
            } else { //model
                [array addObject:[self dicFromObject:object]];
            }
        }
        return [array copy];
        
    } else if ([origin isKindOfClass:[NSDictionary class]]) { //字典
        NSDictionary *originDic = (NSDictionary *)origin;
        NSMutableDictionary *dic = [NSMutableDictionary dictionary];
        for (NSString *key in originDic.allKeys) {
            id object = [originDic objectForKey:key];
            
            if ([object isKindOfClass:[NSString class]] || [object isKindOfClass:[NSNumber class]]) { // string , bool, int ,NSinteger
                [dic setObject:object forKey:key];
            } else if ([object isKindOfClass:[NSArray class]] || [object isKindOfClass:[NSDictionary class]]) { // 数组或字典
                [dic setObject:[self arrayOrDicWithObject:object] forKey:key];
            } else { // model
                [dic setObject:[self dicFromObject:object] forKey:key];
            }
        }
        return [dic copy];
    }
    
    return [NSNull null];
}

@end
