//
//  PLVFJsonConverter.h
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/11/29.
//  Copyright © 2019 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/*
 数据模型与json字符串转换工具类
 */
@interface PLVFJsonConverter : NSObject

/// 从模型数组中获取json字符串
+ (NSString *)jsonStringFromModelArray:(NSArray *)array;

/// 从模型中获取 json 字符串
+ (NSString *)jsonStringFromModel:(NSObject *)object;

/// 从模型中获取 key1=value1&key2=value2&key3=value3 字符串
+ (NSString *)paramJsonFromModel:(NSObject *)object;

//将 model 转化为字典
+ (NSDictionary *)dicFromObject:(NSObject *)object;

//将可能存在 model 数组转化为普通数组
+ (id)arrayOrDicWithObject:(id)origin;

@end

NS_ASSUME_NONNULL_END
