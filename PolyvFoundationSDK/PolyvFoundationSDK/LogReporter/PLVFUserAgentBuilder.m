//
//  PLVFUserAgentBuilder.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/11/29.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFUserAgentBuilder.h"
#import "PLVFLogan.h"
#include <sys/utsname.h>

@interface PLVFUserAgentBuilder ()

@property (nonatomic, copy) NSString *sdkVersion;

@property (nonatomic, copy) NSString *deviceType;

@property (nonatomic, copy) NSString *framework;

@property (nonatomic, copy) NSString *project;

/// 设备类型ID：phone-1、pad-2、tv-3
@property (nonatomic, copy) NSString *deviceTypeID;

/// 技术框架平台ID：原生-01、React Native-02、APICloud-03
@property (nonatomic, copy) NSString *frameworkID;

/// 产品数字标识：点播-01、直播-02、旧的推流-03、云课堂-04、短视频-05、手机开播-06、iPad开播-07、多场景-08
@property (nonatomic, copy) NSString *projectID;


@end

@implementation PLVFUserAgentBuilder

//eg. Darwin/16.3.0
static NSString * DarwinVersion(void) {
    struct utsname u;
    (void) uname(&u);
    return [NSString stringWithFormat:@"Darwin/%@", [NSString stringWithUTF8String:u.release]];
}

//eg. CFNetwork/808.3
static NSString * CFNetworkVersion(void) {
    return [NSString stringWithFormat:@"CFNetwork/%@", [NSBundle bundleWithIdentifier:@"com.apple.CFNetwork"].infoDictionary[@"CFBundleShortVersionString"]];
}

//eg. iOS/10_1
static NSString* deviceVersion() {
    NSString *systemName = [UIDevice currentDevice].systemName;
    NSString *systemVersion = [UIDevice currentDevice].systemVersion;
    return [NSString stringWithFormat:@"%@/%@", systemName, systemVersion];
}

//eg. iPhone5,2
static NSString* deviceName() {
    struct utsname systemInfo;
    uname(&systemInfo);
    return [NSString stringWithUTF8String:systemInfo.machine];
}

/*
//eg. MyApp/1
static NSString* appNameAndVersion() {
    NSString* appName = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleName"];
    NSString* appVersion = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"];
    NSString* buildVersion = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleVersion"];
    return [NSString stringWithFormat:@"%@/%@(%@)", appName, appVersion, buildVersion];
}
*/

#pragma mark - Life Cycle

- (instancetype)init {
    self = [super init];
    if (self) {
        if ([[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPhone) {
            _deviceType = @"phone";
            _deviceTypeID = @"1";
        } else if ([[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPad) {
            _deviceType = @"pad";
            _deviceTypeID = @"2";
        } else {
            _deviceType = [UIDevice currentDevice].model;
            _deviceTypeID = @"3";
        }
        
        _framework = @"origin";
        _frameworkID = @"01";
        _userAgent = [PLVFUserAgentBuilder getDeviceAllInfo];
    }
    return self;
}

#pragma mark - Public

+ (instancetype)sharedBuilder {
    static PLVFUserAgentBuilder *builder = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        builder = [[PLVFUserAgentBuilder alloc] init];
    });
    return builder;
}

- (void)configWithProductType:(PLVFProductType)productType sdkVersion:(NSString *)sdkVersion {
    if (sdkVersion && [sdkVersion isKindOfClass:[NSString class]] && sdkVersion.length > 0) {
        self.sdkVersion = sdkVersion;
    } else {
        self.sdkVersion = @"unknown";
    }
    
    if (productType <= 0 || productType > 99) {
        self.project = @"unknown";
        self.projectID = @"99";
        return;
    }
    
    self.projectID = [NSString stringWithFormat:@"%02zd", productType];
    switch (productType) {
        case PLVFProductTypeVod:
        {
            _project = @"vod";
            break;
        }
        case PLVFProductTypeLive:
        {
            _project = @"live";
            break;
        }
        case PLVFProductTypeRtmp:
        {
            _project = @"rtmp";
            break;
        }
        case PLVFProductTypeCloudClass:
        {
            _project = @"cloudClass";
            break;
        }
        case PLVFProductTypeShortVideo:
        {
            _project = @"shortVideo";
            break;
        }
        case PLVFProductTypeStreamer:
        {
            _project = @"cloudClassStream";
            break;
        }
        case PLVFProductTypeIPadStreamer:
        {
            _project = @"cloudClassIPadStream";
            break;
        }
        case PLVFProductTypeLiveScene:
        {
            _project = @"liveScene";
            break;
        }
        default:
        {
            _project = @"unknown";
            break;
        }
    }
    NSString *loganSdkVersion = [NSString stringWithFormat:@"polyv-iOS-live-sdk-%@",self.sdkVersion];
    PLVLoganSetSdkVersion(loganSdkVersion);
}

- (void)changeFramework:(PLVFFrameworkType)frameworkType {
    self.frameworkID = [NSString stringWithFormat:@"%02zd", frameworkType];
    
    switch (frameworkType) {
        case PLVFFrameworkOrigin:
        {
            _framework = @"origin";
            break;
        }
        case PLVFFrameworkRN:
        {
            _framework = @"RN";
            break;
        }
        case PLVFFrameworkAPICloud:
        {
            _framework = @"APICloud";
            break;
        }
        default:
            break;
    }
}

- (NSString *)formatVersion {
    // sdk 版本号+发版日期/{平台标识}{设备类型}{技术框架平台}{产品}
    NSString *version = [NSString stringWithFormat:@"%@/02%@%@%@", (self.sdkVersion ?: @"null"), self.deviceTypeID, self.frameworkID, self.projectID];
    return version;
}

+ (NSString *)getDeviceNameAndVersion {
    return [NSString stringWithFormat:@"%@ %@", deviceName(), deviceVersion()];
}

+ (NSString *)getDeviceAllInfo {
    return [NSString stringWithFormat:@"%@ %@ %@ %@", deviceName(), deviceVersion(), CFNetworkVersion(), DarwinVersion()];
}

@end
