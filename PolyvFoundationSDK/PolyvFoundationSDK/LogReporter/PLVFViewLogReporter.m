//
//  PLVFViewLogReporter.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/23.
//  Copyright © 2019 PLV. All rights reserved.
//

#define PLVFLOGREPORTER_PROTECTED_ACCESS

#import "PLVFViewLogReporter.h"
#import "PLVFLogReporterInternal.h"
#import "PLVFJsonConverter.h"
#import "PLVFdUtil.h"
#import "PLVDataUtil.h"
#import "PLVFViewLogModel.h"

@interface PLVFViewLogReporter ()

/// 日志发送http地址域名
@property (nonatomic, strong) NSString * _Nonnull baseUrl;
/// 日志发送http路径
@property (nonatomic, strong) NSString * _Nonnull path;

@end

@implementation PLVFViewLogReporter

#pragma mark - Getter & Setter

- (void)setProductType:(PLVFLogProductType)productType {
    [super setProductType:productType];
    
    if (productType == PLVProductTypeVod) {
        self.baseUrl = @"https://prtas.videocc.net";
        self.path = @"/v2/view";
    } else if (productType == PLVProductTypeLive) {
        self.baseUrl = @"https://rtas.videocc.net";
        self.path = @"/v2/view";
    }
}

#pragma mark - Real Time Log Override

/// 将实时日志的数据模型转换为网络请求
- (NSMutableURLRequest *)getLogRequestWithModel:(PLVFLogModel *)model {
    if (model == nil) {
        return nil;
    }
    
    PLVFViewLogModel *logModel = (PLVFViewLogModel *)model;
    if (![logModel isKindOfClass:[PLVFViewLogModel class]]) {
        return nil;
    }
    
    NSMutableURLRequest *request = nil;
    if (self.productType == PLVProductTypeVod) {
        request = [self convertVodModelToRequest:logModel];
    } else if (self.productType == PLVProductTypeLive) {
        request = [self convertLiveModelToRequest:logModel];
    }
    
    return request;
}

#pragma mark - Request Generater

/// 将点播的ViewLog数据模型转换为网络请求
- (NSMutableURLRequest *)convertVodModelToRequest:(PLVFViewLogModel *)model {
    if (model.vid == nil || model.uid == nil) {
        return nil;
    }
    
    NSString *sign = [self vodSignWithPid:model.pid vid:model.vid flow:model.flow pd:model.pd cts:model.cts];
    if (sign == nil) {
        return nil;
    }
        
    NSString *paramString = [PLVFJsonConverter paramJsonFromModel:model];
    NSString *urlString = [NSString stringWithFormat:@"%@%@?sign=%@&%@", self.baseUrl, self.path, sign, paramString];
    if (self.requestXbody &&
        [PLVFSignConfig sharedInstance].encryptType == PLVEncryptType_SM2) {
        urlString = [NSString stringWithFormat:@"%@&eType=2", urlString];
    }
    
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] initWithURL:[NSURL URLWithString:urlString]];
    request.HTTPMethod = @"GET";
    return request;
}

/// 将直播的ViewLog数据模型转换为网络请求
- (NSMutableURLRequest *)convertLiveModelToRequest:(PLVFViewLogModel *)model {
    if (model.cid == nil || model.uid == nil) {
        return nil;
    }
        
    NSString *ts = [PLVFdUtil curTimeStamp];
    NSString *sign = [self liveSignWithPid:model.pid channelId:model.cid flow:model.flow pd:model.pd];
    if (sign == nil) {
        return nil;
    }
        
    NSString *paramString = [PLVFJsonConverter paramJsonFromModel:model];
    NSString *urlString = [NSString stringWithFormat:@"%@%@?sign=%@&ts=%@&%@", self.baseUrl, self.path, sign, ts, paramString];
    if (self.requestXbody &&
        [PLVFSignConfig sharedInstance].encryptType == PLVEncryptType_SM2) {
        urlString = [NSString stringWithFormat:@"%@&eType=2", urlString];
    }
    
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] initWithURL:[NSURL URLWithString:urlString]];
    request.HTTPMethod = @"GET";
    return request;
}

#pragma mark - Sign Generater

/// 点播的网络请求签名
- (NSString *)vodSignWithPid:(NSString *)pid vid:(NSString *)vid flow:(NSNumber *)flow pd:(NSNumber *)pd cts:(NSString *)cts {
    NSString *sign = [NSString stringWithFormat:@"rtas.net%@%@%@%@%@", pid, vid, flow, pd, cts];
    NSString *md5Sign = [PLVDataUtil md5HexDigest:sign];
    return [md5Sign lowercaseString];
}

/// 直播的网络请求签名
- (NSString *)liveSignWithPid:(NSString *)pid channelId:(NSString *)channelId flow:(NSNumber *)flow pd:(NSNumber *)pd{
    NSString *sign = [NSString stringWithFormat:@"rtas.net%@%@%@%@", pid, channelId, flow, pd];
    NSString *md5Sign = [PLVDataUtil md5HexDigest:sign];
    return [md5Sign lowercaseString];
}

@end
