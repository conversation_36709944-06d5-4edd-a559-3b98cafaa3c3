//
//  PLVFLogReporterInternal.h
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/2.
//  Copyright © 2019 PLV. All rights reserved.
//
// 暴露给子类的私有属性和私有方法
// 限制使用此头文件，防止被别的类误用

#ifdef PLVFLOGREPORTER_PROTECTED_ACCESS

NS_ASSUME_NONNULL_BEGIN

@interface PLVFBaseLogReporter ()

- (void)sendRequest:(NSMutableURLRequest *)request
     successHandler:(void (^ _Nullable)(void))successHandler
     failureHandler:(void(^ _Nullable)(NSURLResponse * _Nullable response, NSError * _Nullable error))failureHandler;

@end

@interface PLVFLogReporter ()

/// 能在账户间进行区分的 ID，目前点播、直播的 identifier 都是 userId，开播、互动学堂的 identifier 是 channelId
@property (nonatomic, copy) NSString * _Nullable identifier;

@end

NS_ASSUME_NONNULL_END

#else
#error Only be included by subclass or category!
#endif
