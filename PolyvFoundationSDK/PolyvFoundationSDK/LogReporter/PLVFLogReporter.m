//
//  PLVFLogReporter.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/11/29.
//  Copyright © 2019 PLV. All rights reserved.
//

#define PLVFLOGREPORTER_PROTECTED_ACCESS

/// 批量日志最小发送阈值
static int kPatchLogNumber = 20;
/// 批量日志一次性最多发送条数
static int kMaxLogNumber = 100;
/// 发送失败日志重发间隔
static double kRetryTimeInterval = 60;

#import "PLVFLogReporter.h"
#import "PLVFLogReporterInternal.h"
#import "PLVFLogModel.h"
#import "PLVFFileUtil.h"
#import "PLVFdUtil.h"

@interface PLVFLogReporter ()

/// 发送失败（实时）日志缓存数组
@property (nonatomic, strong) NSMutableArray<PLVFLogModel *> *failLogs;
/// 操作 failLogs 数组的串行队列
@property (nonatomic, strong) dispatch_queue_t failLogQueue;

/// 批量发送日志缓存数组
@property (nonatomic, strong) NSMutableArray<PLVFLogModel *> *patchLogs;
/// 操作 patchLogs 数组的串行队列
@property (nonatomic, strong) dispatch_queue_t patchLogQueue;

/// 实时日志发送失败重试计时器
@property (nonatomic, strong) NSTimer *realTimeLogTimer;
/// 批量日志发送、等待发送批量日志写入缓存计时器
@property (nonatomic, strong) NSTimer *patchLogTimer;

@end

@implementation PLVFLogReporter

#pragma mark - Life Cycle

- (instancetype)init {
    self = [super init];
    if (self) {
        _failLogs = [[NSMutableArray alloc] init];
        NSString *failQueueLabel = [NSString stringWithFormat:@"net.polyv.failure.%@Queue", self];
        self.failLogQueue = dispatch_queue_create([failQueueLabel UTF8String], DISPATCH_QUEUE_SERIAL);

        _patchLogs = [[NSMutableArray alloc] initWithCapacity:kPatchLogNumber];
        NSString *patchQueueLabel = [NSString stringWithFormat:@"net.polyv.patch.%@Queue", self];
        self.patchLogQueue = dispatch_queue_create([patchQueueLabel UTF8String], DISPATCH_QUEUE_SERIAL);

        [self startRealTimeLogTimer];

        [self addObserver:self forKeyPath:@"identifier" options:NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld context:NULL];
    }
    return self;
}

- (void)dealloc {
    [self invalidRealTimeLogTimer];
    [self invalidPatchLogTimer];
    [self removeObserver:self forKeyPath:@"identifier"];
}

#pragma mark - Public

- (void)reportModel:(PLVFLogModel *)model {
    [self reportModel:model retry:YES];
}

- (void)reportModel:(PLVFLogModel *)model retry:(BOOL)retry {
    if (![model isKindOfClass:[PLVFLogModel class]]) {
        return;
    }
    
    PLVFLogModel *logModel = (PLVFLogModel *)model;
    
    if (logModel.patch) { // 批量发送日志
        [self addPatchLog:logModel];
    } else { // 实时发送日志
        [self addRealtimeLog:logModel retry:retry];
    }
}

#pragma mark - Timer

- (void)startRealTimeLogTimer {
    self.realTimeLogTimer = [NSTimer scheduledTimerWithTimeInterval:kRetryTimeInterval target:self selector:@selector(realTimeLogTimerAction) userInfo:nil repeats:YES];
}

- (void)invalidRealTimeLogTimer {
    [_realTimeLogTimer invalidate];
    _realTimeLogTimer = nil;
}

- (void)startPatchLogTimer {
    self.patchLogTimer = [NSTimer scheduledTimerWithTimeInterval:kRetryTimeInterval target:self selector:@selector(patchLogTimerAction) userInfo:nil repeats:YES];
}

- (void)invalidPatchLogTimer {
    if (_patchLogTimer) {
        [_patchLogTimer invalidate];
        _patchLogTimer = nil;
    }
}

- (void)realTimeLogTimerAction {
    [self retryFailLogs]; //对发送失败的日志进行重试
}

- (void)patchLogTimerAction {
    [self sendPatchLog]; //定时发送批量日志

    if (self.identifier) { // 已登录/还未登出
        [self savePatchLogToFile]; // 将还未发送的批量日志保存到本地沙盒
    }
}

#pragma mark - Login & Logout

/// 通过对 identifier 进行监测，获知日志系统的登录、登出事件
/// 具体的登录、登出接口涉及业务逻辑，由子类定义
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSString *,id> *)change context:(void *)context{
    if ([keyPath isEqualToString:@"identifier"] && object == self) {
        if (self.identifier && self.identifier.length > 0) {
            [self didLogin];
        } else {
            [self didLogout];
        }
    }
}

/// 登录时，读取本地沙盒中与该 identifier 有关的批量日志缓存
- (void)didLogin {
    [self readPatchLogFile];
}

/// 登出时，将patchLogs数组的日志进行批量发送
/// 发送成功时清理缓存，发送失败则再次把内存中的批量日志请求写入缓存
- (void)didLogout {
    if (!self.patchLogs || [self.patchLogs count] == 0) {
        return;
    }
    
    __weak typeof(self) weakSelf = self;
    [self sendPatchLog:self.patchLogs successHandler:^{
        [weakSelf deletePatchLogFile];
    } failureHandler:^(NSURLResponse * _Nullable response, NSError * _Nullable error) {
        [weakSelf savePatchLogToFile];
    }];
}

#pragma mark - Real Time Log

/// 发送实时日志，发送失败默认不再重试
- (void)addRealtimeLog:(PLVFLogModel *)model retry:(BOOL)retry {
    if (retry) {
        __weak typeof(self) weakSelf = self;
        [self sendRealtimeLog:model
               successHandler:nil
               failureHandler:^(NSURLResponse * _Nullable response, NSError * _Nullable error) {
            if (error || response) {
                dispatch_async(self.failLogQueue, ^{
                    [weakSelf.failLogs addObject:model];
                });
            }
        }];
    } else {
        [self sendRealtimeLog:model successHandler:nil failureHandler:nil];
    }
}

/// 对failLogs数组里的实时日志进行重试，重试成功移出failLogs数组
- (void)retryFailLogs {
    __weak typeof(self) weakSelf = self;
    
    NSArray *array = [[NSArray alloc] initWithArray:self.failLogs];
    if (![PLVFdUtil checkArrayUseable:array]) {
        return;
    }
    
    __block BOOL shouldBreak = NO;
    for (PLVFLogModel *model in array) {
        if (shouldBreak) {
            break;
        }
        [self sendRealtimeLog:model successHandler:^{
            dispatch_async(self.failLogQueue, ^{
                [weakSelf.failLogs removeObject:model];
            });
        } failureHandler:^(NSURLResponse * _Nullable response, NSError * _Nullable error) {
            shouldBreak = YES;
        }];
    }
}

/// 发送实时日志
- (void)sendRealtimeLog:(PLVFLogModel *)model
         successHandler:(void (^ _Nullable)(void))successHandler
         failureHandler:(void(^ _Nullable)(NSURLResponse * _Nullable response, NSError * _Nullable error))failureHandler {
    
    NSMutableURLRequest *request = [self getLogRequestWithModel:model];
    [self sendRequest:request successHandler:successHandler failureHandler:failureHandler];
}

#pragma mark Need SubClass Override

/// 将实时日志的数据模型转换为网络请求，由子类进行覆写
- (NSMutableURLRequest *)getLogRequestWithModel:(PLVFLogModel *)model {
    return nil;
}

#pragma mark - Patch Log

/// 定时发送批量日志
- (void)sendPatchLog {
    dispatch_async(self.patchLogQueue, ^{
        NSInteger fireLogNumber = 0;
        if ([self.patchLogs count] > kMaxLogNumber) {
            fireLogNumber = kMaxLogNumber;
        }
            
        if (fireLogNumber > 0) {
            NSArray *fireLogs = [self.patchLogs objectsAtIndexes:[NSIndexSet indexSetWithIndexesInRange:NSMakeRange(0, fireLogNumber)]];
            __weak typeof(self) weakSelf = self;
            [self sendPatchLog:fireLogs successHandler:^{
                dispatch_async(self.patchLogQueue, ^{
                    [weakSelf.patchLogs removeObjectsInArray:fireLogs];
                    if (weakSelf.patchLogs.count > 0 && !weakSelf.patchLogTimer) {
                        NSLog(@"PLVTEST sendPatchLog startPatchLogTimer");
                        [weakSelf startPatchLogTimer];
                    } else {
                        NSLog(@"PLVTEST sendPatchLog invalidPatchLogTimer");
                        [weakSelf invalidPatchLogTimer];
                    }
                });
            } failureHandler:^(NSURLResponse * _Nullable response, NSError * _Nullable error) {
                NSLog(@"PLVTEST sendPatchLog failed");
                [weakSelf invalidPatchLogTimer];
                [weakSelf startPatchLogTimer];
            }];
       }
   });
}

/// 接收到需批量发送日志的数据模型
- (void)addPatchLog:(PLVFLogModel *)model {
     dispatch_async(self.patchLogQueue, ^{
         [self.patchLogs addObject:model];
         if (self.patchLogs.count >= kPatchLogNumber) {
             [self sendPatchLog];
         } else if (!self.patchLogTimer) {
             [self startPatchLogTimer];
         }
    });
}

/// 发送批量日志
- (void)sendPatchLog:(NSArray *)logs
      successHandler:(void (^ _Nullable)(void))successHandler
      failureHandler:(void(^ _Nullable)(NSURLResponse * _Nullable response, NSError * _Nullable error))failureHandler {
    
    NSMutableURLRequest *request = [self getPatchLogRequestWithModels:logs];
    [self sendRequest:request successHandler:successHandler failureHandler:failureHandler];
}

#pragma mark Need SubClass Override

/// 将批量日志的数据模型数组转换为网络请求，由子类进行覆写
- (NSMutableURLRequest *)getPatchLogRequestWithModels:(NSArray *)models {
    return nil;
}

#pragma mark - Patch Log Cache

/// 读取本地存储的批量日志缓存
- (void)readPatchLogFile {
    NSString *filePath = [self cacheFilePath];
    NSString *fileName = [self cacheFileName];
    if (filePath == nil || fileName == nil) {
        return;
    }
    
    NSString *filePathAndName = [NSString stringWithFormat:@"%@/%@/%@", [PLVFFileUtil cachesPath], filePath, fileName];
    NSData *data =[PLVFFileUtil dataAtPath:filePathAndName];
    if (data) {
        NSArray *tempArray = [NSJSONSerialization JSONObjectWithData:data options:0 error:nil];
        NSArray *models = [self convertJsonStringToModels:tempArray];
        if (models && [models count] > 0) {
            dispatch_async(self.patchLogQueue, ^{
                [self.patchLogs removeAllObjects];
                [self.patchLogs addObjectsFromArray:models];
            });
        }
    }
}

/// 将尚未发送的批量日志写入缓存
- (void)savePatchLogToFile {
    if (!self.patchLogs || [self.patchLogs count] == 0) {
        return;
    }
    
    NSString *filePath = [self cacheFilePath];
    NSString *fileName = [self cacheFileName];
    if (filePath == nil || fileName == nil) {
        return;
    }
    
    NSString *jsonString = [self convertModelsToJsonString:self.patchLogs];
    if (jsonString == nil) {
        return;
    }
    
    NSData *data = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    if (data) {
        NSString *filePathAndName = [NSString stringWithFormat:@"%@/%@/%@", [PLVFFileUtil cachesPath], filePath, fileName];
        [PLVFFileUtil writeData:data atPath:filePathAndName];
    }
}

/// 清理批量日志缓存
- (void)deletePatchLogFile {
    NSString *filePath = [self cacheFilePath];
    NSString *fileName = [self cacheFileName];
    NSString *filePathAndName = [NSString stringWithFormat:@"%@/%@/%@", [PLVFFileUtil cachesPath], filePath, fileName];
    [PLVFFileUtil deleteFileAtPath:filePathAndName];
}

#pragma mark Need SubClass Override

/// 缓存文件路径由子类根据业务场景定义
- (NSString *)cacheFilePath {
    return nil;
}

/// 缓存文件名由子类根据业务场景定义
- (NSString *)cacheFileName {
    return nil;
}

/// 将日志的数据模型转换为可直接写入缓存的json字符串
- (NSString *)convertModelsToJsonString:(NSArray *)array {
    return nil;
}

/// 将缓存的json字符串转换为日志的数据模型
- (NSArray *)convertJsonStringToModels:(NSArray *)array {
    return nil;
}

@end

