//
//  PLVFTrackLogReporter.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/5/16.
//  Copyright © 2023 PLV. All rights reserved.
//

#define PLVFLOGREPORTER_PROTECTED_ACCESS

#import "PLVFTrackLogReporter.h"
#import "PLVFLogReporterInternal.h"
#import "PLVFJsonConverter.h"
#import "PLVFdUtil.h"
#import "PLVDataUtil.h"
#import "PLVFTrackLogModel.h"
#import "PLVFUserAgentBuilder.h"

@interface PLVFTrackLogReporter ()

/// 日志发送http地址域名
@property (nonatomic, strong) NSString *baseUrl;
/// 日志发送http路径
@property (nonatomic, strong) NSString *path;
/// ltype 字段：点播 1、直播 50、开播 62、互动学堂 71
@property (nonatomic, assign) NSInteger ltype;

/// 其他属性
@property (nonatomic, copy) NSString * _Nullable channelId;
@property (nonatomic, copy) NSString * _Nullable userId;
@property (nonatomic, copy) NSString * _Nullable sessionId;
@property (nonatomic, copy) NSString * _Nullable viewerId;
@property (nonatomic, copy) NSString * _Nullable viewerName;
@property (nonatomic, copy) NSString * _Nullable role;

@end

@implementation PLVFTrackLogReporter

#pragma mark - [ Life Cycle ]

- (instancetype)init {
    self = [super init];
    if (self) {
        self.baseUrl = @"";
        self.path = @"";
        self.channelId = @"";
        self.userId = @"";
        self.viewerId = @"";
        self.viewerName = @"";
        self.role = @"";
        self.sessionId = @"";
    }
    return self;
}

#pragma mark - [ Override ]

#pragma mark Need SubClass Override

/// 将实时日志的数据模型转换为网络请求，由子类进行覆写
- (NSMutableURLRequest *)getLogRequestWithModel:(PLVFLogModel *)model {
    if (model == nil) {
        return nil;
    }
    
    return [self getPatchLogRequestWithModels:@[model]];
}

/// 将批量日志的数据模型数组转换为网络请求，由子类进行覆写
- (NSMutableURLRequest *)getPatchLogRequestWithModels:(NSArray *)models {
    if ([models count] == 0) {
        return nil;
    }
    
    PLVFTrackLogModel *logModel = [self combineModels:models];
    NSMutableURLRequest *request = [self generateRequestWithLogModel:logModel];
    return request;
}

/// 缓存文件路径由子类根据业务场景定义
- (NSString *)cacheFilePath {
    return [NSString stringWithFormat:@"tracklog/%@", [PLVFUserAgentBuilder sharedBuilder].project];
}

/// 缓存文件名由子类根据业务场景定义
- (NSString *)cacheFileName {
    if (self.userId || self.channelId) {
        return [NSString stringWithFormat:@"log_%@.txt", self.identifier];
    } else {
        return nil;
    }
}

/// 将日志的数据模型的data_param字段转换为可直接写入缓存的json字符串
- (NSString *)convertModelsToJsonString:(NSArray *)array {
    if ([array count] == 0) {
        return nil;
    }
    
    PLVFTrackLogModel *logModel = [self combineModels:array];
    NSString *log = [PLVFJsonConverter jsonStringFromModelArray:logModel.data_param];
    return log;
}

/// 将缓存的json字符串转换为日志数据模型
- (NSArray *)convertJsonStringToModels:(NSArray *)array {
    if (array == nil || [array count] == 0) {
        return nil;
    }
    
    NSMutableArray *muArray = [[NSMutableArray alloc] initWithCapacity:[array count]];
    for (NSDictionary *dict in array) {
        if ([dict isKindOfClass:[NSDictionary class]]) {
            PLVFTrackLogModel *logModel = [self generateTemplateModel];
            PLVETrackDataParamModel *data_paramElement = [[PLVETrackDataParamModel alloc] initWithDictionary:dict];
            logModel.data_paramElement = data_paramElement;
            [muArray addObject:logModel];
        }
    }
    return [muArray copy];
}

#pragma mark Getter & Setter

- (void)setProductType:(PLVFLogProductType)productType {
    [super setProductType:productType];
    
    if (productType == PLVProductTypeLive) {
        self.baseUrl = @"https://rtas.videocc.net";
        self.path = @"/da/track";
        self.ltype = 50;
    }
}

#pragma mark - [ Public Method ]

- (void)registerWithChannelId:(NSString *)channelId userId:(NSString *)userId {
    if (self.productType != PLVProductTypeLive) {
        return;
    }
    
    self.channelId = channelId;
    self.userId = userId;
    
    self.identifier = self.userId;
}

- (void)unregister {
    self.identifier = nil;
}

- (void)setupViewerId:(NSString *)viewerId viewerName:(NSString *)viewerName role:(NSString *)role {
    self.viewerId = [PLVFdUtil checkStringUseable:viewerId] ? viewerId : @"";
    self.viewerName = [PLVFdUtil checkStringUseable:viewerName] ? viewerName : @"";
    self.role = [PLVFdUtil checkStringUseable:role] ? role : @"";
}

- (void)setupSessionId:(NSString *)sessionId {
    self.sessionId = [PLVFdUtil checkStringUseable:sessionId] ? sessionId : @"";
}

/// 生成日志上报与事件无关的数据
/// 还缺PLVFTrackLogModel类的local_ts
/// 还缺PLVETrackDataParamModel类的event_type、event_id、occur_time、spec_attrs
- (PLVFTrackLogModel *)generateTemplateModel {
    PLVFTrackLogModel *logModel = [[PLVFTrackLogModel alloc] init];
    
    logModel.project_name = (self.productType == PLVProductTypeLive) ? @"live" : @"";
    logModel.data_type = @"event";
    logModel.sdk_type = @"ios";
    logModel.device_brand = @"Apple";
    logModel.session_uuid = @"N/A";
    logModel.logon_type = @"N/A";
    logModel.project_id = @"N/A";
    logModel.app_id = @"N/A";
    
    NSDictionary *mainBundleInfo = [[NSBundle mainBundle] infoDictionary];
    logModel.app_name = [mainBundleInfo objectForKey:@"CFBundleDisplayName"];
    logModel.app_version = [mainBundleInfo objectForKey:@"CFBundleShortVersionString"];
    
    UIDevice *device = [UIDevice currentDevice];
    logModel.device_uuid = [device.identifierForVendor UUIDString];
    logModel.device_class = device.model;
    logModel.device_model = device.model;
    logModel.os = device.systemName;
    logModel.os_version = device.systemVersion;
    
    logModel.sdk_version = [PLVFUserAgentBuilder sharedBuilder].sdkVersion;
    logModel.ua = [PLVFUserAgentBuilder sharedBuilder].userAgent;
    logModel.nickname = self.viewerName;
    logModel.logon_id = self.viewerId;
    
    return logModel;
}

- (PLVETrackDataParamModel *)generateDataParamModel {
    PLVETrackDataParamModel *data_paramElement = [[PLVETrackDataParamModel alloc] init];
    data_paramElement.event_cata = @"behavior";
    data_paramElement.event_uuid = [[NSUUID UUID] UUIDString];
    data_paramElement.comm_attrs = @{
        @"userId"       : self.userId ?: @"",
        @"channelId"    : self.channelId ?: @"",
        @"sessionId"    : self.sessionId ?: @"",
        @"role"         : self.role ?: @""
    };
    return data_paramElement;
}

#pragma mark - [ Private Method ]

/// 把数组里面所有 model 的 data_paramElement 字段进行合并，放入最后一条 model 的 data_param 字段
- (PLVFTrackLogModel *)combineModels:(NSArray *)array {
    if ([array count] == 0) {
        return nil;
    }
    
    NSMutableArray *muArray = [[NSMutableArray alloc] init];
    for (PLVFTrackLogModel *logModel in array) {
        if ([logModel isKindOfClass:[PLVFTrackLogModel class]]) {
            if (logModel.data_paramElement) {
                [muArray addObject:logModel.data_paramElement];
            } else {
                [muArray addObjectsFromArray:logModel.data_param];
            }
        }
    }
    PLVFTrackLogModel *logModel = [array lastObject];
    logModel.data_param = [muArray copy];
    logModel.data_paramElement = nil;
    return logModel;
}

/// 将日志的数据模型数组转换为网络请求
- (NSMutableURLRequest *)generateRequestWithLogModel:(PLVFTrackLogModel *)model {
    if (self.productType != PLVProductTypeLive) {
        return nil;
    }
    
    if (![PLVFdUtil checkArrayUseable:model.data_param]) {
        return nil;
    }
    
    
    NSTimeInterval interval = [[NSDate date] timeIntervalSince1970];
    model.local_ts = [NSString stringWithFormat:@"%ld", lround(interval)];
    
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    paramDict[@"local_ts"] = model.local_ts;
    paramDict[@"app_id"] = model.app_id;
    paramDict[@"project_id"] = model.project_id;
    paramDict[@"project_name"] = model.project_name;
    
    if (self.enableSignatureNonce) {
        NSString *signatureNonceString = [[NSUUID UUID] UUIDString];
        paramDict[@"signatureNonce"] = signatureNonceString;
    }
    if (self.enableSha256) {
        paramDict[@"signatureMethod"] = @"SHA256";
    }
    
    NSString *sign = [self createStringWithParamDict:paramDict joinString:@"polyv_da_inner"];
    if (self.enableSha256) {
        sign = [[PLVDataUtil sha256String:sign] uppercaseString];
    } else {
        sign = [[PLVDataUtil md5HexDigest:sign] uppercaseString];
    }
    if (sign && sign.length > 0) {
        paramDict[@"sign"] = sign;
    }
    
    NSString *parameterString = [self paramStr:paramDict];
    NSString *urlString = [NSString stringWithFormat:@"%@%@?%@", self.baseUrl, self.path, parameterString];
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] initWithURL:[NSURL URLWithString:urlString]];
    request.HTTPMethod = @"POST";
    [request setTimeoutInterval:12.0];
    
    NSDictionary *postBody = [PLVFJsonConverter dicFromObject:model];
    NSData *data = [NSJSONSerialization dataWithJSONObject:postBody options:NSJSONWritingPrettyPrinted error:nil];
    if (self.requestXbody) {
        if ([PLVFSignConfig sharedInstance].encryptType == PLVEncryptType_SM2) {
            NSString *xbodyString = [PLVDataUtil jsonStringWithJSONObject:postBody];
            xbodyString = [PLVSM2Util formatEncryptString:xbodyString publicKey:[PLVKeyUtil getApiUtilsPublicKey]];
            data = [xbodyString dataUsingEncoding:NSUTF8StringEncoding];
            [request setValue:@"2" forHTTPHeaderField:@"x-e-type"];
        } else {
            NSString *xbodyString = [[PLVDataUtil AES128EncryptData:data withKey:[PLVKeyUtil getApiUtilsKey] iv:[PLVKeyUtil getApiUtilsIv]] base64EncodedStringWithOptions:0];
            data = [xbodyString dataUsingEncoding:NSUTF8StringEncoding];
            [request setValue:@"1" forHTTPHeaderField:@"x-e-type"];
        }
    }
    [request setHTTPBody:data];

    return request;
}

/// 把字典数据按照key的字母排序拼接成key1value1key2value2的字符串，并前后拼上参数joinString
- (NSString *)createStringWithParamDict:(NSDictionary *)paramsDict joinString:(NSString *)joinString {
    if (![PLVFdUtil checkDictionaryUseable:paramsDict]) {
        return @"";
    }
    
    if (![PLVFdUtil checkStringUseable:joinString]) {
        return @"";
    }
    
    NSArray *keys = [paramsDict allKeys];
    NSArray *sortedArray = [keys sortedArrayUsingComparator:^NSComparisonResult(id obj1, id obj2) {
        return [obj1 compare:obj2 options:NSNumericSearch];
    }];
    
    NSMutableString *signString = [NSMutableString string];
    for (NSString *keyString in sortedArray) {
        id value = paramsDict[keyString];
        if ([PLVFdUtil checkStringUseable:value]) {
            [signString appendFormat:@"%@%@", keyString, (NSString *)value];
        } else if (value && [value isKindOfClass:[NSNumber class]]) {
            NSNumber *valueNumber = (NSNumber *)value;
            [signString appendFormat:@"%@%zd", keyString, [valueNumber integerValue]];
        }
    }
    NSString *sign = [joinString stringByAppendingFormat:@"%@%@",signString, joinString];
    return sign;
}

- (NSString*)paramStr:(NSDictionary *)params {
    NSArray *keys = params.allKeys;
    NSMutableString *paramStr = [NSMutableString string];
    for (NSString *key in keys) {
        [paramStr appendFormat:@"%@=%@&", key, params[key]];
    }
    return [paramStr substringToIndex:paramStr.length - 1];
}

@end
