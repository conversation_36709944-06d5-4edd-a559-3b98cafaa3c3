//
//  PLVFBaseLogReporter.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/11/27.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFBaseLogReporter.h"
#import "PLVFUserAgentBuilder.h"
#import "PLVFLogModel.h"
#import "PLVFdUtil.h"
#import "PLVFConsoleLogger.h"

@interface PLVFBaseLogReporter ()

@property (nonatomic, strong) NSURLSession *session;
@end

@implementation PLVFBaseLogReporter

#pragma mark Life Cycle

- (instancetype)init {
    self = [super init];
    if (self) {
        NSURLSessionConfiguration *configuration = [NSURLSessionConfiguration defaultSessionConfiguration];
        configuration.timeoutIntervalForRequest = 15;
        
        _session = [NSURLSession sessionWithConfiguration:configuration delegate:nil delegateQueue:nil];
    }
    return self;
}

#pragma mark Private

- (void)sendRequest:(NSMutableURLRequest *)request
     successHandler:(void (^)(void))successHandler
     failureHandler:(void(^)(NSURLResponse * _Nullable response, NSError * _Nullable error))failureHandler {
    
    if (request == nil) {
        !failureHandler ?: failureHandler(nil, nil);
        return;
    }
    
    //dispatch_semaphore_t semaphore = dispatch_semaphore_create(0); //创建信号量.
    
    NSString* userAgent = [PLVFUserAgentBuilder sharedBuilder].userAgent;
    [request addValue:userAgent forHTTPHeaderField:@"user-Agent"];
    NSString *httpMethod = request.HTTPMethod;
    NSString *logMessage = [NSString stringWithFormat:@"%@ %@", httpMethod, request.URL.absoluteString];
    if ([httpMethod isEqualToString:@"POST"]) {
        NSString *bodyString = [[NSString alloc] initWithData:request.HTTPBody encoding:NSUTF8StringEncoding];
        PLVF_NORMAL_LOG_DEBUG(@"Network", @"--> %@ %@", logMessage, bodyString);
    } else {
        PLVF_NORMAL_LOG_DEBUG(@"Network", @"--> %@", logMessage);
    }
    NSURLSessionDataTask *task = [self.session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        //dispatch_semaphore_signal(semaphore);   //发送信号
        
        if (error) {
            PLVF_NORMAL_LOG_ERROR(@"Network", @"<-- error:%@ %@", error.localizedDescription, logMessage);
            !failureHandler ?: failureHandler(response, error);
        } else {
            NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
            BOOL success = httpResponse.statusCode == 200;
            if (success) {
                PLVF_NORMAL_LOG_DEBUG(@"Network", @"<-- %ld %@", (long)httpResponse.statusCode, logMessage);
                !successHandler ?: successHandler();
            } else {
                PLVF_NORMAL_LOG_ERROR(@"Network", @"<-- error:%ld %@", (long)httpResponse.statusCode, logMessage);
                !failureHandler ?: failureHandler(response, nil);
            }
        }
    }];
    [task resume];
    
   // dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);  //等待
}

@end
