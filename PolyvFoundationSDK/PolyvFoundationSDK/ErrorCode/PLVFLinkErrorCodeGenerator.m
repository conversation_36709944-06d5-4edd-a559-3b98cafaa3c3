//
//  PLVFLinkErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/11.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFLinkErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFLinkErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulLink;
}

+ (NSInteger)errorCode:(PLVFLinkErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFLinkErrorCode)error {
    NSString *description = @"";
    switch (error) {
        /// 网络请求错误
        case PLVFLinkErrorCodeNotifyStreamFailed_ParameterError:
            description = PLVFDLocalizableString(@"notify stream接口调用错误，参数出错");
            break;
        case PLVFLinkErrorCodeNotifyStreamFailed_DataError:
            description = PLVFDLocalizableString(@"notify stream接口调用错误，数据解析出错");
            break;
        
        /// 获取连麦Token接口
        case PLVFLinkErrorCodeMicAuthFailed_ParameterError:
            description = PLVFDLocalizableString(@"获取连麦Token接口调用错误，参数出错");
            break;
        case PLVFLinkErrorCodeMicAuthFailed_CodeError:
            description = PLVFDLocalizableString(@"获取连麦Token接口调用错误，code非200");
            break;
        case PLVFLinkErrorCodeMicAuthFailed_DataError:
            description = PLVFDLocalizableString(@"获取连麦Token接口调用错误，数据解析出错");
            break;
            
        /// 获取连麦SessionID接口
        case PLVFLinkErrorCodeChannelSessionIDFailed_ParameterError:
            description = PLVFDLocalizableString(@"获取连麦SessionID接口调用错误，参数出错");
            break;
            
        /// 获取推流频道信息接口
        case PLVFLinkErrorCodeTeacherLoginFailed_ParameterError:
            description = PLVFDLocalizableString(@"获取推流频道信息接口调用错误，参数出错");
            break;
        case PLVFLinkErrorCodeTeacherLoginFailed_DataError:
            description = PLVFDLocalizableString(@"获取推流频道信息接口调用错误，数据解析出错");
            break;
            
        /// 更新频道直播状态接口
        case PLVFLinkErrorCodeLivestatusEndFailed_ParameterError:
            description = PLVFDLocalizableString(@"更新频道直播状态调用错误，参数出错");
            break;
        case PLVFLinkErrorCodeLivestatusEndFailed_CodeError:
            description = PLVFDLocalizableString(@"更新频道直播状态调用错误，code非200");
            break;
        case PLVFLinkErrorCodeLivestatusEndFailed_DataError:
            description = PLVFDLocalizableString(@"更新频道直播状态调用错误，数据解析出错");
            break;
            
        /// 混流接口
        case PLVFLinkErrorCodeMixActionFailed_ParameterError:
            description = PLVFDLocalizableString(@"混流配置调用错误，参数出错");
            break;
        case PLVFLinkErrorCodeMixActionFailed_CodeError:
            description = PLVFDLocalizableString(@"混流配置调用错误，code非200");
            break;
        case PLVFLinkErrorCodeMixActionFailed_DataError:
            description = PLVFDLocalizableString(@"混流配置调用错误，数据解析出错");
            break;
            
        /// RTC错误
        case PLVFLinkErrorCodeStreamPublishFailed:
            description = PLVFDLocalizableString(@"推流失败");
            break;
        case PLVFLinkErrorCodeJoinChannelFailed:
            description = PLVFDLocalizableString(@"加入RTC房间失败");
            break;
        case PLVFLinkErrorCodeAddPublishStreamUrlFailed:
            description = PLVFDLocalizableString(@"加入推流地址失败");
            break;
            
        /// 系统错误
        case PLVFLinkErrorCodeUnauthorized:
            description = PLVFDLocalizableString(@"连麦需要获取您的音视频权限，请前往设置");
            break;
        default:
            break;
    }
    return description;
}

@end
