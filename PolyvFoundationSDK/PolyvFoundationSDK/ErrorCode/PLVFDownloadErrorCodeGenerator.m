//
//  PLVFDownloadErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/11.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFDownloadErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFDownloadErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulDownload;
}

+ (NSInteger)errorCode:(PLVFDownloadErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFDownloadErrorCode)error {
    NSString *description = @"";
    switch (error) {
        case PLVFDownloadErrorCodeGetVideoInfo_videoIdError:
            description = PLVFDLocalizableString(@"视频id不正确");
            break;
        case PLVFDownloadErrorCodeGetVideoInfo_videoInfoError:
            description = PLVFDLocalizableString(@"视频信息获取失败");
            break;
        case PLVFDownloadErrorCodeDownloadFile_LocalExist:
            description = PLVFDLocalizableString(@"本地文件已存在");
            break;
        case PLVFDownloadErrorCodeDownloadFile_QueueExist:
            description = PLVFDLocalizableString(@"下载任务已存在");
            break;
        case PLVFDownloadErrorCodeGetVideoInfo_videoUrlError:
            description = PLVFDLocalizableString(@"视频下载链接非法");
            break;
        case PLVFDownloadErrorCodeNetWork_noNetWork:
            description = PLVFDLocalizableString(@"网络超时或无网络");
            break;
        case PLVFDownloadErrorCodeDownloadFile_unZipFailed:
            description = PLVFDLocalizableString(@"文件解压失败");
            break;
        case PLVFDownloadErrorCodeDownloadFile_writeDataFailed:
            description = PLVFDLocalizableString(@"文件写入失败");
            break;
        case PLVFDownloadErrorCodeDownloadFile_notEnoughMemory:
            description = PLVFDLocalizableString(@"存储空间不足");
            break;
        default:
            break;
    }
    return description;
}

@end
