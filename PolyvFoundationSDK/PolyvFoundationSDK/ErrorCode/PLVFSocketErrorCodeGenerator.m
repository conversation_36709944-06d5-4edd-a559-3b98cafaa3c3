//
//  PLVFSocketErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by ftao on 2020/1/2.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVFSocketErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFSocketErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulSocket;
}

+ (NSInteger)errorCode:(PLVFSocketErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFSocketErrorCode)error {
    NSString *description = @"";
    switch (error) {
        /// 登录参数问题
        case PLVFSocketErrorCodeParameterInvalid:
            description = PLVFDLocalizableString(@"socket登录用户id、昵称或头像无效");
            break;
            
        /// 网络请求错误
        case PLVFSocketErrorCodeGetToken_ParameterError:
            description = PLVFDLocalizableString(@"获取Token参数无效");
            break;
        case PLVFSocketErrorCodeGetToken_CodeError:
            description = PLVFDLocalizableString(@"获取Token响应非200");
            break;
        case PLVFSocketErrorCodeGetToken_DataError:
            description = PLVFDLocalizableString(@"获取Token数据解析失败");
            break;
        
        /// socket 连接失败
        case PLVFSocketErrorCodeDisconnect:
            description = PLVFDLocalizableString(@"socket服务器失去连接");
            break;
        case PLVFSocketErrorCodeConnectError:
            description = PLVFDLocalizableString(@"socket连接失败");
            break;
        case PLVFSocketErrorCodeTokenInvail:
            description = PLVFDLocalizableString(@"socket连接token无效或过期");
            break;
        case PLVFSocketErrorCodeReconnect:
            description = PLVFDLocalizableString(@"socket重连中");
            break;
        case PLVFSocketErrorCodeGetTokenRetryError:
            description = PLVFDLocalizableString(@"socket获取token重试失败");
            break;
            
        /// socket 登录失败
        case PLVFSocketErrorCodeLoginError:
            description = PLVFDLocalizableString(@"登录ack后台报错");
            break;
        case PLVFSocketErrorCodeLoginAckDataError:
            description = PLVFDLocalizableString(@"登录ack数据解析异常");
            break;
        case PLVFSocketErrorCodeLoginAckParamError:
            description = PLVFDLocalizableString(@"登录socket传递参数非法");
            break;
        case PLVFSocketErrorCodeLoginAckRoomIdError:
            description = PLVFDLocalizableString(@"登录socket房间id不合法");
            break;
        case PLVFSocketErrorCodeLoginAckNicknameError:
            description = PLVFDLocalizableString(@"登录socket头像昵称错误");
            break;
        case PLVFSocketErrorCodeLoginAckBeKicked:
            description = PLVFDLocalizableString(@"登录socket被踢出房间");
            break;
        case PLVFSocketErrorCodeLoginRefuse:
            description = PLVFDLocalizableString(@"登录socket未被授权");
            break;
        case PLVFSocketErrorCodeLoginRelogin:
            description = PLVFDLocalizableString(@"当前用户已在别处登录");
            break;
            
        default:
            break;
    }
    return description;
}

@end
