//
//  PLVFInitErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/1/15.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVFInitErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFInitErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulInit;
}

+ (NSInteger)errorCode:(PLVFInitErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFInitErrorCode)error {
    NSString *description = @"";
    switch (error) {
    /// 网络请求错误
        case PLVFInitErrorCodeLiveLogin_ParameterError:
            description = PLVFDLocalizableString(@"直播登录校验接口调用错误，参数出错");
            break;
        case PLVFInitErrorCodeLiveLogin_CodeError:
            description = PLVFDLocalizableString(@"直播登录校验接口调用错误，code非200");
            break;
        case PLVFInitErrorCodeLiveLogin_DataError:
            description = PLVFDLocalizableString(@"直播登录校验接口调用错误，数据解析出错");
            break;
        
        case PLVFInitErrorCodeVodLogin_ParameterError:
            description = PLVFDLocalizableString(@"回放登录校验接口调用错误，参数出错");
            break;
        case PLVFInitErrorCodeVodLogin_CodeError:
            description = PLVFDLocalizableString(@"回放登录校验接口调用错误，code非200");
            break;
        case PLVFInitErrorCodeVodLogin_DataError:
            description = PLVFDLocalizableString(@"回放登录校验接口调用错误，数据解析出错");
            break;
        
        case PLVFInitErrorCodeLiveInfo_ParameterError:
            description = PLVFDLocalizableString(@"直播获取“频道类型和是否正在直播”接口调用错误，参数出错");
            break;
        case PLVFInitErrorCodeLiveInfo_CodeError:
            description = PLVFDLocalizableString(@"直播获取“频道类型和是否正在直播”接口调用错误，code非200");
            break;
        case PLVFInitErrorCodeLiveInfo_DataError:
            description = PLVFDLocalizableString(@"直播获取“频道类型和是否正在直播”接口调用错误，数据解析出错");
            break;
        
        case PLVFInitErrorCodeVodInfo_ParameterError:
            description = PLVFDLocalizableString(@"回放获取“频道类型”接口调用错误，参数出错");
            break;
        case PLVFInitErrorCodeVodInfo_CodeError:
            description = PLVFDLocalizableString(@"回放获取“频道类型”接口调用错误，code非200");
            break;
        case PLVFInitErrorCodeVodInfo_DataError:
            description = PLVFDLocalizableString(@"回放获取“频道类型”接口调用错误，数据解析出错");
            break;
            
        case PLVFInitErrorCodeChannelInfo_ParameterError:
            description = PLVFDLocalizableString(@"获取频道菜单列表接口调用错误，参数出错");
            break;
        case PLVFInitErrorCodeChannelInfo_CodeError:
            description = PLVFDLocalizableString(@"获取频道菜单列表接口调用错误，code非200");
            break;
        case PLVFInitErrorCodeChannelInfo_DataError:
            description = PLVFDLocalizableString(@"获取频道菜单列表接口调用错误，数据解析出错");
            break;
            
        case PLVFInitErrorCodeUpdateChannelName_ParameterError:
            description = PLVFDLocalizableString(@"修改频道名称接口调用错误，参数出错");
            break;
        case PLVFInitErrorCodeUpdateChannelName_CodeError:
            description = PLVFDLocalizableString(@"修改频道名称接口调用错误，code非200");
            break;
        case PLVFInitErrorCodeUpdateChannelName_DataError:
            description = PLVFDLocalizableString(@"修改频道名称接口调用错误，数据解析出错");
            break;
            
        default:
            break;
    }
    return description;
}

@end
