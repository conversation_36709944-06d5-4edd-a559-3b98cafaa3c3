//
//  PLVFRtmpErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/11.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFRtmpErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFRtmpErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulRtmp;
}

+ (NSInteger)errorCode:(PLVFRtmpErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFRtmpErrorCode)error {
    NSString *description = @"";
    switch (error) {
        case PLVFRtmpErrorCodeForExample:
            description = PLVFDLocalizableString(@"举个例子");
            break;
        default:
            break;
    }
    return description;
}

@end
