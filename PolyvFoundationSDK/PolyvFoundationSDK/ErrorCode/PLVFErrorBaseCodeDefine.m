//
//  PLVFErrorBaseCodeDefine.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/12.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFErrorBaseCodeDefine.h"

NSInteger PLVFErrorBaseCode(NSInteger modul) {
    NSInteger platformCode = 2 * pow(10, 6);
    NSInteger modulCode = modul * pow(10, 4);
    return platformCode + modulCode;
}

NSInteger PLVFErrorModulCode(NSInteger errorCode) {
    if (errorCode / (NSInteger)pow(10, 6) == 2) {
        NSInteger platformCode = 2 * pow(10, 6);
        return (errorCode - platformCode) / pow(10, 4);
    }
    return -1;
}

NSInteger PLVFErrorModulTopLevelCode(NSInteger errorCode) {
    NSInteger platformCode = 2 * (NSInteger)pow(10, 6);
    NSInteger modulCode = PLVFErrorModulCode(errorCode);
    if (modulCode == -1) {
        return -1;
    }
    return (errorCode - platformCode - modulCode * pow(10, 4)) / pow(10, 2);
}

NSInteger PLVFErrorDetailCode(NSInteger errorCode) {
    NSInteger modulCode = PLVFErrorModulCode(errorCode);
    if (modulCode == -1) {
        return -1;
    }
    return (errorCode - PLVFErrorBaseCode(modulCode));
}

@implementation PLVFErrorBaseCodeDefine

@end
