//
//  PLVFChatErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/11.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFChatErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFChatErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulChat;
}

+ (NSInteger)errorCode:(PLVFChatErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFChatErrorCode)error {
    NSString *description = @"";
    switch (error) {
        /// 网络请求错误
        case PLVFChatErrorCodeHistory_ParameterError:
            description = PLVFDLocalizableString(@"获取历史记录参数无效");
            break;
        case PLVFChatErrorCodeHistory_DataError:
            description = PLVFDLocalizableString(@"获取历史记录数据解析失败");
            break;
        
        case PLVFChatErrorCodeOnlineUsers_ParameterError:
            description = PLVFDLocalizableString(@"获取在线成员列表参数无效");
            break;
        case PLVFChatErrorCodeOnlineUsers_DataError:
            description = PLVFDLocalizableString(@"获取在线成员列表数据解析失败");
            break;
            
        case PLVFChatErrorCodeCloseChatroom_ParameterError:
            description = PLVFDLocalizableString(@"禁言设置参数无效");
            break;
        case PLVFChatErrorCodeCloseChatroom_CodeError:
            description = PLVFDLocalizableString(@"禁言设置响应非200");
            break;
        case PLVFChatErrorCodeCloseChatroom_DataError:
            description = PLVFDLocalizableString(@"禁言设置数据解析失败");
            break;

        case PLVFChatErrorCodeGetSwitch_ParameterError:
            description = PLVFDLocalizableString(@"获取聊天室功能开关参数无效");
            break;
        case PLVFChatErrorCodeGetSwitch_CodeError:
            description = PLVFDLocalizableString(@"获取聊天室功能开关响应非200");
            break;
        case PLVFChatErrorCodeGetSwitch_DataError:
            description = PLVFDLocalizableString(@"获取聊天室功能开关数据解析失败");
            break;

        case PLVFChatErrorCodeImageUploadToken_ParameterError:
            description = PLVFDLocalizableString(@"获取图片上传token参数无效");
            break;
        case PLVFChatErrorCodeImageUploadToken_CodeError:
            description = PLVFDLocalizableString(@"获取图片上传token响应非200");
            break;
        case PLVFChatErrorCodeImageUploadToken_DataError:
            description = PLVFDLocalizableString(@"获取图片上传token数据解析失败");
            break;

        case PLVFChatErrorCodeImageUpload_ParameterError:
            description = PLVFDLocalizableString(@"图片上传参数无效");
            break;
        case PLVFChatErrorCodeImageUpload_Failure:
            description = PLVFDLocalizableString(@"图片上传失败");
            break;
            
        case PLVFChatErrorCodeLike_ParameterError:
            description = PLVFDLocalizableString(@"上报点赞数参数无效");
            break;
        case PLVFChatErrorCodeLike_CodeError:
            description = PLVFDLocalizableString(@"上报点赞数响应非200");
            break;
        case PLVFChatErrorCodeLike_DataError:
            description = PLVFDLocalizableString(@"上报点赞数数据解析失败");
            break;
            
        case PLVFChatErrorCodeEmotionImages_ParameterError:
            description = PLVFDLocalizableString(@"获取图片表情列表参数无效");
            break;
        case PLVFChatErrorCodeEmotionImages_CodeError:
            description = PLVFDLocalizableString(@"获取图片表情列表响应非200");
            break;
        case PLVFChatErrorCodeEmotionImages_DataError:
            description = PLVFDLocalizableString(@"获取图片表情列表数据解析失败");
            break;
            
        case PLVFChatErrorCodeKickedUsers_ParameterError:
            description = PLVFDLocalizableString(@"获取被踢出用户列表参数无效");
            break;
        case PLVFChatErrorCodeKickedUsers_CodeError:
            description = PLVFDLocalizableString(@"获取被踢出用户列表响应非200");
            break;
        case PLVFChatErrorCodeKickedUsers_DataError:
            description = PLVFDLocalizableString(@"获取被踢出用户列表数据解析失败");
            break;
            
        case PLVFChatErrorCodeRemindHistory_ParameterError:
            description = PLVFDLocalizableString(@"获取聊天室提醒消息历史记录参数无效");
            break;
        case PLVFChatErrorCodeRemindHistory_CodeError:
            description = PLVFDLocalizableString(@"获取聊天室提醒消息历史记录响应非200");
            break;
        case PLVFChatErrorCodeRemindHistory_DataError:
            description = PLVFDLocalizableString(@"获取聊天室提醒消息历史记录数据解析失败");
            break;
            
        case PLVFChatErrorCodeNewestRedpack_ParameterError:
            description = PLVFDLocalizableString(@"获取聊天室最新红包参数无效");
            break;
        case PLVFChatErrorCodeNewestRedpack_CodeError:
            description = PLVFDLocalizableString(@"获取聊天室最新红包响应非200");
            break;
        case PLVFChatErrorCodeNewestRedpack_DataError:
            description = PLVFDLocalizableString(@"获取聊天室最新红包数据解析失败");
            break;
            
        case PLVFChatErrorCodeRedpackReceiveCache_ParameterError:
            description = PLVFDLocalizableString(@"判断聊天室观众是否已抢红包参数无效");
            break;
        case PLVFChatErrorCodeRedpackReceiveCache_CodeError:
            description = PLVFDLocalizableString(@"判断聊天室观众是否已抢红包响应非200");
            break;
        case PLVFChatErrorCodeRedpackReceiveCache_DataError:
            description = PLVFDLocalizableString(@"判断聊天室观众是否已抢红包数据解析失败");
            break;
            
        case PLVFChatErrorCodeQuestionHistory_ParameterError:
            description = PLVFDLocalizableString(@"获取聊天室提问消息历史记录参数无效");
            break;
        case PLVFChatErrorCodeQuestionHistory_DataError:
            description = PLVFDLocalizableString(@"获取聊天室提问消息历史记录数据解析失败");
            break;
        case PLVFChatErrorCodeQuestionHistory_GetTokenError:
            description = PLVFDLocalizableString(@"获取聊天室提问消息Token失败");
            break;
            
        /// socket 信令问题
        case PLVFChatErrorCodeNotConnect:
            description = PLVFDLocalizableString(@"socket 未连接");
            break;
        case PLVFChatErrorCodeRelogin:
            description = PLVFDLocalizableString(@"您的账号已在别处登录，即将被退出观看");
            break;
        case PLVFChatErrorCodeUserBeKicked:
            description = PLVFDLocalizableString(@"您未被授权观看本直播");
            break;
        case PLVFChatErrorCodeNotLogin:
            description = PLVFDLocalizableString(@"socket 未登录");
            break;
        case PLVFChatErrorCodeRestrict:
            description = PLVFDLocalizableString(@"超出最大同时在线人数限制");
            break;
            
        /// 发言问题
        case PLVFChatErrorCodeRoomBeClosed:
            description = PLVFDLocalizableString(@"消息发送失败，因为房间已被关闭");
            break;
        case PLVFChatErrorCodeUserBeBaned:
            description = PLVFDLocalizableString(@"消息发送失败，因为您已被禁言");
            break;
        case PLVFChatErrorCodeSendMessageError:
            description = PLVFDLocalizableString(@"消息发送失败");
            break;
            
        /// 发图片问题
        case PLVFChatErrorCodeImageReviewFailure:
            description = PLVFDLocalizableString(@"图片包含违规内容");
            break;
            
        default:
            break;
    }
    return description;
}

@end
