//
//  PLVFPlayErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/11.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFPlayErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFPlayErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulPlay;
}

+ (NSInteger)errorCode:(PLVFPlayErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFPlayErrorCode)error {
    NSString *description = @"";
    switch (error) {
        /// 回放信息 接口
        case PLVFPlayErrorCodeGetVideoInfo_ParameterError:
            description = PLVFDLocalizableString(@"回放信息请求失败，参数错误");
            break;
        case PLVFPlayErrorCodeGetVideoInfo_CodeError:
            description = PLVFDLocalizableString(@"回放信息请求失败，code非200");
            break;
        case PLVFPlayErrorCodeGetVideoInfo_DataError:
            description = PLVFDLocalizableString(@"回放信息请求失败，数据解析出错");
            break;
        case PLVFPlayErrorCodeGetVideoInfo_FileUrlError:
            description = PLVFDLocalizableString(@"回放信息请求失败，Url错误");
            break;
        case PLVFPlayErrorCodeGetVideoInfo_RequestFailed:
            description = PLVFDLocalizableString(@"回放信息请求失败");
            break;
            
        /// 频道信息 接口
        case PLVFPlayErrorCodeGetChannelInfo_ParameterError:
            description = PLVFDLocalizableString(@"频道信息请求失败，参数错误");
            break;
        case PLVFPlayErrorCodeGetChannelInfo_CodeError:
            description = PLVFDLocalizableString(@"频道信息请求失败，code非200");
            break;
        case PLVFPlayErrorCodeGetChannelInfo_DataError:
            description = PLVFDLocalizableString(@"频道信息请求失败，数据解析出错");
            break;
        case PLVFPlayErrorCodeGetChannelInfo_RequestFailed:
            description = PLVFDLocalizableString(@"频道信息请求失败");
            break;
            
        /// 限制信息
        case PLVFPlayErrorCodeChannelRestrict_PlayRestrict:
            description = PLVFDLocalizableString(@"该频道设置了限制条件");
            break;
        case PLVFPlayErrorCodeChannelRestrict_RequestFailed:
            description = PLVFDLocalizableString(@"频道限制信息请求失败");
            break;
            
        /// 网络不佳
        case PLVFPlayErrorCodeNetwork_NotGoodNetwork:
            description = PLVFDLocalizableString(@"网络不佳视频加载缓慢");
            break;
            
        /// 直播流状态信息 接口
        case PLVFPlayErrorCodeGetStreamState_ParameterError:
            description = PLVFDLocalizableString(@"直播流状态信息请求失败，参数错误");
            break;
        case PLVFPlayErrorCodeGetStreamState_CodeError:
            description = PLVFDLocalizableString(@"直播流状态信息请求失败，code非200");
            break;
        case PLVFPlayErrorCodeGetStreamState_DataError:
            description = PLVFDLocalizableString(@"直播流状态信息请求失败，数据解析出错");
            break;
        case PLVFPlayErrorCodeGetStreamState_RequestFailed:
            description = PLVFDLocalizableString(@"直播流状态信息请求失败");
            break;
            
        /// SessionID 接口
        case PLVFPlayErrorCodeGetSessionID_ParameterError:
            description = PLVFDLocalizableString(@"直播SessionID请求失败，参数错误");
            break;
        case PLVFPlayErrorCodeGetSessionID_RequestFailed:
            description = PLVFDLocalizableString(@"直播SessionID请求失败");
            break;
            
        /// 画中画
        case PLVFPlayErrorCodePictureInPicture_OpenError:
            description = PLVFDLocalizableString(@"画中画开启失败");
            break;
            

        default:
            break;
    }
    return description;
}

@end
