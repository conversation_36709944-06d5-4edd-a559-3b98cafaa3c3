//
//  PLVFUploadErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/11.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFUploadErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFUploadErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulUpload;
}

+ (NSInteger)errorCode:(PLVFUploadErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFUploadErrorCode)error {
    NSString *description = @"";
    switch (error) {
        /// 网络请求错误
        case PLVFUploadErrorCodeGetToken_ParameterError:
            description = PLVFDLocalizableString(@"获取Token参数无效");
            break;
        case PLVFUploadErrorCodeGetToken_CodeError:
            description = PLVFDLocalizableString(@"获取Token响应非200");
            break;
        case PLVFUploadErrorCodeGetToken_DataError:
            description = PLVFDLocalizableString(@"获取Token数据解析失败");
            break;
        
        /// 文档上传相关异常
        case PLVFUploadErrorCodeDocumentCopyError:
            description = PLVFDLocalizableString(@"拷贝文档到沙盒失败");
            break;
        case PLVFUploadErrorCodeDocumentUploadingExist:
            description = PLVFDLocalizableString(@"文档上传任务已存在");
            break;
        case PLVFUploadErrorCodeDocumentUploadedExist:
            description = PLVFDLocalizableString(@"文档已存在服务端");
            break;
        case PLVFUploadErrorCodeDocumentOSSTaskError:
            description = PLVFDLocalizableString(@"OSS 上传失败");
            break;
        case PLVFUploadErrorCodeDocumentOSSTokenRefreshError:
            description = PLVFDLocalizableString(@"刷新 OSS STS token 失败");
            break;
        default:
            break;
    }
    return description;
}
@end
