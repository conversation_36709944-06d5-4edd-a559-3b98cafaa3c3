//
//  PLVFInteractionErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/1/13.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVFInteractionErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFInteractionErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulInteraction;
}

+ (NSInteger)errorCode:(PLVFInteractionErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFInteractionErrorCode)error {
    NSString *description = @"";
    switch (error) {
        /// web 相关错误
        case PLVFInteractionErrorCodeWebLoadFail:
            description = PLVFDLocalizableString(@"网页加载失败");
            break;
        case PLVFInteractionErrorCodeJS_ParameterError:
            description = PLVFDLocalizableString(@"js 消息参数传递错误");
            break;
        case PLVFInteractionErrorCodeJS_DataError:
            description = PLVFDLocalizableString(@"js 消息接收数据格式错误");
            break;
            
        /// 网络请求错误
        case PLVFInteractionErrorCodeGiveUpLottery_ParameterError:
            description = PLVFDLocalizableString(@"放弃领奖接口调用错误，参数出错");
            break;
        case PLVFInteractionErrorCodeGiveUpLottery_CodeError:
            description = PLVFDLocalizableString(@"放弃领奖接口调用错误，code非200");
            break;
        case PLVFInteractionErrorCodeGiveUpLottery_DataError:
            description = PLVFDLocalizableString(@"放弃领奖接口调用错误，数据解析出错");
            break;
            
        case PLVFInteractionErrorCodePostLottery_ParameterError:
            description = PLVFDLocalizableString(@"提交中奖信息接口调用错误，参数出错");
            break;
        case PLVFInteractionErrorCodePostLottery_CodeError:
            description = PLVFDLocalizableString(@"提交中奖信息接口调用错误，code非200");
            break;
        case PLVFInteractionErrorCodePostLottery_DataError:
            description = PLVFDLocalizableString(@"提交中奖信息接口调用错误，数据解析出错");
            break;
            
        default:
            break;
    }
    return description;
}

@end
