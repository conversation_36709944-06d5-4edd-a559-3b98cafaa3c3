//
//  PLVFPPTErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/11.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFPPTErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFPPTErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulPPT;
}

+ (NSInteger)errorCode:(PLVFPPTErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFPPTErrorCode)error {
    NSString *description = @"";
    switch (error) {
        case PLVFPPTErrorCodeDocumentList_ParameterError:
            description = PLVFDLocalizableString(@"文档列表请求接口调用参数出错");
            break;
        case PLVFPPTErrorCodeDocumentList_CodeError:
            description = PLVFDLocalizableString(@"文档列表请求接口返回code不等于200");
            break;
        case PLVFPPTErrorCodeDocumentList_DataError:
            description = PLVFDLocalizableString(@"文档列表请求接口返回数据解析异常");
            break;
            
        case PLVFPPTErrorCodeDocumentDelete_ParameterError:
            description = PLVFDLocalizableString(@"删除文档接口调用参数出错");
            break;
        case PLVFPPTErrorCodeDocumentDelete_CodeError:
            description = PLVFDLocalizableString(@"删除文档接口返回code不等于200");
            break;
        case PLVFPPTErrorCodeDocumentDelete_DataError:
            description = PLVFDLocalizableString(@"删除文档接口返回数据解析异常");
            break;
            
        case PLVFPPTErrorCodeStatusGet_ParameterError:
            description = PLVFDLocalizableString(@"获取文档转换状态参数无效");
            break;
        case PLVFPPTErrorCodeStatusGet_CodeError:
            description = PLVFDLocalizableString(@"获取文档转换状态响应非200");
            break;
        case PLVFPPTErrorCodeStatusGet_DataError:
            description = PLVFDLocalizableString(@"获取文档转换状态数据解析失败");
            break;

        case PLVFPPTErrorCodeWebLoadFail:
            description = PLVFDLocalizableString(@"网页加载失败");
            break;
        case PLVFPPTErrorCodeJS_ParameterError:
            description = PLVFDLocalizableString(@"js 消息参数传递错误");
            break;
        case PLVFPPTErrorCodeJS_DataError:
            description = PLVFDLocalizableString(@"js 消息 接收数据格式不对");
            break;
        default:
            break;
    }
    return description;
}

@end
