//
//  PLVFHttpErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/1/13.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVFHttpErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFHttpErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulHttp;
}

+ (NSInteger)errorCode:(PLVFHttpErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFHttpErrorCode)error {
    switch (error) {
        case PLVFHttpErrorCodeAllNetworkFailure:
            return PLVFDLocalizableString(@"网络连接异常");
        default:
            return @"";
    }
}

@end
