//
//  PLVFBeautyErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by junotang on 2022/6/5.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVFBeautyErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFBeautyErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulBeauty;
}

+ (NSInteger)errorCode:(PLVFBeautyErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFBeautyErrorCode)error {
    NSString *description = @"";
    switch (error) {
        /// 网络请求错误
        case PLVFBeautyErrorCodeBeautySetting_ParameterError:
            description = PLVFDLocalizableString(@"美颜用户设置接口调用错误，参数出错");
            break;
        case PLVFBeautyErrorCodeBeautySetting_CodeError:
            description = PLVFDLocalizableString(@"美颜用户设置接口调用错误，code非200");
            break;
        case PLVFBeautyErrorCodeBeautySetting_DataError:
            description = PLVFDLocalizableString(@"美颜用户设置接口调用错误，数据解析出错");
            break;
            
        case PLVFBeautyErrorCodeBeautyResource_ParameterError:
            description = PLVFDLocalizableString(@"美颜资源下载调用错误，参数出错");
            break;
        case PLVFBeautyErrorCodeBeautyResource_CodeError:
            description = PLVFDLocalizableString(@"美颜资源下载调用错误，code非200");
            break;
        case PLVFBeautyErrorCodeBeautyResource_DataError:
            description = PLVFDLocalizableString(@"美颜资源下载调用错误，下载失败");
            break;
        case PLVFBeautyErrorCodeResourceHandle_UnzipError:
            description = PLVFDLocalizableString(@"美颜资源解压失败");
            break;
        case PLVFBeautyErrorCodeBeautySDK_ProcessError:
            description = PLVFDLocalizableString(@"美颜SDK处理失败");
            break;
            
        default:
            break;
    }
    return description;
}

@end
