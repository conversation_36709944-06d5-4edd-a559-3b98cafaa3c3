//
//  PLVFRecordErrorCodeGenerator.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/11.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFRecordErrorCodeGenerator.h"
#import "PLVFDI18NUtil.h"

@implementation PLVFRecordErrorCodeGenerator

+ (PLVFErrorCodeModul)modul {
    return PLVFErrorCodeModulRecord;
}

+ (NSInteger)errorCode:(PLVFRecordErrorCode)error {
    PLVFErrorCodeModul modul = [self modul];
    return PLVFErrorBaseCode(modul) + error;
}

+ (NSString *)errorDescription:(PLVFRecordErrorCode)error {
    NSString *description = @"";
    switch (error) {
        case PLVFRecordErrorCodeForExample:
            description = PLVFDLocalizableString(@"举个例子");
            break;
        default:
            break;
    }
    return description;
}

@end
