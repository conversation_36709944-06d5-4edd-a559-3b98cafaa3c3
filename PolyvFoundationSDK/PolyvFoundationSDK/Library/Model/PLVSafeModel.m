//
//  PLVSafeModel.m
//  XYJJsonSafeConvert
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/7/17.
//  Copyright © 2019 plv. All rights reserved.
//

#import "PLVSafeModel.h"

#import <objc/runtime.h>

@implementation PLVSafeModel

#pragma mark - Public
    
- (instancetype)initWithDictionary:(NSDictionary<NSString *, id> *)dictionary {
    self = [super init];
    BOOL valid = [self validateJSON:dictionary withValidator:[[self class] jsonValidator]];
    if (valid) {
        [self setValuesForKeysWithDictionary:dictionary];
        return self;
    } else {
        return nil;
    }
}

#pragma mark - Private

- (NSString *)readPropertyTypeWithObj:(id)obj propertyName:(NSString *)name {
    NSString * propertyTypeString;

    unsigned int propertyCount;
    objc_property_t * properties = class_copyPropertyList([obj class], &propertyCount);
    for(int i = 0; i < propertyCount; i++){
        objc_property_t property = properties[i];
        const char * propertyName = property_getName(property);
        NSString * propertyNameStr = [NSString stringWithUTF8String:propertyName];
        if ([propertyNameStr isEqualToString:name]) {
            const char * propertyType = property_getAttributes(property);
            propertyTypeString = [NSString stringWithUTF8String:propertyType];
            break;
        }
    }
    free(properties);
    return propertyTypeString;
}

#pragma mark - Override
    
- (void)setValuesForKeysWithDictionary:(NSDictionary<NSString *, id> *)keyedValues {
    [super setValuesForKeysWithDictionary:keyedValues];
}

- (void)setValue:(id)value forKey:(NSString *)key {
    if (sizeof(void*) == 4) {
        NSString * propertyTypeString = [self readPropertyTypeWithObj:self propertyName:key];
        if (propertyTypeString.length >= 2) {
            NSString * type = [propertyTypeString substringWithRange:NSMakeRange(1, 1)];
            if ([type isEqualToString:@"b"] || [type isEqualToString:@"c"]) {
                if ([value isKindOfClass:NSString.class]) {
                    NSString * valueString = (NSString *)value;
                    if ([valueString isEqualToString:@"Y"] || [valueString isEqualToString:@"y"]) {
                        value = @YES;
                    }else{
                        value = @NO;
                    }
                }
            }
        }
        [super setValue:value forKey:key];
    } else if (sizeof(void*) == 8) {
        [super setValue:value forKey:key];
    }
    
    NSDictionary *convertMap = [[self class] jsonConvertor];
    if ([[convertMap allKeys] containsObject:key]) {
        NSString *newKey = convertMap[key];
        [self setValue:value forKey:newKey];
    }
}

// 这个方法不可删除，如果发现未标识的字符 可防止意外崩溃
- (void)setValue:(id)value forUndefinedKey:(NSString *)key {
    //NSLog(@"undefinedKey %@, value %@", key, value);
}
    
#pragma mark - PLVJsonValidatorProtocol
    
+ (NSDictionary *)jsonConvertor {
    return @{};
}

+ (NSDictionary *)jsonValidator {
    return @{};
}
    
- (BOOL)validateJSON:(id)json withValidator:(id)jsonValidator {
    if ([json isKindOfClass:[NSDictionary class]] &&
        [jsonValidator isKindOfClass:[NSDictionary class]]) {
        NSDictionary * dict = json;
        NSDictionary * validator = jsonValidator;
        BOOL result = YES;
        NSEnumerator * enumerator = [validator keyEnumerator];
        NSString * key;
        while ((key = [enumerator nextObject]) != nil) {
            id value = dict[key];
            id format = validator[key];
            if ([value isKindOfClass:[NSDictionary class]]
                || [value isKindOfClass:[NSArray class]]) {
                result = [self validateJSON:value withValidator:format];
                if (!result) {
                    break;
                }
            } else {
                if ([value isKindOfClass:format] == NO &&
                    [value isKindOfClass:[NSNull class]] == NO) {
                    result = NO;
                    break;
                }
            }
        }
        return result;
    } else if ([json isKindOfClass:[NSArray class]] &&
               [jsonValidator isKindOfClass:[NSArray class]]) {
        NSArray * validatorArray = (NSArray *)jsonValidator;
        if (validatorArray.count > 0) {
            NSArray * array = json;
            id validator = jsonValidator[0];
            for (id item in array) {
                BOOL result = [self validateJSON:item withValidator:validator];
                if (!result) {
                    return NO;
                }
            }
        }
        return YES;
    } else if ([json isKindOfClass:jsonValidator]) {
        return YES;
    } else {
        return NO;
    }
}

@end
