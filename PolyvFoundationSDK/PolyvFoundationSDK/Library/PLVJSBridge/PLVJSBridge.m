//
//  PLVJSBridge.m
//  PLVFoundationSDK
//
//  Created by <PERSON><PERSON> on 2020/9/9.
//  Copyright © 2020年 PLV. All rights reserved.
//

#import "PLVJSBridge.h"

#import "PLVFdUtil.h"
#import "PLVFWeakProxy.h"
#import "PLVFConsoleLogger.h"

@interface PLVJSBridge () <WKNavigationDelegate,WKUIDelegate,WKScriptMessageHandler>

@property (nonatomic, assign) BOOL webviewLoadFinish; // webview 是否已加载完成
@property (nonatomic, assign) BOOL webviewLoadFaid;   // webview 是否加载失败

@property (nonatomic, strong) NSPointerArray * jsMsgReceivers;
@property (nonatomic, strong) NSMutableArray <NSString *> * jsFunctions;

@property (nonatomic, strong) WKUserContentController *userContentController;
@property (nonatomic, strong) WKWebView * webView;

@property (nonatomic, strong) UIActivityIndicatorView *activityView;

@property (nonatomic, strong) PLVFWeakProxy * weakProxy;

@end

@implementation PLVJSBridge

#pragma mark - [ Life Cycle ]
- (void)dealloc {
    PLVF_NORMAL_LOG_DEBUG(@"JSBridge",@"%s",__FUNCTION__);
    [self destroy];
}


#pragma mark - [ Public Methods ]
- (void)loadWebView:(NSString *)url inView:(UIView *)view {
    PLVF_NORMAL_LOG_DEBUG(@"JSBridge",@"%s",__FUNCTION__);
    [self loadWebViewInView:view];
    
    if (self.debugMode) {  PLVF_NORMAL_LOG_DEBUG(@"JSBridge",@"PLVJS [Debug] - loadWebView url: %@ ",url); }

    NSURL *h5Url = [NSURL URLWithString:url];
    if (h5Url && [h5Url isKindOfClass:NSURL.class]) {
        NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:h5Url cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:20.0];
        [self.webView loadRequest:request];
    }else{
        PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS - loadWebView failed, url illegal : %@",url);
    }
}

- (void)loadHTMLString:(NSString *)htmlString baseURL:(NSURL *)baseURL inView:(UIView *)view {
    [self loadWebViewInView:view];
    
    if (self.debugMode) {  PLVF_NORMAL_LOG_DEBUG(@"JSBridge",@"PLVJS [Debug] - loadHTMLString htmlString: %@ baseURL: %@",htmlString,baseURL); }
    
    if ([PLVFdUtil checkStringUseable:htmlString] && baseURL && [baseURL isKindOfClass:NSURL.class]) {
        [self.webView loadHTMLString:htmlString baseURL:baseURL];
    }else{
        PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS - loadHTMLString failed, param illegal, htmlString: %@ baseURL: %@",htmlString,baseURL);
    }
}

- (void)loadFileURL:(NSURL *)URL allowingReadAccessToURL:(NSURL *)readAccessURL inView:(UIView *)view API_AVAILABLE(ios(9.0)){
    [self loadWebViewInView:view];
    
    if (self.debugMode) {  PLVF_NORMAL_LOG_DEBUG(@"JSBridge",@"PLVJS [Debug] - loadFileURL URL: %@ readAccessURL: %@",URL,readAccessURL); }
    
    if (URL && [URL isKindOfClass:NSURL.class] &&
        readAccessURL && [readAccessURL isKindOfClass:NSURL.class]) {
        [self.webView loadFileURL:URL allowingReadAccessToURL:readAccessURL];
    }else{
        PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS - loadFileURL failed, param illegal, URL: %@ readAccessURL: %@",URL,readAccessURL);
    }
}

- (void)call:(NSString *)jsFunction params:(NSArray *)params {
    if (![PLVFdUtil checkStringUseable:jsFunction]) {
        PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS - call failed, jsFunction illegal : %@",jsFunction);
        return;
    }
    
    __weak typeof(self) weakSelf = self;
    if (!self.webviewLoadFinish) {
        if (self.debugMode) { PLVF_NORMAL_LOG_DEBUG(@"JSBridge",@"PLVJS [Debug] - webview load not finish, wait 1 sec"); }

        dispatch_time_t time = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC));
        dispatch_after(time, dispatch_get_global_queue(0, 0), ^{
            [self call:jsFunction params:params];
        });
    }else{
        NSString *jsonStr = @"()";
        if (params) {
            NSError *jsonErr;
            NSData *arrayData = [NSJSONSerialization dataWithJSONObject:params options:NSJSONWritingPrettyPrinted error:&jsonErr];
            if (jsonErr) {
                PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS - JSON处理失败 %@",jsonErr);
            }else{
                jsonStr = [[NSString alloc] initWithData:arrayData encoding:NSUTF8StringEncoding];
                if ([PLVFdUtil checkStringUseable:jsonStr]) {
                    jsonStr = [jsonStr stringByReplacingOccurrencesOfString:@"[\n  " withString:@"(\n  "];
                    jsonStr = [jsonStr stringByReplacingOccurrencesOfString:@"\n]" withString:@"\n)"];
                    
                    jsonStr = [jsonStr substringFromIndex:1];
                    jsonStr = [jsonStr substringToIndex:(jsonStr.length - 1)];
                    jsonStr = [NSString stringWithFormat:@"(%@)",jsonStr];
                }else{
                    PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS - JSON处理编码失败 %@",arrayData);
                }
            }
        }
        NSString *jsStr = [NSString stringWithFormat:@"%@%@",jsFunction,jsonStr];
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable data, NSError * _Nullable error) {
                if (weakSelf.debugMode) {
                    PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS [Debug] - OC调用%@，error:%@",jsStr,error.description);
                }else{
                    if (error) {
                        PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS - OC调用%@失败，%@",jsFunction,error.description);
                    }
                }
            }];
        });
    }
}

- (void)addJsFunctionsReceiver:(id)receiver {
    if (receiver) {
        [self.jsMsgReceivers addPointer:(__bridge void * _Nullable)(receiver)];
    }else{
        PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS - addJsMessageReceiver failed, jsMsgReceiver is nil : %@",receiver);
    }
}

- (void)addObserveJsFunctions:(NSArray <NSString *> *)jsFunctions {
    if ([PLVFdUtil checkArrayUseable:jsFunctions]) {
        for (NSString * jsFunction in jsFunctions) {
            if ([PLVFdUtil checkStringUseable:jsFunction] && ![self.jsFunctions containsObject:jsFunction]) {
                if (self.debugMode) { PLVF_NORMAL_LOG_DEBUG(@"JSBridge",@"PLVJS [Debug] - addObserveJsFunctions jsFunction:%@",jsFunction); }
                [self.jsFunctions addObject:jsFunction];
                if (_webView) {
                    [self.userContentController addScriptMessageHandler:self.weakProxy name:jsFunction];
                }
            }
        }
    }else{
        PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS - addObserveJsFunctions failed , jsFunctions illegal %@",jsFunctions);
    }
}


#pragma mark - [ Private Methods ]
- (void)loadWebViewInView:(UIView *)view {
    // 销毁资源
    [self destroy];
    
    if (![view isKindOfClass:UIView.class]) {
        PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS - loadWebViewInView failed, is not UIView:%@",view);
        return;
    }

    // 创建 webview
    self.userContentController = [[WKUserContentController alloc] init];
    for (NSString * funcString in self.jsFunctions) {
        if ([PLVFdUtil checkStringUseable:funcString]) {
            [self.userContentController addScriptMessageHandler:self.weakProxy name:funcString];
        }
    }
    
    WKWebViewConfiguration * config = [[WKWebViewConfiguration alloc] init];
    if (PLV_iOSVERSION_Available_10_0) { config.dataDetectorTypes = WKDataDetectorTypeNone; }
#ifdef __IPHONE_13_0
    if (PLV_iOSVERSION_Available_13_0) { config.defaultWebpagePreferences.preferredContentMode = WKContentModeMobile; }
#endif
    config.userContentController = self.userContentController;

    self.webView = [[WKWebView alloc] initWithFrame:CGRectZero configuration:config];
    self.webView.opaque = NO;
    self.webView.UIDelegate = self;
    self.webView.navigationDelegate = self;
    [view addSubview:self.webView];
    
    // 创建 加载指示器
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvJSBridge:webviewLodingShow:)]) {
        [self.delegate plvJSBridge:self webviewLodingShow:YES];
    }
    if (!self.customActivityIndicator) {
        self.activityView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhiteLarge];
        self.activityView.color = [UIColor colorWithRed:0.9 green:0.9 blue:0.9 alpha:1.0];
        [view addSubview:self.activityView];
        [self.activityView startAnimating];
        
        UIActivityIndicatorView * activityView = self.activityView;
        NSDictionary * views = NSDictionaryOfVariableBindings(activityView, view);
        [view addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"H:|-[activityView]-|" options:NSLayoutFormatAlignAllCenterY metrics:nil views:views]]; /// 设置水平居中
        [view addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"V:|-[activityView]-|" options:NSLayoutFormatAlignAllCenterX metrics:nil views:views]]; /// 设置垂直居中
        [self.activityView setTranslatesAutoresizingMaskIntoConstraints:NO];
    }
}

- (void)destroy{
    if (_activityView) {
        [_activityView stopAnimating];
        [_activityView removeFromSuperview];
        _activityView = nil;
    }

    self.webviewLoadFinish = NO;
    self.webviewLoadFaid = NO;
    if (_webView) {
        [_webView stopLoading];
        [_webView removeFromSuperview];
        _webView = nil;
    }
}

#pragma mark Getter
- (NSPointerArray *)jsMsgReceivers{
    if (!_jsMsgReceivers) {
        _jsMsgReceivers = [NSPointerArray weakObjectsPointerArray];
    }
    return _jsMsgReceivers;;
}

- (NSMutableArray <NSString *> *)jsFunctions{
    if (!_jsFunctions) {
        _jsFunctions = [[NSMutableArray <NSString *>alloc] init];
    }
    return _jsFunctions;
}

- (PLVFWeakProxy *)weakProxy{
    if (!_weakProxy) {
        _weakProxy = [PLVFWeakProxy proxyWithTarget:self];
    }
    return _weakProxy;
}

#pragma mark - [ Delegate ]
#pragma mark WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message{
    if (self.debugMode) { PLVF_NORMAL_LOG_DEBUG(@"JSBridge",@"PLVJS [Debug] - didReceiveScriptMessage \n name:%@ \n body:%@",message.name,message.body); }
    
    NSString * method = [message.name stringByAppendingString:@":"];
    SEL selector = NSSelectorFromString(method);
    
    for (id receiver in self.jsMsgReceivers) {
        if ([receiver respondsToSelector:selector]) {
            [receiver performSelector:selector withObject:message.body];
        }
    }
}

#pragma mark WKNavigationDelegate
- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation{
    // 更新 加载状态
    self.webviewLoadFaid = NO;
    self.webviewLoadFinish = YES;
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvJSBridgeWebviewDidFinishLoad:)]) {
        [self.delegate plvJSBridgeWebviewDidFinishLoad:self];
    }
    
    // 停止 加载指示器
    [_activityView stopAnimating];
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvJSBridge:webviewLodingShow:)]) {
        [self.delegate plvJSBridge:self webviewLodingShow:NO];
    }

    UIScrollView * webScrollV = (UIScrollView *)[[_webView subviews] objectAtIndex:0];
    [webScrollV setBounces:NO];
    if (PLV_iOSVERSION_Available_11_0) {
        webScrollV.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    
    [self.webView evaluateJavaScript:@"document.documentElement.style.webkitTouchCallout='none';" completionHandler:nil];
    [self.webView evaluateJavaScript:@"document.documentElement.style.webkitUserSelect='none';"completionHandler:nil];
}

- (void)webView:(WKWebView *)webView didFailNavigation:(WKNavigation *)navigation withError:(NSError *)error{
    PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS - didFailNavigation error:%@",error);
    
    // 更新 加载状态
    self.webviewLoadFinish = NO;
    self.webviewLoadFaid = YES;
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvJSBridgeWebviewDidFailLoad:withError:)]) {
        [self.delegate plvJSBridgeWebviewDidFailLoad:self withError:error];
    }
    
    // 停止 加载指示器
    [_activityView stopAnimating];
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvJSBridge:webviewLodingShow:)]) {
        [self.delegate plvJSBridge:self webviewLodingShow:NO];
    }
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler{
    if (navigationAction.navigationType == WKNavigationTypeLinkActivated){
        NSString * url = navigationAction.request.URL.absoluteString;
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:url]];
        decisionHandler(WKNavigationActionPolicyCancel);
    }else{
        decisionHandler(WKNavigationActionPolicyAllow);
    }
}

#pragma mark WKUIDelegate
- (void)webView:(WKWebView *)webView runJavaScriptConfirmPanelWithMessage:(nonnull NSString *)message initiatedByFrame:(nonnull WKFrameInfo *)frame completionHandler:(nonnull void (^)(BOOL))completionHandler{
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvJSBridge:showConfirmPanelWithMessage:initiatedByFrame:completionHandler:)]) {
        [self.delegate plvJSBridge:self showConfirmPanelWithMessage:message initiatedByFrame:frame completionHandler:^(BOOL result) {
            completionHandler(result);
        }];
    }else{
        PLVF_NORMAL_LOG_ERROR(@"JSBridge",@"PLVJS - show confirm panel failed, delegate not implement method:[%s]",__FUNCTION__);
    }
}

@end
