//
//  PLVBFilterOption.m
//  PLVBusinessSDK
//
//  Created by junotang on 2022/1/18.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBFilterOption.h"

@implementation PLVBFilterOption

- (instancetype)initWithType:(PLVBFilterType)type
                        name:(NSString *)name
                         key:(NSString *)key
                   spellName:(NSString *)spellName
            defaultIntensity:(CGFloat)intensity {
    self = [super init];
    if (self) {
        _filterType = type;
        _filterName = name; 
        _filterKey = key;   
        _filterSpellName = spellName;
        _intensity = intensity;
    }
    return self;
}

@end    
