//
//  PLVBFilterOption.h
//  PLVBusinessSDK
//
//  Created by junotang on 2022/1/18.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN

// 滤镜命名：
// 1.冷白
// 2.牛奶
// 3.蓝调
// 4.元气
// 5.清新
// 6.质感
// 7.粉瓷
// 8.樱红
// 9.胶片
// 10.唯美
typedef NS_ENUM(NSInteger, PLVBFilterType) {
    PLVBFilterType_Original = 0,    // 原图
    PLVBFilterType_LengBai,       // 冷白
    PLVBFilterType_NiuNai,        // 牛奶
    PLVBFilterType_LanDiao,       // 蓝调
    PLVBFilterType_YuanQi,        // 元气
    PLVBFilterType_QingXin,       // 清新
    PLVBFilterType_ZhiGan,        // 质感
    PLVBFilterType_FenCi,         // 粉瓷
    PLVBFilterType_YingHong,      // 樱红
    PLVBFilterType_Jiao<PERSON>ian,      // 胶片
    PLVBFilterType_WeiMei         // 唯美
};

/// 美颜滤镜option
@interface PLVBFilterOption : NSObject

/// 滤镜的中文名
@property (nonatomic, copy) NSString *filterName;

/// 滤镜的中文名拼音
@property (nonatomic, copy) NSString *filterSpellName;

/// 给美颜sdk使用的滤镜名
@property (nonatomic, copy) NSString *filterKey;

/// 滤镜强度    
@property (nonatomic, assign) CGFloat intensity;

/// 滤镜类型
@property (nonatomic, assign) PLVBFilterType filterType;

/// 初始化
/// @param type 滤镜类型
/// @param name 滤镜名称
/// @param spellName 滤镜名称拼音
/// @param intensity 滤镜强度
- (instancetype)initWithType:(PLVBFilterType)type
                        name:(NSString *)name
                         key:(NSString *)key
                   spellName:(NSString *)spellName
            defaultIntensity:(CGFloat)intensity;

@end
    
NS_ASSUME_NONNULL_END
