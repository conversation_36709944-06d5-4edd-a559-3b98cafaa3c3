//
//  PLVBBeautyManager.m
//  PLVBusinessSDK
//
//  Created by junotang on 2022/1/17.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBBeautyManager.h"
#import "PLVBBeautyBytedManager.h"
#import "PLVBBeautyGPUPixelManager.h"
#import <PLVFoundationSDK/PLVFConsoleLogger.h>

@interface PLVBBeautyManager ()

#pragma mark 数据
/// 美颜类型
@property (nonatomic, assign) PLVBBeautyType beautyType;

#pragma mark 状态
/// 当前 美颜是否已就绪
@property (nonatomic, assign) BOOL beautyIsReady;

@end

@implementation PLVBBeautyManager

#pragma mark - [ Life Period ]
- (void)dealloc{
    PLVF_NORMAL_LOG_DEBUG(@"BBeauty",@"%s", __FUNCTION__);
}

- (instancetype)init {
    self = [super init];
    if (self) {
        
    }
    return self;
}

#pragma mark - [ Public Methods ]
+ (instancetype)beautyManagerWithBeautyType:(NSString *)beautyType {
    PLVBBeautyManager *manager;
    if ([beautyType isEqualToString:@"BytedEffect"]) {
        if (NSClassFromString(@"IRenderMsgDelegateManager")) {
            manager = [[NSClassFromString(@"PLVBBeautyBytedManager") alloc]init];
            manager.beautyType = PLVBBeautyType_BE;
        }
        else{
            PLVF_NORMAL_LOG_ERROR(@"BBeauty", @"PLVBBeautyManager - manager create failed ，need PLVBytedEffectSDK SDK");
        }
    }
    else if ([beautyType isEqualToString:@"GPUPixel"]) {
        if (NSClassFromString(@"PLVBBeautyGPUPixelManager")) {
            manager = [[NSClassFromString(@"PLVBBeautyGPUPixelManager") alloc]init];
            manager.beautyType = PLVBBeautyType_GP;
        }
        else{
            PLVF_NORMAL_LOG_ERROR(@"BBeauty", @"PLVBBeautyManager - manager create failed ，need PLVBytedEffectSDK SDK");
        }
    }
    else{
        PLVF_NORMAL_LOG_ERROR(@"BBeauty", @"PLVBBeautyManager - beautyManager create failed, beautyType invalid %@",beautyType);
    }
    
    return manager;
}

/// 创建美颜SDK
/// @param resourceProvider 素材和授权的提供者
- (int)createBeautyTaskWithResourceProvider:(id<PLVBBeautyResourceProvider>)resourceProvider {
    return -1; // 由子类覆写实现
}

/// 销毁美颜SDK
- (int)destroyTask {
    return -1; // 由子类覆写实现
}

#pragma mark 美颜设置

/// 美颜特效是否支持
/// @param option 美颜特效类型
- (BOOL)isBeautyOptionSupport:(PLVBBeautyOption)option {
    return NO;// 由子类覆写实现
}

/// 更新美颜特效
/// @param option 美颜特效类型
/// @param intensity 强度，0~1
- (void)updateBeautyOption:(PLVBBeautyOption)option withIntensity:(CGFloat)intensity {
    // 由子类覆写实现
}

/// 移除美颜特效
/// @param option 美颜特效类型
- (void)removeBeautyOption:(PLVBBeautyOption)option {
    // 由子类覆写实现
}

/// 移除所有美颜特效
- (void)clearBeautyOption {
    // 由子类覆写实现
}

/// 获取支持的美颜滤镜
- (NSArray<PLVBFilterOption *> *)getSupportFilterOptions {
    return @[]; // 由子类覆写实现
}

/// 设置美颜滤镜
/// @param option 滤镜对象
/// @param intensity 强度，0~1
- (void)setFilterOption:(PLVBFilterOption *)option withIntensity:(CGFloat)intensity {
    // 由子类覆写实现
}

- (int)processTexture:(GLuint)texture outputTexture:(GLuint)outputTexture width:(int)width height:(int)height rotate:(int)rotate timeStamp:(double)timeStamp {
    return -1; // 由子类覆写实现
}

- (void)processBytes:(int)width height:(int)height pixels:(const uint8_t *)pixels stride:(int)stride ts:(int64_t)timestamp{
    // 子类覆写

}

- (int)processPixelBuffer:(CVPixelBufferRef)inBuf outBuf:(CVPixelBufferRef)outBuf rotate:(int)rotate timeStamp:(double)timeStamp{
    // 子类重写
    return -1;
}

- (void)setRotation:(int)rotation{
    // 子类覆写

}

@end
