//
//  PLVBBytedHttpRequestProvider.h
//  PLVBusinessSDK
//
//  Created by junotang on 2022/4/7.
//  Copyright © 2022 PLV. All rights reserved.
//

#ifndef PLVBBytedHttpRequestProvider_h
#define PLVBBytedHttpRequestProvider_h


#if __has_include(<BytedLicenseDefine.h>)
    #include "BytedLicenseDefine.h"
    #define BEBytedSDK_Byted
#endif

#ifdef BEBytedSDK_Byted
/// 授权文件的请求接口类，实现网络注入功能
class PLVBBytedHttpRequestProvider: public HttpRequestProvider
{
    
public:
    bool getRequest(const RequestInfo* requestInfo, ResponseInfo& responseInfo) override;
    
    bool postRequest(const RequestInfo* requestInfo, ResponseInfo& responseInfo) override;
    
};
#endif
#endif
