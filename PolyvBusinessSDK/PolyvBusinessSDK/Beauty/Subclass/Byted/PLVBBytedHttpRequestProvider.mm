//
//  PLVBBytedHttpRequestProvider.m
//  PLVBusinessSDK
//
//  Created by junotang on 2022/4/7.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBBytedHttpRequestProvider.h"
#import <Foundation/Foundation.h>

#ifdef BEBytedSDK_Byted
//get请求暂不实现
bool PLVBBytedHttpRequestProvider::getRequest(const RequestInfo* requestInfo, ResponseInfo& responseInfo)
{
    return false;
}

bool PLVBBytedHttpRequestProvider::postRequest(const RequestInfo* requestInfo, ResponseInfo& responseInfo)
{
    NSString* nsUrl = [[NSString alloc] initWithCString:requestInfo->url.c_str() encoding:NSUTF8StringEncoding];
    NSURL *URL = [NSURL URLWithString:nsUrl];
    NSURLSession *session = [NSURLSession sharedSession];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:URL];
    [request setHTTPMethod:@"POST"];
    for (auto iter = requestInfo->requestHead.begin(); iter != requestInfo->requestHead.end(); iter++) {
        NSString* headKey = [[NSString alloc] initWithCString:iter->first.c_str() encoding:NSUTF8StringEncoding];
        NSString* headValue = [[NSString alloc] initWithCString:iter->second.c_str() encoding:NSUTF8StringEncoding];
        [request setValue:headValue forHTTPHeaderField:headKey];
    }
    NSString* nsBody = [[NSString alloc] initWithCString:requestInfo->bodydata encoding:NSUTF8StringEncoding];
    request.HTTPBody = [nsBody dataUsingEncoding:NSUTF8StringEncoding];
    
    __block bool requestRet = false;
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    NSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        if (data)
        {
            NSHTTPURLResponse *urlResponse = (NSHTTPURLResponse *)response;
            responseInfo.status_code = (int)urlResponse.statusCode;
            responseInfo.bodySize = (int)[data length];
            responseInfo.bodydata = new char[responseInfo.bodySize];
            memcpy(responseInfo.bodydata, [data bytes], responseInfo.bodySize);
            requestRet = true;
        }
        dispatch_semaphore_signal(semaphore);
    }];
    
    [dataTask resume];
    dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    
    return requestRet;
}
#endif
