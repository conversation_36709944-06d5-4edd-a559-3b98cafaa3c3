//
//  PLVBBeautyBytedManager.m
//  PLVBusinessSDK
//
//  Created by junotang on 2022/1/17.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBBeautyBytedManager.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVBBytedHttpRequestProvider.h"
#import <OpenGLES/ES2/glext.h>

#if __has_include(<bef_effect_ai_api.h>)
    #import "BytedLicenseDefine.h"
    #import "bef_effect_ai_license_wrapper.h"
    #import "bef_effect_ai_api.h"
    #import "bef_effect_ai_message_define.h"
    #import "bef_effect_ai_error_code_format.h"
    #define BEBytedSDK_Byted
#endif

static NSString *BYTED_LICENSE_URL = @"https://cv-tob.bytedance.com/v1/api/sdk/tob_license/getlicense";

@interface PLVBBeautyBytedManager ()
#ifdef BEBytedSDK_Byted
<RenderMsgDelegate>
{
    bef_effect_handle_t _handle;                    //美颜句柄
    IRenderMsgDelegateManager *_msgDelegateManager; // 美颜消息处理
    
    IBytedLicenseProvider* _licenseProvider;        // 授权文件提供者
    PLVBBytedHttpRequestProvider* _requestProvider; // 授权文件网络注入的实现
    std::string _licenseFilePath;                   // 在线授权文件在本地的路径
    
    NSMutableSet<NSString *>    *_existResourcePathes;
    BOOL                        _needLoadResource;
}

/// 特效Node对应PLVBBeautyOption字典
@property (nonatomic, copy) NSDictionary<NSNumber *,NSString *> *composerNodeDictionary;
/// 特效Key对应PLVBBeautyOption字典
@property (nonatomic, copy) NSDictionary<NSNumber *,NSString *> *composerKeyDictionary;
/// 官方给出的滤镜字典
@property (nonatomic, copy) NSDictionary<NSString *,NSDictionary *> *filterOriginDictionary;

/// 授权请求错误码
@property (nonatomic, assign) int licenseRrrorCode;
/// 授权请求错误信息
@property (nonatomic, copy) NSString *licenseErrorMsg;
/// 美颜sdk是否初始化成功
@property (nonatomic, assign) BOOL beautyInitSuccess;

/// 图像处理工具
@property (nonatomic, strong) PLVImageUtil *imageUtils;

#endif
@end

@implementation PLVBBeautyBytedManager
#ifdef BEBytedSDK_Byted

#pragma mark - [ Life Period ]
- (void)dealloc{
    delete _licenseProvider;
    delete _requestProvider;
}

#pragma mark - [ Father Public Methods ]
- (int)createBeautyTaskWithResourceProvider:(id<PLVBBeautyResourceProvider>)resourceProvider {
    if (!_handle) {
        if (!resourceProvider) {
            PLVF_NORMAL_LOG_ERROR(@"BBeauty", @"PLVBBeautyManager - create beauty failed, resourceProvider is nil");
            return -1;
        }
        if ([EAGLContext currentContext] != self.eaglContext) {
            [EAGLContext setCurrentContext:self.eaglContext];
        }
        
        // 图像处理对象
        self.imageUtils = [[PLVImageUtil alloc] init];
        
        self.resourceProvider = resourceProvider;
        
        self.beautyInitSuccess = NO;
        // 设置授权的提供者
        [self setupLicenseProvider];
        
        _existResourcePathes = [NSMutableSet set];
        _needLoadResource = NO;
        
        int ret = 0;
        // 生成美颜句柄
        ret = bef_effect_ai_create(&_handle);
        [self checkResultWithCode:ret methodName:@"bef_effect_ai_create"];
        // 检查授权
        ret = bef_effect_ai_check_online_license(_handle, [self getlicensePath]);
        if (ret != 0) {
            // 本地授权文件过期的时候，更新授权
            ret = bef_effect_ai_check_online_license(_handle, [self updateLicensePath]);
        }
        [self checkResultWithCode:ret methodName:@"bef_effect_ai_check_license"];
        // 默认配置
        ret = bef_effect_ai_use_pipeline_processor(_handle, YES);
        [self checkResultWithCode:ret methodName:@"bef_effect_ai_use_pipeline_processor"];
        ret = bef_effect_ai_set_render_api(_handle, [self renderAPI]);
        [self checkResultWithCode:ret methodName:@"bef_effect_ai_set_render_api"];
        ret = bef_effect_ai_use_builtin_sensor(_handle, YES);
        [self checkResultWithCode:ret methodName:@"bef_effect_ai_use_builtin_sensor"];
        ret = bef_effect_ai_init(_handle, 10, 10, [self.resourceProvider.modelDirPath UTF8String], "");
        [self checkResultWithCode:ret methodName:@"bef_effect_ai_init"];
        self.beautyInitSuccess = ret == 0;
        // 美颜消息处理
        _msgDelegateManager = [[IRenderMsgDelegateManager alloc] init];
        [_msgDelegateManager addDelegate:self];
        
        return ret;
    }
    return 0;
}

-(int)destroyTask {
    [_msgDelegateManager removeDelegate:self];
    bef_effect_ai_destroy(_handle);
    return 0;
}

#pragma mark 美颜设置
- (BOOL)isBeautyOptionSupport:(PLVBBeautyOption)option {
    NSString *node = [self.composerNodeDictionary objectForKey:@(option)];
    NSString *key = [self.composerKeyDictionary objectForKey:@(option)];
    if (!key || !node) {
        return NO;
    }
    NSString *nodePath = [self.resourceProvider composerNodePath:node];
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL isDir = FALSE;
    BOOL isDirExist = [fileManager fileExistsAtPath:nodePath isDirectory:&isDir];
    if (!isDirExist || !isDir) {
        return NO;
    }
    nodePath = [nodePath stringByAppendingString:@"/.config_file"];
    NSData *data = [NSData dataWithContentsOfFile:nodePath];
    if (!data) {
        return NO;
    }
    NSError *error;
    NSArray *dataArray = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];
    if (error) {
        return NO;
    }
    for (NSString *supportKey in dataArray) {
        if ([supportKey isEqualToString:key]) {
            return YES;
        }
    }
    return NO;
}

- (void)updateBeautyOption:(PLVBBeautyOption)option withIntensity:(CGFloat)intensity {
    NSString *node = [self.composerNodeDictionary objectForKey:@(option)];
    NSString *key = [self.composerKeyDictionary objectForKey:@(option)];
    if (!key || !node) {
        return;
    }
    
    // 设置特效
    [self appendComposerNodes:@[node]];

    // 设置强度
    node = [self.resourceProvider composerNodePath:node];
    bef_effect_result_t result = bef_effect_ai_composer_update_node(_handle, (const char *)[node UTF8String], (const char *)[key UTF8String], intensity);
    [self checkResultWithCode:result methodName:@"bef_effect_ai_composer_update_node"];
}

- (void)removeBeautyOption:(PLVBBeautyOption)option {
    NSString *node = [self.composerNodeDictionary objectForKey:@(option)];
    if (!node) {
        return;
    }
    [self removeComposerNodes:@[node]];
}

- (void)clearBeautyOption {
    NSEnumerator *enumerator = [_existResourcePathes objectEnumerator];
    [self removeComposerNodes:enumerator.allObjects];
}

- (NSArray<PLVBFilterOption *> *)getSupportFilterOptions {
    NSString *filterPath = [self.resourceProvider filterPath:@""];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL isDir = FALSE;
    BOOL isDirExist = [fileManager fileExistsAtPath:filterPath isDirectory:&isDir];
    if (!isDirExist || !isDir) {
        return @[];
    }
    NSError *error;
    NSArray *dirArray = [fileManager contentsOfDirectoryAtPath:filterPath error:&error];
    if (error || !dirArray) {
        return @[];
    }
    NSMutableArray *filterArray = [[NSMutableArray alloc]initWithCapacity:dirArray.count];
    for (NSString *key in dirArray) {
        PLVBFilterOption *option = [[PLVBFilterOption alloc]init];
        option.filterKey = key;
        NSArray *keyNOArray = [key componentsSeparatedByString:@"_"];
        if (keyNOArray.count > 1) {
            NSString *keyNO = keyNOArray[1];
            NSDictionary *filterDict = self.filterOriginDictionary[keyNO];
            if (filterDict) {
                option.filterSpellName = filterDict.allKeys[0];
                option.filterName = filterDict.allValues[0];
            }else {
                option.filterSpellName = @"";
                option.filterName = @"";
            }
        }else {
            option.filterSpellName = @"";
            option.filterName = @"";
        }
        [filterArray addObject:option];
    }
    return (NSArray *)filterArray;
}

- (void)setFilterOption:(PLVBFilterOption *)option withIntensity:(CGFloat)intensity {
    NSString *path = option.filterKey;
    if ([PLVFdUtil checkStringUseable:path]) {
        path = [self.resourceProvider filterPath:path];
    }
    // 滤镜
    bef_effect_result_t status = BEF_RESULT_SUC;
    status = bef_effect_ai_set_color_filter_v2(_handle, [path UTF8String]);
    [self checkResultWithCode:status methodName:@"bef_effect_ai_set_color_filter_v2"];
    //强度
    status = bef_effect_ai_set_intensity(_handle, BEF_INTENSITY_TYPE_GLOBAL_FILTER_V2, intensity);
    [self checkResultWithCode:status methodName:@"bef_effect_ai_set_intensity"];
}

-(int)processTexture:(GLuint)texture outputTexture:(GLuint)outputTexture width:(int)width height:(int)height rotate:(int)rotate timeStamp:(double)timeStamp {
    if (!self.beautyInitSuccess) {
        return -1;
    }
    if (_needLoadResource) {
        _needLoadResource = NO;
        [self loadResource:-1];
    }

    bef_effect_result_t ret = bef_effect_ai_set_width_height(_handle, width, height);
    [self checkResultWithCode:ret methodName:@"bef_effect_ai_set_width_height"];
    ret = bef_effect_ai_set_orientation(_handle, [self getBefRotateType:rotate]);
    [self checkResultWithCode:ret methodName:@"bef_effect_ai_set_orientation"];
    ret = bef_effect_ai_algorithm_texture(_handle, texture, timeStamp);
    [self checkResultWithCode:ret methodName:@"bef_effect_ai_algorithm_texture"];
    ret = bef_effect_ai_process_texture(_handle, texture, outputTexture, timeStamp);
    [self checkResultWithCode:ret methodName:@"bef_effect_ai_process_texture"];
    glEnableVertexAttribArray(0);
    glEnableVertexAttribArray(1);
    return ret;
}

// 具体的字节美颜处理方法
- (int)processPixelBuffer:(CVPixelBufferRef)inBuf outBuf:(CVPixelBufferRef)outBuf rotate:(int)rotate timeStamp:(double)timeStamp{
    if (!self.beautyInitSuccess) {
        return -1;
    }
    if (_needLoadResource) {
        _needLoadResource = NO;
        [self loadResource:-1];
    }
    
    // 此处无需设置opengl 上下文 在rtcmanager 中进行了设置
//    if ([EAGLContext currentContext] != self.eaglContext) {
//        [EAGLContext setCurrentContext:self.eaglContext];
//    }
    
    CVPixelBufferRef pixelBuffer = inBuf;
    // 默认PLVFormatType_BGRA 格式，减少计算
//    PLVPixelBufferInfo *pixelBufferInfo = [self.imageUtils getCVPixelBufferInfo:pixelBuffer];
//    if (pixelBufferInfo.format != PLVFormatType_BGRA) {
//        pixelBuffer = [self.imageUtils transforCVPixelBufferToCVPixelBuffer:inBuf outputFormat:PLVFormatType_BGRA];
//    }
    
    // 创建纹理
    id<PLVGLTexture> inputTexture = (id<PLVGLTexture>)[self.imageUtils transforCVPixelBufferToTexture:pixelBuffer];
    id<PLVGLTexture> outputTexture = nil;
    outputTexture = [self.imageUtils getOutputPixelBufferGLTextureWithWidth:inputTexture.width
                                                                  height:inputTexture.height
                                                                  format:PLVFormatType_BGRA];
    
    // 美颜处理
    bef_effect_result_t ret = bef_effect_ai_set_width_height(_handle, inputTexture.width, inputTexture.height);
    [self checkResultWithCode:ret methodName:@"bef_effect_ai_set_width_height"];
    ret = bef_effect_ai_set_orientation(_handle, [self getBefRotateType:rotate]);
    [self checkResultWithCode:ret methodName:@"bef_effect_ai_set_orientation"];
    ret = bef_effect_ai_algorithm_texture(_handle, inputTexture.texture, timeStamp);
    [self checkResultWithCode:ret methodName:@"bef_effect_ai_algorithm_texture"];
    ret = bef_effect_ai_process_texture(_handle, inputTexture.texture, outputTexture.texture, timeStamp);
    [self checkResultWithCode:ret methodName:@"bef_effect_ai_process_texture"];
    glEnableVertexAttribArray(0);
    glEnableVertexAttribArray(1);
    
    // 处理后的 Texture 提取 CVPixelBufferRef，再还给回 TRTC
    CVPixelBufferRef outputPixelBuffer = [(PLVPixelBufferGLTexture *)outputTexture pixelBuffer];
    // 纹理转CVPixelBuffer
    outputPixelBuffer = [self.imageUtils transforCVPixelBufferToCVPixelBuffer:outputPixelBuffer outputFormat:PLVFormatType_BGRA];
    // 使用简化的内存拷贝方法
    CVPixelBufferLockBaseAddress(outputPixelBuffer, kCVPixelBufferLock_ReadOnly);
    CVPixelBufferLockBaseAddress(outBuf, 0);
    
    void *srcAddress = CVPixelBufferGetBaseAddress(outputPixelBuffer);
    void *dstAddress = CVPixelBufferGetBaseAddress(outBuf);
    
    size_t srcBytesPerRow = CVPixelBufferGetBytesPerRow(outputPixelBuffer);
    size_t dstBytesPerRow = CVPixelBufferGetBytesPerRow(outBuf);
    size_t srcHeight = CVPixelBufferGetHeight(outputPixelBuffer);
    size_t dstHeight = CVPixelBufferGetHeight(outBuf);

    // 逐行拷贝，取较小的行字节数和高度
    // 分辨率为 720 x 1256 整块内存拷贝可能存在问题
    size_t copyBytesPerRow = MIN(srcBytesPerRow, dstBytesPerRow);
    size_t copyHeight = MIN(srcHeight, dstHeight);

    for (size_t row = 0; row < copyHeight; row++) {
        memcpy((uint8_t*)dstAddress + row * dstBytesPerRow,
                (uint8_t*)srcAddress + row * srcBytesPerRow,
                copyBytesPerRow);
    }
    
    CVPixelBufferUnlockBaseAddress(outBuf, 0);
    CVPixelBufferUnlockBaseAddress(outputPixelBuffer, kCVPixelBufferLock_ReadOnly);
    
    // 不能释放
//    if (outputPixelBuffer) {
//        CVBufferRelease(outputPixelBuffer);
//        outputPixelBuffer = NULL;
//    }
    return 0;
}

#pragma mark - [ Private Methods ]

#pragma mark 授权相关
/// 设置请求授权的参数
- (void)setupLicenseProvider {
    _licenseProvider = bef_effect_ai_get_license_wrapper_instance();
    _licenseProvider->setParam("mode", "ONLINE");
    _licenseProvider->setParam("url", [BYTED_LICENSE_URL UTF8String]);
    _licenseProvider->setParam("key", [self.resourceProvider.licenseKey UTF8String]);
    _licenseProvider->setParam("secret", [self.resourceProvider.licenseSecret UTF8String]);
    _licenseProvider->setParam("licensePath", [self.resourceProvider.licensePath UTF8String]);
    
    // 授权下载器的注入
    _requestProvider = new PLVBBytedHttpRequestProvider;
    _licenseProvider->registerHttpProvider(_requestProvider);
}

/// 获取在线授权文件，本地已经存在的情况下直接返回文件路径
- (const char *)getlicensePath {
    _licenseRrrorCode = 0;
    _licenseErrorMsg = @"";
    std::map<std::string, std::string> params;
    _licenseProvider->getLicenseWithParams(params, false, [](const char* retmsg, int retSize, ErrorInfo error, void* userdata){
        PLVBBeautyBytedManager* pThis = CFBridgingRelease(userdata);
        pThis.licenseRrrorCode = error.errorCode;
        pThis.licenseErrorMsg = [[NSString alloc] initWithCString:error.errorMsg.c_str() encoding:NSUTF8StringEncoding];
    }, (void*)CFBridgingRetain(self));

    if (![self checkLicenseResultMethodName: @"getLicensePath"])
        return "";

    _licenseFilePath = _licenseProvider->getParam("licensePath");
    return _licenseFilePath.c_str();
}

/// 更新在线授权文件，无论本地是否存在都去向服务器拉取最新的授权文件
- (const char *)updateLicensePath {
    _licenseRrrorCode = 0;
    _licenseErrorMsg = @"";
    std::map<std::string, std::string> params;
    _licenseProvider->updateLicenseWithParams(params, false, [](const char* retmsg, int retSize, ErrorInfo error, void* userdata){
        PLVBBeautyBytedManager* pThis = CFBridgingRelease(userdata);
        pThis.licenseRrrorCode = error.errorCode;
        pThis.licenseErrorMsg = [[NSString alloc] initWithCString:error.errorMsg.c_str() encoding:NSUTF8StringEncoding];
    }, (void*)CFBridgingRetain(self));

    if (![self checkLicenseResultMethodName: @"updateLicensePath"])
        return "";
    
    _licenseFilePath = _licenseProvider->getParam("licensePath");
    return _licenseFilePath.c_str();
}

- (BOOL)checkLicenseResultMethodName:(NSString *)methodName {
    if (_licenseRrrorCode != 0) {
        NSString *message = @"";
        if ([_licenseErrorMsg length] > 0) {
            message = [NSString stringWithFormat:@"%@-%@", methodName, _licenseErrorMsg];
            PLVF_NORMAL_LOG_ERROR(@"BBeauty", @"PLVBBeautyManager - error:%@ error: %d, %@", methodName, _licenseRrrorCode, _licenseErrorMsg);
        } else {
            message = methodName;
            PLVF_NORMAL_LOG_ERROR(@"BBeauty", @"PLVBBeautyManager - error:%@ error: %d", methodName, _licenseRrrorCode);
        }
        NSDictionary *userInfo = [NSDictionary dictionaryWithObject:methodName forKey:NSLocalizedDescriptionKey];
        NSError *error = [NSError errorWithDomain:@"net.plv.PLVBBeautyManager" code:(NSInteger)_licenseRrrorCode userInfo:userInfo];
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvbBeautyManager:didOccurError:)]) {
            [self.delegate plvbBeautyManager:self didOccurError:error];
        }
        return false;
    }
    return true;
}

#pragma mark 美颜相关
- (bef_ai_rotate_type)getBefRotateType:(int)rotate {
    if (rotate == 0) {
        return BEF_AI_CLOCKWISE_ROTATE_0;
    }
    if (rotate == 1) {
        return BEF_AI_CLOCKWISE_ROTATE_90;
    }
    if (rotate == 2) {
        return BEF_AI_CLOCKWISE_ROTATE_180;
    }
    if (rotate == 3) {
        return BEF_AI_CLOCKWISE_ROTATE_270;
    }
    return BEF_AI_CLOCKWISE_ROTATE_0;
}

/// 拼接美颜特效
/// @param nodes 特效node数组
- (void)appendComposerNodes:(NSArray<NSString *> *)nodes {
    
    for (NSString *node in nodes) {
        if (![_existResourcePathes containsObject:node]) {
            _needLoadResource = YES;
            break;
        }
    }
    [_existResourcePathes addObjectsFromArray:nodes];

    NSMutableArray<NSString *> *paths = [NSMutableArray arrayWithCapacity:nodes.count];
    for (int i = 0; i < nodes.count; i++) {
        [paths addObject:[self.resourceProvider composerNodePath:nodes[i]]];
    }
    nodes = paths;
    
    char **nodesPath = (char **)malloc(nodes.count * sizeof(char *));
    int count = 0;
    
    NSMutableSet *set = [NSMutableSet set];
    for (int i = 0; i < nodes.count; i++) {
        NSString *node = nodes[i];
        if ([set containsObject:node]) {
            continue;
        }
        [set addObject:node];
        
        if ([node canBeConvertedToEncoding:NSUTF8StringEncoding]) {
            NSUInteger strLength  = [node lengthOfBytesUsingEncoding:NSUTF8StringEncoding];
            nodesPath[count] = (char *)malloc((strLength + 1) * sizeof(char *));
            strncpy(nodesPath[count], [node cStringUsingEncoding:NSUTF8StringEncoding], strLength);
            nodesPath[count][strLength] = '\0';
        }
        count++;
    }
    
    bef_effect_result_t result = BEF_RESULT_SUC;
    result = bef_effect_ai_composer_append_nodes(_handle, (const char **)nodesPath, count);
    if (result != BEF_RESULT_SUC) {
        PLVF_NORMAL_LOG_ERROR(@"BBeauty", @"PLVBBeautyManager - bef_effect_ai_composer_set_nodes error: %d", result);
    }
    
    for (int i = 0; i < count; i++) {
        free(nodesPath[i]);
    }

    if (_needLoadResource) {
        [self loadResource:-1];
        _needLoadResource = NO;
    }
}

/// 移除美颜特效
/// @param nodes 特效node数组
- (void)removeComposerNodes:(NSArray<NSString *> *)nodes {
    for (NSString *node in nodes) {
        [_existResourcePathes removeObject:node];
    }

    NSMutableArray<NSString *> *paths = [NSMutableArray arrayWithCapacity:nodes.count];
    for (int i = 0; i < nodes.count; i++) {
        [paths addObject:[self.resourceProvider composerNodePath:nodes[i]]];
    }
    nodes = paths;
    
    char **nodesPath = (char **)malloc(nodes.count * sizeof(char *));
    int count = 0;
    
    NSMutableSet *set = [NSMutableSet set];
    for (int i = 0; i < nodes.count; i++) {
        NSString *node = nodes[i];
        if ([set containsObject:node]) {
            continue;
        }
        [set addObject:node];
        
        if ([node canBeConvertedToEncoding:NSUTF8StringEncoding]) {
            NSUInteger strLength  = [node lengthOfBytesUsingEncoding:NSUTF8StringEncoding];
            nodesPath[count] = (char *)malloc((strLength + 1) * sizeof(char *));
            strncpy(nodesPath[count], [node cStringUsingEncoding:NSUTF8StringEncoding], strLength);
            nodesPath[count][strLength] = '\0';
        }
        
        count++;
    }
    
    bef_effect_result_t result = BEF_RESULT_SUC;
    result = bef_effect_ai_composer_remove_nodes(_handle, (const char **)nodesPath, count);
    if (result != BEF_RESULT_SUC) {
        PLVF_NORMAL_LOG_ERROR(@"BBeauty", @"PLVBBeautyManager - bef_effect_ai_composer_set_nodes error: %d", result);
    }
    
    for (int i = 0; i < count; i++) {
        free(nodesPath[i]);
    }
    free(nodesPath);
}

/// 检查美颜sdk方法的调用结果，有错误则返回上层
/// @param ret 错误码
/// @param methodName 调用方法
-(int)checkResultWithCode:(int)ret methodName:(NSString *)methodName {
    if (ret != 0 && ret != -11 && ret != 1) {
        const char *msg = bef_effect_ai_error_code_get(ret);
        NSString *message = @"";
        
        if (msg != NULL) {
            PLVF_NORMAL_LOG_ERROR(@"BBeauty", @"PLVBBeautyManager - error:%@, ret:%d, msg:%s", methodName, ret, msg);
            message = [NSString stringWithUTF8String:msg];
            
        }else {
            PLVF_NORMAL_LOG_ERROR(@"BBeauty", @"PLVBBeautyManager - error:%@, ret:%d", methodName, ret);
            message = [NSString stringWithFormat:@"%@ error: %d", methodName, ret];
        }
        
        NSDictionary *userInfo = [NSDictionary dictionaryWithObject:methodName forKey:NSLocalizedDescriptionKey];
        NSError *error = [NSError errorWithDomain:@"net.plv.PLVBBeautyManager" code:(NSInteger)ret userInfo:userInfo];
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvbBeautyManager:didOccurError:)]) {
            [self.delegate plvbBeautyManager:self didOccurError:error];
        }
    }
    return ret;
}

- (bef_ai_render_api_type)renderAPI {
    EAGLContext *context = [EAGLContext currentContext];
    EAGLRenderingAPI api = context.API;
    if (api == kEAGLRenderingAPIOpenGLES2) {
        return bef_ai_render_api_gles20;
    }
    return bef_ai_render_api_gles30;
}

- (void)loadResource:(int)timeout {
    bef_effect_ai_load_resource_with_timeout(_handle, timeout);
}

#pragma mark - [ Getter ]
- (BOOL)beautyIsReady {
    return _handle ? YES : NO;
}

- (NSDictionary<NSNumber *,NSString *> *)composerNodeDictionary {
    if (!_composerNodeDictionary) {
        _composerNodeDictionary = @{@(PLVBBeautyOption_BeautyWhiten) : @"beauty_IOS_lite",
                                    @(PLVBBeautyOption_BeautySharp) : @"beauty_IOS_lite",
                                    @(PLVBBeautyOption_BeautySmooth) : @"beauty_IOS_lite",
                                    @(PLVBBeautyOption_ReshapeDeformOverAll) : @"reshape_lite",
                                    @(PLVBBeautyOption_ReshapeDeformEye) : @"reshape_lite",
                                    @(PLVBBeautyOption_ReshapeDeformNose) : @"reshape_lite",
                                    @(PLVBBeautyOption_ReshapeDeformZoomMouth) : @"reshape_lite",
                                    @(PLVBBeautyOption_ReshapeDeformForeHead) : @"reshape_lite",
                                    @(PLVBBeautyOption_ReshapeDeformZoomJawbone) : @"reshape_lite",
                                    @(PLVBBeautyOption_ReshapeBeautyWhitenTeeth) : @"beauty_4Items",
                                    @(PLVBBeautyOption_ReshapeBeautyBrightenEye) : @"beauty_4Items",
                                    
        };
    }
    return _composerNodeDictionary;
}

-(NSDictionary<NSNumber *,NSString *> *)composerKeyDictionary {
    if (!_composerKeyDictionary) {
        _composerKeyDictionary = @{@(PLVBBeautyOption_BeautyWhiten) : @"whiten",
                                   @(PLVBBeautyOption_BeautySharp) : @"sharp",
                                   @(PLVBBeautyOption_BeautySmooth) : @"smooth",
                                   @(PLVBBeautyOption_ReshapeDeformOverAll) : @"Internal_Deform_Overall",
                                   @(PLVBBeautyOption_ReshapeDeformEye) : @"Internal_Deform_Eye",
                                   @(PLVBBeautyOption_ReshapeDeformNose) : @"Internal_Deform_Nose",
                                   @(PLVBBeautyOption_ReshapeDeformZoomMouth) : @"Internal_Deform_ZoomMouth",
                                   @(PLVBBeautyOption_ReshapeDeformForeHead) : @"Internal_Deform_Forehead",
                                   @(PLVBBeautyOption_ReshapeDeformZoomJawbone) : @"Internal_Deform_Zoom_Jawbone",
                                   @(PLVBBeautyOption_ReshapeBeautyWhitenTeeth) : @"BEF_BEAUTY_WHITEN_TEETH",
                                   @(PLVBBeautyOption_ReshapeBeautyBrightenEye) : @"BEF_BEAUTY_BRIGHTEN_EYE",
                                    
        };
    }
    return _composerKeyDictionary;
}

-(NSDictionary<NSString *,NSDictionary *> *)filterOriginDictionary {
    if (!_filterOriginDictionary) {
        _filterOriginDictionary = @{@"01" : @{@"baixi" : @"白皙"},
                                    @"02" : @{@"naiyou" : @"奶油"},
                                    @"03" : @{@"yangqi" : @"氧气"},
                                    @"04" : @{@"jiegeng" : @"桔梗"},
                                    @"05" : @{@"luolita" : @"洛丽塔"},
                                    @"06" : @{@"mitao" : @"蜜桃"},
                                    @"07" : @{@"makalong" : @"马卡龙"},
                                    @"08" : @{@"paomo" : @"泡沫"},
                                    @"09" : @{@"yinghua" : @"樱花"},
                                    @"10" : @{@"musi" : @"慕斯"},
                                    @"11" : @{@"wuyu" : @"物语"},
                                    @"12" : @{@"beihaidao" : @"北海道"},
                                    @"13" : @{@"riza" : @"日杂"},
                                    @"14" : @{@"xiyatu" : @"西雅图"},
                                    @"15" : @{@"jingmi" : @"静谧"},
                                    @"16" : @{@"jiaopian" : @"胶片"},
                                    @"17" : @{@"nuanyang" : @"暖阳"},
                                    @"18" : @{@"jiuri" : @"旧日"},
                                    @"19" : @{@"hongchun" : @"红唇"},
                                    @"20" : @{@"julandiao" : @"橘蓝调"},
                                    @"21" : @{@"tuise" : @"褪色"},
                                    @"22" : @{@"heibai" : @"黑白"},
                                    @"23" : @{@"wenrou" : @"温柔"},
                                    @"24" : @{@"lianaichaotian" : @"恋爱超甜"},
                                    @"25" : @{@"chujian" : @"初见"},
                                    @"26" : @{@"andiao" : @"暗调"},
                                    @"27" : @{@"naicha" : @"奶茶"},
                                    @"28" : @{@"soft" : @"soft"},
                                    @"29" : @{@"xiyang" : @"夕阳"},
                                    @"30" : @{@"lengyang" : @"冷氧"},
                                    @"31" : @{@"haibianrenxiang" : @"海边人像"},
                                    @"32" : @{@"gaojihui" : @"高级灰"},
                                    @"33" : @{@"haidao" : @"海岛"},
                                    @"34" : @{@"qianxia" : @"浅夏"},
                                    @"35" : @{@"yese" : @"夜色"},
                                    @"36" : @{@"hongzong" : @"红棕"},
                                    @"37" : @{@"qingtou" : @"清透"},
                                    @"38" : @{@"ziran2" : @"自然"},
                                    @"39" : @{@"suda" : @"苏打"},
                                    @"40" : @{@"jiazhou" : @"加州"},
                                    @"41" : @{@"shise" : @"食色"},
                                    @"42" : @{@"chuanwei" : @"川味"},
                                    @"43" : @{@"meishijiaopian" : @"美式胶片"},
                                    @"44" : @{@"hongsefugu" : @"红色复古"},
                                    @"45" : @{@"lvtu" : @"旅途"},
                                    @"46" : @{@"nuanhuang" : @"暖黄"},
                                    @"47" : @{@"landiaojiaopian" : @"蓝调胶片"}
        };
    }
    return _filterOriginDictionary;
}

#pragma mark - [ Delegate ]
#pragma mark - RenderMsgDelegate
- (BOOL)msgProc:(unsigned int)unMsgID arg1:(int)nArg1 arg2:(int)nArg2 arg3:(const char *)cArg3 {
    PLVF_NORMAL_LOG_INFO(@"BBeauty", @"PLVBBeautyManager - message received, type: %d, arg: %d, %d, %s", unMsgID, nArg1, nArg2, cArg3);
    return NO;
}

#endif
@end
