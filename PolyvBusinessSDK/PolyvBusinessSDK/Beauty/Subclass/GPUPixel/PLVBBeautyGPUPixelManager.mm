//
//  PLVBeautyGPUPixelManager.m
//  PLVBusinessSDK
//
//  Created by polyv on 2024/9/11.
//  Copyright © 2024 PLV. All rights reserved.
//

#import "PLVBBeautyGPUPixelManager.h"

#if __has_include(<gpupixel/gpupixel.h>)
    #import <gpupixel/gpupixel.h>
   
    #define GPUPixel_SDK
#endif

#ifdef GPUPixel_SDK
using namespace gpupixel;

@interface PLVBBeautyGPUPixelManager (){
    
    bool captureYuvFrame;
    std::shared_ptr<SourceRawDataInput> gpuPixelRawInput_;
    std::shared_ptr<BeautyFaceFilter> beauty_face_filter_;
    std::shared_ptr<StyleFilter> style_filter_;
    std::shared_ptr<TargetRawDataOutput> targetRawOutput_;
    std::shared_ptr<FaceReshapeFilter> face_reshape_filter_;
//    std::shared_ptr<gpupixel::FaceMakeupFilter> lipstick_filter_;
//    std::shared_ptr<gpupixel::FaceMakeupFilter> blusher_filter_;
}

@property(nonatomic, assign) CGFloat beautyValue;
@property(nonatomic, assign) CGFloat whithValue;
@property(nonatomic, assign) CGFloat saturationValue;
@property(nonatomic, assign) CGFloat thinFaceValue;
@property(nonatomic, assign) CGFloat eyeValue;
@property(nonatomic, assign) CGFloat lipstickValue;
@property(nonatomic, assign) CGFloat blusherValue;

@property(nonatomic, strong) NSArray<PLVBFilterOption *> *supportFilterOptions;
@property(nonatomic, strong) PLVBFilterOption *currentFilterOption;
@property(nonatomic, assign) BOOL destroyed; // 已经执行销毁

@end

#endif

@implementation PLVBBeautyGPUPixelManager

#ifdef GPUPixel_SDK
// 添加 dealloc 方法
- (void)dealloc {
    NSLog(@"PLVBBeautyGPUPixelManager - dealloc");
}

-(int)destroyTask {
    [self cleanup];
    return 0;
}

// 添加清理方法
- (void)cleanup {
    _destroyed = YES;
    // 暂时不手动释放 反复进出开播页面会概率崩溃
#ifdef GPUPixel_SDK
//    gpupixel::GPUPixelContext::getInstance()->runSync([&] {
//        // 清理回调
//        if (gpuPixelRawInput) {
//            gpuPixelRawInput->RegLandmarkCallback(nullptr);
//        }
//        
//        if (targetRawOutput_) {
//            targetRawOutput_->setPixelsCallbck(nullptr);
//        }
//        
//        // 断开 filter 链接
//        if (gpuPixelRawInput) {
//            gpuPixelRawInput->removeAllTargets();
//        }
//        
//        // 重置 shared_ptr
//        gpuPixelRawInput.reset();
//        beauty_face_filter_.reset();
//        style_filter_.reset();
//        face_reshape_filter_.reset();
//        targetRawOutput_.reset();
//    });
//    gpuPixelRawInput_ = nil;
//    beauty_face_filter_ = nil;
//    style_filter_ = nil;
//    face_reshape_filter_ = nil;
//    targetRawOutput_ = nil;
#endif
}

#pragma mark [init]
- (instancetype)init{
    if (self = [super init]){
        //
        [self setupManager];
        self.eyeValue = 0;
    }
    
    return self;
}

- (void)setupManager{
    [self initVideoFilter];
    [self setFaultValue];
}

-(void)initVideoFilter {
    gpupixel::GPUPixelContext::getInstance()->runSync([&] {
      
    gpuPixelRawInput_ = SourceRawDataInput::create();
  
    // 口红
//    lipstick_filter_ = LipstickFilter::create();
    // 腮红
//    blusher_filter_ = BlusherFilter::create();
    // 美白
    beauty_face_filter_ = BeautyFaceFilter::create();
    // 滤镜
    style_filter_ = StyleFilter::create();
    // 瘦脸
    face_reshape_filter_ = FaceReshapeFilter::create();
 
    gpuPixelRawInput_->RegLandmarkCallback([=](std::vector<float> landmarks) {
      face_reshape_filter_->SetFaceLandmarks(landmarks);
    });
  
    // create filter
    targetRawOutput_ = TargetRawDataOutput::create();
        __weak typeof(self) weakSelf = self;
    targetRawOutput_->setPixelsCallbck([=](const uint8_t* data, int width, int height, int64_t timestamp) {
        // 数据回调
        if (weakSelf.datacallback){
            weakSelf.datacallback((uint8_t*)data, width, height, timestamp);
        }
    });
    
    // filter pipline
    gpuPixelRawInput_->addTarget(style_filter_)
                     ->addTarget(beauty_face_filter_)
                     ->addTarget(face_reshape_filter_)
                     ->addTarget(targetRawOutput_);
  });
}

- (void)setFaultValue{
//    [self setBeautyValue:10];
//    [self setWhithValue:10];
//    [self setThinFaceValue:10];
//    [self setEyeValue:10];
}

#pragma mark - 属性赋值
/// 磨皮
- (void)setBeautyValue:(CGFloat)value {
    _beautyValue = value *1.25;
    beauty_face_filter_->setBlurAlpha(_beautyValue);
}

/// 美白
- (void)setWhithValue:(CGFloat)value{
    _whithValue = value *0.4;
    beauty_face_filter_->setWhite(_whithValue);
}
    
- (void)setSaturationValue:(CGFloat)value{
    _saturationValue = value;
}

/// 瘦脸
- (void)setThinFaceValue:(CGFloat)value{
    _thinFaceValue = value *0.05;
    face_reshape_filter_->setFaceSlimLevel(_thinFaceValue);
}

/// 大眼
- (void)setEyeValue:(CGFloat)value{
    _eyeValue = value *0.2;
    face_reshape_filter_->setEyeZoomLevel(_eyeValue);
}

/// 口红
- (void)setLipstickValue:(CGFloat)value{
//    _lipstickValue = value;
//    lipstick_filter_->setBlendLevel(value/10);
}

- (void)setBlusherValue:(CGFloat)value{
//    _blusherValue = value;
//    blusher_filter_->setBlendLevel(value/10);
}

#pragma mark [public]

- (void)processBytes:(int)width height:(int)height pixels:(const uint8_t *)pixels stride:(int)stride ts:(int64_t)timestamp{
    if (self.destroyed)
        return;
    if (gpuPixelRawInput_){
        gpuPixelRawInput_->uploadBytes(pixels, width, height, stride);
    }
}

- (BOOL)beautyIsReady{
    return YES;
}

- (void)updateBeautyOption:(PLVBBeautyOption)option withIntensity:(CGFloat)intensity{
    //美颜：美白[0,0.15]，磨皮[0,0.8]
    //磨皮
    //美白

    //脸部：瘦脸[0,0.05]，大眼[0,0.2]
    //大眼
    //瘦脸
    switch (option) {
        case PLVBBeautyOption_BeautyWhiten:
            [self setWhithValue:intensity];
            break;
        case PLVBBeautyOption_BeautySmooth:
            [self setBeautyValue:intensity];
            break;
        case PLVBBeautyOption_ReshapeDeformOverAll:
            [self setThinFaceValue:intensity];
            break;
        case PLVBBeautyOption_ReshapeDeformEye:
            [self setEyeValue:intensity];
            break;;
        default:
            break;
    }
}

/// 获取支持的美颜滤镜
- (NSArray<PLVBFilterOption *> *)getSupportFilterOptions{
//    滤镜命名：
//    1.冷白
//    2.牛奶
//    3.蓝调
//    4.元气
//    5.清新
//    6.质感
//    7.粉瓷
//    8.樱红
//    9.胶片
//    10.唯美
    if (!_supportFilterOptions) {
        _supportFilterOptions = @[
            [[PLVBFilterOption alloc] initWithType:PLVBFilterType_LengBai name:@"冷白" key:@"cuslengbai" spellName:@"cuslengbai" defaultIntensity:0.5],
            [[PLVBFilterOption alloc] initWithType:PLVBFilterType_NiuNai name:@"牛奶" key:@"cusniunai" spellName:@"cusniunai" defaultIntensity:0.5],
            [[PLVBFilterOption alloc] initWithType:PLVBFilterType_LanDiao name:@"蓝调" key:@"cuslandiao" spellName:@"cuslandiao" defaultIntensity:0.5],
            [[PLVBFilterOption alloc] initWithType:PLVBFilterType_YuanQi name:@"元气" key:@"cusyuanqi" spellName:@"cusyuanqi" defaultIntensity:0.5],
            [[PLVBFilterOption alloc] initWithType:PLVBFilterType_QingXin name:@"清新" key:@"cusqinxin" spellName:@"cusqinxin" defaultIntensity:0.5],
            [[PLVBFilterOption alloc] initWithType:PLVBFilterType_ZhiGan name:@"质感" key:@"cuszhigan" spellName:@"cuszhigan" defaultIntensity:0.5],
            [[PLVBFilterOption alloc] initWithType:PLVBFilterType_FenCi name:@"粉瓷" key:@"cusfenci" spellName:@"cusfenci" defaultIntensity:0.5],
            [[PLVBFilterOption alloc] initWithType:PLVBFilterType_YingHong name:@"樱红" key:@"cusyinhong" spellName:@"cusyinhong" defaultIntensity:0.5],
            [[PLVBFilterOption alloc] initWithType:PLVBFilterType_JiaoPian name:@"胶片" key:@"cusjiaopian" spellName:@"cusjiaopian" defaultIntensity:0.5],
            [[PLVBFilterOption alloc] initWithType:PLVBFilterType_WeiMei name:@"唯美" key:@"cusweimei" spellName:@"cusweimei" defaultIntensity:0.5]
        ];
    }
    return _supportFilterOptions;
}

/// 设置美颜滤镜
/// @param option 滤镜对象
/// @param intensity 强度，0~1
- (void)setFilterOption:(PLVBFilterOption *)option withIntensity:(CGFloat)intensity{
    if (!option) {
        return;
    }
    if (self.currentFilterOption.filterType != option.filterType) {
        style_filter_->setStyleType((StyleType)option.filterType);
    } 
    style_filter_->setIntensity(intensity);
    self.currentFilterOption = option;
}

#endif

@end
