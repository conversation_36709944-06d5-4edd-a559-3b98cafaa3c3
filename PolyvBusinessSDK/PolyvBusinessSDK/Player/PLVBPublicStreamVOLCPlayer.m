//
//  PLVBPublicStreamVOLCPlayer.m
//  PLVBusinessSDK
//
//  Created by juno on 2022/9/13.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBPublicStreamVOLCPlayer.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#if __has_include(<VolcEngineRTC/objc/rtc/ByteRTCEngineKit.h>)
    #import <VolcEngineRTC/objc/rtc/ByteRTCEngineKit.h>
    #define VOLCRtcSdk_ios
#endif

#define VOLCRtcEngineCLS NSClassFromString(@"ByteRTCEngineKit")
#define VOLCRtcVideoCanvasCLS NSClassFromString(@"ByteRTCVideoCanvas")

@interface PLVBPublicStreamVOLCPlayer ()<ByteRTCEngineDelegate>
#ifdef VOLCRtcSdk_ios
#pragma mark 数据
@property (nonatomic, copy) NSString *rtcAppId;
@property (nonatomic, copy) NSString *publicStreamId;

#pragma mark RTC对象
@property (nonatomic, strong) ByteRTCEngineKit *engine;

#endif
@end

@implementation PLVBPublicStreamVOLCPlayer
#ifdef VOLCRtcSdk_ios

#pragma mark - [ Life Period ]
- (void)dealloc{
    PLVF_NORMAL_LOG_DEBUG(@"BVOLCPlayer", @"volc dealloc");
}

#pragma mark - [ Public Methods ]
- (void)updatePublicStreamInfoWithStr:(NSString *)publicStreamInfoStr {
    if (![PLVFdUtil checkStringUseable:publicStreamInfoStr]) {
        PLVF_NORMAL_LOG_ERROR(@"BVOLCPlayer", @"PLVBPublicStreamVOLCPlayer - publicStreamInfo decode failed, publicStreamInfoStr invalid %@",publicStreamInfoStr);
        return;
    }
    NSDictionary *decodeDict = [self publicStreamInfoDecodeDictWithStr:publicStreamInfoStr];
    
    self.rtcAppId = decodeDict[@"appId"];
    self.publicStreamId = decodeDict[@"publicStreamId"];
    
    if (![PLVFdUtil checkStringUseable:self.rtcAppId] ||
        ![PLVFdUtil checkStringUseable:self.publicStreamId]) {
        NSLog(@"PLVBPublicStreamVOLCPlayer - publicStreamInfo decode failed, rtc AppId invalid %@, publicStreamId invalid %@", self.rtcAppId, self.publicStreamId);
    }
}

- (void)createRTCEngine {
    if (!_engine) {
        if (![PLVFdUtil checkStringUseable:self.rtcAppId]) {
            PLVF_NORMAL_LOG_ERROR(@"BVOLCPlayer", @"PLVBPublishStreamVOLCPlayer - init engine failed,appId is nil %@",self.rtcAppId);
            return;
        }
        /// 创建 RTC引擎
        _engine = [[VOLCRtcEngineCLS alloc] initWithAppId:self.rtcAppId delegate:self parameters:nil];
    }
}

- (int)setupDisplaySuperview:(UIView *_Nullable)displaySuperview {
    if (!self.engine ||
        ![PLVFdUtil checkStringUseable:self.publicStreamId]) {
        return -1;
    }
    if (!displaySuperview) {
        return [self.engine setPublicStreamVideoCanvas:self.publicStreamId withCanvas:nil];
    }
    ByteRTCVideoCanvas *canvas = [[VOLCRtcVideoCanvasCLS alloc]init];
    canvas.view = displaySuperview;
    canvas.renderMode = ByteRTCRenderModeFit;
    return [self.engine setPublicStreamVideoCanvas:self.publicStreamId withCanvas:canvas];
}

- (int)play {
    if (!self.engine ||
        ![PLVFdUtil checkStringUseable:self.publicStreamId]) {
        PLVF_NORMAL_LOG_ERROR(@"BVOLCPlayer", @"PLVBPublishStreamVOLCPlayer - play failed,publicStreamId is nil %@",self.publicStreamId);
        return -1;
    }
    int result = [self.engine startPlayPublicStream:self.publicStreamId];
    return result;
}

- (int)stop {
    if (!self.engine ||
        ![PLVFdUtil checkStringUseable:self.publicStreamId]) {
        PLVF_NORMAL_LOG_ERROR(@"BVOLCPlayer", @"PLVBPublishStreamVOLCPlayer - stop failed,publicStreamId is nil %@",self.publicStreamId);
        return -1;
    }
    return [self.engine stopPlayPublicStream:self.publicStreamId];
}

- (void)destroyRTCEngine {
    [self.engine destroyEngine];
    self.engine.delegate = nil;
    self.engine = nil;
}

#pragma mark - [ Private Methods ]
- (NSDictionary *)publicStreamInfoDecodeDictWithStr:(NSString *)string {
    NSData *encryptedData = [PLVDataUtil dataForHexString:string];
    NSData *base64Data = [PLVDataUtil AES128DecryptData:encryptedData withKey:@"polyvliveSDKAuth" iv:@"1111000011110000"];
    if (base64Data) {
        NSData *decryptedData = [[NSData alloc] initWithBase64EncodedData:base64Data options:NSDataBase64DecodingIgnoreUnknownCharacters];
        if (decryptedData) {
            NSError *parseErr = nil;
            NSDictionary *jsonDict = [NSJSONSerialization JSONObjectWithData:decryptedData options:NSJSONReadingMutableContainers error:&parseErr];
            if (parseErr) {
                PLVF_NORMAL_LOG_ERROR(@"BVOLCPlayer", @"PLVBPublicStreamVOLCPlayer - parseErr:%@",parseErr.localizedDescription);
            }
            return jsonDict;
        }else{
            PLVF_NORMAL_LOG_ERROR(@"BVOLCPlayer", @"PLVBPublicStreamVOLCPlayer - streamInfo, decrypt failed-2");
            return nil;
        }
    }else{
        PLVF_NORMAL_LOG_ERROR(@"BVOLCPlayer", @"PLVBPublicStreamVOLCPlayer - streamInfo, decrypt failed-1");
        return nil;
    }
}

#pragma mark - [ Delegate ]
#pragma mark 公共流回调
- (void)rtcEngine:(ByteRTCEngineKit *)engine onPlayPublicStreamResult:(NSString *)publicStreamId errorCode:(int)errorCode {
    if (errorCode != 0) {
        PLVF_NORMAL_LOG_ERROR(@"BVOLCPlayer", @"PLVBPublicStreamVOLCPlayer - playPublicStream error code %ld",(long)errorCode);
    }
}

- (void)rtcEngine:(ByteRTCEngineKit *)engine onPublicStreamStats:(NSString *)publicStreamId audioStats:(const ByteRTCRemoteAudioStats *)audioStats videoStats:(const ByteRTCRemoteVideoStats *)videoStats {
    if ([self.delegate respondsToSelector:@selector(plvbPublicStreamPlayer:videoLossRate:audioLossRate:)]) {
        [self.delegate plvbPublicStreamPlayer:self videoLossRate:videoStats.videoLossRate audioLossRate:audioStats.audioLossRate];
    }
}

#endif
@end
