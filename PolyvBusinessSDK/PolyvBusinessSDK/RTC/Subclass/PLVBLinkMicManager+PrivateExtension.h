//
//  PLVBLinkMicManager+PrivateExtension.h
//  PLVBusinessSDK
//
//  Created by <PERSON><PERSON> on 2020/4/16.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVBLinkMicManager.h"

#import <PLVFoundationSDK/PLVFoundationSDK.h>

NS_ASSUME_NONNULL_BEGIN

@interface PLVBLinkMicManager ()

#pragma mark 数据
@property (nonatomic, assign) PLVBLinkMicRTCType rtcType;
@property (nonatomic, strong) PLVBRTCVideoViewCanvasModel * currentLocalPreviewCanvasModel;
@property (nonatomic, strong) PLVBRTCVideoEncoderConfiguration * currentVideoEncoderConfiguration;

#pragma mark 账户相关
@property (nonatomic, copy) NSString * rtcAppId;
@property (nonatomic, copy) NSString * rtcAppSign;
@property (nonatomic, copy) NSString * rtcToken;
@property (nonatomic, copy) NSString * appGroup;

#pragma mark 频道信息
@property (nonatomic, copy) NSString * teacherId;     // 讲师连麦ID不为频道号时，使用该属性判断是否为讲师
@property (nonatomic, copy) NSString * channelId;     // 频道号ID（房间号）
@property (nonatomic, copy) NSString * userLinkMicId; // 本地用户连麦ID

#pragma mark 状态信息
@property (nonatomic, assign) BOOL rtcTokenAvailable;
@property (nonatomic, assign) BOOL hadJoinedRTC;
@property (nonatomic, assign) PLVBLinkMicConnectionStateType connectionState;
@property (nonatomic, assign) PLVBLinkMicNetworkQuality networkQuality DEPRECATED_MSG_ATTRIBUTE("已废弃，请使用localNetworkQuality");
@property (nonatomic, assign) PLVBRTCNetworkQuality localNetworkQuality;
@property (nonatomic, assign) PLVBLinkMicRoleType roleType;
@property (nonatomic, assign) BOOL streamPublishing;
@property (nonatomic, assign) BOOL screenCaptureStarted;

@property (nonatomic, assign) PLVBRTCVideoMirrorMode localVideoMirrorMode;
@property (nonatomic, assign) BOOL localCameraFront;
@property (nonatomic, assign) BOOL localCameraTorchOpen;
@property (nonatomic, assign) BOOL localCameraOpen;
@property (nonatomic, assign) BOOL localVideoStreamMute;
@property (nonatomic, assign) BOOL localVideoStreamPlaceholderOpen;
@property (nonatomic, assign) BOOL localVideoEnable;

@property (nonatomic, assign) CGFloat localMicVolume;
@property (nonatomic, assign) BOOL localMicOpen;
@property (nonatomic, assign) BOOL localAudioEnable;
@property (nonatomic, assign) BOOL localAudioStreamMute;
@property (nonatomic, assign) PLVBLinkMicNoiseCancellationLevel localNoiseCancellationLevel;
@property (nonatomic, assign) BOOL localExternalDeviceEnabled;

#pragma mark 推流信息
@property (nonatomic, assign) NSUInteger videoCDNWidth;      // CDN宽度
@property (nonatomic, assign) NSUInteger videoCDNHeight;     // CDN高度
@property (nonatomic, assign) PLVBRTCStreamSourceType publishStreamSourceType; // 当前 推流的源类型

#pragma mark 渲染相关
@property (nonatomic, strong) EAGLContext *eaglContext;  // opengl上下文

/// 配置RtcToken相关信息
///
/// 由子类实现具体方法
///
/// @param decodeDict 父类解密后的Token信息
- (void)setupRTCToken:(NSDictionary *)decodeDict;

#pragma mark For Subclass
- (NSError *)errorWithCode:(NSInteger)code errorDescription:(NSString *)errorDes;

#pragma mark For Subclass Callback
- (void)callbackForCanSetupLocalHardwareDefaultState;

- (void)callbackForDidOccurError:(NSError *)error;

@end

NS_ASSUME_NONNULL_END
