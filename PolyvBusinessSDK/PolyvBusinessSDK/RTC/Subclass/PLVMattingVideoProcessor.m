#import "PLVMattingVideoProcessor.h"
#import <UIKit/UIKit.h>

@interface PLVMattingVideoProcessor ()
@property (nonatomic, strong) VNGeneratePersonSegmentationRequest *segmentationRequest API_AVAILABLE(ios(15.0));
@property (nonatomic, strong) CIImage *backgroundImage; // 替换背景

@property (nonatomic, assign) NSInteger count;
@property (nonatomic, strong) CIImage *maskImage;

@property (nonatomic, strong) CIContext *ciContext;
// 在类中添加一个 buffer pool
@property (nonatomic, assign) CVPixelBufferPoolRef pixelBufferPool;
@property (nonatomic, assign) CGSize resolutionSize; // 图像大小

@end

@implementation PLVMattingVideoProcessor

- (instancetype)init {
    self = [super init];
    if (self) {
        
        [self setupVision];
    }
    return self;
}

- (void)dealloc {
    if (_pixelBufferPool) {
        CVPixelBufferPoolRelease(_pixelBufferPool);
        _pixelBufferPool = NULL;
    }
}

// 初始化 buffer pool
- (void)setupPixelBufferPoolWithWidth:(int)width height:(int)height {
    if (self.pixelBufferPool) {
        CVPixelBufferPoolRelease(self.pixelBufferPool);
        _pixelBufferPool = NULL;
    }
    
    NSDictionary *attributes = @{
        (id)kCVPixelBufferPixelFormatTypeKey: @(kCVPixelFormatType_32BGRA),
        (id)kCVPixelBufferWidthKey: @(width),
        (id)kCVPixelBufferHeightKey: @(height),
        (id)kCVPixelBufferIOSurfacePropertiesKey: @{}
    };
    
    CVPixelBufferPoolCreate(kCFAllocatorDefault, NULL, (__bridge CFDictionaryRef)attributes, &_pixelBufferPool);
    self.resolutionSize = CGSizeMake(width, height);
}

// 从 pool 获取 buffer
- (CVPixelBufferRef)createPixelBufferFromPool:(CGSize )resolutionSize {
    CVPixelBufferRef pixelBuffer = NULL;
    CVReturn status = CVPixelBufferPoolCreatePixelBuffer(kCFAllocatorDefault, self.pixelBufferPool, &pixelBuffer);
    return (status == kCVReturnSuccess) ? pixelBuffer : NULL;
}

// 配置Vision请求
- (void)setupVision {
    if (@available(iOS 15.0, *)) {
        self.segmentationRequest = [[VNGeneratePersonSegmentationRequest alloc] initWithCompletionHandler:^(VNRequest * _Nonnull request, NSError * _Nullable error) {
            if (error) {
                NSLog(@"人物分割失败: %@", error.localizedDescription);
                return;
            }
        }];
    } else {
        // Fallback on earlier versions
    }
    if (@available(iOS 15.0, *)) {
        self.segmentationRequest.qualityLevel = VNGeneratePersonSegmentationRequestQualityLevelBalanced;
    } else {
        // Fallback on earlier versions
    } // 平衡性能与精度

    // 优化 CIContext 配置以提高性能
    NSDictionary *options = @{
        kCIContextWorkingColorSpace: [NSNull null], // 禁用颜色管理以提高性能
        kCIContextOutputColorSpace: [NSNull null],
        kCIContextUseSoftwareRenderer: @NO, // 强制使用 GPU
        kCIContextCacheIntermediates: @NO // 缓存中间结果
    };
    
    self.ciContext = [CIContext contextWithOptions:options];
    
    // 暂时不需要缓冲池了 简化了图像数据处理
//    self.resolutionSize = CGSizeMake(720, 1280);
//    [self setupPixelBufferPoolWithWidth:self.resolutionSize.width height:self.resolutionSize.height];
}

- (int)handleAIMatting:(CVPixelBufferRef )pixelBufferRef mode:(PLVBLinkMicAIMattingMode)mode image:(UIImage *)image{
    CVPixelBufferRef pixelBuffer = pixelBufferRef;
    if (!pixelBuffer) return -1;
    
    if (mode == PLVBLinkMicAIMattingModeNone){
        return -1;
    }
    
    // 更新mask
    VNImageRequestHandler *requestHandler = [[VNImageRequestHandler alloc] initWithCVPixelBuffer:pixelBuffer options:@{}];
    NSError *error = nil;
    if (@available(iOS 15.0, *)) {
        [requestHandler performRequests:@[self.segmentationRequest] error:&error];
    } else {
        // Fallback on earlier versions
    }
    if (error) {
        NSLog(@"Vision请求失败: %@", error.localizedDescription);
        return -1;
    }
    
    if (@available(iOS 15.0, *)) {
        VNPixelBufferObservation *result = self.segmentationRequest.results.firstObject;
        if (!result) return -1;
        self.maskImage = [CIImage imageWithCVPixelBuffer:result.pixelBuffer];
    } else {
        // Fallback on earlier versions
    }
    
    // 获取前景和蒙版
    CIImage *inputImage = [CIImage imageWithCVPixelBuffer:pixelBuffer];
    CIImage *maskImage = self.maskImage;
    
    self.maskImage = maskImage;
    
    // 根据前景和蒙版的大小计算缩放因子
    CGSize inputSize = inputImage.extent.size;
    CGSize maskSize = maskImage.extent.size;
    
    CGFloat scaleX = inputSize.width / maskSize.width;
    CGFloat scaleY = inputSize.height / maskSize.height;
    
    //  缩放蒙版
    maskImage = [maskImage imageByApplyingTransform:CGAffineTransformMakeScale(scaleX, scaleY)];
    
    // 调整背景图片
    UIImage *adjustImage = [self resizeImage:image toSize:inputSize];
    
    // 打印图像大小
//    NSLog(@"Input Image Size: %@", NSStringFromCGSize(inputImage.extent.size));
//    NSLog(@"Mask Image Size: %@", NSStringFromCGSize(maskImage.extent.size));
//    NSLog(@"Background Image Size: %@", NSStringFromCGSize(self.backgroundImage.extent.size));
//    NSLog(@"adjustImage Image Size: %@", NSStringFromCGSize(adjustImage.size));
        
    CIImage *outputImage = nil;
    if (mode == PLVBLinkMicAIMattingModeCustomImage){
        //
        self.backgroundImage = [CIImage imageWithCGImage:adjustImage.CGImage];
        outputImage = [self blendImagesWithForeground:inputImage mask:maskImage background:self.backgroundImage];
    }
    else if (mode == PLVBLinkMicAIMattingModeBlue){
        // 背景模糊
        // 尽量减少数据类型转化 高斯模糊后直接获取CIImage
        self.backgroundImage = [self ciImageApplyBlurToPixelBuffer:pixelBuffer radius:15];
        outputImage = [self blendImagesWithForeground:inputImage mask:maskImage background:self.backgroundImage];
    }
    
    if (outputImage) {
        // 直接将 outputImage 渲染到输入的 pixelBufferRef
        [self renderCIImage:outputImage toPixelBuffer:pixelBuffer];
    }
    
    return 0;
}

- (CIImage *)ciImageApplyBlurToPixelBuffer:(CVPixelBufferRef)pixelBuffer radius:(CGFloat)radius {
    if (!pixelBuffer) {
        return nil;
    }
    
    // Lock the pixel buffer
    CVPixelBufferLockBaseAddress(pixelBuffer, 0);
    
    // Create a CIImage from the pixel buffer
    CIImage *inputImage = [CIImage imageWithCVPixelBuffer:pixelBuffer];
    
    // Create and configure the blur filter
    CIFilter *blurFilter = [CIFilter filterWithName:@"CIGaussianBlur"];
    [blurFilter setValue:inputImage forKey:kCIInputImageKey];
    [blurFilter setValue:@(radius) forKey:kCIInputRadiusKey];
    
    // Get the output image
    CIImage *outputImage = [blurFilter valueForKey:kCIOutputImageKey];

     // Unlock the input pixel buffer
    CVPixelBufferUnlockBaseAddress(pixelBuffer, 0);
    
    return outputImage;
}

/**
 将 UIImage 调整到指定大小
 
 @param image 原始 UIImage 对象
 @param size 目标大小
 @return 调整大小后的 UIImage 对象
 */
- (UIImage *)resizeImage:(UIImage *)image toSize:(CGSize)size {
    UIGraphicsBeginImageContextWithOptions(size, NO, image.scale);
    [image drawInRect:CGRectMake(0, 0, size.width, size.height)];
    UIImage *resizedImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return resizedImage;
}

// 图像合成
- (CIImage *)blendImagesWithForeground:(CIImage *)foreground mask:(CIImage *)mask background:(CIImage *)background {
    CIFilter *blendFilter = [CIFilter filterWithName:@"CIBlendWithMask"];
    
    
    [blendFilter setValue:foreground forKey:kCIInputImageKey];
    [blendFilter setValue:background forKey:kCIInputBackgroundImageKey];
    [blendFilter setValue:mask forKey:kCIInputMaskImageKey];
    
    return blendFilter.outputImage;
}

- (void)renderCIImage:(CIImage *)ciImage toPixelBuffer:(CVPixelBufferRef)pixelBuffer {
    if (!ciImage || !pixelBuffer) {
        return;
    }
    
    // 锁定像素缓冲区进行写入
    CVPixelBufferLockBaseAddress(pixelBuffer, 0);
    
    // 渲染到现有的 CVPixelBuffer，添加错误处理
    @try {
        [self.ciContext render:ciImage toCVPixelBuffer:pixelBuffer];
    } @catch (NSException *exception) {
        NSLog(@"CIContext 渲染异常: %@", exception.reason);
    } @finally {
        // 确保在任何情况下都解锁像素缓冲区
        CVPixelBufferUnlockBaseAddress(pixelBuffer, 0);
    }
}

@end
