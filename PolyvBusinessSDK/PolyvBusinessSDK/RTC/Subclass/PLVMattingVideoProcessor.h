//
//  PLVMattingVideoProcessor.h
//  AIMattings
//
//  Created by polyv on 2025/2/20.
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>
#import <Vision/Vision.h>
#import <CoreImage/CoreImage.h>
#import <UIKit/UIKit.h>
#import "PLVBRTCDefine.h"

@interface PLVMattingVideoProcessor : NSObject <AVCaptureVideoDataOutputSampleBufferDelegate>

/// AI抠像处理
/// @param pixelBufferRef 输入图像数据
/// @param mode 抠像模式
/// @param image 自定义背景图
/// @param 0 成功  其他失败
- (int)handleAIMatting:(CVPixelBufferRef )pixelBufferRef
                  mode:(PLVBLinkMicAIMattingMode)mode
                 image:(UIImage *)image;


@end
