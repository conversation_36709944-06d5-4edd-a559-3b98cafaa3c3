//
//  PLVBLinkMicUCloudManager.m
//  PLVBusinessSDK
//
//  Created by <PERSON><PERSON> on 2020/4/16.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVBLinkMicUCloudManager.h"

#import "PLVBLinkMicManager+PrivateExtension.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

#if __has_include(<UCloudRtcSdk_ios/UCloudRtcSdk_ios.h>)
    #import <UCloudRtcSdk_ios/UCloudRtcSdk_ios.h>
    #define UCloudRtcSdk_ios
#endif

#define UCloudRtcStreamStatsInfoCLS NSClassFromString(@"UCloudRtcStreamStatsInfo")
#define UCloudRtcStreamCLS NSClassFromString(@"UCloudRtcStream")
#define UCloudRtcAudioStatsCLS NSClassFromString(@"UCloudRtcAudioStats")
#define UCloudRtcEngineCLS NSClassFromString(@"UCloudRtcEngine")
#define UCloudRtcMixConfigCLS NSClassFromString(@"UCloudRtcMixConfig")

@interface PLVBLinkMicUCloudSubscribeModel : NSObject

@property (nonatomic, weak) UIView * renderSuperView;
@property (nonatomic, copy) NSString * userRTCId;
@property (nonatomic, copy) NSString * subscribedStreamId;
@property (nonatomic, assign) PLVBRTCSubscribeStreamMediaType mediaType;
@property (nonatomic, assign) PLVBRTCSubscribeStreamSubscribeMode subscribeMode;

+ (instancetype)modelWithUserRTCId:(NSString *)userRTCId renderSuperView:(UIView *)renderSuperView subscribeMediaType:(PLVBRTCSubscribeStreamMediaType)subscribeMediaType subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode;

+ (NSString *)subscribeModelKeyWithUserRTCId:(NSString *)userRTCId subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode;

- (NSString *)subscribeModelKey;

@end

@implementation PLVBLinkMicUCloudSubscribeModel

+ (instancetype)modelWithUserRTCId:(NSString *)userRTCId renderSuperView:(UIView *)renderSuperView subscribeMediaType:(PLVBRTCSubscribeStreamMediaType)subscribeMediaType subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode{
    if (![PLVFdUtil checkStringUseable:userRTCId] ||
        !renderSuperView ||
        ![renderSuperView isKindOfClass:UIView.class]) {
        return nil;
    }
    PLVBLinkMicUCloudSubscribeModel * model = [[PLVBLinkMicUCloudSubscribeModel alloc] init];
    model.userRTCId = userRTCId;
    model.renderSuperView = renderSuperView;
    model.mediaType = subscribeMediaType;
    model.subscribeMode = subscribeMode;
    return model;
}

+ (NSString *)subscribeModelKeyWithUserRTCId:(NSString *)userRTCId subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode{
    NSString * subscribeModeString;
    if (subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_Camera) {
        subscribeModeString = @"Camera";
    }else if(subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_Screen){
        subscribeModeString = @"Screen";
    }else{
        subscribeModeString = @"ScreenFirst_Mix";
    }
    NSString * subscribeModelKey = [NSString stringWithFormat:@"%@-%@",userRTCId,subscribeModeString];
    return subscribeModelKey;
}

- (NSString *)subscribeModelKey{
    return [PLVBLinkMicUCloudSubscribeModel subscribeModelKeyWithUserRTCId:self.userRTCId subscribeMode:self.subscribeMode];
}

@end

@interface PLVBLinkMicUCloudManager ()
#ifdef UCloudRtcSdk_ios
<UCloudRtcEngineDelegate>
{
    EAGLContext *glcontext;
}
#pragma mark RTC对象
@property (nonatomic, strong) UCloudRtcEngine * engine;
@property (nonatomic, strong) UCloudRtcMixConfig * currentMixConfig;/// 当前的转推配置

#pragma mark 数据维护
@property (nonatomic, strong) NSMutableDictionary <NSString *, PLVBLinkMicUCloudSubscribeModel *> * wantSubscribeModelDict;
@property (nonatomic, strong) NSMutableDictionary * roomStreamDict; /// 房间流字典
@property (nonatomic, strong) NSMutableDictionary * subscribedStreamDict; /// 已订阅流字典 (注意: 需收到‘已订阅回调’的，才被认为是'已订阅')
@property (nonatomic, strong) NSMutableDictionary * volumeDict;
@property (nonatomic, assign) int resumeValueCount; /// 恢复原始值次数
@property (nonatomic, assign) CFAbsoluteTime lastSubscribeTime; ///记录上一次订阅流的时间
@property (nonatomic, assign) NSTimeInterval delaySubscribeInterval; ///需要延迟订阅的毫秒数默认为0

#pragma mark 状态
@property (nonatomic, assign) BOOL originalIsGeneratingDeviceOrientationNotificationsValue; /// 原始转屏状态值
@property (nonatomic, assign) BOOL hadSetupDefault; /// 是否已经设置过默认值
@property (nonatomic, assign) BOOL hadSetupRtcOrientationMode; /// 是否已经设置过采集方向
@property (nonatomic, assign) UCloudRtcEnginePublishState localStreamPublishState; /// 本地流发布状态
@property (nonatomic, assign) NSInteger currentReasonableRtcOrientationMode; /// 当前合理的采集方向

#pragma mark 功能对象
@property (nonatomic, strong) dispatch_queue_t dictSafeQueue;
@property (nonatomic, strong) NSTimer * reportAudioVolumeTimer;
@property (nonatomic, copy) void (^joinRTCChannelBlock) (void);
@property (nonatomic, strong) PLVImageUtil *imageUtils;

#endif
@end

@implementation PLVBLinkMicUCloudManager
#ifdef UCloudRtcSdk_ios

@synthesize roleType = _roleType;
@synthesize subscribedRemoteStreamNeedMute = _subscribedRemoteStreamNeedMute;

#pragma mark - [ Life Period ]
- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [self destroyReportAudioVolumeTimer];
}

- (instancetype)init{
    if (self = [super init]) {
        self.currentReasonableRtcOrientationMode = -1;
        self.delaySubscribeInterval = 0;
        _subscribedRemoteStreamNeedMute = NO;
    }
    return self;
}


#pragma mark - [ Father Public Methods ]
#pragma mark 基础调用
- (void)createRTCEngine{
    if (!_engine) {
        if (![PLVFdUtil checkStringUseable:self.rtcAppId]) {
            PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - init engine failed,appId is nil %@",self.rtcAppId);
            return;
        }
        _engine = [[UCloudRtcEngineCLS alloc]initWithAppID:self.rtcAppId appKey:self.rtcAppSign completionBlock:^(int errorCode) {
            if (errorCode != 0) {
                PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - create engine failed, errorCode %d",errorCode);
            }else{
                PLVF_NORMAL_LOG_INFO(@"BLinkMic", @"PLVBLinkMicManager - create engine success");
            }
        }];
        _engine.engineMode = UCloudRtcEngineModeNormal;
        _engine.delegate = self;
        _engine.isAutoSubscribe = NO; // 加入房间后将自动订阅远端音视频 默认为YES
        _engine.isAutoPublish = NO;   // 加入房间后将自动发布本地音视频 默认为YES
        _engine.isOnlyAudio = NO;     // 将启用纯音频模式 默认为NO
        _engine.isTrackVolume = YES;
        _engine.enableScreenAudio = YES; // 是否开启桌面共享的音频
        
        [_engine.logger setLogLevel:UCloudRtcLogLevel_OFF];
        #ifdef DEBUG
        // [_engine.logger setLogLevel:UCloudRtcLogLevel_DEBUG];
        [_engine.logger setLogLevel:UCloudRtcLogLevel_ERROR];
        #endif
                
        [self statusBarOrientationChange:nil];
        
        // 201201，Lincal，UCloud 1.6.5
        // 对”默认状态的设置“，并不适合用UCloud公开的以下两项属性
        // _engine.enableLocalAudio = self.micDefaultOpen;
        // _engine.enableLocalVideo = self.cameraDefaultOpen;
        
        // (即rtc模式)  UCloudRtcEngineRoomType_Communicate = 0,// 互动学堂、视频会议 默认值
        // (即live模式) UCloudRtcEngineRoomType_Broadcast = 1,  // 大班课
        // 若 开播端此模式 与 移动端此模式 不一致，将导致二者之间无法通信（画面不可见、麦克风摄像头控制的回调不触发）
        _engine.roomType = UCloudRtcEngineRoomType_Broadcast;
        PLVF_NORMAL_LOG_INFO(@"BLinkMic", @"PLVBLinkMicManager - urtc version %@",[UCloudRtcEngineCLS currentVersion]);
        
        self.localAudioStreamMute = NO;
        self.localVideoStreamMute = NO;
        self.hadSetupRtcOrientationMode = NO;
        self.localStreamPublishState = UCloudRtcEnginePublishStateUnPublish;
        self.publishStreamSourceType = PLVBRTCStreamSourceType_Camera;
        
        [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(statusBarOrientationChange:)
                                             name:UIApplicationDidChangeStatusBarOrientationNotification object:nil];
        
        glcontext = [[EAGLContext alloc] initWithAPI:kEAGLRenderingAPIOpenGLES3];
        if ([EAGLContext currentContext] != glcontext) {
            [EAGLContext setCurrentContext:glcontext];
        }
        self.eaglContext = glcontext;
        self.imageUtils = [[PLVImageUtil alloc] init];
    }
}

- (void)destroyRTCEngine{
    __weak typeof(self) weakSelf = self;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_MSEC)), dispatch_get_main_queue(), ^{
        weakSelf.engine = nil;
        weakSelf.engine.delegate = nil;
    });
}

- (int)joinRtcChannelWithChannelId:(NSString *)channelId userLinkMicId:(NSString *)userLinkMicId{
    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:userLinkMicId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - join rtc channel failed,channelId:%@, userLinkMicId:%@",channelId,userLinkMicId);
        return -1;
    }else{
        self.channelId = channelId;
        self.userLinkMicId = userLinkMicId;
    }
    
    self.originalIsGeneratingDeviceOrientationNotificationsValue = [[UIDevice currentDevice] isGeneratingDeviceOrientationNotifications];
    self.resumeValueCount = 0;
    [self createRTCEngine];
    [self createReportAudioVolumeTimer];
    [self createResource];

    /// 获取流权限并更新
    NSString * UCStreamAuthority;
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManagerGetUCStreamAuthority:)]) {
        UCStreamAuthority = [self.delegate plvbLinkMicManagerGetUCStreamAuthority:self];
        
        if ([PLVFdUtil checkStringUseable:UCStreamAuthority]) {
            if ([UCStreamAuthority isEqualToString:@"Upload"]) {
                self.engine.streamProfile = UCloudRtcEngine_StreamProfileUpload; // 设置流权限
            }else if([UCStreamAuthority isEqualToString:@"All"]){
                self.engine.streamProfile = UCloudRtcEngine_StreamProfileAll; // 设置流权限
            }else{
                PLVF_NORMAL_LOG_WARN(@"BLinkMic", @"PLVBLinkMicManager - join rtc channel error, return authority illegal, will be default 'all'");
                self.engine.streamProfile = UCloudRtcEngine_StreamProfileAll; // 设置默认流权限
            }
        }else{
            PLVF_NORMAL_LOG_WARN(@"BLinkMic", @"BLinkMic", @"PLVBLinkMicManager - join rtc channel error, authority nil, will be default 'all'");
            self.engine.streamProfile = UCloudRtcEngine_StreamProfileAll; // 设置默认流权限
        }
    }else{
        PLVF_NORMAL_LOG_WARN(@"BLinkMic", @"PLVBLinkMicManager - join rtc channel error, method not be implement, will be default 'all'");
        self.engine.streamProfile = UCloudRtcEngine_StreamProfileAll; // 设置默认流权限
    }
    
    __weak typeof(self) weakSelf = self;
    [self.engine joinRoomWithRoomId:self.channelId userId:self.userLinkMicId token:self.rtcToken completionHandler:^(NSDictionary * _Nonnull response, int errorCode) {
        //NSLog(@"PLVBLinkMicManager - joinRoom response %@ errorCode %d",response,errorCode);
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - joinRoom errorCode %d",errorCode);
        if (errorCode == 0) {
            PLVF_NORMAL_LOG_INFO(@"BLinkMic", @"PLVBLinkMicManager - join rtc channel success,channel:%@, uid:%@", weakSelf.channelId, weakSelf.userLinkMicId);
            
            weakSelf.hadJoinedRTC = YES;
            
            dispatch_async(dispatch_get_main_queue(), ^{
                if (self.joinRTCChannelBlock){
                    self.joinRTCChannelBlock();
                    self.joinRTCChannelBlock = nil;
                }
                
                if ([weakSelf.delegate respondsToSelector:@selector(plvbLinkMicManager:joinRTCChannelComplete:uid:)]) {
                    [weakSelf.delegate plvbLinkMicManager:weakSelf joinRTCChannelComplete:weakSelf.channelId uid:weakSelf.userLinkMicId];
                }
                            
                if ([weakSelf.delegate respondsToSelector:@selector(plvbLinkMicManager:didJoinedOfUid:)]) { // 对应声网开播逻辑，需回调一次老师进入
                    [weakSelf.delegate plvbLinkMicManager:weakSelf didJoinedOfUid:weakSelf.channelId];
                }
            });
        }else{
            PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - join rtc channel failed, errorCode:%d",errorCode);
            
            dispatch_async(dispatch_get_main_queue(), ^{
                if ([weakSelf.delegate respondsToSelector:@selector(plvbLinkMicManager:joinRTCChannelFailure:uid:)]) {
                    [weakSelf.delegate plvbLinkMicManager:weakSelf joinRTCChannelFailure:weakSelf.channelId uid:weakSelf.userLinkMicId];
                }
            });
        }
    }];
    return 0;
}

- (int)leaveRtcChannel{
    if (dispatch_queue_get_label(DISPATCH_CURRENT_QUEUE_LABEL) == dispatch_queue_get_label(dispatch_get_main_queue())) {
        if (self.hadJoinedRTC) {
            if (self.localStreamPublishState == UCloudRtcEnginePublishStatePublishing ||
                self.localStreamPublishState == UCloudRtcEnginePublishStatePublishSucceed ||
                self.localStreamPublishState == UCloudRtcEnginePublishStateRepublishing){
                [self stopPushLocalStream];
            }
            int ret = [self.engine leaveRoom];
            [self destroyStreamManageResource];
            if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:leaveRTCChannelComplete:)]) {
                [self.delegate plvbLinkMicManager:self leaveRTCChannelComplete:self.channelId];
            }
            [self destroyResource];
            return ret;
        }else{
            return 0;
        }
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self leaveRtcChannel];
        });
        return 0;
    }
}

- (void)switchSubscribeStreamMediaTypeWithRTCUserId:(NSString *)rtcUserId mediaType:(PLVBRTCSubscribeStreamMediaType)toMediaType{
    BOOL remoteUser = ![rtcUserId isEqualToString:self.userLinkMicId];
    if (remoteUser) {
        PLVBLinkMicUCloudSubscribeModel * model;
        NSArray <PLVBLinkMicUCloudSubscribeModel *> * modelArray = [self readRTCUserSubscribeModelArray:rtcUserId];
        /// 通过遍历，找到对应的 订阅Model
        for (PLVBLinkMicUCloudSubscribeModel * modelInArray in modelArray) {
            if (modelInArray && [modelInArray isKindOfClass:PLVBLinkMicUCloudSubscribeModel.class]) {
                model = modelInArray;
                break;
            }
        }
        
        if (model) {
            model.mediaType = toMediaType;
            
            BOOL isTeacher = NO;
            if ([PLVFdUtil checkStringUseable:self.teacherId]) {
                isTeacher = [rtcUserId isEqualToString:self.teacherId];
            } else {
                isTeacher = [rtcUserId isEqualToString:self.channelId];
            }

            UIView * superView = model.renderSuperView;
            
            NSString * streamDictKey_video = [rtcUserId stringByAppendingString:@"-video"];
            NSString * streamDictKey_screen = [rtcUserId stringByAppendingString:@"-screen"];
            
            UCloudRtcStream * streamInRoom_video = [self.subscribedStreamDict objectForKey:streamDictKey_video];
            if (streamInRoom_video && [streamInRoom_video isKindOfClass:UCloudRtcStreamCLS.class]) {
                BOOL subscribeAudio = model.mediaType & PLVBRTCSubscribeStreamMediaType_Audio;
                [self.engine setRemoteStream:streamInRoom_video muteAudio:!subscribeAudio];
                
                BOOL subscribeVideo = model.mediaType & PLVBRTCSubscribeStreamMediaType_Video;
                plv_dispatch_main_async_safe(^{
                    for (UIView * subview in superView.subviews) { [subview removeFromSuperview]; }
                    if (subscribeVideo) {
                        if (superView && [superView isKindOfClass:UIView.class]) {
                            streamInRoom_video.remoteVideoViewMode = isTeacher ? UCloudRtcVideoViewModeScaleAspectFit : UCloudRtcVideoViewModeScaleAspectFill;
                            [streamInRoom_video renderOnView:superView];
                        }
                    }
                })
                [self.engine setRemoteStream:streamInRoom_video muteVideo:!subscribeVideo];
            }
            
            UCloudRtcStream * streamInRoom_screen = [self.subscribedStreamDict objectForKey:streamDictKey_screen];
            if (streamInRoom_screen && [streamInRoom_screen isKindOfClass:UCloudRtcStreamCLS.class]) {
                BOOL subscribeAudio = model.mediaType & PLVBRTCSubscribeStreamMediaType_Audio;
                [self.engine setRemoteStream:streamInRoom_screen muteAudio:!subscribeAudio];
                
                BOOL subscribeVideo = model.mediaType & PLVBRTCSubscribeStreamMediaType_Video;
                plv_dispatch_main_async_safe(^{
                    for (UIView * subview in superView.subviews) { [subview removeFromSuperview]; }
                    if (subscribeVideo) {
                        if (superView && [superView isKindOfClass:UIView.class]) {
                            [streamInRoom_screen renderOnView:superView];
                        }
                    }
                })
                [self.engine setRemoteStream:streamInRoom_screen muteVideo:!subscribeVideo];
            }
        }
    }
}

- (void)switchPublishStreamSourceType:(PLVBRTCStreamSourceType)streamSourceType{
    if (![PLVFdUtil checkStringUseable:self.appGroup]) {
        PLVF_NORMAL_LOG_WARN(@"BLinkMic", @"PLVBLinkMicManager - switchPublishStreamSourceType failed, Please setup appGroup first");
        return;
    }
    
    if (self.publishStreamSourceType == streamSourceType) {
        return;
    }
    
    self.publishStreamSourceType = streamSourceType;
    plv_dispatch_main_async_safe(^{
        if (streamSourceType == PLVBRTCStreamSourceType_Camera) {
            if (!self.screenCaptureStarted) {
                [self uCloudRtcEngineScreenCaptureDidFinished:self.engine reason:UCloudRtcScreenFinishedByEngine];
            }

            [self.engine unpublishWithMediaType:UCloudRtcStreamMediaTypeScreen];
            if (@available(iOS 11.0, *)) {
                [self.engine stopScreenCapture];
            }
        } else if(streamSourceType == PLVBRTCStreamSourceType_Screen) {
            if (@available(iOS 11.0, *)) {
                [self.engine startScreenCaptureWithAppGroup:self.appGroup];
            }
            [self.engine publishWithMediaType:UCloudRtcStreamMediaTypeScreen];
  
            if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManagerDidScreenCaptureStarted:)]) {
                [self.delegate plvbLinkMicManagerDidScreenCaptureStarted:self];
            }
        }
    })
}

- (void)muteSubscribedRemoteStreamInLocalWithMute:(BOOL)mute{
    _subscribedRemoteStreamNeedMute = mute;
    
    for (PLVBLinkMicUCloudSubscribeModel *model in self.wantSubscribeModelDict.allValues) {
        BOOL isTeacher = NO;
        if ([PLVFdUtil checkStringUseable:self.teacherId]) {
            isTeacher = [model.userRTCId isEqualToString:self.teacherId];
        } else {
            isTeacher = [model.userRTCId isEqualToString:self.channelId];
        }

        UIView *superView = model.renderSuperView;

        NSString *streamDictKey_video = [model.userRTCId stringByAppendingString:@"-video"];
        NSString *streamDictKey_screen = [model.userRTCId stringByAppendingString:@"-screen"];

        UCloudRtcStream *streamInRoom_video = [self.subscribedStreamDict objectForKey:streamDictKey_video];
        if (streamInRoom_video && [streamInRoom_video isKindOfClass:UCloudRtcStreamCLS.class]) {
            [self.engine setRemoteStream:streamInRoom_video muteAudio:mute];
            plv_dispatch_main_async_safe(^{
                if (mute) {
                    for (UIView *subview in superView.subviews) {
                        [subview removeFromSuperview];
                    }
                } else {
                    if (superView && [superView isKindOfClass:UIView.class]) {
                        streamInRoom_video.remoteVideoViewMode = isTeacher ? UCloudRtcVideoViewModeScaleAspectFit : UCloudRtcVideoViewModeScaleAspectFill;
                        [streamInRoom_video renderOnView:superView];
                    }
                }
            })
            if (model.mediaType == PLVBRTCSubscribeStreamMediaType_Video) {
                [self.engine setRemoteStream:streamInRoom_video muteVideo:mute];
            }
        }

        UCloudRtcStream *streamInRoom_screen = [self.subscribedStreamDict objectForKey:streamDictKey_screen];
        if (streamInRoom_screen && [streamInRoom_screen isKindOfClass:UCloudRtcStreamCLS.class]) {
            [self.engine setRemoteStream:streamInRoom_screen muteAudio:mute];
            plv_dispatch_main_async_safe(^{
                if (mute) {
                    for (UIView *subview in superView.subviews) {
                        [subview removeFromSuperview];
                    }
                } else {
                    if (superView && [superView isKindOfClass:UIView.class]) {
                        [streamInRoom_screen renderOnView:superView];
                    }
                }
            })
            [self.engine setRemoteStream:streamInRoom_screen muteVideo:mute];
        }
    }
}


- (void)subscribeStreamWithRTCUserId:(NSString *)rtcUserId renderOnView:(UIView *)renderSuperView mediaType:(PLVBRTCSubscribeStreamMediaType)mediaType{
    [self subscribeStreamWithRTCUserId:rtcUserId renderOnView:renderSuperView mediaType:mediaType subscribeMode:PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix];
}

- (void)subscribeStreamWithRTCUserId:(NSString *)rtcUserId renderOnView:(UIView *)renderSuperView mediaType:(PLVBRTCSubscribeStreamMediaType)mediaType subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode{
    if (![PLVFdUtil checkStringUseable:rtcUserId] || !renderSuperView || ![renderSuperView isKindOfClass:UIView.class]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - add preview error uid %@ superView %@",rtcUserId,renderSuperView);
        return;
    }
    
    BOOL isTeacher = NO;
    if ([PLVFdUtil checkStringUseable:self.teacherId]) {
        isTeacher = [rtcUserId isEqualToString:self.teacherId];
    } else {
        isTeacher = [rtcUserId isEqualToString:self.channelId];
    }
    
    BOOL localUser = [rtcUserId isEqualToString:self.userLinkMicId];
    if (localUser) { // 本地
        [self.engine setPreviewMode:isTeacher ? UCloudRtcVideoViewModeScaleAspectFit : UCloudRtcVideoViewModeScaleAspectFill];
        [self.engine setLocalPreview:renderSuperView];
    } else { // 远端
        PLVBLinkMicUCloudSubscribeModel * model = [PLVBLinkMicUCloudSubscribeModel modelWithUserRTCId:rtcUserId renderSuperView:renderSuperView subscribeMediaType:mediaType subscribeMode:subscribeMode];
        if (model) {
            [self.wantSubscribeModelDict setObject:model forKey:[model subscribeModelKey]];
            
            /// ScreenFirst_Mix 时，对其他使用方式的排除
        }else{
            PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - subscribeStreamWithRTCUserId failed ,model illegal");
        }
        
        UCloudRtcStream * stream;
        
        NSString * streamDictKey_video = [rtcUserId stringByAppendingString:@"-video"];
        NSString * streamDictKey_screen = [rtcUserId stringByAppendingString:@"-screen"];
        
        UCloudRtcStream * streamInRoom_video = [self.roomStreamDict objectForKey:streamDictKey_video];
        UCloudRtcStream * streamInRoom_screen = [self.roomStreamDict objectForKey:streamDictKey_screen];
        
        if (subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix) {
            if (streamInRoom_video && streamInRoom_screen) {
                stream = streamInRoom_screen;
            }else if(streamInRoom_screen){
                stream = streamInRoom_screen;
            }else{
                stream = streamInRoom_video;
            }
        }else if(subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_Camera){
            stream = streamInRoom_video;
        }else if(subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_Screen){
            stream = streamInRoom_screen;
        }
        
        if (stream && [stream isKindOfClass:UCloudRtcStreamCLS.class]) {
            /// 211026 Lincal 以下‘时间判断逻辑’背景记录
            /// 一次性订阅16路流，会存在线程卡顿问题
            /// 最终云飞排查确认为 subscribeMethod 首次调用会极易导致卡线程；且后续 subscribeMethod调用，每次间隔必须有200ms以上，否则也会卡线程；
            /// 因此新增以下逻辑，作为UcloudSDK的临时使用兼容
            /// 后续仍需等 Ucloud 进行修复优化，Ucloud处理后，以下‘时间判断逻辑’可按实际情况移除
            if (CFAbsoluteTimeGetCurrent() - self.lastSubscribeTime < 0.1) {
                if (self.delaySubscribeInterval == 0) {
                    self.delaySubscribeInterval += 1000;
                } else {
                    self.delaySubscribeInterval += 200;
                }
                __weak typeof(self) weakSelf = self;
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(self.delaySubscribeInterval * NSEC_PER_MSEC)), dispatch_get_main_queue(), ^{
                    [weakSelf.engine subscribeMethod:stream];
                });
            } else {
                self.delaySubscribeInterval = 0;
                [self.engine subscribeMethod:stream];
            }
            self.lastSubscribeTime = CFAbsoluteTimeGetCurrent();
        }
    }
}

- (void)unsubscribeStreamWithRTCUserId:(NSString *)rtcUserId{
    [self unsubscribeStreamWithRTCUserId:rtcUserId subscribeMode:PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix];
}

- (void)unsubscribeStreamWithRTCUserId:(NSString *)rtcUserId subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode{
    if (![PLVFdUtil checkStringUseable:rtcUserId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - removeUserSubscribeUserId failed ,rtcUserId illegal %@",rtcUserId);
        return;
    }
    
    if (dispatch_queue_get_label(DISPATCH_CURRENT_QUEUE_LABEL) == dispatch_queue_get_label(dispatch_get_main_queue())) {
        BOOL localUser = [rtcUserId isEqualToString:self.userLinkMicId];
        if (localUser) { // 本地
            [self.engine setLocalPreview:nil];
        } else { // 远端
            NSArray <PLVBLinkMicUCloudSubscribeModel *> * modelArray = [self readRTCUserSubscribeModelArray:rtcUserId];
            PLVBLinkMicUCloudSubscribeModel * model;
            /// 通过遍历，找到对应的 订阅Model
            for (PLVBLinkMicUCloudSubscribeModel * modelInArray in modelArray) {
                if (modelInArray && [modelInArray isKindOfClass:PLVBLinkMicUCloudSubscribeModel.class]) {
                    if (modelInArray.subscribeMode == subscribeMode) { /// 混合流订阅方式
                        model = modelInArray;
                        break;
                    }
                }
            }
            
            UIView * superView = model.renderSuperView;
            plv_dispatch_main_async_safe(^{
                for (UIView * subview in superView.subviews) { [subview removeFromSuperview]; }
            })
            NSString * subscribeModelKeyString = [model subscribeModelKey];
            if ([PLVFdUtil checkStringUseable:subscribeModelKeyString]) { [self.wantSubscribeModelDict removeObjectForKey:[model subscribeModelKey]]; }

            NSString * streamDictKey_video = [rtcUserId stringByAppendingString:@"-video"];
            NSString * streamDictKey_screen = [rtcUserId stringByAppendingString:@"-screen"];

            UCloudRtcStream * streamInRoom_video = [self.subscribedStreamDict objectForKey:streamDictKey_video];
            if (streamInRoom_video && [streamInRoom_video isKindOfClass:UCloudRtcStreamCLS.class] &&
                (subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix || subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_Camera)){
                [self.engine unSubscribeMethod:streamInRoom_video];
                [self.subscribedStreamDict removeObjectForKey:streamInRoom_video];
            }
            
            UCloudRtcStream * streamInRoom_screen = [self.subscribedStreamDict objectForKey:streamDictKey_screen];
            if (streamInRoom_screen && [streamInRoom_screen isKindOfClass:UCloudRtcStreamCLS.class] &&
                (subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix || subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_Screen)) {
                [self.engine unSubscribeMethod:streamInRoom_screen];
                [self.subscribedStreamDict removeObjectForKey:streamInRoom_screen];
            }
        }
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self unsubscribeStreamWithRTCUserId:rtcUserId];
        });
    }
}

- (void)setTeacherUserId:(NSString *)teacherId {
    if (![PLVFdUtil checkStringUseable:teacherId]) {
        return;
    }
    
    self.teacherId = teacherId;
}

- (int)setUserRoleTo:(PLVBLinkMicRoleType)roleType{
    _roleType = roleType;
    if (roleType == PLVBLinkMicRoleBroadcaster) {
        if (self.localStreamPublishState == UCloudRtcEnginePublishStateUnPublish ||
            self.localStreamPublishState == UCloudRtcEnginePublishStatePublishFailed ||
            self.localStreamPublishState == UCloudRtcEnginePublishStatePublishStoped){
            __weak typeof(self) weakSelf = self;
            self.joinRTCChannelBlock = ^{
                [weakSelf startPushLocalStream];
            };
            
            if (self.hadJoinedRTC){
                self.joinRTCChannelBlock();
                self.joinRTCChannelBlock = nil;
            }
        }else{
            // NSLog(@"PLVBLinkMicManager - warn, no need switch role to %ld, publish state illegal %ld",roleType,self.localStreamPublishState);
        }
    }else{
        if (self.localStreamPublishState == UCloudRtcEnginePublishStatePublishing ||
            self.localStreamPublishState == UCloudRtcEnginePublishStatePublishSucceed ||
            self.localStreamPublishState == UCloudRtcEnginePublishStateRepublishing){
            if (self.hadJoinedRTC){
                [self stopPushLocalStream];
            }else{
                // NSLog(@"PLVBLinkMicManager - warn, no need to switch role to %ld, out of rtc room");
            }
        }else{
            // NSLog(@"PLVBLinkMicManager - warn, no need to switch role to %ld, publish state illegal %ld",roleType,self.localStreamPublishState);
        }
    }
    return 0;
}

#pragma mark 本地 摄像头 使用
- (void)setupVideoEncoderConfiguration:(PLVBRTCVideoEncoderConfiguration *)videoEncoderConfiguration{
    if (videoEncoderConfiguration && [videoEncoderConfiguration isKindOfClass:PLVBRTCVideoEncoderConfiguration.class]) {
        [super setupVideoEncoderConfiguration:videoEncoderConfiguration];

        UCloudRtcEngineVideoProfile targetVideoProfile = [self readURTCVideoResolution:videoEncoderConfiguration];
        self.engine.videoProfile = targetVideoProfile;
        if (self.localCameraFront) {
            if (videoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Auto ||
                videoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Enabled) {
                self.engine.videoEncoderMirrorMode = UCloudRtcVideoMirrorModeEnabled;
            }else{
                self.engine.videoEncoderMirrorMode = UCloudRtcVideoMirrorModeDisabled;
            }
        }else{
            /// UCloud 不支持后置摄像头镜像，此句仅表示固定为此值
            self.engine.videoEncoderMirrorMode = UCloudRtcVideoMirrorModeDisabled;
        }
                
        if (self.streamPublishing) {
            UCloudRtcMixConfig * mixConfig = self.currentMixConfig;
            mixConfig.bitrate = videoEncoderConfiguration.videoBitrate;
            mixConfig.width = self.videoCDNWidth;
            mixConfig.height = self.videoCDNHeight;
            mixConfig.framerate = videoEncoderConfiguration.videoFrameRate;
            [self.engine updateMixConfig:mixConfig];
        }
    }else{
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - setupVideoEncoderConfiguration failed, videoEncoderConfiguration invalid %@",videoEncoderConfiguration);
    }
}

- (void)setupLocalPreviewWithCanvasModel:(PLVBRTCVideoViewCanvasModel *)canvasModel{
    if (![canvasModel checkModelValid]) {
        NSLog(@"PLVBLinkMicManager - setupLocalPreviewWithCanvasModel failed, canvasModel invalid, userRTCId:%@ renderCanvasView:%@",canvasModel.userRTCId, canvasModel.renderCanvasView);
        return;
    }
    self.currentLocalPreviewCanvasModel = canvasModel;
    
    [self.engine setPreviewMode:(canvasModel.rtcVideoVideoFillMode == PLVBRTCVideoViewFillMode_Fit ? UCloudRtcVideoViewModeScaleAspectFit : UCloudRtcVideoViewModeScaleAspectFill)];
    [self.engine setLocalPreview:canvasModel.renderCanvasView];
}

- (int)setupLocalVideoPreviewMirrorMode:(PLVBRTCVideoMirrorMode)mirrorMode{
    if (self.localCameraFront) {
        if (mirrorMode == PLVBRTCVideoMirrorMode_Auto ||
            mirrorMode == PLVBRTCVideoMirrorMode_Enabled) {
            self.engine.mirrorMode = UCloudRtcVideoMirrorModeEnabled;
        }else{
            self.engine.mirrorMode = UCloudRtcVideoMirrorModeDisabled;
        }
    }else{
        /// UCloud 不支持后置摄像头镜像，此句仅表示固定为此值
        self.engine.mirrorMode = UCloudRtcVideoMirrorModeDisabled;
    }
    self.localVideoMirrorMode = mirrorMode;
    return 0;
}

- (int)switchLocalUserCamera:(BOOL)frontCamera{
    if (self.localCameraFront != frontCamera) {
        int resultCode = [self.engine switchCamera];
        if (resultCode == 0) {
            self.localCameraFront = frontCamera;
            if (frontCamera) { self.localCameraTorchOpen = NO; }
        }
        return resultCode;
    }else{
        return 0;
    }
}

- (int)openLocalUserCameraTorch:(BOOL)openCameraTorch{
    int resultCode = [self.engine setTorchMode:(openCameraTorch ? UCloudRtcTorchModeOn : UCloudRtcTorchModeOff)];
    if (resultCode == 0) { self.localCameraTorchOpen = openCameraTorch; }
    return resultCode;
}

- (int)openLocalUserCamera:(BOOL)openCamera{
    int resultCode = [self muteLocalVideoStream:!openCamera];
    if (resultCode == 0 || resultCode == -1) { self.localCameraOpen = openCamera; }
    return resultCode;
}

- (int)localVideoStreamOpenPlaceholder:(BOOL)openPlaceholder{
    return 0; /// UCloud不支持
}

- (int)enableLocalVideo:(BOOL)enabled{
    return 0; /// UCloud不支持
}

- (int)startLocalPreview:(BOOL)start{
    return 0; /// UCloud不支持
}

- (int)muteLocalVideoStream:(BOOL)mute{
    if (self.localStreamPublishState == UCloudRtcEnginePublishStatePublishing ||
        self.localStreamPublishState == UCloudRtcEnginePublishStateRepublishing) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - muteLocalVideoStream failed %ld",(long)self.localStreamPublishState);
        return -2;
    }
    if (self.engine &&
        !self.hadSetupRtcOrientationMode &&
        self.currentReasonableRtcOrientationMode >= 0) {
        self.engine.orientationMode = self.currentReasonableRtcOrientationMode;
        self.hadSetupRtcOrientationMode = YES;
    }
    int resultCode = [self.engine openCamera:!mute];
    if (resultCode == 0) { self.localVideoStreamMute = mute; }
    return resultCode;
}

- (void)enableLocalVideoFrameProcess:(BOOL)enabled {
    self.engine.enableGetVideoCapture = enabled;
}

#pragma mark 本地 麦克风 使用
- (int)changeLocalMicVolume:(CGFloat)micVolume{
    return 0; /// UCloud不支持
}

- (int)openLocalUserMic:(BOOL)openMic{
    int resultCode = [self muteLocalAudioStream:!openMic];
    if (resultCode == 0 || resultCode == -1) { self.localMicOpen = openMic; }
    return resultCode;
}

- (int)enableLocalAudio:(BOOL)enabled{
    return 0; /// UCloud不支持
}

- (int)muteLocalAudioStream:(BOOL)mute{
    if (self.localStreamPublishState == UCloudRtcEnginePublishStatePublishing ||
        self.localStreamPublishState == UCloudRtcEnginePublishStateRepublishing) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - muteLocalAudioStream failed %ld",(long)self.localStreamPublishState);
        return -2;
    }
    UCloudRtcStreamMediaType mediaType = self.publishStreamSourceType == PLVBRTCStreamSourceType_Screen ? UCloudRtcStreamMediaTypeScreen : UCloudRtcStreamMediaTypeCamera;
    int resultCode = [self.engine muteLocalAudio:mute mediaType:mediaType];
    if (resultCode == 0) { self.localAudioStreamMute = mute; }
    return resultCode;
}

#pragma mark 推流相关
- (int)addPublishStreamUrl:(NSString *)streamUrl transcodingEnabled:(BOOL)transcodingEnabled{
    if ([PLVFdUtil checkStringUseable:streamUrl]) {
        UCloudRtcMixConfig * mixConfig = [[UCloudRtcMixConfigCLS alloc]init];
        mixConfig.pushurl = @[streamUrl];
        mixConfig.addstreammode = 2; /// 1自动 2手动
        
        mixConfig.bitrate = self.currentVideoEncoderConfiguration.videoBitrate;
        mixConfig.framerate = 15;
        mixConfig.width = self.videoCDNWidth;
        mixConfig.height = self.videoCDNHeight;
        
        mixConfig.mainviewuid = self.userLinkMicId;
        mixConfig.mainviewtype = 1;
        mixConfig.streams = @[
            @{@"user_id" : [NSString stringWithFormat:@"%@",self.userLinkMicId],
              @"media_type" : @1}
        ];
        
        self.currentMixConfig = mixConfig;
        int resultCode = [self.engine startRelay:mixConfig];
        return resultCode;
    }else{
        return -1;
    }
}

- (int)removePublishStreamUrl:(NSString *)streamUrl{
    if ([PLVFdUtil checkStringUseable:streamUrl]) {
        self.currentMixConfig = nil;
        int resultCode = [self.engine stopRelay:@[streamUrl]];
        return resultCode;
    }else{
        return -1;
    }
}

#pragma mark Getter
- (BOOL)engineIsReady{
    return _engine ? YES : NO;
}


#pragma mark - [ Father Private Methods ]
- (void)setupRTCToken:(NSDictionary *)decodeDict{
    self.rtcAppId = decodeDict[@"appId"];
    self.rtcAppSign = decodeDict[@"appToken"];
    self.rtcToken = decodeDict[@"token"];
}


#pragma mark - [ Private Methods ]
- (void)createResource{
    _wantSubscribeModelDict = [[NSMutableDictionary alloc] init];
    _roomStreamDict = [[NSMutableDictionary alloc]init];
    _subscribedStreamDict = [[NSMutableDictionary alloc]init];
    _volumeDict = [[NSMutableDictionary alloc] init];
    _dictSafeQueue = dispatch_queue_create("PLVBLinkMicUCloudManagerDictionarySafeQueue", DISPATCH_QUEUE_SERIAL);
}

- (void)destroyResource{
    [self resumeToOriginalIsGeneratingDeviceOrientationNotificationsValue];
    [self destroyReportAudioVolumeTimer];
    
    [self destroyStreamManageResource];
    
    _dictSafeQueue = nil;

    self.currentMixConfig = nil;
    
    self.hadJoinedRTC = NO;
    self.hadSetupDefault = NO;
    
    self.localAudioStreamMute = NO;
    self.localVideoStreamMute = NO;
    self.hadSetupRtcOrientationMode = NO;
    self.localStreamPublishState = UCloudRtcEnginePublishStateUnPublish;
}

- (void)destroyStreamManageResource{
    [_roomStreamDict removeAllObjects];
    _roomStreamDict = nil;
    
    [_wantSubscribeModelDict removeAllObjects];
    _wantSubscribeModelDict = nil;
    
    [_subscribedStreamDict removeAllObjects];
    _subscribedStreamDict = nil;
    
    [_volumeDict removeAllObjects];
    _volumeDict = nil;
}

- (void)createReportAudioVolumeTimer{
    if (_reportAudioVolumeTimer) {
        [self destroyReportAudioVolumeTimer];
    }
    _reportAudioVolumeTimer = [NSTimer scheduledTimerWithTimeInterval:0.3 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(reportAudioVolumeTimerAction:) userInfo:nil repeats:YES];
}

- (void)destroyReportAudioVolumeTimer{
    [_reportAudioVolumeTimer invalidate];
    _reportAudioVolumeTimer = nil;
}

- (void)cleanVolumeDictionary{
    [_volumeDict removeAllObjects];
}

- (void)resumeToOriginalIsGeneratingDeviceOrientationNotificationsValue{
    if (self.resumeValueCount >= 10) { return; }
    BOOL currentValue = [[UIDevice currentDevice] isGeneratingDeviceOrientationNotifications];
    if (currentValue != self.originalIsGeneratingDeviceOrientationNotificationsValue) {
        if (currentValue) {
            [[UIDevice currentDevice] endGeneratingDeviceOrientationNotifications];
        }else{
            [[UIDevice currentDevice] beginGeneratingDeviceOrientationNotifications];
        }
        self.resumeValueCount++;
    }
    BOOL resultValue = [[UIDevice currentDevice] isGeneratingDeviceOrientationNotifications];
    if (resultValue != self.originalIsGeneratingDeviceOrientationNotificationsValue) {
        [self resumeToOriginalIsGeneratingDeviceOrientationNotificationsValue];
    }else{
        self.resumeValueCount = 0;
    }
}

- (UCloudRtcEngineVideoProfile)readURTCVideoResolution:(PLVBRTCVideoEncoderConfiguration *)videoEncoderConfiguration{
    CGFloat resolutionWidth = videoEncoderConfiguration.videoResolution.width;
    CGFloat resolutionHeight = videoEncoderConfiguration.videoResolution.height;
    CGFloat widthHeithScale = resolutionWidth / resolutionHeight;
    
    UCloudRtcEngineVideoProfile urtcResolution;
    if (widthHeithScale <= 1.4){
        /// 宽高比4:3
        if (resolutionHeight >= 480){
            urtcResolution = UCloudRtcEngine_VideoProfile_480P;
        }else if (resolutionHeight >= 360){
            urtcResolution = UCloudRtcEngine_VideoProfile_360P_1;
        }else{
            urtcResolution = UCloudRtcEngine_VideoProfile_180P_1;
        }
    }else{
        /// 宽高比16:9
        if (resolutionHeight >= 1080) {
            urtcResolution = UCloudRtcEngine_VideoProfile_1080P;
        }else if (resolutionHeight >= 720) {
            urtcResolution = UCloudRtcEngine_VideoProfile_720P;
        }else if (resolutionHeight >= 360){
            urtcResolution = UCloudRtcEngine_VideoProfile_360P_2;
        }else{
            urtcResolution = UCloudRtcEngine_VideoProfile_180P_2;
        }
    }
    return urtcResolution;
}

/// 从【-uCloudRtcEngine:didReceiveStreamStatus:】回调的 status 数据整理出用户的上行时延（如多个音轨video/audio，取其中的最大值）
- (NSDictionary *)rttDictWithStreamStatus:(NSArray<UCloudRtcStreamStatsInfo*> *_Nonnull)status {
    NSMutableDictionary *muDict = [[NSMutableDictionary alloc] init];
    for (UCloudRtcStreamStatsInfo * info in status) {
        if ([info isKindOfClass:UCloudRtcStreamStatsInfoCLS.class]) {
            if (info.userId &&
                [info.userId isKindOfClass:[NSString class]]) {
                if (muDict[info.userId]) {
                    NSNumber *rttNumber = muDict[info.userId];
                    muDict[info.userId] = @(MAX(info.rtt, rttNumber.integerValue));
                } else {
                    muDict[info.userId] = @(info.rtt);
                }
            }
        }
    }
    return [muDict copy];;
}

/// 从【-uCloudRtcEngine:didReceiveStreamStatus:】回调的 status 数据整理出本地用户的音量大小数据
- (NSInteger )volumeWithStreamStatus:(NSArray<UCloudRtcStreamStatsInfo*> *_Nonnull)status {
    for (UCloudRtcStreamStatsInfo * info in status) {
        if ([info isKindOfClass:UCloudRtcStreamStatsInfoCLS.class]) {
            if ([info.trackType isEqualToString:@"audio"]) {
                if (info.userId && [info.userId isKindOfClass:[NSString class]]) {
                    if ([info.userId isEqualToString:self.userLinkMicId]) {
                        return info.volume;
                    }
                }
            }
        }
    }
    return 0;
}

- (void)startPushLocalStream{
    plv_dispatch_main_async_safe(^{
        self.localAudioStreamMute = NO;
        self.localVideoStreamMute = NO;
        [self.engine publish];
    })
}

- (void)stopPushLocalStream{
    plv_dispatch_main_async_safe(^{
        if (self.localVideoStreamMute) {
            [self muteLocalVideoStream:YES];
        }
        if (self.localAudioStreamMute) {
            [self muteLocalAudioStream:YES];
        }
        self.hadSetupDefault = NO;
        [self.engine unPublish];
    })
}

- (NSArray <PLVBLinkMicUCloudSubscribeModel *> *)readRTCUserSubscribeModelArray:(NSString *)userRTCId{
    NSMutableArray * muArray = [[NSMutableArray alloc] init];
    PLVBLinkMicUCloudSubscribeModel * model_ScreenFirst_Mix = [self.wantSubscribeModelDict objectForKey:[PLVBLinkMicUCloudSubscribeModel subscribeModelKeyWithUserRTCId:userRTCId subscribeMode:PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix]];
    if (model_ScreenFirst_Mix) { [muArray addObject:model_ScreenFirst_Mix]; }
    
    PLVBLinkMicUCloudSubscribeModel * model_Camera = [self.wantSubscribeModelDict objectForKey:[PLVBLinkMicUCloudSubscribeModel subscribeModelKeyWithUserRTCId:userRTCId subscribeMode:PLVBRTCSubscribeStreamSubscribeMode_Camera]];
    if (model_Camera) { [muArray addObject:model_Camera]; }

    PLVBLinkMicUCloudSubscribeModel * model_Screen = [self.wantSubscribeModelDict objectForKey:[PLVBLinkMicUCloudSubscribeModel subscribeModelKeyWithUserRTCId:userRTCId subscribeMode:PLVBRTCSubscribeStreamSubscribeMode_Screen]];
    if (model_Screen) { [muArray addObject:model_Screen]; }
    
    return muArray;
}

#pragma mark - [ Event ]
#pragma mark Timer
- (void)reportAudioVolumeTimerAction:(NSTimer *)timer{
    if (self.dictSafeQueue) {
        __weak typeof(self) weakSelf = self;
        dispatch_async(self.dictSafeQueue, ^{
            if (weakSelf.volumeDict.count > 0) {
                NSDictionary * volumeDictReturn = [weakSelf.volumeDict copy];
                dispatch_async(dispatch_get_main_queue(), ^{
                    if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvbLinkMicManager:reportAudioVolumeOfSpeakers:)]) {
                        [weakSelf.delegate plvbLinkMicManager:weakSelf reportAudioVolumeOfSpeakers:volumeDictReturn];
                    }
                });
                [weakSelf cleanVolumeDictionary];
            }
        });
    }
}

#pragma mark Notification
- (void)statusBarOrientationChange:(NSNotification *)notification{
    __weak typeof(self) weakSelf = self;
    plv_dispatch_main_async_safe(^{
        UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
        UCloudRtcOrientationMode targetRtcOrientationMode;
        if (orientation == UIInterfaceOrientationLandscapeLeft) {
            targetRtcOrientationMode = UCloudRtcOrientationModeLandscapeLeft;
        }else if (orientation == UIInterfaceOrientationLandscapeRight) {
            targetRtcOrientationMode = UCloudRtcOrientationModeLandscapeRight;
        }else{
            targetRtcOrientationMode = UCloudRtcOrientationModePortrait;
        }
        weakSelf.engine.orientationMode = targetRtcOrientationMode;
        weakSelf.currentReasonableRtcOrientationMode = targetRtcOrientationMode;
        if (weakSelf.engine) { weakSelf.hadSetupRtcOrientationMode = YES; }
    })
}


#pragma mark - [ Delegate ]
#pragma mark UCloudRtcEngineDelegate
/**退出房间*/
- (void)uCloudRtcEngineDidLeaveRoom:(UCloudRtcEngine *_Nonnull)manager{
    if (_engine) {
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:leaveRTCChannelComplete:)]) {
            [self.delegate plvbLinkMicManager:self leaveRTCChannelComplete:self.channelId];
        }
    }
}

/**与房间的连接断开*/
- (void)uCloudRtcEngineDisconnectRoom:(UCloudRtcEngine *_Nonnull)manager{
    // 在 connectState 回调中处理即可
}

/**发布状态的变化*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager didChangePublishState:(UCloudRtcEnginePublishState)publishState mediaType:(UCloudRtcStreamMediaType)mediaType{
    self.localStreamPublishState = publishState;
    // NSLog(@"PLVBLinkMicManager - didChangePublishState %ld",(long)publishState);
    switch (publishState) {
        case UCloudRtcEnginePublishStateUnPublish:{
            //NSLog(@"POLYVTEST - UCloudRtcEnginePublishStateUnPublish 未推流状态");
            self.hadSetupDefault = NO;
        }
            break;
        case UCloudRtcEnginePublishStatePublishing: {
            //NSLog(@"POLYVTEST - UCloudRtcEnginePublishStateUnPublish 推流中");
        }
            break;
        case UCloudRtcEnginePublishStatePublishSucceed:{
            //NSLog(@"POLYVTEST - UCloudRtcEnginePublishStateUnPublish 推流成功");
            [self.engine muteLocalAudio:self.localAudioStreamMute mediaType:mediaType];
            /// 需配置默认项
            if (!self.hadSetupDefault) {
                __weak typeof(self) weakSelf = self;
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [weakSelf callbackForCanSetupLocalHardwareDefaultState];
                });
                self.hadSetupDefault = YES;
            }
        }
            break;
        case UCloudRtcEnginePublishStateRepublishing: {
        }
            break;
        case UCloudRtcEnginePublishStatePublishFailed: {
        }
            break;
        case UCloudRtcEnginePublishStatePublishStoped: {
            self.hadSetupDefault = NO;
        }
            break;
        default:
            break;
    } // 不以此作为 hadJoinedRTC 状态的依据
}

- (UCloudRtcStream * _Nullable)getUserVideoStream:(NSString *)userId{
    UCloudRtcStream * teacherVideoStream;
    if ([PLVFdUtil checkStringUseable:userId]) {
        teacherVideoStream = [self.subscribedStreamDict objectForKey:[userId stringByAppendingString:@"-video"]];
        return teacherVideoStream;
    }
    return nil;
}

#pragma mark 流 订阅/取消订阅
/**
@brief 收到远程流
@param stream 远端流对象
@discussion 当成功订阅远程流时会收到该回调。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager receiveRemoteStream:(UCloudRtcStream *_Nonnull)stream{
    // NSLog(@"PLVBLinkMicManager - receiveRemoteStream %@", stream);
    if (![PLVFdUtil checkStringUseable:stream.userId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - receiveRemoteStream illegal stream userId %@", stream.userId);
        return;
    }
    
    NSString * userId = stream.userId;
    NSString * streamDictKey = userId;
    
    BOOL isTeacher = NO;
    if ([PLVFdUtil checkStringUseable:self.teacherId]) {
        isTeacher = [userId isEqualToString:self.teacherId];
    } else {
        isTeacher = [userId isEqualToString:self.channelId];
    }
    
    UCloudRtcStreamMediaType mediaType = stream.mediaType;
    
    NSString * otherVideoStreamDictKey = userId;
    if (mediaType == UCloudRtcStreamMediaTypeCamera) { /// 已订阅 一个新的 摄像头流
        streamDictKey = [streamDictKey stringByAppendingString:@"-video"];
    }else if (mediaType == UCloudRtcStreamMediaTypeScreen){ /// 已订阅 一个新的 屏幕流
        otherVideoStreamDictKey = [userId stringByAppendingString:@"-video"];
        streamDictKey = [streamDictKey stringByAppendingString:@"-screen"];
    }
        
    NSArray <PLVBLinkMicUCloudSubscribeModel *> * modelArray = [self readRTCUserSubscribeModelArray:userId];
    PLVBLinkMicUCloudSubscribeModel * model;
    /// 通过遍历，找到对应的订阅Model
    for (PLVBLinkMicUCloudSubscribeModel * modelInArray in modelArray) {
        if (modelInArray && [modelInArray isKindOfClass:PLVBLinkMicUCloudSubscribeModel.class]) {
            /// 判断 流的订阅方式
            if (modelInArray.subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix) { /// 混合流订阅方式
                model = modelInArray;
                break;
            } else if (modelInArray.subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_Camera) { /// 仅订阅摄像头流方式
                if (mediaType == UCloudRtcStreamMediaTypeCamera) {
                    model = modelInArray;
                    break;
                }
            } else if (modelInArray.subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_Screen) { /// 仅订阅屏幕流方式
                if (mediaType == UCloudRtcStreamMediaTypeScreen) {
                    model = modelInArray;
                    break;
                }
            }
        }
    }
    
    /// mute audio
    BOOL subscribeAudio = model.mediaType & PLVBRTCSubscribeStreamMediaType_Audio;
    subscribeAudio = self.subscribedRemoteStreamNeedMute ? NO : subscribeAudio; /// 当前若需禁用远端流，则新订阅流将同步禁用
    [self.engine setRemoteStream:stream muteAudio:!subscribeAudio];
    
    /// mute video
    BOOL subscribeVideo = model.mediaType & PLVBRTCSubscribeStreamMediaType_Video;
    subscribeVideo = self.subscribedRemoteStreamNeedMute ? NO : subscribeVideo; /// 当前若需禁用远端流，则新订阅流将同步禁用
    [self.engine setRemoteStream:stream muteVideo:!subscribeVideo];
        
    /// 渲染
    UIView * superView = model.renderSuperView;
    model.subscribedStreamId = stream.streamId;
    plv_dispatch_main_async_safe(^{
        for (UIView * subview in superView.subviews) { [subview removeFromSuperview]; }
        if (subscribeVideo) {
            if (superView && [superView isKindOfClass:UIView.class]) {
                stream.remoteVideoViewMode = isTeacher ? UCloudRtcVideoViewModeScaleAspectFit : UCloudRtcVideoViewModeScaleAspectFill;
                [stream renderOnView:superView];
            }
        }
    })
    
    if (mediaType == UCloudRtcStreamMediaTypeCamera) { /// 已订阅 一个新的 摄像头流
        UCloudRtcStream * lastStreamInSubscribedDict = [self.subscribedStreamDict objectForKey:streamDictKey];
        if (lastStreamInSubscribedDict && [lastStreamInSubscribedDict isKindOfClass:UCloudRtcStreamCLS.class]) {
            if (![lastStreamInSubscribedDict.streamId isEqualToString:stream.streamId]) {
                [self.engine unSubscribeMethod:lastStreamInSubscribedDict];
            }
            [self.subscribedStreamDict removeObjectForKey:streamDictKey];
        }
    }else if (mediaType == UCloudRtcStreamMediaTypeScreen){ /// 已订阅 一个新的 屏幕流
        if (model.subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix) {
            UCloudRtcStream * otherVideoStream = [self.subscribedStreamDict objectForKey:otherVideoStreamDictKey];
            if (otherVideoStream && [otherVideoStream isKindOfClass:UCloudRtcStreamCLS.class]) {
                [self.engine unSubscribeMethod:otherVideoStream];
                [self.subscribedStreamDict removeObjectForKey:otherVideoStreamDictKey];
            }
        }
        
        UCloudRtcStream * lastStreamInSubscribedDict = [self.subscribedStreamDict objectForKey:streamDictKey];
        if (lastStreamInSubscribedDict && [lastStreamInSubscribedDict isKindOfClass:UCloudRtcStreamCLS.class]) {
            if (![lastStreamInSubscribedDict.streamId isEqualToString:stream.streamId]) {
                [self.engine unSubscribeMethod:lastStreamInSubscribedDict];
            }
            [self.subscribedStreamDict removeObjectForKey:streamDictKey];
        }
    }
    
    /// 记录
    [self.subscribedStreamDict setObject:stream forKey:streamDictKey];
}

/**
@brief 远程流断开（取消订阅）
@param stream 远端流对象
@discussion 当取消订阅远程流或远程流退出房间时会收到该回调。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager didRemoveStream:(UCloudRtcStream *_Nonnull)stream{
    // NSLog(@"PLVBLinkMicManager - didRemoveStream %@", stream);
    if (![PLVFdUtil checkStringUseable:stream.userId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - didRemoveStream illegal stream userId %@", stream.userId);
        return;
    }
    
    NSString * userId = stream.userId;
    NSString * streamDictKey = userId;
    UCloudRtcStreamMediaType mediaType = stream.mediaType;

    NSString * otherVideoStreamDictKey = userId;
    if (mediaType == UCloudRtcStreamMediaTypeCamera) { /// 断开一个 摄像头流
        streamDictKey = [userId stringByAppendingString:@"-video"];
    }else if (mediaType == UCloudRtcStreamMediaTypeScreen){ /// 断开一个 屏幕流
        otherVideoStreamDictKey = [userId stringByAppendingString:@"-video"];
        streamDictKey = [userId stringByAppendingString:@"-screen"];
    }
    
    NSArray <PLVBLinkMicUCloudSubscribeModel *> * modelArray = [self readRTCUserSubscribeModelArray:userId];
    PLVBLinkMicUCloudSubscribeModel * model;
    /// 通过遍历，找到对应的 订阅Model
    for (PLVBLinkMicUCloudSubscribeModel * modelInArray in modelArray) {
        if (modelInArray && [modelInArray isKindOfClass:PLVBLinkMicUCloudSubscribeModel.class]) {
            /// 判断 流的订阅方式
            if (modelInArray.subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix) { /// 混合流订阅方式
                model = modelInArray;
                break;
            } else if (modelInArray.subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_Camera) { /// 仅订阅摄像头流方式
                if (mediaType == UCloudRtcStreamMediaTypeCamera) {
                    model = modelInArray;
                    break;
                }
            } else if (modelInArray.subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_Screen) { /// 仅订阅屏幕流方式
                if (mediaType == UCloudRtcStreamMediaTypeScreen) {
                    model = modelInArray;
                    break;
                }
            }
        }
    }
    
    UIView * superView = model.renderSuperView;
    /// 移除渲染
    plv_dispatch_main_async_safe(^{
        if ([model.subscribedStreamId isEqualToString:stream.streamId]) {
            for (UIView * subview in superView.subviews) { [subview removeFromSuperview]; }
        }
    })

    if (mediaType == UCloudRtcStreamMediaTypeCamera) { /// 断开一个 摄像头流
        UCloudRtcStream * streamInSubscribedDict = [self.subscribedStreamDict objectForKey:streamDictKey];
        if (streamInSubscribedDict && [streamInSubscribedDict isKindOfClass:UCloudRtcStreamCLS.class]) {
            [self.subscribedStreamDict removeObjectForKey:streamDictKey];
        }
    }else if (mediaType == UCloudRtcStreamMediaTypeScreen){ /// 断开一个 屏幕流
        UCloudRtcStream * streamInSubscribedDict = [self.subscribedStreamDict objectForKey:streamDictKey];
        if (streamInSubscribedDict && [streamInSubscribedDict isKindOfClass:UCloudRtcStreamCLS.class]) {
            [self.subscribedStreamDict removeObjectForKey:streamDictKey];
        }
        
        if (model.subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix) {
            /// 移除后，检查是否仍有订阅意向 且 没有渲染着其他流
            if (model && model.renderSuperView.subviews.count == 0) {
                UCloudRtcStream * otherVideoStream = [self.roomStreamDict objectForKey:otherVideoStreamDictKey];
                if (otherVideoStream && [otherVideoStream isKindOfClass:UCloudRtcStreamCLS.class]) {
                    [self.engine subscribeMethod:otherVideoStream];
                }
            }
        }
    }
}

#pragma mark 流 加入/退出
/**
@brief 手动订阅模式下 可订阅流加入
@param stream 流对象
@discussion 手动订阅模式下,当有新的流加入房间会收到该回调 注：该流需要客户端主动发起订阅。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)channel newStreamHasJoinRoom:(UCloudRtcStream *_Nonnull)stream{
    // NSLog(@"PLVBLinkMicManager - newStreamHasJoinRoom %@", stream.userId);
    if (![PLVFdUtil checkStringUseable:stream.userId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - newStreamHasJoinRoom illegal stream userId %@", stream.userId);
        return;
    }
    
    NSString * userId = stream.userId;
    NSString * streamDictKey = userId;
    NSString * screenStreamDictKey = userId;

    UCloudRtcStreamMediaType mediaType = stream.mediaType;
    PLVBRTCSubscribeStreamSourceType streamSourceType = PLVBRTCSubscribeStreamSourceType_Unknown;
    if (mediaType == UCloudRtcStreamMediaTypeCamera) { /// 收到 摄像头流
        streamSourceType = PLVBRTCSubscribeStreamSourceType_Camera;
        streamDictKey = [streamDictKey stringByAppendingString:@"-video"];
        screenStreamDictKey = [screenStreamDictKey stringByAppendingString:@"-screen"];
    }else if (mediaType == UCloudRtcStreamMediaTypeScreen){ /// 收到 屏幕流
        streamSourceType = PLVBRTCSubscribeStreamSourceType_Screen;
        streamDictKey = [streamDictKey stringByAppendingString:@"-screen"];
    }
    [self.roomStreamDict setObject:stream forKey:streamDictKey];
    
    NSArray <PLVBLinkMicUCloudSubscribeModel *> * modelArray = [self readRTCUserSubscribeModelArray:userId];
    /// 通过遍历，找到对应的 订阅Model
    for (PLVBLinkMicUCloudSubscribeModel * model in modelArray) {
        if (model && [model isKindOfClass:PLVBLinkMicUCloudSubscribeModel.class]) {
            /// 判断 流的订阅方式
            if (model.subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix) { /// 混合流订阅方式
                if (mediaType == UCloudRtcStreamMediaTypeCamera) {
                    UCloudRtcStream * screenStreamInSubscribedDict = [self.subscribedStreamDict objectForKey:screenStreamDictKey];
                    if (!screenStreamInSubscribedDict) {
                        [self.engine subscribeMethod:stream];
                    }else{
                        /// 本身有订阅 屏幕流，则忽略，不订阅此流
                    }
                }else if (mediaType == UCloudRtcStreamMediaTypeScreen){
                    [self.engine subscribeMethod:stream];
                }
                break;
            } else if (model.subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_Camera) { /// 仅订阅摄像头流方式
                if (mediaType == UCloudRtcStreamMediaTypeCamera) {
                    [self.engine subscribeMethod:stream];
                    break;
                }
            } else if (model.subscribeMode == PLVBRTCSubscribeStreamSubscribeMode_Screen) { /// 仅订阅屏幕流方式
                if (mediaType == UCloudRtcStreamMediaTypeScreen) {
                    [self.engine subscribeMethod:stream];
                    break;
                }
            }
        }
    }
    
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didNewStreamJoinRoomOfUid:)]) {
        [self.delegate plvbLinkMicManager:self didNewStreamJoinRoomOfUid:userId];
    }
    
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:streamJoinRoom:userRTCId:)]) {
        [self.delegate plvbLinkMicManager:self streamJoinRoom:streamSourceType userRTCId:userId];
    }
    
    // 后加入用户不会收到先加入用户的 '-uCloudRtcEngine:remoteMute:' 回调，如果此处发现接收到的流已被 mute，应在此处补充触发以下回调。
    if (stream.muteaudio) {
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didAudioMuted:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didAudioMuted:YES byUid:userId];
        }
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didAudioMuted:streamSourceType:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didAudioMuted:YES streamSourceType:streamSourceType byUid:userId];
        }
    }
    
    if (stream.mutevideo){
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didVideoMuted:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didVideoMuted:YES byUid:userId];
        }
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didVideoMuted:streamSourceType:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didVideoMuted:YES streamSourceType:streamSourceType byUid:userId];
        }
    }
}

/**
@brief 远程流断开 v1.7.2
@param stream 远端流对象
@discussion 当远程流退出房间时会收到该回调。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager removeRemoteStream:(UCloudRtcStream *_Nonnull)stream{
    // NSLog(@"PLVBLinkMicManager - removeRemoteStream %@", stream.userId);
    if (![PLVFdUtil checkStringUseable:stream.userId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - removeRemoteStream leaveRoom illegal stream userId %@", stream.userId);
        return;
    }
    
    NSString * userId = stream.userId;
    NSString * streamDictKey = userId;
    NSString * otherStreamDictKey = userId;

    UCloudRtcStreamMediaType mediaType = stream.mediaType;
    PLVBRTCSubscribeStreamSourceType streamSourceType = PLVBRTCSubscribeStreamSourceType_Unknown;
    if (mediaType == UCloudRtcStreamMediaTypeCamera) { /// 断开 摄像头流
        streamSourceType = PLVBRTCSubscribeStreamSourceType_Camera;
        streamDictKey = [streamDictKey stringByAppendingString:@"-video"];
        otherStreamDictKey = [otherStreamDictKey stringByAppendingString:@"-screen"];
    }else if (mediaType == UCloudRtcStreamMediaTypeScreen){ /// 断开 屏幕流
        streamSourceType = PLVBRTCSubscribeStreamSourceType_Screen;
        streamDictKey = [streamDictKey stringByAppendingString:@"-screen"];
        otherStreamDictKey = [otherStreamDictKey stringByAppendingString:@"-video"];
    }
    UCloudRtcStream * streamInRoomDict = [self.roomStreamDict objectForKey:streamDictKey];
    if (streamInRoomDict) {
        [self.roomStreamDict removeObjectForKey:streamDictKey];
    }
    
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:streamLeaveRoom:userRTCId:)]) {
        [self.delegate plvbLinkMicManager:self streamLeaveRoom:streamSourceType userRTCId:userId];
    }
    
    UCloudRtcStream * otherStreamInRoomDict = [self.roomStreamDict objectForKey:otherStreamDictKey];
    if (!otherStreamInRoomDict) {
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:remoteUserTotalStreamsDidLeaveRoom:)]) {
            [self.delegate plvbLinkMicManager:self remoteUserTotalStreamsDidLeaveRoom:userId];
        }
    }
}

/**
@brief 手动订阅模式下 可订阅流退出
@param stream 流对象
@discussion 手动订阅模式下,当有未订阅过的流退出房间会收到该回调。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)channel streamHasLeaveRoom:(UCloudRtcStream *_Nonnull)stream{
    // NSLog(@"PLVBLinkMicManager - streamHasLeaveRoom %@", stream.userId);
    if (![PLVFdUtil checkStringUseable:stream.userId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - streamHasLeaveRoom illegal stream userId %@", stream.userId);
        return;
    }
}

#pragma mark 成员 加入/退出
/**
@brief 新成员加入
@param memberInfo 新成员信息
@discussion 当新用户加入房间会收到该回调 注：可能同时收到该回调和可订阅流加入的回调。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager memberDidJoinRoom:(NSDictionary *_Nonnull)memberInfo{
    NSString * userId = memberInfo[@"user_id"];
    // NSLog(@"PLVBLinkMicManager - memberDidJoinRoom %@", userId);
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didJoinedOfUid:)]) {
        [self.delegate plvbLinkMicManager:self didJoinedOfUid:userId];
    }
}

/**
@brief 成员退出
@param memberInfo 成员信息
@discussion 当用户退出房间会收到该回调 注：可能同时收到该回调和可订阅流退出的回调。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager memberDidLeaveRoom:(NSDictionary *_Nonnull)memberInfo{
    NSString * userId = memberInfo[@"user_id"];
    // NSLog(@"PLVBLinkMicManager - memberDidLeaveRoom %@", userId);
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didOfflineOfUid:)]) {
        [self.delegate plvbLinkMicManager:self didOfflineOfUid:userId];
    }
}

/**
@brief 流状态回调
@param status 流状态信息
@discussion 流状态信息包含音频轨道和视频轨道的数据信息。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager didReceiveStreamStatus:(NSArray<UCloudRtcStreamStatsInfo*> *_Nonnull)status{
    if (![PLVFdUtil checkArrayUseable:status]) {
        return;
    }
    
    NSDictionary *rttDict = nil;
    NSInteger volume = 0;
    for (NSArray *infoArray in status) {
        if ([PLVFdUtil checkArrayUseable:infoArray]) { // 使用UCloud新修改的数据结构(即’数组包子数组，子数组包对象‘)来读取
            rttDict = [self rttDictWithStreamStatus:infoArray];
            volume = [self volumeWithStreamStatus:infoArray];
        } else { // 发现数据结构不是新的那种(即’数组包子数组，子数组包对象‘)，尝试使用旧的包裹形式(即’数组包对象‘)来读取
            rttDict = [self rttDictWithStreamStatus:status];
        }
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:userRttDict:)]) {
        [self.delegate plvbLinkMicManager:self userRttDict:rttDict];
    }
    
    float percent = [NSNumber numberWithInteger:volume].floatValue / 100.0;
    
    BOOL audible = percent >= 0.156 ? YES : NO;
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:localVoiceValue:receivedLocalAudibleVoice:)]) {
        [self.delegate plvbLinkMicManager:self localVoiceValue:percent receivedLocalAudibleVoice:audible];
    }
    
    // NSLog(@"PLVBLinkMicManager - 流 状态回调 status %@",status);
    /* 取消此处音量回调
    if ([PLVFdUtil checkArrayUseable:status]) {
        NSMutableDictionary * volumeDict = [[NSMutableDictionary alloc] init];
        for (NSArray * infoArray in status) {
            if ([PLVFdUtil checkArrayUseable:infoArray]) {
                // 使用UCloud新修改的数据结构(即’数组包子数组，子数组包对象‘)来读取
                for (UCloudRtcStreamStatsInfo * info in infoArray) {
                    if ([info isKindOfClass:UCloudRtcStreamStatsInfoCLS.class]) {
                        NSString * userLinkMicId = [NSString stringWithFormat:@"%lu",(unsigned long)info.streamId];
                        float percent = [NSNumber numberWithInteger:info.volume].floatValue / 100.0;
                        NSNumber * volume = [NSNumber numberWithFloat:percent];
                        [volumeDict setObject:volume forKey:userLinkMicId];
                    }else{
                        NSLog(@"PLVBLinkMicManager - didReceiveStreamStatus stream read failed, method 1, status %@", status);
                    }
                }
            }else{
                // 发现数据结构不是新的那种(即’数组包子数组，子数组包对象‘)，尝试使用旧的包裹形式(即’数组包对象‘)来读取
                for (UCloudRtcStreamStatsInfo * info in status) {
                    if ([info isKindOfClass:UCloudRtcStreamStatsInfoCLS.class]) {
                        NSString * userLinkMicId = [NSString stringWithFormat:@"%lu",(unsigned long)info.streamId];
                        float percent = [NSNumber numberWithInteger:info.volume].floatValue / 100.0;
                        NSNumber * volume = [NSNumber numberWithFloat:percent];
                        [volumeDict setObject:volume forKey:userLinkMicId];
                    }else{
                        NSLog(@"PLVBLinkMicManager - didReceiveStreamStatus stream read failed, method 2, status %@", status);
                    }
                }
            }
        }
        
        if ([PLVFdUtil checkDictionaryUseable:volumeDict]) {
            if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:reportAudioVolumeOfSpeakers:)]) {
                [self.delegate plvbLinkMicManager:self reportAudioVolumeOfSpeakers:volumeDict];
            }
        }
    } */
}

/**
@brief 流连接失败的回调
@param streamId 流ID
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager streamConnectionFailed:(NSString *_Nonnull)streamId{
    //NSLog(@"PLVBLinkMicManager - 流 连接失败 streamId %@",streamId);
}

/**错误的回调*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager error:(UCloudRtcError *_Nonnull)error{
    PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - error %@ code %ld,type %ld",error.message,(long)error.code,(long)error.errorType);
    NSError * finalError = [super errorWithCode:error.code errorDescription:error.description];
    [super callbackForDidOccurError:finalError];
    
    if (error.errorType == UCloudRtcErrorTypeDisconnect) {
        [self leaveRtcChannel];
    }
}

/**
@brief 远端音视频禁用或打开的回调
@param remoteMuteInfo 回调信息:用户ID、是否禁用、轨道类型
@discussion 当远端用户禁用/打开摄像头/麦克风时会收到该回调。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)channel remoteMute:(NSDictionary *_Nonnull)remoteMuteInfo{
    //NSLog(@"PLVBLinkMicManager - 远端 音视频 开关 的回调 remoteMuteInfo %@",remoteMuteInfo);
    
    // 用户连麦Id
    NSString * userId = [NSString stringWithFormat:@"%@",remoteMuteInfo[@"user_id"]];
    // 0-打开 1-关闭
    BOOL open = [NSString stringWithFormat:@"%@",remoteMuteInfo[@"mute"]].integerValue == 0;
    // 1-音频 2-视频
    NSString * track_type = [NSString stringWithFormat:@"%@",remoteMuteInfo[@"track_type"]];
    // 1-摄像头流 2-屏幕流
    NSString * media_type = [NSString stringWithFormat:@"%@",remoteMuteInfo[@"media_type"]];
    PLVBRTCSubscribeStreamSourceType sourceType = PLVBRTCSubscribeStreamSourceType_Unknown;
    if ([media_type isEqualToString:@"1"]){
        sourceType = PLVBRTCSubscribeStreamSourceType_Camera;
    } else if([media_type isEqualToString:@"2"]){
        sourceType = PLVBRTCSubscribeStreamSourceType_Screen;
    }

    if ([track_type isEqualToString:@"1"]) {
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didAudioMuted:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didAudioMuted:!open byUid:userId];
        }
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didAudioMuted:streamSourceType:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didAudioMuted:!open streamSourceType:sourceType byUid:userId];
        }
    }else if ([track_type isEqualToString:@"2"]){
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didVideoMuted:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didVideoMuted:!open byUid:userId];
        }
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didVideoMuted:streamSourceType:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didVideoMuted:!open streamSourceType:sourceType byUid:userId];
        }
    }

}

/**
@brief 网络连接状态变化
@param connectState UCloudRtcConnectState 网络连接状态
@discussion 当网络连接状态发生变化时会收到该回调。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager connectState:(UCloudRtcConnectState)connectState{
    switch (connectState) {
        case UCloudRtcConnectStateDisConnect: // 连接断开
        {
            self.connectionState = PLVBLinkMicConnectionStateDisconnected;
        }
            break;
        case UCloudRtcConnectStateConnecting: // 连接中
        {
            self.connectionState = PLVBLinkMicConnectionStateConnecting;
        }
            break;
        case UCloudRtcConnectStateConnected: // 连接成功
        {
            self.connectionState = PLVBLinkMicConnectionStateConnected;
        }
            break;
        case UCloudRtcConnectStateConnectFailed: // 连接失败
        {
            self.connectionState = PLVBLinkMicConnectionStateFailed;
        }
            break;
        case UCloudRtcConnectStateReConnected: // 重连成功
        {
            self.connectionState = PLVBLinkMicConnectionStateConnected;
        }
            break;
        default:
            break;
    }
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:rtcConnectionStateDidChanged:)]) {
        [self.delegate plvbLinkMicManager:self rtcConnectionStateDidChanged:self.connectionState];
    }
}

/**
@brief 音量回调
@param audioStatus 音量信息
@discussion 200ms回调一次音量信息，如果存在多个远端用户200ms内会被触发多次。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager didReceiveAudioStatus:(UCloudRtcAudioStats *_Nonnull)audioStatus{
//    NSLog(@"POLYVTEST - 音量回调 userId %@ volume %ld, %@",audioStatus.userId,audioStatus.volume,[NSThread isMainThread]?@"主线程":@"非主线程");
    if (audioStatus && [audioStatus isKindOfClass:UCloudRtcAudioStatsCLS.class]) {
        if ([PLVFdUtil checkStringUseable:audioStatus.userId]) {
            float percent = [NSNumber numberWithInteger:audioStatus.volume].floatValue / 100.0;
            NSNumber * volume = [NSNumber numberWithFloat:percent];
            if (self.dictSafeQueue) {
                __weak typeof(self) weakSelf = self;
                dispatch_async(self.dictSafeQueue, ^{
                    [weakSelf.volumeDict setObject:volume forKey:audioStatus.userId];
                });
            }
        }
    }
}


/**
@brief 开始录制、转推的回调
@discussion 开启云端转推录制服务成功时会收到该回调，回调信息里面包含录制生成的视频文件的文件名；手动添加流到录制、转推中，也会在该代理方法中回调。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager didChangeRelayState:(UCloudRtcMixState)mixState response:(UCloudRtcMixResponse *_Nullable)relayResponse{
    BOOL startSucess = (mixState == UCloudRtcMixStateSuccess);
    if (startSucess) {
        self.streamPublishing = YES;
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:streamPublishedResult:)]) {
            [self.delegate plvbLinkMicManager:self streamPublishedResult:startSucess];
        }
    }
    
    BOOL stopSucess = (mixState == UCloudRtcMixStateStop);
    if (stopSucess) {
        self.streamPublishing = NO;
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManagerStreamUnpublished:)]) {
            [self.delegate plvbLinkMicManagerStreamUnpublished:self];
        }
    }
}

/**
@brief 查询录制、转推的信息
@param response 回调信息
@discussion 查询录制、转推的信息，response包含type、mixId、filename。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager queryMix:(UCloudRtcMixResponse *_Nullable)response{
    //NSLog(@"PLVBLinkMicManager - 查询录制、转推的信息 的回调 queryMix %@",response);
}

/**
@brief 通话中每个用户的网络上下行质量报告回调
@param userId       用户 ID
@param txQuality 该用户的上行网络质量
@param rxQuality 该用户的下行网络质量
@discussion 流状态信息包含音频轨道和视频轨道的数据信息。
*/
- (void)uCloudRtcEngine:(UCloudRtcEngine *_Nonnull)manager networkQuality:(NSString *)userId txQuality:(UCloudRtcNetworkQuality)txQuality rxQuality:(UCloudRtcNetworkQuality)rxQuality{
    PLVBLinkMicNetworkQuality txNetworkQuality = [self transformNetworkQuality:txQuality];
    PLVBLinkMicNetworkQuality rxNetworkQuality = [self transformNetworkQuality:rxQuality];
    
    BOOL localUser = [userId isEqualToString:self.userLinkMicId];
    if (localUser) {
        self.networkQuality = txNetworkQuality;
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:networkQualityDidChanged:)]) {
            [self.delegate plvbLinkMicManager:self networkQualityDidChanged:txNetworkQuality];
        }
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:userNetworkQualityDidChanged:txQuality:rxQuality:)]) {
        [self.delegate plvbLinkMicManager:self userNetworkQualityDidChanged:userId txQuality:txNetworkQuality rxQuality:rxNetworkQuality];
    }
}

- (PLVBLinkMicNetworkQuality)transformNetworkQuality:(UCloudRtcNetworkQuality)quality{
    PLVBLinkMicNetworkQuality finalQuality = PLVBLinkMicNetworkQualityUnknown;
    switch (quality) {
        case UCloudRtcNetworkQualityExcellent:
        case UCloudRtcNetworkQualityGood:
            finalQuality = PLVBLinkMicNetworkQualityGood;
            break;
        case UCloudRtcNetworkQualityPoor:
        case UCloudRtcNetworkQualityPoorer:
            finalQuality = PLVBLinkMicNetworkQualityFine;
            break;
        case UCloudRtcNetworkQualityPoorest:
            finalQuality = PLVBLinkMicNetworkQualityBad;
            break;
        case UCloudRtcNetworkQualityDisconnect:
            finalQuality = PLVBLinkMicNetworkQualityDown;
            break;
        default:
            break;
    }
    return finalQuality;
}

#pragma mark 屏幕采集回调

/**
@brief 桌面采集开始
*/
- (void)uCloudRtcEngineScreenCaptureDidStarted:(UCloudRtcEngine *_Nonnull)manager {
    self.screenCaptureStarted = YES;
}

/**
@brief 桌面采集结束
*/
- (void)uCloudRtcEngineScreenCaptureDidFinished:(UCloudRtcEngine *_Nonnull)manager reason:(UCloudRtcScreenFinishedReason)reason {
    self.screenCaptureStarted = NO;
    PLVBRTCScreenCaptureFinishedReason finishReason = (reason == UCloudRtcScreenFinishedByEngine) ? PLVBRTCScreenCaptureFinishedReason_Engine : PLVBRTCScreenCaptureFinishedReason_Extension;
    if (finishReason == PLVBRTCScreenCaptureFinishedReason_Extension) { /// 如果用户主动点击控制中心停止 则需要 切换到摄像头采集
        [self switchPublishStreamSourceType:PLVBRTCStreamSourceType_Camera];
    }

    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:didScreenCaptureStopedReason:)]) {
        [self.delegate plvbLinkMicManager:self didScreenCaptureStopedReason:finishReason];
    }
}

#pragma mark 摄像头采集视频数据回调

-(CVPixelBufferRef)uCloudRtcEngine:(UCloudRtcEngine *)manager didOutputPixelBufferRef:(CVPixelBufferRef)pixelBufferRef {
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:captureVideoFrameTextureId:videoFrameWidth:videoFrameHeight:videoFrameTimeStamp:processedVideoFrameTextureId:)]) {
        if ([EAGLContext currentContext] != glcontext) {
            [EAGLContext setCurrentContext:glcontext];
        }
        CVPixelBufferRef pixelBuffer = nil;
        // 调整数据格式
        PLVPixelBufferInfo *pixelBufferInfo = [self.imageUtils getCVPixelBufferInfo:pixelBufferRef];
        if (pixelBufferInfo.format != PLVFormatType_BGRA) {
            pixelBuffer = [self.imageUtils transforCVPixelBufferToCVPixelBuffer:pixelBufferRef outputFormat:PLVFormatType_BGRA];
        }
        // 创建纹理
        id<PLVGLTexture> texture = (id<PLVGLTexture>)[self.imageUtils transforCVPixelBufferToTexture:pixelBuffer];
        id<PLVGLTexture> outTexture = nil;
        outTexture = [self.imageUtils getOutputPixelBufferGLTextureWithWidth:texture.width
                                                                      height:texture.height
                                                                      format:PLVFormatType_BGRA];
        double timeStamp = [[[NSDate alloc]init]timeIntervalSince1970];
        // 回调给美颜sdk处理数据
        int ret = [self.delegate plvbLinkMicManager:self
                         captureVideoFrameTextureId:texture.texture
                                    videoFrameWidth:texture.width
                                   videoFrameHeight:texture.height
                                videoFrameTimeStamp:timeStamp
                       processedVideoFrameTextureId:outTexture.texture];
        if (ret != 0) {
            outTexture = texture;
        }
        // 处理后的 Texture 提取 CVPixelBufferRef，再还给回UCloud
        CVPixelBufferRef outputPixelBuffer = [(PLVPixelBufferGLTexture *)outTexture pixelBuffer];
        outputPixelBuffer = [self.imageUtils transforCVPixelBufferToCVPixelBuffer:outputPixelBuffer outputFormat:PLVFormatType_YUV420F];
        // 按照ucloud回调的数据格式，将处理后的数据填回CVPixelBufferRef
        [self.imageUtils fillPixelBuffer:pixelBufferRef withInputPixelBuffer:outputPixelBuffer];
        return pixelBufferRef;
    }
    return pixelBufferRef;
}
#endif
@end
