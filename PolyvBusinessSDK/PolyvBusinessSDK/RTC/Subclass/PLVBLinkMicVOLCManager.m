//
//  PLVBLinkMicVOLCManager.m
//  PLVBusinessSDK
//
//  Created by juno on 2022/8/7.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBLinkMicVOLCManager.h"
#import "PLVBLinkMicManager+PrivateExtension.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

#if __has_include(<VolcEngineRTC/objc/rtc/ByteRTCEngineKit.h>)
    #import <VolcEngineRTC/objc/rtc/ByteRTCEngineKit.h>
    #define VOLCRtcSdk_ios
#endif

#if __has_include(<RTCEngineExt/RTCEngineExt.h>)
    #import <RTCEngineExt/RTCEngineExt.h>
    #define VOLCRtcSdkExt_ios
#endif

#define VOLCRtcEngineCLS NSClassFromString(@"ByteRTCEngineKit")
#define VOLCRtcEngineExtCLS NSClassFromString(@"RTCEngineExt")
#define VOLCRtcUserInfoCLS NSClassFromString(@"ByteRTCUserInfo")
#define VOLCRtcRoomConfigCLS NSClassFromString(@"ByteRTCRoomConfig")
#define VOLCRtcVideoEncoderConfigCLS NSClassFromString(@"ByteRTCVideoEncoderConfig")
#define VOLCRtcVideoSolutionCLS NSClassFromString(@"ByteRTCVideoSolution")
#define VOLCRtcVideoCaptureConfigCLS NSClassFromString(@"ByteRTCVideoCaptureConfig")
#define VOLCRtcVideoPreprocessorConfigCLS NSClassFromString(@"ByteRTCVideoPreprocessorConfig")
#define VOLCRtcVideoCanvasCLS NSClassFromString(@"ByteRTCVideoCanvas")

@interface PLVBLinkMicVOLCRemoteUserStreamModel : NSObject
#ifdef VOLCRtcSdk_ios
@property (nonatomic, assign) BOOL hadCameraStream;     //!< 是否存在摄像头流
@property (nonatomic, assign) BOOL hadScreenStream;     //!< 是否存在屏幕流
@property (nonatomic, assign) ByteRTCMediaStreamType cameraStreamType;  //!< 摄像头流类型
@property (nonatomic, assign) ByteRTCMediaStreamType screenStreamType;  //!< 屏幕流类型

@property (nonatomic, weak) UIView *renderSuperView;    //!< 该用户流渲染的视图

#endif
@end

@implementation PLVBLinkMicVOLCRemoteUserStreamModel

@end

@interface PLVBLinkMicVOLCManager ()

#ifdef VOLCRtcSdk_ios
<ByteRTCEngineDelegate, LiveTranscodingDelegate, ByteRTCVideoProcessorDelegate>
{
    EAGLContext *glcontext;
}
#pragma mark RTC对象
@property (nonatomic, strong) ByteRTCEngineKit *engine;
@property (nonatomic, strong) ByteRTCVideoCanvas *localCanvas;

#pragma mark 数据维护
@property (nonatomic, strong) NSMutableDictionary <NSString *, PLVBLinkMicVOLCRemoteUserStreamModel *> *subscribedStreamDict;       //!< 已订阅流的情况，key = userId， value = PLVBLinkMicVOLCRemoteUserStreamModel
@property (nonatomic, strong) NSMutableDictionary <NSString *, PLVBLinkMicVOLCRemoteUserStreamModel *> *roomStreamDict;     //!< 房间里流记录情况

#pragma mark 状态
@property (nonatomic, assign) BOOL localStreamPublishing;       //!< 本地流是否正在发布
@property (nonatomic, assign) BOOL hadSetupDefault;     //!< 是否已经设置过默认值
@property (nonatomic, assign) BOOL screenFinishedByEngine;      //!< 是否调用engine的stop方法结束的屏幕采集
@property (nonatomic, assign) BOOL localCameraJustSwitched;      //!< 本地摄像头是否刚刚翻转

#pragma mark 功能对象
@property (nonatomic, copy) void (^joinRTCChannelBlock) (void);
@property (nonatomic, strong) PLVImageUtil *imageUtils;

#endif
@end

@implementation PLVBLinkMicVOLCManager

#ifdef VOLCRtcSdk_ios
@synthesize roleType = _roleType;
@synthesize subscribedRemoteStreamNeedMute = _subscribedRemoteStreamNeedMute;

#pragma mark - [ Life Period ]
- (void)dealloc{
    PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"volc dealloc");
}

- (instancetype)init{
    if (self = [super init]) {
        _subscribedRemoteStreamNeedMute = NO;
    }
    return self;
}

#pragma mark - [ Father Public Methods ]
- (void)createRTCEngine {
    if (!_engine) {
        if (![PLVFdUtil checkStringUseable:self.rtcAppId]) {
            PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - init engine failed,appId is nil %@",self.rtcAppId);
            return;
        }
        /// 创建 RTC引擎
#ifdef VOLCRtcSdkExt_ios
        _engine = [[VOLCRtcEngineExtCLS alloc] initWithAppId:self.rtcAppId delegate:self parameters:@{}];
#else
        _engine = [[VOLCRtcEngineCLS alloc] initWithAppId:self.rtcAppId delegate:self parameters:nil];
#endif
        [_engine setExtensionConfig:self.appGroup];
        [_engine setAudioVolumeIndicationInterval:300];
        
        self.localAudioStreamMute = NO;
        self.localVideoStreamMute = NO;
        self.localStreamPublishing = NO;
        self.screenFinishedByEngine = NO;
        self.publishStreamSourceType = PLVBRTCStreamSourceType_Camera;
        self.subscribedStreamDict = [[NSMutableDictionary alloc]init];
        self.roomStreamDict = [[NSMutableDictionary alloc]init];
        
        glcontext = [[EAGLContext alloc] initWithAPI:kEAGLRenderingAPIOpenGLES3];
        if ([EAGLContext currentContext] != glcontext) {
            [EAGLContext setCurrentContext:glcontext];
        }
        self.eaglContext = glcontext;
        self.imageUtils = [[PLVImageUtil alloc] init];
    }
}

- (void)destroyRTCEngine {
    __weak typeof(self) weakSelf = self;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_MSEC)), dispatch_get_main_queue(), ^{
        [weakSelf.engine destroyEngine];
        weakSelf.engine.delegate = nil;
        weakSelf.engine = nil;
    });
}

- (int)joinRtcChannelWithChannelId:(NSString *)channelId userLinkMicId:(NSString *)userLinkMicId {
    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:userLinkMicId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - join rtc channel failed,channelId:%@, userLinkMicId:%@",channelId,userLinkMicId);
        return -1;
    }else{
        self.channelId = channelId;
        self.userLinkMicId = userLinkMicId;
    }
    
    ByteRTCUserInfo *userInfo = [[VOLCRtcUserInfoCLS alloc] init];
    userInfo.userId = self.userLinkMicId;
    
    ByteRTCRoomConfig *roomConfig = [[VOLCRtcRoomConfigCLS alloc] init];
    roomConfig.isAutoPublish = NO;
    roomConfig.isAutoSubscribeAudio = NO;
    roomConfig.isAutoSubscribeVideo = YES;
    roomConfig.profile = ByteRTCRoomProfileLiveBroadcasting;
    
    [self.engine joinRoomByKey:self.rtcToken roomId:self.channelId userInfo:userInfo rtcRoomConfig:roomConfig];
    return 0;
}

- (int)leaveRtcChannel{
    if (self.hadJoinedRTC) {
        int ret = [self.engine leaveRoom];
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:leaveRTCChannelComplete:)]) {
            [self.delegate plvbLinkMicManager:self leaveRTCChannelComplete:self.channelId];
        }
        [self destroyResource];
        return ret;
    }else{
        return 0;
    }
}

/// 切换订阅流音视频类型
- (void)switchSubscribeStreamMediaTypeWithRTCUserId:(NSString *)rtcUserId mediaType:(PLVBRTCSubscribeStreamMediaType)toMediaType {
    PLVBLinkMicVOLCRemoteUserStreamModel *model = self.subscribedStreamDict[rtcUserId];
    if (model) {
        ByteRTCMediaStreamType rtcMediaType = (ByteRTCMediaStreamType)toMediaType;
        // 先取消订阅，再订阅新的流
        [self.engine unSubscribeStream:rtcUserId mediaStreamType:model.cameraStreamType];
        [self updateSubscribedStreamDictStatus:NO streamIndex:ByteRTCStreamIndexMain streamType:model.cameraStreamType byUid:rtcUserId];
        
        [self.engine subscribeStream:rtcUserId mediaStreamType:rtcMediaType];
        [self updateSubscribedStreamDictStatus:YES streamIndex:ByteRTCStreamIndexMain streamType:rtcMediaType byUid:rtcUserId];
    }
}

/// 切换推送流的类型
- (void)switchPublishStreamSourceType:(PLVBRTCStreamSourceType)streamSourceType {
    if (![PLVFdUtil checkStringUseable:self.appGroup]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - switchPublishStreamSourceType failed, Please setup appGroup first");
        return;
    }
    if (self.publishStreamSourceType == streamSourceType) {
        return;
    }
    self.publishStreamSourceType = streamSourceType;
    if (streamSourceType == PLVBRTCStreamSourceType_Camera) {
        /// 切换到摄像头采集
        if (@available(iOS 11.0, *)) {
            [self.engine stopScreenCapture];
        }
        [self enableLocalVideo:self.localVideoEnable];

        if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:didScreenCaptureStopedReason:)]) {
            [self.delegate plvbLinkMicManager:self didScreenCaptureStopedReason:0];
        }
    } else if (streamSourceType == PLVBRTCStreamSourceType_Screen) {
        /// 切换到屏幕采集
        [self.engine stopVideoCapture];
        [self.engine setLocalVideoCanvas:ByteRTCStreamIndexMain withCanvas:nil];
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManagerDidScreenCaptureStarted:)]) {
            [self.delegate plvbLinkMicManagerDidScreenCaptureStarted:self];
        }
    }
}

/// 全体远端流mute
- (void)muteSubscribedRemoteStreamInLocalWithMute:(BOOL)mute {
    _subscribedRemoteStreamNeedMute = mute;
    for (NSString *keyUserId in self.subscribedStreamDict.allKeys) {
        PLVBLinkMicVOLCRemoteUserStreamModel *model = self.subscribedStreamDict[keyUserId];
        if (mute) {
            [self.engine setRemoteVideoCanvas:keyUserId withIndex:ByteRTCStreamIndexMain withCanvas:nil];
        }else {
            ByteRTCVideoCanvas *canvas = [[VOLCRtcVideoCanvasCLS alloc] init];
            canvas.view = model.renderSuperView;
            canvas.uid = keyUserId;
            canvas.roomId = self.channelId;
            canvas.renderMode = [self judgeUserIsATeacher:keyUserId] ? ByteRTCRenderModeFit : ByteRTCRenderModeHidden;
            [self.engine setRemoteVideoCanvas:keyUserId withIndex:ByteRTCStreamIndexMain withCanvas:canvas];
        }
    }
}

/// 订阅流
- (void)subscribeStreamWithRTCUserId:(NSString *)rtcUserId renderOnView:(UIView *)renderSuperView mediaType:(PLVBRTCSubscribeStreamMediaType)mediaType{
    if (![PLVFdUtil checkStringUseable:rtcUserId] || !renderSuperView || ![renderSuperView isKindOfClass:UIView.class]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - add preview error uid %@ superView %@",rtcUserId,renderSuperView);
        return;
    }
    
    if ([rtcUserId isEqualToString:self.userLinkMicId]) {
        // 设置本地视频渲染视图
        [self setupLocalPreviewWithRenderSuperView:renderSuperView];
    } else {
        // 订阅流
        ByteRTCMediaStreamType rtcMediaType = (ByteRTCMediaStreamType)mediaType;
        [self.engine subscribeStream:rtcUserId mediaStreamType:rtcMediaType];
        
        // 设置远端渲染视图
        if (!_subscribedRemoteStreamNeedMute) {
            ByteRTCVideoCanvas *canvas = [[VOLCRtcVideoCanvasCLS alloc] init];
            canvas.view = renderSuperView;
            canvas.uid = rtcUserId;
            canvas.roomId = self.channelId;
            canvas.renderMode = [self judgeUserIsATeacher:rtcUserId] ? ByteRTCRenderModeFit : ByteRTCRenderModeHidden;
            [self.engine setRemoteVideoCanvas:rtcUserId withIndex:ByteRTCStreamIndexMain withCanvas:canvas];
        }
        
        // 更新本地订阅 model
        [self updateSubscribedStreamDictStatus:YES streamIndex:ByteRTCStreamIndexMain streamType:rtcMediaType byUid:rtcUserId];
        PLVBLinkMicVOLCRemoteUserStreamModel *model = self.subscribedStreamDict[rtcUserId];
        if (model) {
            model.renderSuperView = renderSuperView;
        }
    }
}

/// 订阅流
- (void)subscribeStreamWithRTCUserId:(NSString *)rtcUserId renderOnView:(UIView *)renderSuperView mediaType:(PLVBRTCSubscribeStreamMediaType)mediaType subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode {
    [self subscribeStreamWithRTCUserId:rtcUserId renderOnView:renderSuperView mediaType:mediaType];
}

/// 取消订阅
- (void)unsubscribeStreamWithRTCUserId:(NSString *)rtcUserId {
    if (![PLVFdUtil checkStringUseable:rtcUserId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - removeUserSubscribeUserId failed ,rtcUserId illegal %@",rtcUserId);
        return;
    }
    BOOL localUser = [rtcUserId isEqualToString:self.userLinkMicId];
    if (localUser) {
        // 本地
        self.localCanvas = nil;
        [self.engine setLocalVideoCanvas:ByteRTCStreamIndexMain withCanvas:nil];
    } else {
        // 远端
        PLVBLinkMicVOLCRemoteUserStreamModel *model = self.subscribedStreamDict[rtcUserId];
        if (model) {
            // 取消渲染绑定
            [self.engine setRemoteVideoCanvas:rtcUserId withIndex:ByteRTCStreamIndexMain withCanvas:nil];
            // 取消订阅
            [self updateSubscribedStreamDictStatus:NO streamIndex:ByteRTCStreamIndexMain streamType:ByteRTCMediaStreamTypeBoth byUid:rtcUserId];
            // 更新本地订阅 model
            model.renderSuperView = nil;
            [self.engine unSubscribeStream:rtcUserId mediaStreamType:ByteRTCMediaStreamTypeBoth];
        }
    }
}

/// 取消订阅
- (void)unsubscribeStreamWithRTCUserId:(NSString *)rtcUserId subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode{
    [self unsubscribeStreamWithRTCUserId:rtcUserId];
}

- (void)setTeacherUserId:(NSString *)teacherId {
    if (![PLVFdUtil checkStringUseable:teacherId]) {
        return;
    }
    self.teacherId = teacherId;
}

- (int)setUserRoleTo:(PLVBLinkMicRoleType)roleType{
    _roleType = roleType;
    if (roleType == PLVBLinkMicRoleBroadcaster) {
        [self.engine setUserVisibility:YES];
        if (!self.localStreamPublishing){
            __weak typeof(self) weakSelf = self;
            self.joinRTCChannelBlock = ^{
                [weakSelf startPushLocalStream];
            };
            
            if (self.hadJoinedRTC){
                self.joinRTCChannelBlock();
                self.joinRTCChannelBlock = nil;
            }
        }
    }else{
        [self.engine setUserVisibility:NO];
        self.localStreamPublishing = NO;
        self.hadSetupDefault = NO;
    }
    return 0;
}

#pragma mark 本地 摄像头 使用
- (void)setupVideoEncoderConfiguration:(PLVBRTCVideoEncoderConfiguration *)videoEncoderConfiguration{
    if (videoEncoderConfiguration && [videoEncoderConfiguration isKindOfClass:PLVBRTCVideoEncoderConfiguration.class]) {
        [super setupVideoEncoderConfiguration:videoEncoderConfiguration];

        ByteRTCVideoSolution *solution = [[VOLCRtcVideoSolutionCLS alloc] init];
        solution.videoSize = videoEncoderConfiguration.videoResolution;
        solution.frameRate = videoEncoderConfiguration.videoFrameRate;
        solution.maxKbps = -1;
        if (videoEncoderConfiguration.videoQosPreference == PLVBRTCVideoQosPreferenceSmooth) {
            solution.encoderPreference = ByteRTCVideoEncoderPreferenceMaintainFramerate;
        } else {
            solution.encoderPreference = ByteRTCVideoEncoderPreferenceMaintainQuality;
        }
        [self.engine setVideoEncoderConfig:@[solution]];
        
        ByteRTCVideoCaptureConfig *config = [[VOLCRtcVideoCaptureConfigCLS alloc]init];
        config.preference = ByteRTCVideoCapturePreferenceMannal;
        config.videoSize = videoEncoderConfiguration.videoResolution;
        config.frameRate = videoEncoderConfiguration.videoFrameRate;
        [self.engine setVideoCaptureConfig:config];
        
        
        if (videoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Auto) {
            [self.engine setLocalVideoMirrorType:self.localCameraFront ? ByteRTCMirrorTypeRenderAndEncoder : ByteRTCMirrorTypeNone];
        }else if (videoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Enabled){
            [self.engine setLocalVideoMirrorType:ByteRTCMirrorTypeRenderAndEncoder];
        }else{
            [self.engine setLocalVideoMirrorType:ByteRTCMirrorTypeNone];
        }
    }else{
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - setupVideoEncoderConfiguration failed, videoEncoderConfiguration invalid %@",videoEncoderConfiguration);
    }
}

- (void)setupLocalPreviewWithCanvasModel:(PLVBRTCVideoViewCanvasModel *)canvasModel {
    if (![canvasModel checkModelValid]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - setupLocalPreviewWithCanvasModel failed, canvasModel invalid, userRTCId:%@ renderCanvasView:%@",canvasModel.userRTCId, canvasModel.renderCanvasView);
        return;
    }
    self.currentLocalPreviewCanvasModel = canvasModel;
    [self setupLocalPreviewWithRenderSuperView:canvasModel.renderCanvasView];
}

- (int)setupLocalVideoPreviewMirrorMode:(PLVBRTCVideoMirrorMode)mirrorMode {
    if (self.localCameraFront) {
        if (mirrorMode == PLVBRTCVideoMirrorMode_Auto ||
            mirrorMode == PLVBRTCVideoMirrorMode_Enabled) {
            [self.engine setLocalVideoMirrorMode:ByteRTCMirrorModeOn];
        }else{
            [self.engine setLocalVideoMirrorMode:ByteRTCMirrorModeOff];
        }
    }else{
        /// VOLC 不支持后置摄像头镜像，此句仅表示固定为此值
        [self.engine setLocalVideoMirrorMode:ByteRTCMirrorModeOff];
    }
    self.localVideoMirrorMode = mirrorMode;
    return 0;
}

- (int)switchLocalUserCamera:(BOOL)frontCamera {
    ByteRTCCameraID cameraID = frontCamera ? ByteRTCCameraIDFront : ByteRTCCameraIDBack;
    int resultCode = [self.engine switchCamera:cameraID];
    if (resultCode == 0) {
        self.localCameraFront = frontCamera;
        if (frontCamera) { self.localCameraTorchOpen = NO; }
        if (self.currentVideoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Auto) {
            [self.engine setLocalVideoMirrorType:self.localCameraFront ? ByteRTCMirrorTypeRenderAndEncoder : ByteRTCMirrorTypeNone];
        } else if (self.currentVideoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Enabled){
            [self.engine setLocalVideoMirrorType:ByteRTCMirrorTypeRenderAndEncoder];
        } else {
            [self.engine setLocalVideoMirrorType:ByteRTCMirrorTypeNone];
        }
        self.localCameraJustSwitched = YES;
    }
    return resultCode;
}

- (int)openLocalUserCameraTorch:(BOOL)openCameraTorch {
    int resultCode = -1;
    if (openCameraTorch) {
        if ([self.engine isCameraTorchSupported]) {
            resultCode = [self.engine setCameraTorch:ByteRTCTorchStateOn];
        }
    }else {
        resultCode = [self.engine setCameraTorch:ByteRTCTorchStateOff];
    }
    if (resultCode == 0) { self.localCameraTorchOpen = openCameraTorch; }
    return resultCode;
}

- (int)openLocalUserCamera:(BOOL)openCamera {
    [self enableLocalVideo:openCamera];
    [self muteLocalVideoStream:!openCamera];
    self.localCameraOpen = openCamera;
    return 0;
}

- (int)localVideoStreamOpenPlaceholder:(BOOL)openPlaceholder{
    return 0; /// VOLC暂未对接
}

- (int)enableLocalVideo:(BOOL)enabled{
    if (enabled) {
        [self.engine startVideoCapture];
        [self.engine setLocalVideoCanvas:ByteRTCStreamIndexMain withCanvas:self.localCanvas];
    }else {
        [self.engine stopVideoCapture];
        [self.engine setLocalVideoCanvas:ByteRTCStreamIndexMain withCanvas:nil];
    }
    self.localVideoEnable = enabled;
    return 0;
}

- (int)startLocalPreview:(BOOL)start{
    [self enableLocalVideo:start];
    return 0;
}

- (int)muteLocalVideoStream:(BOOL)mute {
    if (mute) {
        [self.engine unpublishStream:ByteRTCMediaStreamTypeVideo];
    }else {
        [self.engine publishStream:ByteRTCMediaStreamTypeVideo];
    }
    self.localVideoStreamMute = mute;
    return 0;
}

- (void)enableLocalVideoFrameProcess:(BOOL)enabled {
    if (enabled) {
        ByteRTCVideoPreprocessorConfig *config = [[VOLCRtcVideoPreprocessorConfigCLS alloc]init];
        config.required_pixel_format = ByteRTCVideoPixelFormatUnknown;
        [self.engine registerLocalVideoProcessor:self withConfig:config];
    }else {
        [self.engine registerLocalVideoProcessor:nil withConfig:nil];
    }
}

#pragma mark 本地 麦克风 使用
- (int)changeLocalMicVolume:(CGFloat)micVolume{
    [self.engine setCaptureVolume:ByteRTCStreamIndexMain volume:micVolume];
    self.localMicVolume = micVolume;
    return 0;
}

- (int)openLocalUserMic:(BOOL)openMic {
    [self enableLocalAudio:openMic];
    [self muteLocalAudioStream:!openMic];
    self.localMicOpen = openMic;
    return 0;
}

- (int)enableLocalAudio:(BOOL)enabled {
    if (enabled) {
        [self.engine startAudioCapture];
    }else{
        [self.engine stopAudioCapture];
    }
    self.localAudioEnable = enabled;
    return 0;
}

- (int)muteLocalAudioStream:(BOOL)mute {
    if (mute) {
        [self.engine unpublishStream:ByteRTCMediaStreamTypeAudio];
    }else {
        [self.engine publishStream:ByteRTCMediaStreamTypeAudio];
    }
    self.localAudioStreamMute = mute;
    return 0;
}

- (int)switchNoiseCancellationLevelTo:(PLVBLinkMicNoiseCancellationLevel)level {
    self.localNoiseCancellationLevel = level;
    return 0; // Volc暂不支持
}

- (int)enableExternalDevice:(BOOL)enabled {
    self.localExternalDeviceEnabled = enabled;
    return 0; // Volc暂不支持
}

#pragma mark Getter
- (BOOL)engineIsReady{
    return _engine ? YES : NO;
}

#pragma mark - [ Father Private Methods ]
- (void)setupRTCToken:(NSDictionary *)decodeDict {
    self.rtcAppId = decodeDict[@"appId"];
    self.rtcToken = decodeDict[@"accessToken"];
    
    if (self.hadJoinedRTC) {
        [self.engine updateToken:self.rtcToken];
    }
}

#pragma mark - [ Private Methods ]
- (void)destroyResource {
    self.hadJoinedRTC = NO;
    self.hadSetupDefault = NO;
    self.localStreamPublishing = NO;
    self.localAudioStreamMute = NO;
    self.localVideoStreamMute = NO;
    self.screenFinishedByEngine = NO;
    [self.subscribedStreamDict removeAllObjects];
    self.subscribedStreamDict = nil;
    [self.roomStreamDict removeAllObjects];
    self.roomStreamDict = nil;
}

/// 设置本地渲染视图
- (void)setupLocalPreviewWithRenderSuperView:(UIView *)renderSuperView {
    ByteRTCRenderMode mode = [self judgeUserIsATeacher:self.userLinkMicId] ? ByteRTCRenderModeFit : ByteRTCRenderModeHidden;
    if (self.currentLocalPreviewCanvasModel) {
        mode = self.currentLocalPreviewCanvasModel.rtcVideoVideoFillMode == PLVBRTCVideoViewFillMode_Fit ? ByteRTCRenderModeFit : ByteRTCRenderModeHidden;
    }
    ByteRTCVideoCanvas *canvas = [[VOLCRtcVideoCanvasCLS alloc] init];
    canvas.view = renderSuperView;
    canvas.uid = self.userLinkMicId;
    canvas.roomId = self.channelId;
    canvas.renderMode = mode;
    self.localCanvas = canvas;
    [self.engine setLocalVideoCanvas:ByteRTCStreamIndexMain withCanvas:canvas];
}

/// 判断某用户是不是老师身份
- (BOOL)judgeUserIsATeacher:(NSString *)rtcUserId {
    BOOL isTeacher = NO;
    if ([PLVFdUtil checkStringUseable:self.teacherId]) {
        isTeacher = [rtcUserId isEqualToString:self.teacherId];
    } else {
        isTeacher = [rtcUserId isEqualToString:self.channelId];
    }
    return isTeacher;
}

/// 开始推本地流
- (void)startPushLocalStream {
    [self.engine publishStream:ByteRTCMediaStreamTypeBoth];
    self.localStreamPublishing = YES;
    [self muteLocalAudioStream:self.localAudioStreamMute];
    /// 需配置默认项
    if (!self.hadSetupDefault) {
        [self callbackForCanSetupLocalHardwareDefaultState];
        self.hadSetupDefault = YES;
    }
}

/// 回调mute事件
-(void)callbackRemoteUserStreamStatus:(BOOL)mute streamType:(ByteRTCMediaStreamType)type streamIndex:(ByteRTCStreamIndex)index byUid:(NSString *)userId {
    PLVBRTCSubscribeStreamSourceType sourceType = index == ByteRTCStreamIndexMain ? PLVBRTCSubscribeStreamSourceType_Camera : PLVBRTCSubscribeStreamSourceType_Screen;
    
    if (type & ByteRTCMediaStreamTypeAudio) {
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didAudioMuted:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didAudioMuted:mute byUid:userId];
        }
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didAudioMuted:streamSourceType:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didAudioMuted:mute streamSourceType:sourceType byUid:userId];
        }
    }
    
    if (type & ByteRTCMediaStreamTypeVideo){
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didVideoMuted:streamSourceType:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didVideoMuted:mute streamSourceType:sourceType byUid:userId];
        }
        
        PLVBLinkMicVOLCRemoteUserStreamModel *model = self.subscribedStreamDict[userId];
        if (mute && model) {
            if ((model.hadCameraStream && (model.cameraStreamType & ByteRTCMediaStreamTypeVideo) && index == ByteRTCStreamIndexScreen) ||
                (model.hadScreenStream && (model.screenStreamType & ByteRTCMediaStreamTypeVideo) && index == ByteRTCStreamIndexMain)) {
                // 单流适配——当存在视频流且屏幕流退出时或者当存在屏幕流且视频流退出时，不会上报video mute
                return;
            }
        }
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didVideoMuted:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didVideoMuted:mute byUid:userId];
        }
    }
}

/// 更新已订阅的流信息
-(void)updateSubscribedStreamDictStatus:(BOOL)join streamIndex:(ByteRTCStreamIndex)index streamType:(ByteRTCMediaStreamType)type byUid:(NSString *)userId {
    if (!join && !self.subscribedStreamDict[userId]) {
        return;
    }
    if (self.subscribedStreamDict[userId]) {
        // 更新
        PLVBLinkMicVOLCRemoteUserStreamModel *model = self.subscribedStreamDict[userId];
        if (index == ByteRTCStreamIndexMain) {
            model.cameraStreamType = join ?
            model.cameraStreamType | type :
            model.cameraStreamType &~ type;
            model.hadCameraStream = model.cameraStreamType & ByteRTCMediaStreamTypeBoth;
        }else {
            model.screenStreamType = join ?
            model.screenStreamType | type :
            model.screenStreamType &~ type;
            model.hadScreenStream = model.screenStreamType & ByteRTCMediaStreamTypeBoth;
        }
        if (!model.hadCameraStream && !model.hadScreenStream) {
            [self.subscribedStreamDict removeObjectForKey:userId];
        }
        else {
            [self.subscribedStreamDict setObject:model forKey:userId];
        }
    }
    else {
        // 新增
        PLVBLinkMicVOLCRemoteUserStreamModel *model = [[PLVBLinkMicVOLCRemoteUserStreamModel alloc]init];
        if (index ==  ByteRTCStreamIndexMain) {
            model.hadCameraStream = YES;
            model.cameraStreamType = type;
        }else {
            model.hadScreenStream = YES;
            model.screenStreamType = type;
        }
        [self.subscribedStreamDict setObject:model forKey:userId];
    }
}

/// 更新房间流的信息
-(void)updateRoomStreamDictStatus:(BOOL)join streamIndex:(ByteRTCStreamIndex)index streamType:(ByteRTCMediaStreamType)type byUid:(NSString *)userId {
    if (!join && !self.roomStreamDict[userId]) {
        return;
    }
    if (self.roomStreamDict[userId]) {
        // 更新
        PLVBLinkMicVOLCRemoteUserStreamModel *model = self.roomStreamDict[userId];
        if (index == ByteRTCStreamIndexMain) {
            model.cameraStreamType = join ?
            model.cameraStreamType | type :
            model.cameraStreamType &~ type;
            model.hadCameraStream = model.cameraStreamType & ByteRTCMediaStreamTypeBoth;
        }else {
            model.screenStreamType = join ?
            model.screenStreamType | type :
            model.screenStreamType &~ type;
            model.hadScreenStream = model.screenStreamType & ByteRTCMediaStreamTypeBoth;
        }
        if (!model.hadCameraStream && !model.hadScreenStream) {
            [self.roomStreamDict removeObjectForKey:userId];
        }
        else {
            [self.roomStreamDict setObject:model forKey:userId];
        }
    }
    else {
        // 新增
        PLVBLinkMicVOLCRemoteUserStreamModel *model = [[PLVBLinkMicVOLCRemoteUserStreamModel alloc]init];
        if (index ==  ByteRTCStreamIndexMain) {
            model.hadCameraStream = YES;
            model.cameraStreamType = type;
        }else {
            model.hadScreenStream = YES;
            model.screenStreamType = type;
        }
        [self.roomStreamDict setObject:model forKey:userId];
    }
}

#pragma mark - [ Delegate ]
#pragma mark 引擎事件回调
/// 状态相关
- (void)rtcEngine:(ByteRTCEngineKit *_Nonnull)engine onRoomStats:(ByteRTCRoomStats *_Nonnull)stats {
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvbLinkMicManager:rtcStatistics:)]) {
        
        PLVBRTCStatistics *statistics = [[PLVBRTCStatistics alloc] init];
        statistics.rtt = stats.rtt;
        statistics.upLoss = stats.txLostrate * 100;
        statistics.downLoss = stats.rxLostrate * 100;
        
        [self.delegate plvbLinkMicManager:self rtcStatistics:statistics];
    }
}

- (void)rtcEngine:(ByteRTCEngineKit *)engine onError:(ByteRTCErrorCode)errorCode {
    NSString *reason = [VOLCRtcEngineCLS getErrorDescription:errorCode];
    PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - error %@ code %ld",reason,(long)errorCode);
    NSError * finalError = [super errorWithCode:errorCode errorDescription:reason];
    [super callbackForDidOccurError:finalError];
    
    if (errorCode == ByteRTCErrorCodeDuplicateLogin ||
        errorCode == ByteRTCErrorCodeKickedOut ||
        errorCode == ByteRTCErrorCodeTokenExpired ||
        errorCode == ByteRTCErrorCodeUpdateTokenWithInvalidToken ||
        errorCode == ByteRTCErrorCodeRoomDismiss) {
        [self leaveRtcChannel];
    }
}

- (void)onTokenWillExpire:(ByteRTCEngineKit *)engine {
    PLVF_NORMAL_LOG_DEBUG(@"BLinkMic", @"%@", NSStringFromSelector(_cmd));
    self.rtcTokenAvailable = NO;
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManagerTokenExpires:)]) {
        [self.delegate plvbLinkMicManagerTokenExpires:self];
    }
}

#pragma mark 房间事件回调
- (void)rtcEngine:(ByteRTCEngineKit *)engine onRoomStateChanged:(NSString *)roomId withUid:(NSString *)uid state:(NSInteger)state extraInfo:(NSString *)extraInfo {
    __weak typeof(self) weakSelf = self;
    if ([uid isEqualToString:self.userLinkMicId]) {
        // 本地用户进房
        if (state == 0) {
            PLVF_NORMAL_LOG_DEBUG(@"BLinkMic", @"PLVBLinkMicManager - join rtc channel success,channel:%@, uid:%@, result %ld", self.channelId, self.userLinkMicId, (long)state);
            
            self.hadJoinedRTC = YES;
            plv_dispatch_main_async_safe(^{
                if (self.joinRTCChannelBlock){
                    self.joinRTCChannelBlock();
                    self.joinRTCChannelBlock = nil;
                }
                
                if ([weakSelf.delegate respondsToSelector:@selector(plvbLinkMicManager:joinRTCChannelComplete:uid:)]) {
                    [weakSelf.delegate plvbLinkMicManager:weakSelf joinRTCChannelComplete:weakSelf.channelId uid:weakSelf.userLinkMicId];
                }
            })
        }else {
            PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - join rtc channel failed, errorCode:%ld",(long)state);
            
            self.hadJoinedRTC = NO;
            if (state == -1006) {
                if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:leaveRTCChannelComplete:)]) {
                    [self.delegate plvbLinkMicManager:self leaveRTCChannelByServerComplete:self.channelId];
                }
            }
            plv_dispatch_main_async_safe(^{
                if ([weakSelf.delegate respondsToSelector:@selector(plvbLinkMicManager:joinRTCChannelFailure:uid:)]) {
                    [weakSelf.delegate plvbLinkMicManager:weakSelf joinRTCChannelFailure:weakSelf.channelId uid:weakSelf.userLinkMicId];
                }
            });
        }
    }
}

- (void)rtcEngine:(ByteRTCEngineKit *)engine onLeaveRoomWithStats:(ByteRTCRoomStats *)stats {
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:leaveRTCChannelComplete:)]) {
        [self.delegate plvbLinkMicManager:self leaveRTCChannelComplete:self.channelId];
    }
    [self destroyResource];
}


- (void)rtcEngine:(ByteRTCEngineKit *)engine connectionChangedToState:(ByteRTCConnectionState)state {
    switch (state) {
        case ByteRTCConnectionStateDisconnected: // 连接中断
        {
            self.connectionState = PLVBLinkMicConnectionStateDisconnected;
        }
            break;
        case ByteRTCConnectionStateConnecting: // 首次连接中
        {
            self.connectionState = PLVBLinkMicConnectionStateConnecting;
        }
            break;
        case ByteRTCConnectionStateConnected: // 首次连接成功
        {
            self.connectionState = PLVBLinkMicConnectionStateConnected;
        }
            break;
        case ByteRTCConnectionStateReconnecting: // 重新连接中
        {
            self.connectionState = PLVBLinkMicConnectionStateReconnecting;
        }
            break;
        case ByteRTCConnectionStateReconnected: // 断开后重连成功
        {
            self.connectionState = PLVBLinkMicConnectionStateConnected;
        }
            break;
        case ByteRTCConnectionStateLost: // 断开超过10s，仍然会重连
        {
            self.connectionState = PLVBLinkMicConnectionStateFailed;
        }
            break;
        default:
            break;
    }
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:rtcConnectionStateDidChanged:)]) {
        [self.delegate plvbLinkMicManager:self rtcConnectionStateDidChanged:self.connectionState];
    }
}

#pragma mark 成员事件回调
- (void)rtcEngine:(ByteRTCEngineKit *)engine onUserJoined:(ByteRTCUserInfo *)userInfo elapsed:(NSInteger)elapsed {
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didJoinedOfUid:)]) {
        [self.delegate plvbLinkMicManager:self didJoinedOfUid:userInfo.userId];
    }
}

- (void)rtcEngine:(ByteRTCEngineKit *)engine onUserLeave:(NSString *)uid reason:(ByteRTCUserOfflineReason)reason {
    if (reason == ByteRTCUserOfflineReasonSwitchToInvisible) {
        
    }else {
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didOfflineOfUid:)]) {
            [self.delegate plvbLinkMicManager:self didOfflineOfUid:uid];
        }
    }
}

#pragma mark 音视频 流事件回调
/// 流的订阅/取消订阅结果回调
- (void)rtcEngine:(ByteRTCEngineKit *)engine onStreamSubscribed:(ByteRTCSubscribeState)state userId:(NSString *)userId subscribeConfig:(ByteRTCSubscribeConfig *)info {
    
}

/// 新的摄像头流加入房间
- (void)rtcEngine:(ByteRTCEngineKit *)engine onUserPublishStream:(NSString *)userId type:(ByteRTCMediaStreamType)type {
    if (![PLVFdUtil checkStringUseable:userId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - newStreamHasJoinRoom illegal stream userId %@", userId);
        return;
    }

    [self updateRoomStreamDictStatus:YES streamIndex:ByteRTCStreamIndexMain streamType:type byUid:userId];
    
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didNewStreamJoinRoomOfUid:)]) {
        [self.delegate plvbLinkMicManager:self didNewStreamJoinRoomOfUid:userId];
    }
    
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:streamJoinRoom:userRTCId:)]) {
        [self.delegate plvbLinkMicManager:self streamJoinRoom:PLVBRTCSubscribeStreamSourceType_Camera userRTCId:userId];
    }
    
    [self callbackRemoteUserStreamStatus:NO streamType:type streamIndex:ByteRTCStreamIndexMain byUid:userId];
}

/// 新的屏幕流加入房间
- (void)rtcEngine:(ByteRTCEngineKit *)engine onUserPublishScreen:(NSString *)userId type:(ByteRTCMediaStreamType)type {
    if (![PLVFdUtil checkStringUseable:userId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - newScreenStreamHasJoinRoom illegal stream userId %@", userId);
        return;
    }
    
    [self updateRoomStreamDictStatus:YES streamIndex:ByteRTCStreamIndexScreen streamType:type byUid:userId];
    
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didNewStreamJoinRoomOfUid:)]) {
        [self.delegate plvbLinkMicManager:self didNewStreamJoinRoomOfUid:userId];
    }
    
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:streamJoinRoom:userRTCId:)]) {
        [self.delegate plvbLinkMicManager:self streamJoinRoom:PLVBRTCSubscribeStreamSourceType_Screen userRTCId:userId];
    }
    
    [self callbackRemoteUserStreamStatus:NO streamType:type streamIndex:ByteRTCStreamIndexScreen byUid:userId];
}

/// 远端摄像头流退出
- (void)rtcEngine:(ByteRTCEngineKit *)engine onUserUnPublishStream:(NSString *)userId type:(ByteRTCMediaStreamType)type reason:(ByteRTCStreamRemoveReason)reason {
    if (![PLVFdUtil checkStringUseable:userId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - removeRemoteStream leaveRoom illegal stream userId %@", userId);
        return;
    }
    
    [self updateRoomStreamDictStatus:NO streamIndex:ByteRTCStreamIndexMain streamType:type byUid:userId];
    
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:streamLeaveRoom:userRTCId:)]) {
        [self.delegate plvbLinkMicManager:self streamLeaveRoom:PLVBRTCSubscribeStreamSourceType_Camera userRTCId:userId];
    }
    
    [self callbackRemoteUserStreamStatus:YES streamType:type streamIndex:ByteRTCStreamIndexMain byUid:userId];
    
    if (![self.roomStreamDict objectForKey:userId]) {
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:remoteUserTotalStreamsDidLeaveRoom:)]) {
            [self.delegate plvbLinkMicManager:self remoteUserTotalStreamsDidLeaveRoom:userId];
        }
    }
}

/// 远端屏幕流退出
- (void)rtcEngine:(ByteRTCEngineKit *)engine onUserUnPublishScreen:(NSString *)userId type:(ByteRTCMediaStreamType)type reason:(ByteRTCStreamRemoveReason)reason {
    if (![PLVFdUtil checkStringUseable:userId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - removeRemoteStream leaveRoom illegal stream userId %@", userId);
        return;
    }
    [self updateRoomStreamDictStatus:NO streamIndex:ByteRTCStreamIndexScreen streamType:type byUid:userId];
    
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:streamLeaveRoom:userRTCId:)]) {
        [self.delegate plvbLinkMicManager:self streamLeaveRoom:PLVBRTCSubscribeStreamSourceType_Screen userRTCId:userId];
    }
    
    [self callbackRemoteUserStreamStatus:YES streamType:type streamIndex:ByteRTCStreamIndexScreen byUid:userId];
    
    if (![self.roomStreamDict objectForKey:userId]) {
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:remoteUserTotalStreamsDidLeaveRoom:)]) {
            [self.delegate plvbLinkMicManager:self remoteUserTotalStreamsDidLeaveRoom:userId];
        }
    }
}

#pragma mark 硬件设备事件
- (void)rtcEngine:(ByteRTCEngineKit * _Nonnull)engine onAudioVolumeIndication:(NSArray<ByteRTCAudioVolumeInfo *> * _Nonnull)speakers totalRemoteVolume:(NSInteger)totalRemoteVolume {
    if ([PLVFdUtil checkArrayUseable:speakers]) {
        NSMutableDictionary * volumeDict = [[NSMutableDictionary alloc] init];
        for (ByteRTCAudioVolumeInfo * info in speakers) {
            float percent = [NSNumber numberWithUnsignedInteger:info.linearVolume].floatValue / 255.0;
            NSNumber * volume = [NSNumber numberWithFloat:percent];
            if (volume && [volume isKindOfClass:NSNumber.class] &&
                [PLVFdUtil checkStringUseable:info.uid]) {
                [volumeDict setObject:volume forKey:info.uid];
            }
            if ([info.uid isEqualToString:self.userLinkMicId]) {
                BOOL audible = percent >= 0.156 ? YES : NO;
                if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:localVoiceValue:receivedLocalAudibleVoice:)]) {
                    [self.delegate plvbLinkMicManager:self localVoiceValue:percent receivedLocalAudibleVoice:audible];
                }
            }
        }
        if ([PLVFdUtil checkDictionaryUseable:volumeDict]) {
            if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:reportAudioVolumeOfSpeakers:)]) {
                [self.delegate plvbLinkMicManager:self reportAudioVolumeOfSpeakers:volumeDict];
            }
        }
    }
}

#pragma mark 屏幕采集回调
- (void)rtcEngine:(ByteRTCEngineKit *)engine onVideoDeviceStateChanged:(NSString *)device_id device_type:(ByteRTCVideoDeviceType)device_type device_state:(ByteRTCMediaDeviceState)device_state device_error:(ByteRTCMediaDeviceError)device_error {
    if (device_type == ByteRTCVideoDeviceTypeScreenCaptureDevice) {
        if (device_state == ByteRTCMediaDeviceStateStarted) {
            [self.engine publishScreen:ByteRTCMediaStreamTypeBoth];//发布屏幕共享流
        } else if (device_state == ByteRTCMediaDeviceStateStopped ||
                   device_state == ByteRTCMediaDeviceStateRuntimeError) {
            [self switchPublishStreamSourceType:PLVBRTCStreamSourceType_Camera];
            [self.engine unpublishScreen:ByteRTCMediaStreamTypeBoth];//关闭屏幕共享
        }
    }
}

- (void)rtcEngine:(ByteRTCEngineKit* _Nonnull)engine log:(NSDictionary* _Nonnull)dict {
    PLVF_NORMAL_LOG_DEBUG(@"BLinkMic", @"VOLCManager - log: %@", dict);
}

#pragma mark 采集视频数据回调
- (ByteRTCVideoFrame* _Nullable) processVideoFrame:(ByteRTCVideoFrame* _Nonnull)src_frame {
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:captureVideoFrameTextureId:videoFrameWidth:videoFrameHeight:videoFrameTimeStamp:processedVideoFrameTextureId:)]) {
        
        if ([EAGLContext currentContext] != glcontext) {
            [EAGLContext setCurrentContext:glcontext];
        }
            
        CVPixelBufferRef pixelBuffer = nil;
        // 调整数据格式
        PLVPixelBufferInfo *pixelBufferInfo = [self.imageUtils getCVPixelBufferInfo:src_frame.textureBuf];
        if (pixelBufferInfo.format != PLVFormatType_BGRA) {
            pixelBuffer = [self.imageUtils transforCVPixelBufferToCVPixelBuffer:src_frame.textureBuf outputFormat:PLVFormatType_BGRA];
        }
        
        // 创建纹理
        id<PLVGLTexture> texture = (id<PLVGLTexture>)[self.imageUtils transforCVPixelBufferToTexture:pixelBuffer];
        id<PLVGLTexture> outTexture = nil;
        outTexture = [self.imageUtils getOutputPixelBufferGLTextureWithWidth:texture.width
                                                                      height:texture.height
                                                                      format:PLVFormatType_BGRA];
        double timeStamp = [[[NSDate alloc]init]timeIntervalSince1970];
        
        // 回调给美颜sdk处理数据
        int ret = [self.delegate plvbLinkMicManager:self
                         captureVideoFrameTextureId:texture.texture
                                    videoFrameWidth:texture.width
                                   videoFrameHeight:texture.height
                                videoFrameTimeStamp:timeStamp
                       processedVideoFrameTextureId:outTexture.texture];
        if (ret != 0) {
            return src_frame;
        }
        
        if (self.localCameraJustSwitched) { // 避免翻转时开启美颜导致画面可能出现某一帧倒置的情况，需要多处理2次
            self.localCameraJustSwitched = NO;
            int firstRet = [self.delegate plvbLinkMicManager:self
                             captureVideoFrameTextureId:texture.texture
                                        videoFrameWidth:texture.width
                                       videoFrameHeight:texture.height
                                    videoFrameTimeStamp:timeStamp
                           processedVideoFrameTextureId:outTexture.texture];
            if (firstRet != 0) {
                return src_frame;
            }
            
            int secondRet = [self.delegate plvbLinkMicManager:self
                             captureVideoFrameTextureId:texture.texture
                                        videoFrameWidth:texture.width
                                       videoFrameHeight:texture.height
                                    videoFrameTimeStamp:timeStamp
                           processedVideoFrameTextureId:outTexture.texture];
            if (secondRet != 0) {
                return src_frame;
            }
        }
        
        // 处理后的 Texture 提取 CVPixelBufferRef，再还给回 VOLC
        CVPixelBufferRef outputPixelBuffer = [(PLVPixelBufferGLTexture *)outTexture pixelBuffer];
        if (pixelBufferInfo.format == PLVFormatType_YUVY420) {
            outputPixelBuffer = [self.imageUtils transforCVPixelBufferToCVPixelBuffer:outputPixelBuffer outputFormat:PLVFormatType_YUVY420];
            [self.imageUtils fillY420PixelBuffer:src_frame.textureBuf withInputPixelBuffer:outputPixelBuffer];
        }else {
            outputPixelBuffer = [self.imageUtils transforCVPixelBufferToCVPixelBuffer:outputPixelBuffer outputFormat:PLVFormatType_YUV420V];
            [self.imageUtils fillPixelBuffer:src_frame.textureBuf withInputPixelBuffer:outputPixelBuffer];
        }
        return src_frame;
    }
    return src_frame;
}

#pragma mark 统计和质量事件回调
- (void)rtcEngine:(ByteRTCEngineKit *_Nonnull)engine onLocalStreamStats:(const ByteRTCLocalStreamStats *_Nonnull)stats {
    PLVBLinkMicNetworkQuality txNetworkQuality = [self transformNetworkQuality:stats.tx_quality];
    
    PLVBRTCNetworkQuality txNetwork = [self transformPLVNetworkQualityFromVOLCNetworkQuality:stats.tx_quality];
    
    self.networkQuality = txNetworkQuality;
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:networkQualityDidChanged:)]) {
        [self.delegate plvbLinkMicManager:self networkQualityDidChanged:txNetworkQuality];
    }
    
    self.localNetworkQuality = txNetwork;
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvbLinkMicManager:localNetworkQualityDidChanged:)]) {
        [self.delegate plvbLinkMicManager:self localNetworkQualityDidChanged:txNetwork];
    }
    
}

- (void)rtcEngine:(ByteRTCEngineKit *_Nonnull)engine onRemoteStreamStats:(const ByteRTCRemoteStreamStats *_Nonnull)stats {
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:userNetworkQualityDidChanged:txQuality:rxQuality:)]) {
        PLVBLinkMicNetworkQuality txNetworkQuality = [self transformNetworkQuality:stats.tx_quality];
        PLVBLinkMicNetworkQuality rxNetworkQuality = [self transformNetworkQuality:stats.rx_quality];
        
        [self.delegate plvbLinkMicManager:self userNetworkQualityDidChanged:stats.uid txQuality:txNetworkQuality rxQuality:rxNetworkQuality];
    }
    
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvbLinkMicManager:networkQualityChangedWithUserId:txQuality:rxQuality:)]) {
        [self.delegate plvbLinkMicManager:self networkQualityChangedWithUserId:stats.uid txQuality:[self transformPLVNetworkQualityFromVOLCNetworkQuality:stats.tx_quality] rxQuality:[self transformPLVNetworkQualityFromVOLCNetworkQuality:stats.rx_quality]];
    }
}

- (PLVBLinkMicNetworkQuality)transformNetworkQuality:(ByteRTCNetworkQuality)quality{
    PLVBLinkMicNetworkQuality finalQuality = PLVBLinkMicNetworkQualityUnknown;
    switch (quality) {
        case ByteRTCNetworkQualityUnknown:
        case ByteRTCNetworkQualityExcellent:
        case ByteRTCNetworkQualityGood:
            finalQuality = PLVBLinkMicNetworkQualityGood;
            break;
        case ByteRTCNetworkQualityPoor:
            finalQuality = PLVBLinkMicNetworkQualityFine;
            break;
        case ByteRTCNetworkQualityBad:
            finalQuality = PLVBLinkMicNetworkQualityBad;
            break;
        case ByteRTCNetworkQualityVeryBad:
            finalQuality = PLVBLinkMicNetworkQualityDown;
            break;
        default:
            break;
    }
    return finalQuality;
}

- (PLVBRTCNetworkQuality)transformPLVNetworkQualityFromVOLCNetworkQuality:(ByteRTCNetworkQuality)quality {
    PLVBRTCNetworkQuality finalQuality = PLVBRTCNetworkQuality_Unknown;
    switch (quality) {
        case ByteRTCNetworkQualityUnknown:
            finalQuality = PLVBRTCNetworkQuality_Excellent; // 上行不发数据时返回，此时可认为正常网络状态
            break;
        case ByteRTCNetworkQualityExcellent:
            finalQuality = PLVBRTCNetworkQuality_Excellent;
            break;
        case ByteRTCNetworkQualityGood:
            finalQuality = PLVBRTCNetworkQuality_Good;
            break;
        case ByteRTCNetworkQualityPoor:
            finalQuality = PLVBRTCNetworkQuality_Poor;
            break;
        case ByteRTCNetworkQualityBad:
            finalQuality = PLVBRTCNetworkQuality_Bad;
            break;
        case ByteRTCNetworkQualityVeryBad:
            finalQuality = PLVBRTCNetworkQuality_VBad;
            break;
        default:
            break;
    }
    return finalQuality;
}

#endif
@end
