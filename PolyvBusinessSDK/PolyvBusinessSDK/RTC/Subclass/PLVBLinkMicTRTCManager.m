//
//  PLVBLinkMicTRTCManager.m
//  PLVBusinessSDK
//
//  Created by <PERSON><PERSON> on 2021/2/4.
//  Copyright © 2021 PLV. All rights reserved.
//

#import "PLVBLinkMicTRTCManager.h"

#import "PLVBLinkMicManager+PrivateExtension.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVMattingVideoProcessor.h"
#import <Accelerate/Accelerate.h>

#if __has_include(<TXLiteAVSDK_TRTC/TRTCCloud.h>)
    #import <TXLiteAVSDK_TRTC/TRTCStatistics.h>
    #import <TXLiteAVSDK_TRTC/TRTCCloud.h>
    #import <TXLiteAVSDK_TRTC/TXDeviceManager.h>
    #define TXLiteAVSDK_TRTC
#endif

#define TRTCCloudCLS NSClassFromString(@"TRTCCloud")
#define TRTCParamsCLS NSClassFromString(@"TRTCParams")
#define TRTCVideoEncParamCLS NSClassFromString(@"TRTCVideoEncParam")
#define TRTCNetworkQosParamCLS NSClassFromString(@"TRTCNetworkQosParam")
#define TRTCRenderParamsCLS NSClassFromString(@"TRTCRenderParams")
#define TRTCPublishCDNParamCLS NSClassFromString(@"TRTCPublishCDNParam")
#define TRTCQualityInfoCLS NSClassFromString(@"TRTCQualityInfo")

@interface PLVBLinkMicTRTCSubscribeModel : NSObject

@property (nonatomic, copy) NSString * rtcUserId;
@property (nonatomic, weak) UIView * renderSuperView;
@property (nonatomic, assign) PLVBRTCSubscribeStreamMediaType mediaType;

+ (instancetype)modelWithRTCUserId:(NSString *)rtcUserId renderSuperView:(UIView *)renderSuperView subscribeMediaType:(PLVBRTCSubscribeStreamMediaType)subscribeMediaType;

@end

@implementation PLVBLinkMicTRTCSubscribeModel

+ (instancetype)modelWithRTCUserId:(NSString *)rtcUserId renderSuperView:(UIView *)renderSuperView subscribeMediaType:(PLVBRTCSubscribeStreamMediaType)subscribeMediaType{
    PLVBLinkMicTRTCSubscribeModel * model = [[PLVBLinkMicTRTCSubscribeModel alloc] init];
    if ([PLVFdUtil checkStringUseable:rtcUserId]) {
        model.rtcUserId = rtcUserId;
    }
    if (renderSuperView && [renderSuperView isKindOfClass:UIView.class]) {
        model.renderSuperView = renderSuperView;
    }
    model.mediaType = subscribeMediaType;
    return model;
}

@end

@interface PLVBLinkMicTRTCManager ()
#ifdef TXLiteAVSDK_TRTC
<TRTCCloudDelegate,
TRTCVideoFrameDelegate>{
    EAGLContext *glcontext;
}

#pragma mark RTC对象
@property (nonatomic, strong) TRTCCloud * engine;
@property (nonatomic, strong) TRTCRenderParams * localRenderParams;
@property (nonatomic, strong) TXDeviceManager * deviceManger;

#pragma mark 数据对象
@property (nonatomic, strong) NSMutableArray <NSString *>* videoAvailableArray; /// 视频画面可用的 用户数组
@property (nonatomic, strong) NSMutableDictionary <NSString *, PLVBLinkMicTRTCSubscribeModel *> * waitingVideoAvailableModelDict; /// 等待视频画面可用的 用户数组
@property (nonatomic, assign) BOOL hadBeenPortrait; // 12.0版本存在镜像+横屏翻转异常 出现在竖屏切横屏后
@property (nonatomic, strong) UIImage *stickerImage;


#pragma mark 功能对象
@property (nonatomic, strong) UIView * localRenderSuperView;

#pragma mark AI 抠像
@property (nonatomic, strong) PLVMattingVideoProcessor *videoProcessor;
@property (nonatomic, assign) PLVBLinkMicAIMattingMode aiMattingMode;
@property (nonatomic, strong) UIImage *aiMattingBgImage;

#pragma mark 图像处理
@property (nonatomic, strong) PLVImageUtil *imageUtils;
@property (nonatomic, assign) BOOL enableBeauty; // 开启美颜
@property (nonatomic, assign) BOOL enableSticker; // 开启贴图
@property (nonatomic, assign) BOOL enableMatting; // 开启抠像
@property (nonatomic, assign) BOOL enableLocalVideoPrcess; // 摄像头本地数据回调

#endif
@end

@implementation PLVBLinkMicTRTCManager
#ifdef TXLiteAVSDK_TRTC

@synthesize roleType = _roleType;

#pragma mark - [ Life Period ]
- (void)dealloc{
    NSLog(@"%@", NSStringFromSelector(_cmd));
}


#pragma mark - [ Father Public Methods ]
- (void)createRTCEngine{
    if (!_engine) {
        if (![PLVFdUtil checkStringUseable:self.rtcAppId]) {
            PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - init engine failed,appId is nil %@",self.rtcAppId);
            return;
        }
        /// 创建 RTC引擎
        _engine = [TRTCCloudCLS sharedInstance];
        _engine.delegate = self;
        [_engine enableAudioVolumeEvaluation:300];
        
        [TRTCCloudCLS setLogLevel:TRTCLogLevelInfo];
        [TRTCCloudCLS setConsoleEnabled:NO];
        
        /// 创建 设备管理器
        _deviceManger = [_engine getDeviceManager];
        [_deviceManger setSystemVolumeType:self.localExternalDeviceEnabled ? TXSystemVolumeTypeMedia : TXSystemVolumeTypeAuto];
        [_engine setGSensorMode:TRTCGSensorMode_UIFixLayout];
        
        /// 调用[setLocalAudioMuteAction] 确保[muteLocalAudio]后，还能继续获取音量大小信息
        NSDictionary *param = @{@"api" : @"setLocalAudioMuteAction",
                                @"params" : @{@"volumeEvaluation" : @1},
        };
        if ([NSJSONSerialization isValidJSONObject:param]) {
            NSData *jsonData = [NSJSONSerialization dataWithJSONObject:param options:NSJSONWritingPrettyPrinted error:nil];
            NSString *paramString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
            [self.engine callExperimentalAPI:paramString];
        }
        
        /// 调用[enableAudioAGC] 确保外接有线耳机时声音不会过小
        NSDictionary *enableAGCParam = @{
            @"api": @"enableAudioAGC",
            @"params": @{
                @"enable":@(YES), // YES：开启 NO：关闭
                @"level":@(100),//可选参数，enable 为 YES 时生效，用于指定 AGC 的级别，支持的取值有: 0、100，0 表示关闭 AGC，100 表示最高级别
            },
        };
        if ([NSJSONSerialization isValidJSONObject:enableAGCParam]) {
            NSData *enableAGCJsonData = [NSJSONSerialization dataWithJSONObject:enableAGCParam options:NSJSONWritingPrettyPrinted error:nil];
            NSString *enableAGCParamString = [[NSString alloc] initWithData:enableAGCJsonData encoding:NSUTF8StringEncoding];
            [self.engine callExperimentalAPI:enableAGCParamString];
        }
        
        self.publishStreamSourceType = PLVBRTCStreamSourceType_Camera;
        
        self.videoProcessor = [[PLVMattingVideoProcessor alloc] init];
        self.aiMattingMode = PLVBLinkMicAIMattingModeNone;
        
        glcontext = [[EAGLContext alloc] initWithAPI:kEAGLRenderingAPIOpenGLES3];
        if ([EAGLContext currentContext] != glcontext) {
            [EAGLContext setCurrentContext:glcontext];
        }
        self.eaglContext = glcontext;
        self.imageUtils = [[PLVImageUtil alloc] init];
    }
}

- (void)destroyRTCEngine{
    _engine.delegate = nil;
    _engine = nil;
    [TRTCCloudCLS destroySharedIntance];
}

- (int)joinRtcChannelWithChannelId:(NSString *)channelId userLinkMicId:(NSString *)userLinkMicId{
    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:userLinkMicId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - join rtc channel failed,channelId:%@, userLinkMicId:%@",channelId,userLinkMicId);
        return -1;
    }else{
        self.channelId = channelId;
        self.userLinkMicId = userLinkMicId;
    }
        
    [self createResource];
    
    /// 加入房间
    TRTCParams * joinRoomParams = [[TRTCParamsCLS alloc] init];
    joinRoomParams.strRoomId = self.channelId;
    joinRoomParams.userId = self.userLinkMicId;
    joinRoomParams.role = TRTCRoleAnchor;
    UInt32 int32RtcAppId = stringToUInt32([self.rtcAppId UTF8String]);
    joinRoomParams.sdkAppId = int32RtcAppId;
    joinRoomParams.userSig = self.rtcAppSign;
    [self.engine enterRoom:joinRoomParams appScene:TRTCAppSceneLIVE];
    return 0;
}

- (int)leaveRtcChannel{
    if (self.hadJoinedRTC) {
        [self.engine exitRoom];
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:leaveRTCChannelComplete:)]) {
            [self.delegate plvbLinkMicManager:self leaveRTCChannelComplete:self.channelId];
        }
        [self destroyResource];
        return 0;
    }else{
        return 0;
    }
}
 
- (void)subscribeStreamWithRTCUserId:(NSString *)rtcUserId renderOnView:(UIView *)renderSuperView mediaType:(PLVBRTCSubscribeStreamMediaType)mediaType{
    if (![PLVFdUtil checkStringUseable:rtcUserId] || !renderSuperView || ![renderSuperView isKindOfClass:UIView.class]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - add preview error uid %@ superView %@",rtcUserId,renderSuperView);
        return;
    }
    
    BOOL localUser = [rtcUserId isEqualToString:self.userLinkMicId];
    if (localUser) { // 本地
        self.localRenderSuperView = renderSuperView;
        
        /// 20220722 Lincal
        /// 选择此处进行 “配置默认项” 的原因，是此处代表着 本地当前用户 也将推流至远端。
        /// 即代表本地硬件(摄像头、麦克风)将开启，因此适合进行默认开关配置
        /// 配置默认项
        [self callbackForCanSetupLocalHardwareDefaultState];
    } else { // 远端
        PLVBLinkMicTRTCSubscribeModel * model = [PLVBLinkMicTRTCSubscribeModel modelWithRTCUserId:rtcUserId renderSuperView:renderSuperView subscribeMediaType:mediaType];
        if ([self.videoAvailableArray containsObject:rtcUserId]) {
            /// 流可用
            [self renderRemoteUserVideoView:model];
        }else{
            /// 流不可用
            [self.waitingVideoAvailableModelDict setObject:model forKey:rtcUserId];
        }
    }
}

- (void)subscribeStreamWithRTCUserId:(NSString *)rtcUserId renderOnView:(UIView *)renderSuperView mediaType:(PLVBRTCSubscribeStreamMediaType)mediaType subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode{
    /// TRTC不支持 ‘根据流订阅方式’ 将流进行渲染
    [self subscribeStreamWithRTCUserId:rtcUserId renderOnView:renderSuperView mediaType:mediaType];
}

- (void)unsubscribeStreamWithRTCUserId:(NSString *)rtcUserId{
    if (![PLVFdUtil checkStringUseable:rtcUserId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - removeUserSubscribeUserId failed ,rtcUserId illegal %@",rtcUserId);
        return;
    }
    
    BOOL localUser = [rtcUserId isEqualToString:self.userLinkMicId];
    if (localUser) { // 本地
        [self.engine stopLocalPreview];
    } else { // 远端
        [self.engine stopRemoteView:rtcUserId streamType:TRTCVideoStreamTypeBig];
    }
}

- (void)switchSubscribeStreamMediaTypeWithRTCUserId:(NSString *)rtcUserId mediaType:(PLVBRTCSubscribeStreamMediaType)toMediaType{
    
}

- (void)switchPublishStreamSourceType:(PLVBRTCStreamSourceType)streamSourceType{
    if (![PLVFdUtil checkStringUseable:self.appGroup]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - switchPublishStreamSourceType failed, Please setup appGroup first");
        return ;
    }
    
    if (self.publishStreamSourceType == streamSourceType) {
        return;
    }
    
    self.publishStreamSourceType = streamSourceType;
    if (streamSourceType == PLVBRTCStreamSourceType_Camera) {
        /// 切换到摄像头采集
        if (!self.screenCaptureStarted) {
            [self onScreenCaptureStoped:0];
        }
        if (@available(iOS 11.0, *)) {
            [self.engine stopScreenCapture];
        }
        
        [self openLocalUserCamera:self.localCameraOpen];
    } else if (streamSourceType == PLVBRTCStreamSourceType_Screen) {
        /// 切换到屏幕采集
        [self.engine stopLocalPreview];
        [self muteLocalVideoStream:NO];
        
        if (@available(iOS 11.0, *)) {
            [self.engine startScreenCaptureByReplaykit:TRTCVideoStreamTypeBig encParam:nil appGroup:self.appGroup];
        }
        
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManagerDidScreenCaptureStarted:)]) {
            [self.delegate plvbLinkMicManagerDidScreenCaptureStarted:self];
        }
    }
}

- (int)setUserRoleTo:(PLVBLinkMicRoleType)roleType{
    _roleType = roleType;
    TRTCRoleType trtcRoleType = ((roleType == PLVBLinkMicRoleBroadcaster) ? TRTCRoleAnchor : TRTCRoleAudience);
    [self.engine switchRole:trtcRoleType];
    return 0;
}

#pragma mark 本地 摄像头 使用
- (void)setupVideoEncoderConfiguration:(PLVBRTCVideoEncoderConfiguration *)videoEncoderConfiguration{
    if (videoEncoderConfiguration && [videoEncoderConfiguration isKindOfClass:PLVBRTCVideoEncoderConfiguration.class]) {
        [super setupVideoEncoderConfiguration:videoEncoderConfiguration];

        TRTCVideoEncParam * videoEncParam = [[TRTCVideoEncParamCLS alloc]init];
        videoEncParam.videoResolution = [self readTRTCVideoResolution:videoEncoderConfiguration];
        videoEncParam.videoBitrate = (int)videoEncoderConfiguration.videoBitrate;
        videoEncParam.videoFps = (int)videoEncoderConfiguration.videoFrameRate;
        videoEncParam.resMode = (videoEncoderConfiguration.videoOutputOrientationMode == PLVBRTCVideoOutputOrientationMode_Landscape ? TRTCVideoResolutionModeLandscape : TRTCVideoResolutionModePortrait);
        [self.engine setVideoEncoderParam:videoEncParam];
        if (videoEncoderConfiguration.videoOutputOrientationMode == PLVBRTCVideoOutputOrientationMode_Portrait) {
            self.hadBeenPortrait = YES;
        }
        
        if (videoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Auto) {
            [self.engine setVideoEncoderMirror:self.localCameraFront];
        }else if (videoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Enabled){
            [self.engine setVideoEncoderMirror:YES];
        }else{
            [self.engine setVideoEncoderMirror:NO];
        }
        
        TRTCNetworkQosParam *param = [[TRTCNetworkQosParamCLS alloc] init];
        param.controlMode = TRTCQosControlModeServer;
        if (videoEncoderConfiguration.videoQosPreference == PLVBRTCVideoQosPreferenceSmooth) {
            param.preference = TRTCVideoQosPreferenceSmooth;
        } else {
            param.preference = TRTCVideoQosPreferenceClear;
        }
        [self.engine setNetworkQosParam:param];
    }else{
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - setupVideoEncoderConfiguration failed, videoEncoderConfiguration invalid %@",videoEncoderConfiguration);
    }
}

- (void)setupLocalPreviewWithCanvasModel:(PLVBRTCVideoViewCanvasModel *)canvasModel{
    if (![canvasModel checkModelValid]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - setupLocalPreviewWithCanvasModel failed, canvasModel invalid, userRTCId:%@ renderCanvasView:%@",canvasModel.userRTCId, canvasModel.renderCanvasView);
        return;
    }
    
    /// 设置本地图像渲染设置
    TRTCRenderParams * localRenderParams = [[TRTCRenderParamsCLS alloc]init];
    localRenderParams.rotation = TRTCVideoRotation_0;
    localRenderParams.fillMode = (canvasModel.rtcVideoVideoFillMode == PLVBRTCVideoViewFillMode_Fit ? TRTCVideoFillMode_Fit : TRTCVideoFillMode_Fill);
    localRenderParams.mirrorType = TRTCVideoMirrorTypeAuto;
    self.localRenderParams = localRenderParams;
    [self.engine setLocalRenderParams:localRenderParams];
    
    self.localRenderSuperView = canvasModel.renderCanvasView;
    self.currentLocalPreviewCanvasModel = canvasModel;
}

- (int)setupLocalVideoPreviewMirrorMode:(PLVBRTCVideoMirrorMode)mirrorMode{
    self.localRenderParams.mirrorType = (TRTCVideoMirrorType)mirrorMode;
    [self.engine setLocalRenderParams:self.localRenderParams];
    self.localVideoMirrorMode = mirrorMode;
    return 0;
}

- (int)switchLocalUserCamera:(BOOL)frontCamera{
    int resultCode = (int)[self.deviceManger switchCamera:frontCamera];
    if (resultCode == 0) {
        self.localCameraFront = frontCamera;
        if (frontCamera) { self.localCameraTorchOpen = NO; }
        if (self.currentVideoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Auto) {
            [self.engine setVideoEncoderMirror:frontCamera];
        }
    }
    return resultCode;
}

- (int)openLocalUserCameraTorch:(BOOL)openCameraTorch{
    int resultCode = (int)[self.deviceManger enableCameraTorch:openCameraTorch];
    if (resultCode == 0) { self.localCameraTorchOpen = openCameraTorch; }
    return resultCode;
}

- (int)openLocalUserCamera:(BOOL)openCamera{
    if (openCamera) {
        if (self.localRenderSuperView) {
            [self.engine startLocalPreview:self.localCameraFront view:self.localRenderSuperView];
            [self muteLocalVideoStream:!openCamera];
        }
    }else{
        [self.engine stopLocalPreview];
        [self muteLocalVideoStream:!openCamera];
    }
    
    self.localVideoEnable = openCamera;
    self.localCameraOpen = openCamera;
    return 0;
}

- (int)localVideoStreamOpenPlaceholder:(BOOL)openPlaceholder{
    return 0; /// TRTC暂未对接
}

- (int)enableLocalVideo:(BOOL)enabled{
    return 0; /// TRTC不支持
}

- (int)startLocalPreview:(BOOL)start{
    return 0; /// TRTC不支持
}

- (int)muteLocalVideoStream:(BOOL)mute{
    [self.engine muteLocalVideo:mute];
    self.localVideoStreamMute = mute;
    return 0;
}

/// 上层美颜开关 层层调用 最终在这里控制rtc采集数据回调
/// 火山引擎，声网 RTC 暂不支持贴图 AI抠像操作  继续由美颜开关控制RTC数据回调
/// TRTC支持 AI抠像 贴图等后期处理功能 需要由各个功能综合控制 RTC数据回调
- (void)enableLocalVideoFrameProcess:(BOOL)enabled {
    self.enableBeauty = enabled;
    
    BOOL open = self.enableLocalVideoPrcess;
    [self openLocalVideoProcess:open];
}

- (BOOL)enableLocalVideoPrcess{
    return (self.enableBeauty || self.enableSticker || self.enableMatting);
}

- (void)openLocalVideoProcess:(BOOL)open{
    if (open) {
        [self.engine setLocalVideoProcessDelegete:self
                                      pixelFormat:TRTCVideoPixelFormat_32BGRA
                                       bufferType:TRTCVideoBufferType_PixelBuffer];
    }else {
        [self.engine setLocalVideoProcessDelegete:nil
                                  pixelFormat:TRTCVideoPixelFormat_32BGRA
                                   bufferType:TRTCVideoBufferType_PixelBuffer];
        [self updateLocalRenderParamsRotation:NO];
    }
}

#pragma mark -- Image Sticker
- (void)setStickerImage:(UIImage *)stickerImage{
   if (!stickerImage) {
       _stickerImage = nil;
       _enableSticker = NO;
       BOOL open = self.enableLocalVideoPrcess;
       [self openLocalVideoProcess:open];
       
        return;
    }
    _enableSticker = YES;
    UIImage *rotatedStickerImage = stickerImage;
    PLVBRTCVideoEncoderConfiguration *currentVideoEncoderConfiguration = self.currentVideoEncoderConfiguration;
    if (currentVideoEncoderConfiguration.videoOutputOrientationMode == PLVBRTCVideoOutputOrientationMode_Landscape){
        // Rotate the sticker image 90 degrees counterclockwise
        rotatedStickerImage = [self rotateImage:stickerImage counterClockwise:YES];
    }
    
    _stickerImage = rotatedStickerImage;
    
    BOOL open = self.enableLocalVideoPrcess;
    [self openLocalVideoProcess:open];
    
}

#pragma mark -- AI Matting
- (void)setAIMattingMode:(PLVBLinkMicAIMattingMode)mode image:(UIImage *)image{
    self.aiMattingMode = mode;
    self.aiMattingBgImage = image;
    
    PLVBRTCVideoEncoderConfiguration *currentVideoEncoderConfiguration = self.currentVideoEncoderConfiguration;
    if (currentVideoEncoderConfiguration.videoOutputOrientationMode == PLVBRTCVideoOutputOrientationMode_Landscape && image){
        // Rotate the background image 90 degrees counterclockwise
        self.aiMattingBgImage = [self rotateImage:image counterClockwise:YES];
    }
    
    _enableMatting = (mode == PLVBLinkMicAIMattingModeNone ? NO:YES);
    
    BOOL open = self.enableLocalVideoPrcess;
    [self openLocalVideoProcess:open];
}

- (UIImage *)rotateImage:(UIImage *)image counterClockwise:(BOOL)counterClockwise {
   CGFloat scale = image.scale;
   CGSize size = image.size;
   
   // For 90 degree rotation, swap width and height
   UIGraphicsBeginImageContextWithOptions(CGSizeMake(size.height, size.width), NO, scale);
   CGContextRef context = UIGraphicsGetCurrentContext();
   
   // Important: For correct orientation without mirroring
   // We need to apply a flip + rotation to counteract Core Graphics coordinate system
   CGContextTranslateCTM(context, size.height / 2, size.width / 2);
   CGContextRotateCTM(context, counterClockwise ? -M_PI_2 : M_PI_2);
   
   // Due to Core Graphics coordinate system, we need this flip to avoid mirroring
   CGContextScaleCTM(context, 1.0, -1.0);
   
   // Draw the image centered in the context, offset by the translation
   CGContextDrawImage(context, CGRectMake(-size.width / 2, -size.height / 2, size.width, size.height), image.CGImage);
   
   // Get the rotated image
   UIImage *rotatedImage = UIGraphicsGetImageFromCurrentImageContext();
   UIGraphicsEndImageContext();
   
   return rotatedImage;
}

#pragma mark 本地 麦克风 使用
- (int)changeLocalMicVolume:(CGFloat)micVolume{
    return 0; /// TRTC暂未对接
}

- (int)openLocalUserMic:(BOOL)openMic{
    [self enableLocalAudio:openMic];
    
    [self.engine muteLocalAudio:!openMic];
    self.localMicOpen = openMic;
    return 0;
}

- (int)enableLocalAudio:(BOOL)enabled{
    if (enabled) {
        TRTCAudioQuality audioQuality = TRTCAudioQualitySpeech;
        switch (self.localNoiseCancellationLevel) {
            case PLVBLinkMicNoiseCancellationLevelSoft:
                audioQuality = TRTCAudioQualityDefault;
                break;
            case PLVBLinkMicNoiseCancellationLevelAggressive:
            default:
                break;
        }
        [self.engine startLocalAudio:audioQuality];
        self.localAudioEnable = enabled;
    }else{
        /// TRTC不支持
    }
    return 0;
}

- (int)muteLocalAudioStream:(BOOL)mute{
    return 0; /// TRTC不支持
}

- (int)switchNoiseCancellationLevelTo:(PLVBLinkMicNoiseCancellationLevel)level {
    self.localNoiseCancellationLevel = level;
    if (self.localAudioEnable) {
        [self enableLocalAudio:self.localAudioEnable];
    }
    return 0;
}

- (int)enableExternalDevice:(BOOL)enabled {
    self.localExternalDeviceEnabled = enabled;
    [self.deviceManger setSystemVolumeType:enabled ? TXSystemVolumeTypeMedia : TXSystemVolumeTypeAuto];
    return 0;
}

#pragma mark 流相关
- (int)addPublishStreamUrl:(NSString *)streamUrl transcodingEnabled:(BOOL)transcodingEnabled{
    TRTCPublishCDNParam * publishCDNParam = [[TRTCPublishCDNParamCLS alloc] init];
    publishCDNParam.appId = self.rtcAppId.intValue;
    publishCDNParam.bizId = self.rtcToken.intValue;
    publishCDNParam.url = streamUrl;
    [self.engine startPublishCDNStream:publishCDNParam];
    return 0;
}

- (int)removePublishStreamUrl:(NSString *)streamUrl{
    [self.engine stopPublishCDNStream];
    return 0;
}

#pragma mark Getter
- (BOOL)engineIsReady{
    return _engine ? YES : NO;
}


#pragma mark - [ Father Private Methods ]
- (void)setupRTCToken:(NSDictionary *)decodeDict{
    self.rtcAppId = decodeDict[@"sdkAppId"];
    self.rtcAppSign = decodeDict[@"userSign"];
    self.rtcToken = decodeDict[@"bizId"];
}


#pragma mark - [ Private Methods ]
- (void)createResource{
    _videoAvailableArray = [[NSMutableArray alloc] init];
    _waitingVideoAvailableModelDict = [[NSMutableDictionary alloc] init];
}

- (void)destroyResource{
    self.hadJoinedRTC = NO;
    
    self.localAudioStreamMute = NO;
    self.localVideoStreamMute = NO;
    
    [_videoAvailableArray removeAllObjects];
    _videoAvailableArray = nil;
    [_waitingVideoAvailableModelDict removeAllObjects];
    _waitingVideoAvailableModelDict = nil;
}

- (void)renderRemoteUserVideoView:(PLVBLinkMicTRTCSubscribeModel *)model{
    if (![PLVFdUtil checkStringUseable:model.rtcUserId] || !model.renderSuperView || ![model.renderSuperView isKindOfClass:UIView.class]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - renderRemoteUserVideoView error uid %@ superView %@",model.rtcUserId,model.renderSuperView);
        return;
    }
    
    if (dispatch_queue_get_label(DISPATCH_CURRENT_QUEUE_LABEL) == dispatch_queue_get_label(dispatch_get_main_queue())) {
        BOOL isTeacher = [model.rtcUserId isEqualToString:self.channelId];
        
        /// 设置远端图像渲染设置
        TRTCRenderParams * remoteRenderParams = [[TRTCRenderParamsCLS alloc]init];
        remoteRenderParams.rotation = TRTCVideoRotation_0;
        remoteRenderParams.fillMode = isTeacher ? TRTCVideoFillMode_Fit : TRTCVideoFillMode_Fill;
        remoteRenderParams.mirrorType = TRTCVideoMirrorTypeAuto;
        [self.engine setRemoteRenderParams:model.rtcUserId streamType:TRTCVideoStreamTypeBig params:remoteRenderParams];
        
        [self.engine startRemoteView:model.rtcUserId streamType:TRTCVideoStreamTypeBig view:model.renderSuperView];
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self renderRemoteUserVideoView:model];
        });
    }
}

- (TRTCVideoResolution)readTRTCVideoResolution:(PLVBRTCVideoEncoderConfiguration *)videoEncoderConfiguration{
    CGFloat resolutionWidth = videoEncoderConfiguration.videoResolution.width;
    CGFloat resolutionHeight = videoEncoderConfiguration.videoResolution.height;
    CGFloat widthHeithScale = resolutionWidth / resolutionHeight;
    
    TRTCVideoResolution trtcResolution;
    if (widthHeithScale == 1.0) {
        /// 宽高比1:1
        if (resolutionHeight >= 480) {
            trtcResolution = TRTCVideoResolution_480_480;
        }else if (resolutionHeight >= 270){
            trtcResolution = TRTCVideoResolution_270_270;
        }else if (resolutionHeight >= 160){
            trtcResolution = TRTCVideoResolution_160_160;
        }else{
            trtcResolution = TRTCVideoResolution_120_120;
        }
    }else if (widthHeithScale <= 1.4){
        /// 宽高比4:3
        if (resolutionHeight >= 720) {
            trtcResolution = TRTCVideoResolution_960_720;
        }else if (resolutionHeight >= 480){
            trtcResolution = TRTCVideoResolution_640_480;
        }else if (resolutionHeight >= 360){
            trtcResolution = TRTCVideoResolution_480_360;
        }else if (resolutionHeight >= 240){
            trtcResolution = TRTCVideoResolution_320_240;
        }else{
            trtcResolution = TRTCVideoResolution_240_180;
        }
    }else{
        /// 宽高比16:9
        if (resolutionHeight >= 1080) {
            trtcResolution = TRTCVideoResolution_1920_1080;
        }else if (resolutionHeight >= 720) {
            trtcResolution = TRTCVideoResolution_1280_720;
        }else if (resolutionHeight >= 540){
            trtcResolution = TRTCVideoResolution_960_540;
        }else if (resolutionHeight >= 360){
            trtcResolution = TRTCVideoResolution_640_360;
        }else if (resolutionHeight >= 270){
            trtcResolution = TRTCVideoResolution_480_270;
        }else{
            trtcResolution = TRTCVideoResolution_320_180;
        }
    }
    return trtcResolution;
}

static unsigned int stringToUInt32(const char *str)
{
    unsigned int result = 0, i = 0;
    char *tmp = NULL;
    for (i = 0; isspace(str[i]) && i < strlen(str); i++);
    tmp = str+i;
    while (*tmp)
    {
        result = result * 10 + *tmp - '0';
        tmp++;
    }
    return result;
}

/// 12.0版本存在镜像+横屏翻转异常的情况，需要主动调整本地角度
- (void)updateLocalRenderParamsRotation:(BOOL)needRotate {
    if (needRotate && self.hadBeenPortrait) {
        if (self.localRenderParams && self.localRenderParams.rotation != TRTCVideoRotation_180) {
            self.localRenderParams.rotation = TRTCVideoRotation_180;
            [self.engine setLocalRenderParams:self.localRenderParams];
        }
    } else {
        if (self.localRenderParams && self.localRenderParams.rotation != TRTCVideoRotation_0) {
            self.localRenderParams.rotation = TRTCVideoRotation_0;
            [self.engine setLocalRenderParams:self.localRenderParams];
        }
    }
}

#pragma mark - [ Delegate ]
#pragma mark 房间事件回调
/**
* 2.1 已加入房间的回调
*
* 调用 TRTCCloud 中的 enterRoom() 接口执行进房操作后，会收到来自 SDK 的 onEnterRoom(result) 回调：
*
* - 如果加入成功，result 会是一个正数（result > 0），代表加入房间的时间消耗，单位是毫秒（ms）。
* - 如果加入失败，result 会是一个负数（result < 0），代表进房失败的错误码。
* 进房失败的错误码含义请参见[错误码](https://cloud.tencent.com/document/product/647/32257)。
*
* @note 在 Ver6.6 之前的版本，只有进房成功会抛出 onEnterRoom(result) 回调，进房失败由 onError() 回调抛出。
*       在 Ver6.6 及之后改为：进房成功返回正的 result，进房失败返回负的 result，同时进房失败也会有 onError() 回调抛出。
*f
* @param result result > 0 时为进房耗时（ms），result < 0 时为进房错误码。
*/
- (void)onEnterRoom:(NSInteger)result{
    PLVF_NORMAL_LOG_INFO(@"BLinkMic", @"PLVBLinkMicManager - join rtc channel success,channel:%@, uid:%@, result %ld", self.channelId, self.userLinkMicId, (long)result);
    
    self.hadJoinedRTC = YES;
    
    __weak typeof(self) weakSelf = self;
    plv_dispatch_main_async_safe(^{
        if ([weakSelf.delegate respondsToSelector:@selector(plvbLinkMicManager:joinRTCChannelComplete:uid:)]) {
            [weakSelf.delegate plvbLinkMicManager:weakSelf joinRTCChannelComplete:weakSelf.channelId uid:weakSelf.userLinkMicId];
        }
    })
}

/**
 * 2.2 离开房间的事件回调
 *
 * 调用 TRTCCloud 中的 exitRoom() 接口会执行退出房间的相关逻辑，例如释放音视频设备资源和编解码器资源等。
 * 待资源释放完毕，SDK 会通过 onExitRoom() 回调通知到您。
 *
 * 如果您要再次调用 enterRoom() 或者切换到其他的音视频 SDK，请等待 onExitRoom() 回调到来之后再执行相关操作。
 * 否则可能会遇到音频设备（例如 iOS 里的 AudioSession）被占用等各种异常问题。
 *
 * @param reason 离开房间原因，0：主动调用 exitRoom 退房；1：被服务器踢出当前房间；2：当前房间整个被解散。
 */
- (void)onExitRoom:(NSInteger)reason{
    if (_engine) {
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:leaveRTCChannelComplete:)]) {
            [self.delegate plvbLinkMicManager:self leaveRTCChannelComplete:self.channelId];
        }
        
        if (reason == 1) {
            if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:leaveRTCChannelComplete:)]) {
                [self.delegate plvbLinkMicManager:self leaveRTCChannelByServerComplete:self.channelId];
            }
        }
        
        [self destroyResource];
    }
}

#pragma mark 成员事件回调
/**
 * 3.1 有用户加入当前房间
 *
 * 出于性能方面的考虑，在两种不同的应用场景下，该通知的行为会有差别：
 * - 通话场景（TRTCAppSceneVideoCall 和 TRTCAppSceneAudioCall）：该场景下用户没有角色的区别，任何用户进入房间都会触发该通知。
 * - 直播场景（TRTCAppSceneLIVE 和 TRTCAppSceneVoiceChatRoom）：该场景不限制观众的数量，如果任何用户进出都抛出回调会引起很大的性能损耗，所以该场景下只有主播进入房间时才会触发该通知，观众进入房间不会触发该通知。
 *
 *
 * @note 注意 onRemoteUserEnterRoom 和 onRemoteUserLeaveRoom 只适用于维护当前房间里的“成员列表”，如果需要显示远程画面，建议使用监听 onUserVideoAvailable() 事件回调。
 *
 * @param userId 用户标识
 */
- (void)onRemoteUserEnterRoom:(NSString *)userId{
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didJoinedOfUid:)]) {
        [self.delegate plvbLinkMicManager:self didJoinedOfUid:userId];
    }
    
    /// 适配处理
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didVideoMuted:byUid:)]) {
        [self.delegate plvbLinkMicManager:self didVideoMuted:YES byUid:userId];
    }
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didAudioMuted:byUid:)]) {
        [self.delegate plvbLinkMicManager:self didAudioMuted:YES byUid:userId];
    }
}

/**
 * 3.2 有用户离开当前房间
 *
 * 与 onRemoteUserEnterRoom 相对应，在两种不同的应用场景下，该通知的行为会有差别：
 * - 通话场景（TRTCAppSceneVideoCall 和 TRTCAppSceneAudioCall）：该场景下用户没有角色的区别，任何用户的离开都会触发该通知。
 * - 直播场景（TRTCAppSceneLIVE 和 TRTCAppSceneVoiceChatRoom）：只有主播离开房间时才会触发该通知，观众离开房间不会触发该通知。
 *
 * @param userId 用户标识
 * @param reason 离开原因，0 表示用户主动退出房间，1 表示用户超时退出，2 表示被踢出房间。
 */
- (void)onRemoteUserLeaveRoom:(NSString *)userId reason:(NSInteger)reason{
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didOfflineOfUid:)]) {
        [self.delegate plvbLinkMicManager:self didOfflineOfUid:userId];
    }
}

/**
 * 3.3 远端用户是否存在可播放的主路画面（一般用于摄像头）
 *
 * 当您收到 onUserVideoAvailable(userid, YES) 通知时，表示该路画面已经有可用的视频数据帧到达。
 * 此时，您需要调用 startRemoteView(userid) 接口加载该用户的远程画面。
 * 然后，您会收到名为 onFirstVideoFrame(userid) 的首帧画面渲染回调。
 *
 * 当您收到 onUserVideoAvailable(userid, NO) 通知时，表示该路远程画面已被关闭，
 * 可能由于该用户调用了 muteLocalVideo() 或 stopLocalPreview()。
 *
 * @param userId 用户标识
 * @param available 画面是否开启
 */
- (void)onUserVideoAvailable:(NSString *)userId available:(BOOL)available{
    /// 记录 视频画面 可用状态
    if ([PLVFdUtil checkStringUseable:userId]) {
        if (available) {
            if (![userId isEqualToString:self.userLinkMicId]) {
                [self.videoAvailableArray addObject:userId];
            }
            PLVBLinkMicTRTCSubscribeModel * model = [self.waitingVideoAvailableModelDict objectForKey:userId];
            if (model) {
                [self renderRemoteUserVideoView:model];
                [self.waitingVideoAvailableModelDict removeObjectForKey:userId];
            }
        }else{
            [self.videoAvailableArray removeObject:userId];
        }
    }
    
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didVideoMuted:byUid:)]) {
        [self.delegate plvbLinkMicManager:self didVideoMuted:!available byUid:userId];
    }
}

/**
 * 3.4 远端用户是否存在可播放的辅路画面（一般用于屏幕分享）
 *
 * @note 显示辅路画面使用的函数是 startRemoteSubStreamView() 而非 startRemoteView()。
 * @param userId 用户标识
 * @param available 屏幕分享是否开启
 */
- (void)onUserSubStreamAvailable:(NSString *)userId available:(BOOL)available{
    
}

/**
 * 3.5 远端用户是否存在可播放的音频数据
 *
 * @param userId 用户标识
 * @param available 声音是否开启
 */
- (void)onUserAudioAvailable:(NSString *)userId available:(BOOL)available{
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didAudioMuted:byUid:)]) {
        [self.delegate plvbLinkMicManager:self didAudioMuted:!available byUid:userId];
    }
}

/**
 * 3.6 开始渲染本地或远程用户的首帧画面
 *
 * 如果 userId == nil，代表开始渲染本地采集的摄像头画面，需要您先调用 startLocalPreview 触发。
 * 如果 userId != nil，代表开始渲染远程用户的首帧画面，需要您先调用 startRemoteView 触发。
 *
 * @note 只有当您调用 startLocalPreivew()、startRemoteView() 或 startRemoteSubStreamView() 之后，才会触发该回调。
 *
 * @param userId 本地或远程用户 ID，如果 userId == nil 代表本地，userId != nil 代表远程。
 * @param streamType 视频流类型：摄像头或屏幕分享。
 * @param width  画面宽度
 * @param height 画面高度
 */
- (void)onFirstVideoFrame:(NSString*)userId streamType:(TRTCVideoStreamType)streamType width:(int)width height:(int)height{
    
}

/**
 * 3.7 开始播放远程用户的首帧音频（本地声音暂不通知）
 *
 * @param userId 远程用户 ID。
 */
- (void)onFirstAudioFrame:(NSString*)userId{
    
}

#pragma mark 统计和质量事件回调
/**
 * 4.1 网络质量，该回调每2秒触发一次，统计当前网络的上行和下行质量
 *
 * @note userId == nil 代表自己当前的视频质量
 *
 * @param localQuality 上行网络质量
 * @param remoteQuality 下行网络质量
 */
- (void)onNetworkQuality: (TRTCQualityInfo*)localQuality remoteQuality:(NSArray<TRTCQualityInfo*>*)remoteQuality{
    // NSLog(@"POLYVTEST - userId %@ quality %ld //// %@",localQuality.userId,(long)localQuality.quality,remoteQuality);
    PLVBLinkMicNetworkQuality txNetworkQuality = [self transformNetworkQuality:localQuality.quality];
    PLVBLinkMicNetworkQuality rxNetworkQuality = PLVBLinkMicNetworkQualityUnknown;
    TRTCQualityInfo * remoteQualityInfo = remoteQuality.firstObject;
    if ([remoteQualityInfo isKindOfClass:TRTCQualityInfoCLS.class]) {
        rxNetworkQuality = [self transformNetworkQuality:remoteQualityInfo.quality];
    }
    
    PLVBRTCNetworkQuality txNetwork = [self transformPLVNetworkQualityFromTRTCNetworkQuality:localQuality.quality];
    PLVBRTCNetworkQuality rxNetwork = PLVBRTCNetworkQuality_Unknown;
    if (remoteQuality.firstObject && [remoteQuality.firstObject isKindOfClass:[TRTCQualityInfo class]]) {
        rxNetwork = [self transformPLVNetworkQualityFromTRTCNetworkQuality:remoteQuality.firstObject.quality];
    }
    
    BOOL localUser = ![PLVFdUtil checkStringUseable:localQuality.userId];
    if (localUser) {
        self.networkQuality = txNetworkQuality;
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:networkQualityDidChanged:)]) {
            [self.delegate plvbLinkMicManager:self networkQualityDidChanged:txNetworkQuality];
        }
        
        self.localNetworkQuality = txNetwork;
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(plvbLinkMicManager:localNetworkQualityDidChanged:)]) {
            [self.delegate plvbLinkMicManager:self localNetworkQualityDidChanged:txNetwork];
        }
    }
    
    NSString * userRTCId = localUser ? self.userLinkMicId : localQuality.userId;
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:userNetworkQualityDidChanged:txQuality:rxQuality:)]) {
        [self.delegate plvbLinkMicManager:self userNetworkQualityDidChanged:userRTCId txQuality:txNetworkQuality rxQuality:rxNetworkQuality];
    }
    
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvbLinkMicManager:networkQualityChangedWithUserId:txQuality:rxQuality:)]) {
        [self.delegate plvbLinkMicManager:self networkQualityChangedWithUserId:userRTCId txQuality:txNetwork rxQuality:rxNetwork];
    }
}

- (void)onStatistics:(TRTCStatistics *)statistics {
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvbLinkMicManager:rtcStatistics:)]) {
        
        PLVBRTCStatistics *stats = [[PLVBRTCStatistics alloc] init];
        stats.rtt = statistics.rtt;
        stats.upLoss = statistics.upLoss;
        stats.downLoss = statistics.downLoss;
        
        [self.delegate plvbLinkMicManager:self rtcStatistics:stats];
    }
}

- (PLVBLinkMicNetworkQuality)transformNetworkQuality:(TRTCQuality)quality{
    PLVBLinkMicNetworkQuality finalQuality = PLVBLinkMicNetworkQualityUnknown;
    switch (quality) {
        case TRTCQuality_Excellent:
        case TRTCQuality_Good:
            finalQuality = PLVBLinkMicNetworkQualityGood;
            break;
        case TRTCQuality_Poor:
        case TRTCQuality_Bad:
            finalQuality = PLVBLinkMicNetworkQualityFine;
            break;
        case TRTCQuality_Vbad:
            finalQuality = PLVBLinkMicNetworkQualityBad;
            break;
        case TRTCQuality_Down:
            finalQuality = PLVBLinkMicNetworkQualityDown;
            break;
        default:
            break;
    }
    return finalQuality;
}

- (PLVBRTCNetworkQuality)transformPLVNetworkQualityFromTRTCNetworkQuality:(TRTCQuality)quality{
    PLVBRTCNetworkQuality finalQuality = PLVBRTCNetworkQuality_Unknown;
    switch (quality) {
        case TRTCQuality_Excellent:
            finalQuality = PLVBRTCNetworkQuality_Excellent;
            break;
        case TRTCQuality_Good:
            finalQuality = PLVBRTCNetworkQuality_Good;
            break;
        case TRTCQuality_Poor:
            finalQuality = PLVBRTCNetworkQuality_Poor;
            break;
        case TRTCQuality_Bad:
            finalQuality = PLVBRTCNetworkQuality_Bad;
            break;
        case TRTCQuality_Vbad:
            finalQuality = PLVBRTCNetworkQuality_VBad;
            break;
        case TRTCQuality_Down:
            finalQuality = PLVBRTCNetworkQuality_Down;
            break;
        default:
            break;
    }
    return finalQuality;
}

#pragma mark 服务器事件回调
/**
 * 5.1 SDK 跟服务器的连接断开
 */
- (void)onConnectionLost{
    self.connectionState = PLVBLinkMicConnectionStateDisconnected;
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:rtcConnectionStateDidChanged:)]) {
        [self.delegate plvbLinkMicManager:self rtcConnectionStateDidChanged:self.connectionState];
    }
}

/**
 * 5.2 SDK 尝试重新连接到服务器
 */
- (void)onTryToReconnect{
    self.connectionState = PLVBLinkMicConnectionStateReconnecting;
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:rtcConnectionStateDidChanged:)]) {
        [self.delegate plvbLinkMicManager:self rtcConnectionStateDidChanged:self.connectionState];
    }
}

/**
 * 5.3 SDK 跟服务器的连接恢复
 */
- (void)onConnectionRecovery{
    self.connectionState = PLVBLinkMicConnectionStateConnected;
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:rtcConnectionStateDidChanged:)]) {
        [self.delegate plvbLinkMicManager:self rtcConnectionStateDidChanged:self.connectionState];
    }
}


#pragma mark 硬件设备事件
/**
 * 6.1 摄像头准备就绪
 */
- (void)onCameraDidReady{
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManagerCameraDidReady:)]) {
        [self.delegate plvbLinkMicManagerCameraDidReady:self];
    }
}

/**
 * 6.2 麦克风准备就绪
 */
- (void)onMicDidReady{
    
}

/**
 * 6.4 用于提示音量大小的回调，包括每个 userId 的音量和远端总音量
 *
 * 您可以通过调用 TRTCCloud 中的 enableAudioVolumeEvaluation 接口来开关这个回调或者设置它的触发间隔。
 * 需要注意的是，调用 enableAudioVolumeEvaluation 开启音量回调后，无论频道内是否有人说话，都会按设置的时间间隔调用这个回调;
 * 如果没有人说话，则 userVolumes 为空，totalVolume 为 0。
 *
 * @param userVolumes 所有正在说话的房间成员的音量，取值范围 0 - 100。
 * @param totalVolume 所有远端成员的总音量, 取值范围 0 - 100。
 * @note userId 为 nil 时表示自己的音量，userVolumes 内仅包含正在说话（音量不为 0 ）的用户音量信息。
 */
- (void)onUserVoiceVolume:(NSArray<TRTCVolumeInfo *> *)userVolumes totalVolume:(NSInteger)totalVolume{
    if ([PLVFdUtil checkArrayUseable:userVolumes]) {
        NSMutableDictionary * volumeDict = [[NSMutableDictionary alloc] init];
        for (TRTCVolumeInfo * info in userVolumes) {
            NSString * userLinkMicId = [PLVFdUtil checkStringUseable:info.userId] ? info.userId : self.userLinkMicId;
            float percent = [NSNumber numberWithUnsignedInteger:info.volume].floatValue / 100.0;
            NSNumber * volume = [NSNumber numberWithFloat:percent];
            if (volume && [volume isKindOfClass:NSNumber.class] &&
                [PLVFdUtil checkStringUseable:userLinkMicId]) {
                [volumeDict setObject:volume forKey:userLinkMicId];
            }
            if ([userLinkMicId isEqualToString:self.userLinkMicId]) {
                BOOL audible = percent >= 0.156 ? YES : NO;
                if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:localVoiceValue:receivedLocalAudibleVoice:)]) {
                    [self.delegate plvbLinkMicManager:self localVoiceValue:percent receivedLocalAudibleVoice:audible];
                }
                break;
            }
        }
        if ([PLVFdUtil checkDictionaryUseable:volumeDict]) {
            if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:reportAudioVolumeOfSpeakers:)]) {
                [self.delegate plvbLinkMicManager:self reportAudioVolumeOfSpeakers:volumeDict];
            }
        }
    }
}

/**
 * 8.3 启动旁路推流到 CDN 完成的回调
 *
 * 对应于 TRTCCloud 中的 startPublishCDNStream() 接口
 *
 * @note Start 回调如果成功，只能说明转推请求已经成功告知给腾讯云，如果目标 CDN 有异常，还是有可能会转推失败。
 */
- (void)onStartPublishCDNStream:(int)err errMsg:(NSString *)errMsg{
    BOOL sucess = err == 0;
    if (sucess) {
        self.streamPublishing = YES;
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:streamPublishedResult:)]) {
        [self.delegate plvbLinkMicManager:self streamPublishedResult:sucess];
    }
}

/**
 * 8.4 停止旁路推流到 CDN 完成的回调
 *
 * 对应于 TRTCCloud 中的 stopPublishCDNStream() 接口
 *
 */
- (void)onStopPublishCDNStream:(int)err errMsg:(NSString *)errMsg{
    self.streamPublishing = NO;
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManagerStreamUnpublished:)]) {
        [self.delegate plvbLinkMicManagerStreamUnpublished:self];
    }
}

/**
 * 8.5 设置云端的混流转码参数的回调，对应于 TRTCCloud 中的 setMixTranscodingConfig() 接口
 *
 * @param err 0表示成功，其余值表示失败
 * @param errMsg 具体错误原因
 */
- (void)onSetMixTranscodingConfig:(int)err errMsg:(NSString*)errMsg{
    
}

/**
 * 9.1 屏幕分享开启的事件回调
 *
 * 当您通过 {@link startScreenCapture} 等相关接口启动屏幕分享时，SDK 便会抛出此事件回调。
 */
- (void)onScreenCaptureStarted {
    self.screenCaptureStarted = YES;
}

/**
 * 9.4 屏幕分享停止的事件回调
 *
 * 当您通过 {@link stopScreenCapture} 停止屏幕分享时，SDK 便会抛出此事件回调。
 * @param reason 停止原因，0：用户主动停止；1：屏幕窗口关闭导致停止；2：表示屏幕分享的显示屏状态变更（如接口被拔出、投影模式变更等）。
 */
- (void)onScreenCaptureStoped:(int)reason {
    self.screenCaptureStarted = NO;
    PLVBRTCScreenCaptureFinishedReason finishReason = (reason == 0) ? PLVBRTCScreenCaptureFinishedReason_Engine : PLVBRTCScreenCaptureFinishedReason_Extension;
    // TODO: TRTC12.0 控制中心停止reason回调为0,11.4版本回调为1，暂时忽略是否为外部导致
//    if (finishReason == PLVBRTCScreenCaptureFinishedReason_Extension) { /// 如果用户主动点击控制中心停止 则需要 切换到摄像头采集
//        [self switchPublishStreamSourceType:PLVBRTCStreamSourceType_Camera];
//    }
    [self switchPublishStreamSourceType:PLVBRTCStreamSourceType_Camera];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:didScreenCaptureStopedReason:)]) {
        [self.delegate plvbLinkMicManager:self didScreenCaptureStopedReason:finishReason];
    }
}

#pragma mark -- TRTCVideoFrameDelegate --
/// 1 美颜处理
/// 2 AI抠像
/// 3 贴图
/// 后期处理顺序不能乱，否则效果会有影响
/// 比如美颜：会影响到虚拟背景，贴图效果 ； AI抠像，影响到贴图效果（贴图中存在人像）
- (uint32_t)onProcessVideoFrame:(TRTCVideoFrame *)srcFrame dstFrame:(TRTCVideoFrame *)dstFrame {
    // srcFrame dstFrame 中pixelBuffer 已经分配内存，但dstFrame 中图像数据为空
    // 如果没有任何后期处理 需要执行
    // dstFrame.pixelBuffer = srcFrame.pixelBuffer;

    // 1 美颜处理
    BOOL isButtyEffect = NO;
    if (self.beautyLightEngine && self.enableBeauty){
        // 轻美颜sdk
        if (!self.beautyLightEngine.beautyIsReady){
            dstFrame.pixelBuffer = srcFrame.pixelBuffer;
            return 0;
        }
        
        // RTC
        PLVBRTCVideoEncoderConfiguration *currentVideoEncoderConfiguration = self.currentVideoEncoderConfiguration;
        if (currentVideoEncoderConfiguration.videoOutputOrientationMode == PLVBRTCVideoOutputOrientationMode_Landscape &&
            currentVideoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Disabled &&
            self.localCameraFront) {
            [self updateLocalRenderParamsRotation:YES];
        } else {
            [self updateLocalRenderParamsRotation:NO];
        }
        
        // BGRA 格式数据
        CVPixelBufferRef pixelBuffer = srcFrame.pixelBuffer;
        CVPixelBufferLockBaseAddress(pixelBuffer, kCVPixelBufferLock_ReadOnly);
        int width = (int)CVPixelBufferGetWidth(pixelBuffer);
        int height = (int)CVPixelBufferGetHeight(pixelBuffer);
        int stride = (int)CVPixelBufferGetBytesPerRow(pixelBuffer)/4;
        uint8_t* pixels = (uint8_t *)CVPixelBufferGetBaseAddress(pixelBuffer);

        // 横屏的时候需要调整图片方向 否则美颜sdk 基于人像检测相关功能无法使用
        if (currentVideoEncoderConfiguration.videoOutputOrientationMode == PLVBRTCVideoOutputOrientationMode_Landscape){
            // 对数据进行顺时针旋转90 猜测：RTC摄像机视频方向为 向右
            // 旋转后图像 相关参数发生变化
            uint8_t* rotate_pixels = NULL;
            if (self.localCameraFront){
                rotate_pixels = rotateImageData90Degrees(pixels, width, height, stride);
            }
            else{
                rotate_pixels = rotateImage270Degrees(pixels, width, height, stride);
            }

            int new_width = height;
            int new_height = width;
            int new_stride = new_width;
            
            // 同步数据处理 美颜数据回调
            self.beautyLightEngine.datacallback = ^(uint8_t * _Nonnull pixel, int width, int height, int64_t ts) {
                // 对图像数据 逆时针旋转90
                uint8_t* process_data = NULL;
                if (self.localCameraFront){
                    process_data = rotateImage270Degrees(pixel, width, height, width);
                }
                else{
                    process_data = rotateImageData90Degrees(pixel, width, height, width);
                }

                CVPixelBufferLockBaseAddress(dstFrame.pixelBuffer, kCVPixelBufferLock_ReadOnly);
                uint8_t *pixelBufferBaseAddress = CVPixelBufferGetBaseAddress(dstFrame.pixelBuffer);
                size_t bytesPerRow = CVPixelBufferGetBytesPerRow(dstFrame.pixelBuffer);
                size_t ori_height = CVPixelBufferGetHeight(dstFrame.pixelBuffer);
                size_t ori_width = CVPixelBufferGetWidth(dstFrame.pixelBuffer);
                // 对pixelbuffer 数据更新
                for (size_t row = 0; row < ori_height; row++) {
                    memcpy(pixelBufferBaseAddress + row * bytesPerRow, process_data + row * ori_width * 4, ori_width * 4);
                }
                CVPixelBufferUnlockBaseAddress(dstFrame.pixelBuffer, kCVPixelBufferLock_ReadOnly);
                free(process_data);
            };
            // 美颜数据处理
            [self.beautyLightEngine processBytes:new_stride height:new_height pixels:rotate_pixels stride:new_stride ts:srcFrame.timestamp];
            // 解锁缓冲区，修改完毕 允许系统进一步处理
            CVPixelBufferUnlockBaseAddress(pixelBuffer, kCVPixelBufferLock_ReadOnly);
            // 释放手动分配的资源
            // 后续优化，创建缓冲区，不比频繁创建、释放
            free(rotate_pixels);
        }
        else{
            // 竖屏推流
            // 同步数据处理 美颜数据回调
            self.beautyLightEngine.datacallback = ^(uint8_t * _Nonnull pixel, int width, int height, int64_t ts) {
                
                CVPixelBufferLockBaseAddress(dstFrame.pixelBuffer, kCVPixelBufferLock_ReadOnly);
                uint8_t *pixelBufferBaseAddress = CVPixelBufferGetBaseAddress(dstFrame.pixelBuffer);
                size_t bytesPerRow = CVPixelBufferGetBytesPerRow(dstFrame.pixelBuffer);
                
                // 对pixelbuffer 数据更新 处理完毕的数据，填充到目标pixelbuffer
                for (size_t row = 0; row < height; row++) {
                    memcpy(pixelBufferBaseAddress + row * bytesPerRow, pixel + row * width * 4, width * 4);
                }
                // 解锁缓冲区，修改完毕 允许系统进一步处理
                CVPixelBufferUnlockBaseAddress(dstFrame.pixelBuffer, kCVPixelBufferLock_ReadOnly);
            };
            // 美颜数据处理
            [self.beautyLightEngine processBytes:stride height:height pixels:pixels stride:stride ts:srcFrame.timestamp];
            // 解锁缓冲区，修改完毕 允许系统进一步处理
            CVPixelBufferUnlockBaseAddress(pixelBuffer, kCVPixelBufferLock_ReadOnly);
        }
        
        isButtyEffect = YES;
    }
    else if (self.enableBeauty)
    {
        
        // 字节美颜sdk处理
//        if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:captureVideoFrameTextureId:videoFrameWidth:videoFrameHeight:videoFrameTimeStamp:processedVideoFrameTextureId:)]) {
//            PLVBRTCVideoEncoderConfiguration *currentVideoEncoderConfiguration = self.currentVideoEncoderConfiguration;
//            if (currentVideoEncoderConfiguration.videoOutputOrientationMode == PLVBRTCVideoOutputOrientationMode_Landscape &&
//                currentVideoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Disabled &&
//                self.localCameraFront) {
//                [self updateLocalRenderParamsRotation:YES];
//            } else {
//                [self updateLocalRenderParamsRotation:NO];
//            }
//            
//            int ret = [self.delegate plvbLinkMicManager:self
//                             captureVideoFrameTextureId:srcFrame.textureId
//                                        videoFrameWidth:srcFrame.width
//                                       videoFrameHeight:srcFrame.height
//                                    videoFrameTimeStamp:srcFrame.timestamp
//                           processedVideoFrameTextureId:dstFrame.textureId];
//            if (ret != 0) {
//                dstFrame.textureId = srcFrame.textureId;
//            }
//        }
        
        // CVPixelBuffer 数据处理
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:captureVideoFramePixelBuffer:videoFrameWidth:videoFrameHeight:videoFrameTimeStamp:processedVideoFramePixelBuffer:)]){
            
            // 设置opengl 上下文，需要做像素格式转化，保持同一个上下文
            if ([EAGLContext currentContext] != glcontext) {
                [EAGLContext setCurrentContext:glcontext];
            }
            PLVBRTCVideoEncoderConfiguration *currentVideoEncoderConfiguration = self.currentVideoEncoderConfiguration;
            if (currentVideoEncoderConfiguration.videoOutputOrientationMode == PLVBRTCVideoOutputOrientationMode_Landscape &&
                currentVideoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Disabled &&
                self.localCameraFront) {
                [self updateLocalRenderParamsRotation:YES];
            } else {
                [self updateLocalRenderParamsRotation:NO];
            }
            
            int ret = [self.delegate plvbLinkMicManager:self
                           captureVideoFramePixelBuffer:srcFrame.pixelBuffer
                                        videoFrameWidth:srcFrame.width
                                       videoFrameHeight:srcFrame.height
                                    videoFrameTimeStamp:srcFrame.timestamp
                         processedVideoFramePixelBuffer:dstFrame.pixelBuffer];
            
            isButtyEffect = YES;

            if (ret != 0) {
                isButtyEffect = NO;
            }
        }
    }

    // 其他图像后期处理
    // 2 AI 抠像
    CVPixelBufferRef pixelSrcBuffer = isButtyEffect ? dstFrame.pixelBuffer : srcFrame.pixelBuffer;
    BOOL isAIMatting = NO;
    int ret = [self.videoProcessor handleAIMatting:pixelSrcBuffer mode:self.aiMattingMode image:self.aiMattingBgImage];
    if (ret == 0){
        isAIMatting = YES;
    }
   
    // 3 贴图操作
    [self stickerPressetor:self.stickerImage pixelBuffer:pixelSrcBuffer];
    
    // 最后判断 减少内存操作 如果未经过美颜处理 需要将src数据 传递给dsr 数据
    if (!isButtyEffect){
        dstFrame.pixelBuffer = srcFrame.pixelBuffer;
    }
    
    return 0;
}

CVReturn copyPixelBuffer(CVPixelBufferRef srcBuffer, CVPixelBufferRef dstBuffer) {
    // 锁定源缓冲区和目标缓冲区
    CVPixelBufferLockBaseAddress(srcBuffer, kCVPixelBufferLock_ReadOnly);
    CVPixelBufferLockBaseAddress(dstBuffer, 0);
    
    // 获取缓冲区的基地址
    void *srcAddress = CVPixelBufferGetBaseAddress(srcBuffer);
    void *dstAddress = CVPixelBufferGetBaseAddress(dstBuffer);
    
    // 获取行字节数和高度
    size_t bytesPerRow = CVPixelBufferGetBytesPerRow(srcBuffer);
    size_t height = CVPixelBufferGetHeight(srcBuffer);
    
    // 执行内存拷贝
    memcpy(dstAddress, srcAddress, bytesPerRow * height);
    
    // 解锁缓冲区
    CVPixelBufferUnlockBaseAddress(dstBuffer, 0);
    CVPixelBufferUnlockBaseAddress(srcBuffer, kCVPixelBufferLock_ReadOnly);
    
    return kCVReturnSuccess;
}


CVReturn copyPixelBufferWithVImage(CVPixelBufferRef srcBuffer, CVPixelBufferRef dstBuffer) {
    CVPixelBufferLockBaseAddress(srcBuffer, kCVPixelBufferLock_ReadOnly);
    CVPixelBufferLockBaseAddress(dstBuffer, 0);
    
    void *srcAddress = CVPixelBufferGetBaseAddress(srcBuffer);
    void *dstAddress = CVPixelBufferGetBaseAddress(dstBuffer);
    
    size_t srcBytesPerRow = CVPixelBufferGetBytesPerRow(srcBuffer);
    size_t dstBytesPerRow = CVPixelBufferGetBytesPerRow(dstBuffer);
    size_t srcWidth = CVPixelBufferGetWidth(srcBuffer);
    size_t srcHeight = CVPixelBufferGetHeight(srcBuffer);
    size_t dstWidth = CVPixelBufferGetWidth(dstBuffer);
    size_t dstHeight = CVPixelBufferGetHeight(dstBuffer);
    
    // 取较小的值
    size_t width = MIN(srcWidth, dstWidth);
    size_t height = MIN(srcHeight, dstHeight);
    size_t srcRowBytes = MIN(srcBytesPerRow, dstBytesPerRow);
    
    vImage_Buffer src = {
        .data = srcAddress,
        .height = height,
        .width = width,
        .rowBytes = srcRowBytes
    };
    
    vImage_Buffer dst = {
        .data = dstAddress,
        .height = height,
        .width = width,
        .rowBytes = srcRowBytes
    };
    
    // 假设是 BGRA 格式
    vImage_Error error = vImageCopyBuffer(&src, &dst, 4, kvImageNoFlags);
    
    CVPixelBufferUnlockBaseAddress(dstBuffer, 0);
    CVPixelBufferUnlockBaseAddress(srcBuffer, kCVPixelBufferLock_ReadOnly);
    
    return (error == kvImageNoError) ? kCVReturnSuccess : kCVReturnError;
}

// chatgpt
- (uint8_t *)rotateImage90Clockwise:(uint8_t *)data width:(NSUInteger)width height:(NSUInteger)height stride:(NSUInteger)stride {
    // 计算旋转后图像的宽度和高度
    NSUInteger newWidth = height;
    NSUInteger newHeight = width;
    NSUInteger newStride = newWidth * 4; // 每个像素4个字节（BGRA）
    
    // 分配新的图像数据
    uint8_t *rotatedData = (uint8_t *)malloc(newHeight * newStride);
    if (!rotatedData) {
        return NULL; // 内存分配失败
    }
    
    // 进行顺时针旋转
    for (NSUInteger y = 0; y < height; y++) {
        for (NSUInteger x = 0; x < width; x++) {
            // 计算旋转后的位置
            NSUInteger newX = height - 1 - y;
            NSUInteger newY = x;
            // 获取原始数据的指针
            uint8_t *sourcePixel = data + y * stride + x * 4; // 每个像素4个字节
            // 将数据拷贝到旋转后的位置
            uint8_t *destPixel = rotatedData + newY * newStride + newX * 4; // 每个像素4个字节
            memcpy(destPixel, sourcePixel, 4); // 拷贝BGRA数据
        }
    }
    
    return rotatedData; // 返回旋转后的图像数据
}

- (uint8_t *)rotateImage270Clockwise:(uint8_t *)data width:(NSUInteger)width height:(NSUInteger)height stride:(NSUInteger)stride {
    // 计算旋转后图像的宽度和高度
    NSUInteger newWidth = height;
    NSUInteger newHeight = width;
    NSUInteger newStride = newWidth * 4; // 每个像素4个字节（BGRA）
    
    // 分配新的图像数据
    uint8_t *rotatedData = (uint8_t *)malloc(newHeight * newStride);
    if (!rotatedData) {
        return NULL; // 内存分配失败
    }
    
    // 进行顺时针270度旋转
    for (NSUInteger y = 0; y < height; y++) {
        for (NSUInteger x = 0; x < width; x++) {
            // 计算旋转后的位置
            NSUInteger newX = y;
            NSUInteger newY = width - 1 - x;
            // 获取原始数据的指针
            uint8_t *sourcePixel = data + y * stride + x * 4; // 每个像素4个字节
            // 将数据拷贝到旋转后的位置
            uint8_t *destPixel = rotatedData + newY * newStride + newX * 4; // 每个像素4个字节
            memcpy(destPixel, sourcePixel, 4); // 拷贝BGRA数据
        }
    }
    
    return rotatedData; // 返回旋转后的图像数据
}


// Genie
uint8_t* rotateImage270Degrees(uint8_t *data, int width, int height, int stride) {
    // 创建一个新的缓冲区来存储旋转后的图像数据
    uint8_t *rotatedData = (uint8_t *)malloc(height * stride *4);

    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            // 源像素的位置
            int srcIndex = y * stride*4 + x * 4;

            // 目标像素的新位置
            // 顺时针旋转270度，相当于逆时针旋转90度，此时新位置为 (height - 1 - y, x)
//            int destIndex = x * stride + (height - 1 - y) * 4;
            int destIndex = (width - 1 - x) * height*4 + y * 4;

            // 将数据复制到新位置
            rotatedData[destIndex]     = data[srcIndex];     // B
            rotatedData[destIndex + 1] = data[srcIndex + 1]; // G
            rotatedData[destIndex + 2] = data[srcIndex + 2]; // R
            rotatedData[destIndex + 3] = data[srcIndex + 3]; // A
        }
    }
    
    return rotatedData;
}

// 旋转90度就是将像素点从 src[y * width + x] 映射到 dst[x* height + height - y - 1]
 uint8_t * rotateImageData90Degrees(uint8_t *data, int width, int height, int stride) {
    // 创建一个新的缓冲区来存储旋转后的图像数据
//    uint8_t *rotatedData = (uint8_t *)malloc(width * height *4);
    uint8_t *rotatedData = (uint8_t *)malloc(stride * height *4);

    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            // 源像素的位置
//            int srcIndex = y * stride + x * 4;
            int srcIndex = y * stride *4 + x * 4;

            // 目标像素的新位置
            // 顺时针旋转90度，新位置为 (x, height - 1 - y)
//            int destIndex = (x * height + (height - 1 - y)) * 4;
            int destIndex = x * height*4 + (height - 1 - y) * 4;

            // 将数据复制到新位置
            rotatedData[destIndex]     = data[srcIndex];     // B
            rotatedData[destIndex + 1] = data[srcIndex + 1]; // G
            rotatedData[destIndex + 2] = data[srcIndex + 2]; // R
            rotatedData[destIndex + 3] = data[srcIndex + 3]; // A
        }
    }
    
    return rotatedData;
}

- (int)stickerPressetor:(UIImage *)stickerImage
            pixelBuffer:(CVPixelBufferRef )pixelBuffer{
    
    if (self.stickerImage && pixelBuffer) {
        // 锁定源缓冲区进行读取
        CVPixelBufferLockBaseAddress(pixelBuffer, 0);
        
        // 获取缓冲区尺寸和属性
        size_t width = CVPixelBufferGetWidth(pixelBuffer);
        size_t height = CVPixelBufferGetHeight(pixelBuffer);
        size_t srcBytesPerRow = CVPixelBufferGetBytesPerRow(pixelBuffer);
            
        // 获取源和目标缓冲区基地址
        uint8_t *srcBaseAddress = (uint8_t *)CVPixelBufferGetBaseAddress(pixelBuffer);
        
        // 步骤2: 在目标缓冲区上创建图形上下文以绘制贴纸
        CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
        CGContextRef context = NULL;
        
        if (colorSpace) {
            context = CGBitmapContextCreate(srcBaseAddress,
                                          width,
                                          height,
                                          8,
                                          srcBytesPerRow,
                                          colorSpace,
                                          kCGBitmapByteOrder32Little | kCGImageAlphaPremultipliedFirst);
            CGColorSpaceRelease(colorSpace);
        }
        
        if (context) {
            // 计算等比例缩放后的尺寸
            CGFloat aspectRatio = self.stickerImage.size.width / self.stickerImage.size.height;
            CGFloat destWidth = width;
            CGFloat destHeight = destWidth / aspectRatio;
            
            // 如果高度超出，则以高度为基准
            if (destHeight > height) {
                destHeight = height;
                destWidth = destHeight * aspectRatio;
            }
            
            // 计算居中位置
            CGFloat xOffset = (width - destWidth) / 2;
            CGFloat yOffset = (height - destHeight) / 2;
            
            // 绘制贴纸图像
            CGRect stickerRect = CGRectMake(xOffset, yOffset, destWidth, destHeight);
            CGContextDrawImage(context, stickerRect, self.stickerImage.CGImage);
            
            // 释放上下文
            CGContextRelease(context);
        }
        
        // 解锁缓冲区
        CVPixelBufferUnlockBaseAddress(pixelBuffer, 0);
    }
    
    return 0;
}


#endif

@end
