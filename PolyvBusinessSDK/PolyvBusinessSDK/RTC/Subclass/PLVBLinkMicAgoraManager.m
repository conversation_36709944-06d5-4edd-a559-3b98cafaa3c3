//
//  PLVBLinkMicAgoraManager.m
//  PLVBusinessSDK
//
//  Created by <PERSON><PERSON> on 2020/4/16.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVBLinkMicAgoraManager.h"

#import "PLVBLinkMicManager+PrivateExtension.h"

#if __has_include(<AgoraRtcKit/AgoraRtcEngineKit.h>)
    #import <AgoraRtcKit/AgoraRtcEngineKit.h>
    #define AgoraSDK_iOS
#endif

#define AgoraRtcEngineKitCLS NSClassFromString(@"AgoraRtcEngineKit")
#define AgoraVideoEncoderConfigurationCLS NSClassFromString(@"AgoraVideoEncoderConfiguration")
#define AgoraRtcVideoCanvasCLS NSClassFromString(@"AgoraRtcVideoCanvas")
#define AgoraLiveTranscodingUserCLS NSClassFromString(@"AgoraLiveTranscodingUser")
#define AgoraLiveTranscodingCLS NSClassFromString(@"AgoraLiveTranscoding")
#define AgoraImageCLS NSClassFromString(@"AgoraImage")

#ifdef AgoraSDK_iOS
@interface AgoraRtcEngineKitStandIn : NSObject

- (int)startPreview;
- (int)stopPreview;

@end

@implementation AgoraRtcEngineKitStandIn

- (int)startPreview{
    PLVF_NORMAL_LOG_DEBUG(@"BLinkMic", @"PLVMethod - 'startPreview'");
    return -1;
}
- (int)stopPreview{
    PLVF_NORMAL_LOG_DEBUG(@"BLinkMic", @"PLVMethod - 'stopPreview'");
    return -1;
}

@end

@interface PLVBLinkMicAgoraManager ()<AgoraRtcEngineDelegate, AgoraVideoDataFrameProtocol>
{
    EAGLContext *glcontext;
}
#pragma mark RTC对象
@property (nonatomic, strong) AgoraRtcEngineKit * agoraKit;
@property (nonatomic, strong) AgoraLiveTranscoding * transcoding;
@property (nonatomic, strong) AgoraRtcEngineKitStandIn * agoraKitStandIn;

#pragma mark 状态
@property (nonatomic, assign) BOOL videoDidEnabled; /// 视频模块是否已启用
@property (nonatomic, assign) BOOL hadFirstStartLocalPreview; /// 是否已启动过本地预览
@property (nonatomic, assign) BOOL leavingChannel; /// 是否正在离开RTC频道
@property (nonatomic, assign) BOOL isProcessVideoData; /// 是否处理回调的视频数据

#pragma mark 功能对象
@property (nonatomic, strong) PLVImageUtil *imageUtils;


@end

@interface AgoraRtcEngineKit ()

- (int)enableAudioVolumeIndication:(NSInteger)interval smooth:(NSInteger)smooth;
- (int)enableAudioVolumeIndication:(NSInteger)interval smooth:(NSInteger)smooth report_vad:(BOOL)report_vad;

@end
#endif

@implementation PLVBLinkMicAgoraManager

#ifdef AgoraSDK_iOS
@synthesize roleType = _roleType;

#pragma mark - [ Father Public Methods ]
- (void)createRTCEngine{
    if (!AgoraRtcEngineKitCLS) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVLinkMicManager - manager create failed, need ARtcSDK");
        return;
    }

    if (!_agoraKit) {
        if (![PLVFdUtil checkStringUseable:self.rtcAppId]) {
            PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - init engine failed, appId is nil %@",self.rtcAppId);
            return;
        }
        
        _agoraKit = [AgoraRtcEngineKitCLS sharedEngineWithAppId:self.rtcAppId delegate:self];
        _agoraKitStandIn = AgoraRtcEngineKitCLS ? _agoraKit : [[AgoraRtcEngineKitStandIn alloc] init];
        [_agoraKit setChannelProfile:AgoraChannelProfileLiveBroadcasting];
        [_agoraKit setAudioProfile:AgoraAudioProfileDefault scenario:AgoraAudioScenarioEducation];
        [_agoraKit setClientRole:(_roleType == PLVBLinkMicRoleBroadcaster ? AgoraClientRoleBroadcaster : AgoraClientRoleAudience)];
        [_agoraKit enableWebSdkInteroperability:YES];
        // 关闭AI降噪，使用私有参数
        [_agoraKit enableDeepLearningDenoise:NO];
        [_agoraKit setParameters:@"{\"che.audio.set_apm_para\":{\"function\":\"sw_ans\",\"param\":[{\"force\": false,\"enable\":false}]}}"];
        [self switchNoiseCancellationLevelTo:self.localNoiseCancellationLevel];
        if ([_agoraKit respondsToSelector:@selector(enableAudioVolumeIndication:smooth:)]) {
            [_agoraKit enableAudioVolumeIndication:300 smooth:3];
        }else if ([_agoraKit respondsToSelector:@selector(enableAudioVolumeIndication:smooth:report_vad:)]){
            [_agoraKit enableAudioVolumeIndication:300 smooth:3 report_vad:YES];
        }
        [_agoraKit enableLastmileTest];
        
        self.publishStreamSourceType = PLVBRTCStreamSourceType_Camera;
        glcontext = [[EAGLContext alloc] initWithAPI:kEAGLRenderingAPIOpenGLES3];
        if ([EAGLContext currentContext] != glcontext) {
            [EAGLContext setCurrentContext:glcontext];
        }
        self.eaglContext = glcontext;
        self.imageUtils = [[PLVImageUtil alloc] init];
    }
}

- (void)destroyRTCEngine{
    [AgoraRtcEngineKitCLS destroy];
    self.agoraKit = nil;
    
    self.videoDidEnabled = NO;
    self.hadFirstStartLocalPreview = NO;
    self.leavingChannel = NO;
    self.hadJoinedRTC = NO;
}

- (int)joinRtcChannelWithChannelId:(NSString *)channelId userLinkMicId:(NSString *)userLinkMicId{
    if (!AgoraRtcEngineKitCLS) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVLinkMicManager - manager create failed ，need ARtcSDK");
        return -100;
    }
    
    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:userLinkMicId]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - join rtc channel failed,channelId:%@, userLinkMicId:%@",channelId,userLinkMicId);
        return -1;
    }else{
        self.channelId = channelId;
        self.userLinkMicId = userLinkMicId;
    }
    
    __weak typeof(self) weakSelf = self;
    int code = [self.agoraKit joinChannelByToken:self.rtcToken channelId:channelId info:nil uid:userLinkMicId.integerValue joinSuccess:^(NSString * _Nonnull channel, NSUInteger uid, NSInteger elapsed) {
        PLVF_NORMAL_LOG_INFO(@"BLinkMic", @"PLVBLinkMicManager - join rtc channel success,channel:%@, uid:%lu, elapsed:%ld", channel, (unsigned long)uid, (long)elapsed);
        
        weakSelf.hadJoinedRTC = YES;
        dispatch_async(dispatch_get_main_queue(), ^{
            if ([weakSelf.delegate respondsToSelector:@selector(plvbLinkMicManager:joinRTCChannelComplete:uid:)]) {
                [weakSelf.delegate plvbLinkMicManager:weakSelf joinRTCChannelComplete:channel uid:weakSelf.userLinkMicId];
            }
            
            [weakSelf callbackForCanSetupLocalHardwareDefaultState];
        });
    }];
    return code;
}

- (int)leaveRtcChannel{
    if (self.hadJoinedRTC) {
        if (self.leavingChannel) {
            return -1;
        }else{
            self.leavingChannel = YES;
            __weak typeof(self) weakSelf = self;
            int code = [self.agoraKit leaveChannel:^(AgoraChannelStats *stat) {
                weakSelf.leavingChannel = NO;
                weakSelf.hadJoinedRTC = NO;
            }];
            if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:leaveRTCChannelComplete:)]) {
                [self.delegate plvbLinkMicManager:self leaveRTCChannelComplete:self.channelId];
            }
            return code;
        }
    }else{
        self.hadJoinedRTC = NO;
        
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:leaveRTCChannelComplete:)]) {
            [self.delegate plvbLinkMicManager:self leaveRTCChannelComplete:self.channelId];
        }
        return 0;
    }
}

- (void)subscribeStreamWithRTCUserId:(NSString *)rtcUserId renderOnView:(UIView *)renderSuperView mediaType:(PLVBRTCSubscribeStreamMediaType)mediaType{
    if (rtcUserId.integerValue < 0) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - add rtc canvas failed %@",rtcUserId);
        return;
    }
    BOOL isTeacher = [rtcUserId isEqualToString:self.channelId];
    BOOL remoteUser = ![rtcUserId isEqualToString:self.userLinkMicId];
    AgoraRtcVideoCanvas *canvas = [[AgoraRtcVideoCanvasCLS alloc] init];
    canvas.uid = rtcUserId.integerValue;
    canvas.view = renderSuperView;
    canvas.renderMode = isTeacher ? AgoraVideoRenderModeFit : AgoraVideoRenderModeHidden;
    if (remoteUser) {
        [self.agoraKit setupRemoteVideo:canvas];
    } else {
        [self.agoraKit setupLocalVideo:canvas];
    }
}

- (void)subscribeStreamWithRTCUserId:(NSString *)rtcUserId renderOnView:(UIView *)renderSuperView mediaType:(PLVBRTCSubscribeStreamMediaType)mediaType subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode{
    /// ARTC不支持 ‘根据流订阅方式’ 将流进行渲染
    [self subscribeStreamWithRTCUserId:rtcUserId renderOnView:renderSuperView mediaType:mediaType];
}

- (void)switchPublishStreamSourceType:(PLVBRTCStreamSourceType)streamSourceType{
    if (![PLVFdUtil checkStringUseable:self.appGroup]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - switchPublishStreamSourceType failed, Please setup appGroup first");
        return;
    }
}

- (int)setUserRoleTo:(PLVBLinkMicRoleType)roleType{
    _roleType = roleType;
    return [self.agoraKit setClientRole:(AgoraClientRole)roleType];
}

#pragma mark 本地 摄像头 使用
- (void)setupLocalPreviewWithCanvasModel:(PLVBRTCVideoViewCanvasModel *)canvasModel{
    if (![canvasModel checkModelValid]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - setupLocalPreviewWithCanvasModel failed, canvasModel invalid, userRTCId:%@ renderCanvasView:%@",canvasModel.userRTCId, canvasModel.renderCanvasView);
        return;
    }
    
    self.currentLocalPreviewCanvasModel = canvasModel;
    
    AgoraRtcVideoCanvas *canvas = [[AgoraRtcVideoCanvasCLS alloc] init];
    canvas.uid = canvasModel.userRTCId.intValue;
    canvas.view = canvasModel.renderCanvasView;
    canvas.renderMode = (canvasModel.rtcVideoVideoFillMode == PLVBRTCVideoViewFillMode_Fit ? AgoraVideoRenderModeFit : AgoraVideoRenderModeHidden);
    [self.agoraKit setupLocalVideo:canvas];
}

- (int)setupLocalVideoPreviewMirrorMode:(PLVBRTCVideoMirrorMode)mirrorMode{
    int resultCode1 = [self enableLocalVideo:NO];
    int resultCode2 = [self.agoraKit setLocalVideoMirrorMode:(AgoraVideoMirrorMode)mirrorMode];
    int resultCode3 = [self enableLocalVideo:YES];
    
    if (resultCode1 > 0) { resultCode1 = 0; }
    if (resultCode2 > 0) { resultCode2 = 0; }
    if (resultCode3 > 0) { resultCode3 = 0; }
    int resultCode = resultCode1 + resultCode2 + resultCode3;
    
    if (resultCode == 0) { self.localVideoMirrorMode = mirrorMode; }
    return resultCode;
}

- (int)switchLocalUserCamera:(BOOL)frontCamera{
    if (self.localCameraFront != frontCamera) {
        int resultCode = [self.agoraKit switchCamera];
        if (resultCode == 0) {
            self.localCameraFront = frontCamera;
            if (frontCamera) { self.localCameraTorchOpen = NO; }
            [self setupVideoEncoderConfiguration:self.currentVideoEncoderConfiguration];
        }
        return resultCode;
    }else{
        return 0;
    }
}

- (int)openLocalUserCameraTorch:(BOOL)openCameraTorch{
    int resultCode = [self.agoraKit setCameraTorchOn:openCameraTorch];
    if (resultCode == 0) { self.localCameraTorchOpen = openCameraTorch; }
    return resultCode;
}

- (int)openLocalUserCamera:(BOOL)openCamera{
    int resultCode1 = [self enableLocalVideo:openCamera];
    int resultCode2 = [self startLocalPreview:openCamera];
    [self muteLocalVideoStream:!openCamera];
    
    if (self.roleType != PLVBLinkMicRoleBroadcaster &&
        !self.hadFirstStartLocalPreview &&
        openCamera) {
        self.hadFirstStartLocalPreview = YES;
        [self enableLocalVideo:YES];
    }
    
    if (resultCode1 > 0) { resultCode1 = 0; }
    if (resultCode2 > 0) { resultCode2 = 0; }
    int resultCode = resultCode1 + resultCode2;
    
    if (resultCode == 0) { self.localCameraOpen = openCamera; }
    return resultCode;
}

- (int)localVideoStreamOpenPlaceholder:(BOOL)openPlaceholder{
    self.localVideoStreamPlaceholderOpen = openPlaceholder;
    int resultCode = [self setupLiveBackgroundImage];
    return resultCode;
}

- (int)enableLocalVideo:(BOOL)enabled{
    if (!self.videoDidEnabled) {
        self.videoDidEnabled = YES;
        [self.agoraKit enableVideo];
    }
    int resultCode = [self.agoraKit enableLocalVideo:enabled];
    if (resultCode == 0) { self.localVideoEnable = enabled; }
    return resultCode;
}

- (int)startLocalPreview:(BOOL)start{
    int resultCode;
    if (start) {
        if (!self.videoDidEnabled) {
            self.videoDidEnabled = YES;
            [self.agoraKit enableVideo];
        }
        resultCode = [self.agoraKitStandIn startPreview];
        // resultCode = [self.agoraKit setParameters:@"{\"rtc.video.preview\":\"true\"}"];
    }else{
        resultCode = [self.agoraKitStandIn stopPreview];
        // resultCode = [self.agoraKit setParameters:@"{\"rtc.video.preview\":\"false\"}"];
    }
    return resultCode;
}

- (int)muteLocalVideoStream:(BOOL)mute{
    if (self.roleType != PLVBLinkMicRoleBroadcaster) {
        return -1;
    }
    int resultCode = [self.agoraKit muteLocalVideoStream:mute];
    if (resultCode == 0) { self.localVideoStreamMute = mute; }
    return resultCode;
}

- (void)enableLocalVideoFrameProcess:(BOOL)enabled {
    self.isProcessVideoData = enabled;
    if (enabled) {
        [self.agoraKit setVideoDataFrame:self];
    }
}

#pragma mark 本地 麦克风 使用
- (int)changeLocalMicVolume:(CGFloat)micVolume{
    int resultCode = [self.agoraKit adjustRecordingSignalVolume:micVolume];
    if (resultCode == 0) { self.localMicVolume = micVolume; }
    return resultCode;
}

- (int)openLocalUserMic:(BOOL)openMic{
    int resultCode = [self enableLocalAudio:openMic];
    [self muteLocalAudioStream:!openMic];
    
    if (resultCode == 0) { self.localMicOpen = openMic; }
    return resultCode;
}

- (int)enableLocalAudio:(BOOL)enabled{
    int resultCode = [self.agoraKit enableLocalAudio:enabled];
    if (resultCode == 0) { self.localAudioEnable = enabled; }
    return resultCode;
}

- (int)muteLocalAudioStream:(BOOL)mute{
    if (self.roleType != PLVBLinkMicRoleBroadcaster) {
        return -1;
    }
    int resultCode = [self.agoraKit muteLocalAudioStream:mute];
    if (resultCode == 0) { self.localAudioStreamMute = mute; }
    return resultCode;
}

- (int)switchNoiseCancellationLevelTo:(PLVBLinkMicNoiseCancellationLevel)level {
    switch (level) {
        case PLVBLinkMicNoiseCancellationLevelSoft:
            [self.agoraKit setParameters:@"{\"che.audio.ns.mode\":0}"];
            [self.agoraKit setParameters:@"{\"che.audio.enable.nsng\":true}"];
            [self.agoraKit setParameters:@"{\"che.audio.nsng.lowerBound\":80}"];
            [self.agoraKit setParameters:@"{\"che.audio.nsng.lowerMask\":50}"];
            [self.agoraKit setParameters:@"{\"che.audio.nsng.statisticalbound\":5}"];
            [self.agoraKit setParameters:@"{\"che.audio.nsng.finallowermask\":30}"];
            [self.agoraKit setParameters:@"{\"che.audio.nsng.enhfactorstastical\":200}"];
            break;
        case PLVBLinkMicNoiseCancellationLevelAggressive:
            [self.agoraKit setParameters:@"{\"che.audio.ns.mode\":2}"];
            [self.agoraKit setParameters:@"{\"che.audio.enable.nsng\":true}"];
            [self.agoraKit setParameters:@"{\"che.audio.nsng.lowerBound\":10}"];
            [self.agoraKit setParameters:@"{\"che.audio.nsng.lowerMask\":10}"];
            [self.agoraKit setParameters:@"{\"che.audio.nsng.statisticalbound\":0}"];
            [self.agoraKit setParameters:@"{\"che.audio.nsng.finallowermask\":8}"];
            [self.agoraKit setParameters:@"{\"che.audio.nsng.enhfactorstastical\":200}"];
            break;
        default:
            break;
    }
    self.localNoiseCancellationLevel = level;
    return 0;
}

- (int)enableExternalDevice:(BOOL)enabled {
    self.localExternalDeviceEnabled = enabled;
    return 0; // 声网当前不支持设置媒体音量或通话音量
}

#pragma mark 流相关
- (void)setupVideoEncoderConfiguration:(PLVBRTCVideoEncoderConfiguration *)videoEncoderConfiguration{
    if (videoEncoderConfiguration && [videoEncoderConfiguration isKindOfClass:PLVBRTCVideoEncoderConfiguration.class]) {
        [super setupVideoEncoderConfiguration:videoEncoderConfiguration];
        
        AgoraVideoEncoderConfiguration * configuration = [[AgoraVideoEncoderConfigurationCLS alloc] initWithSize:videoEncoderConfiguration.videoResolution frameRate:videoEncoderConfiguration.videoFrameRate bitrate:videoEncoderConfiguration.videoBitrate orientationMode:(videoEncoderConfiguration.videoOutputOrientationMode == PLVBRTCVideoOutputOrientationMode_Landscape ? AgoraVideoOutputOrientationModeFixedLandscape : AgoraVideoOutputOrientationModeAdaptative)];
        if (self.localCameraFront) {
            if (videoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Auto ||
                videoEncoderConfiguration.videoMirrorMode == PLVBRTCVideoMirrorMode_Enabled) {
                configuration.mirrorMode = AgoraVideoMirrorModeEnabled;
            }else{
                configuration.mirrorMode = AgoraVideoMirrorModeDisabled;
            }
        }else{
            configuration.mirrorMode = AgoraVideoMirrorModeDisabled;
        }
        
        if (videoEncoderConfiguration.videoQosPreference == PLVBRTCVideoQosPreferenceSmooth) {
            configuration.degradationPreference = AgoraDegradationMaintainFramerate;
        } else {
            configuration.degradationPreference = AgoraDegradationMaintainQuality;
        }
        [self.agoraKit setVideoEncoderConfiguration:configuration];
        
        if (self.transcoding != nil) {
            self.transcoding.videoBitrate = videoEncoderConfiguration.videoBitrate;
            self.transcoding.size = CGSizeMake(self.videoCDNWidth, self.videoCDNHeight);
            [self.agoraKit setLiveTranscoding:self.transcoding];
        }
    }else{
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - setupVideoEncoderConfiguration failed, videoEncoderConfiguration invalid %@",videoEncoderConfiguration);
    }
}

- (int)addPublishStreamUrl:(NSString *)streamUrl transcodingEnabled:(BOOL)transcodingEnabled{
    return [self.agoraKit addPublishStreamUrl:streamUrl transcodingEnabled:transcodingEnabled];
}

- (int)removePublishStreamUrl:(NSString *)streamUrl{
    return [self.agoraKit removePublishStreamUrl:streamUrl];
}

- (void)setupLiveTranscoding{
    self.transcoding = [[AgoraLiveTranscodingCLS alloc] init];
    self.transcoding.size = CGSizeMake(self.videoCDNWidth, self.videoCDNHeight);
    self.transcoding.videoBitrate = self.currentVideoEncoderConfiguration.videoBitrate;
    self.transcoding.videoFramerate = self.currentVideoEncoderConfiguration.videoFrameRate;
    self.transcoding.lowLatency = NO;
    self.transcoding.videoGop = 30;
    self.transcoding.videoCodecProfile = AgoraVideoCodecProfileTypeHigh;
    self.transcoding.backgroundColor = [UIColor blackColor];
    
    self.transcoding.audioSampleRate = AgoraAudioSampleRateType48000;
    self.transcoding.audioBitrate = 48;
    self.transcoding.audioChannels = 2;
    self.transcoding.transcodingExtraInfo = nil;
    self.transcoding.watermark = nil;
}

- (int)setupLiveTranscodingUser{
    AgoraLiveTranscodingUser * me = [self createLiveTranscodingUser:self.channelId.integerValue];
    self.transcoding.transcodingUsers = [self setupVideoFrame:me hideLocalUser:self.localVideoStreamPlaceholderOpen];
    int ret = [self.agoraKit setLiveTranscoding:self.transcoding];
    if (ret < 0) { PLVF_NORMAL_LOG_ERROR(@"LinkMic",@"PLVBLinkMicManager - setup transcoding configuration failed"); }
    return ret;
}

- (int)setupLiveTranscodingExtraInfo:(NSString *)extroInfo{
    int ret = -1;
    if ([PLVFdUtil checkStringUseable:extroInfo] && self.transcoding) {
        self.transcoding.transcodingExtraInfo = extroInfo;
        ret = [self.agoraKit setLiveTranscoding:self.transcoding];
    }
    return ret;
}

- (int)setupLiveBackgroundImage{
    if (self.localVideoStreamPlaceholderOpen) {
        AgoraImage * bgImg = [[AgoraImageCLS alloc]init];
        bgImg.url = [NSURL URLWithString:@"https://liveimages.videocc.net/uploadimage/20191120/chat_img_379012_15742451023697.png"];
        CGSize videoSize = [self getVideoSize];
        bgImg.rect = CGRectMake(0, 0, videoSize.width, videoSize.height);
        self.transcoding.backgroundImage = bgImg;
    }else{
        self.transcoding.backgroundImage = nil;
    }
    return [self setupLiveTranscodingUser];
}

#pragma mark Getter
- (BOOL)engineIsReady{
    return _agoraKit ? YES : NO;
}


#pragma mark - [ Father Private Methods ]
- (void)setupRTCToken:(NSDictionary *)decodeDict{
    self.rtcAppId = decodeDict[@"connect_appId"];
    self.rtcToken = decodeDict[@"connect_channel_key"];
    
    if (self.hadJoinedRTC) {
        [self.agoraKit renewToken:self.rtcToken];
    }
}


#pragma mark - [ Private Methods ]
- (AgoraLiveTranscodingUser *)createLiveTranscodingUser:(NSUInteger)uid{
    AgoraLiveTranscodingUser * user = [[AgoraLiveTranscodingUserCLS alloc] init];
    user.uid = uid;
    user.zOrder = 1;
    user.alpha = 1;
    user.audioChannel = 0;
    return user;
}

- (NSMutableArray *)setupVideoFrame:(AgoraLiveTranscodingUser *)localUser hideLocalUser:(BOOL)hideLocalUser{
    NSUInteger count = 0;
    NSMutableArray * users = [NSMutableArray arrayWithCapacity:1.0 + count];
    
    NSUInteger col = ceil(sqrt(1.0 + count));
    
    CGFloat colWidth = self.videoCDNWidth / col;
    CGFloat colHeight = colWidth * (self.videoCDNHeight * 1.0 / self.videoCDNWidth);
    NSUInteger maxRowIndex = count / col;
    // NSUInteger maxColIndex = count % col;
    CGFloat colY = (self.videoCDNHeight - colHeight * (1 + maxRowIndex)) * 0.5;
    localUser.rect = CGRectMake(0.0, colY, hideLocalUser ? 1 : colWidth, hideLocalUser ? 1 : colHeight);
    users[0] = localUser;
    return users;
}

- (CGSize)getVideoSize{
    NSUInteger count = 0;
    NSUInteger col = ceil(sqrt(1.0 + count));
    
    CGFloat colWidth = self.videoCDNWidth / col;
    CGFloat colHeight = colWidth * (self.videoCDNHeight * 1.0 / self.videoCDNWidth);
    return CGSizeMake(colWidth, colHeight);
}


#pragma mark - [ Delegate ]
#pragma mark AgoraRtcEngineDelegate
/// 状态相关
- (void)rtcEngine:(AgoraRtcEngineKit *)engine reportRtcStats:(AgoraChannelStats *)stats{
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvbLinkMicManager:rtcStatistics:)]) {
        
        PLVBRTCStatistics *statistics = [[PLVBRTCStatistics alloc] init];
        statistics.rtt = stats.lastmileDelay;
        statistics.upLoss = stats.txPacketLossRate;
        statistics.downLoss = stats.rxPacketLossRate;
        
        [self.delegate plvbLinkMicManager:self rtcStatistics:statistics];
    }
}

- (void)rtcEngine:(AgoraRtcEngineKit * _Nonnull)engine remoteVideoStateChangedOfUid:(NSUInteger)uid state:(AgoraVideoRemoteState)state reason:(AgoraVideoRemoteStateReason)reason elapsed:(NSInteger)elapsed{
    
    /*
    BOOL localUser = [self.userLinkMicId isEqualToString:@(uid).stringValue];
    if (localUser) {
        BOOL videoDidEnabled = ((state == AgoraVideoRemoteStateDecoding) && (reason == AgoraVideoRemoteStateReasonRemoteUnmuted));
        self.videoDidEnabled = videoDidEnabled;
        if (!videoDidEnabled) {
            self.localVideoEnable = NO;
        }
    }*/
}

- (void)rtcEngine:(AgoraRtcEngineKit *)engine remoteAudioStateChangedOfUid:(NSUInteger)uid state:(AgoraAudioRemoteState)state reason:(AgoraAudioRemoteStateReason)reason elapsed:(NSInteger)elapsed {
    if (state == AgoraAudioRemoteStateStopped) {
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didAudioMuted:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didAudioMuted:YES byUid:@(uid).stringValue];
        }
    } else if (state == AgoraAudioRemoteStateDecoding) {
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didAudioMuted:byUid:)]) {
            [self.delegate plvbLinkMicManager:self didAudioMuted:NO byUid:@(uid).stringValue];
        }
    }
}

- (void)rtcEngine:(AgoraRtcEngineKit *)engine didOccurWarning:(AgoraWarningCode)warningCode {
}

- (void)rtcEngine:(AgoraRtcEngineKit *)engine didOccurError:(AgoraErrorCode)errorCode {
    NSError * error = [super errorWithCode:errorCode errorDescription:nil];
    [super callbackForDidOccurError:error];

    if (errorCode != AgoraErrorCodeLeaveChannelRejected) {
        [self leaveRtcChannel];
    }
}

- (void)rtcEngineRequestToken:(AgoraRtcEngineKit *)engine{
    PLVF_NORMAL_LOG_INFO(@"BLinkMic", @"%@", NSStringFromSelector(_cmd));
    self.rtcTokenAvailable = NO;
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManagerTokenExpires:)]) {
        [self.delegate plvbLinkMicManagerTokenExpires:self];
    }
}

- (void)rtcEngine:(AgoraRtcEngineKit *)engine connectionChangedToState:(AgoraConnectionStateType)state reason:(AgoraConnectionChangedReason)reason{
    self.connectionState = (PLVBLinkMicConnectionStateType)state;
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:rtcConnectionStateDidChanged:)]) {
        [self.delegate plvbLinkMicManager:self rtcConnectionStateDidChanged:self.connectionState];
    }
    
    if (reason == AgoraConnectionChangedBannedByServer) {
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:leaveRTCChannelComplete:)]) {
            [self.delegate plvbLinkMicManager:self leaveRTCChannelByServerComplete:self.channelId];
        }
    }
}

- (void)rtcEngineConnectionDidInterrupted:(AgoraRtcEngineKit *)engine {
    PLVF_NORMAL_LOG_WARN(@"BLinkMic", @"%@", NSStringFromSelector(_cmd));
}

- (void)rtcEngineConnectionDidLost:(AgoraRtcEngineKit *)engine {
    PLVF_NORMAL_LOG_WARN(@"BLinkMic", @"%@", NSStringFromSelector(_cmd));
}

- (void)rtcEngineCameraDidReady:(AgoraRtcEngineKit *)engine {
    PLVF_NORMAL_LOG_INFO(@"BLinkMic", @"%@", NSStringFromSelector(_cmd));
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManagerCameraDidReady:)]) {
        [self.delegate plvbLinkMicManagerCameraDidReady:self];
    }
}

- (void)rtcEngine:(AgoraRtcEngineKit * _Nonnull)engine
reportAudioVolumeIndicationOfSpeakers:(NSArray<AgoraRtcAudioVolumeInfo *> * _Nonnull)speakers
      totalVolume:(NSInteger)totalVolume{
    if (speakers && speakers.count > 0) {
        NSMutableDictionary * volumeDict = [[NSMutableDictionary alloc] init];
        for (AgoraRtcAudioVolumeInfo * info in speakers) {
            NSString * userLinkMicId = info.uid == 0 ? self.userLinkMicId : [NSString stringWithFormat:@"%lu",(unsigned long)info.uid];
            float percent = [NSNumber numberWithUnsignedInteger:info.volume].floatValue / 255.0;
            NSNumber * volume = [NSNumber numberWithFloat:percent];
            [volumeDict setObject:volume forKey:userLinkMicId];
            if (info.uid == 0) {
                BOOL audible = percent >= 0.156 ? YES : NO;
                if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:localVoiceValue:receivedLocalAudibleVoice:)]) {
                    [self.delegate plvbLinkMicManager:self localVoiceValue:percent receivedLocalAudibleVoice:audible];
                }
                break;
            }
        }
        if ([PLVFdUtil checkDictionaryUseable:volumeDict]) {
            if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:reportAudioVolumeOfSpeakers:)]) {
                [self.delegate plvbLinkMicManager:self reportAudioVolumeOfSpeakers:volumeDict];
            }
        }
    }
}

- (void)rtcEngineVideoDidStop:(AgoraRtcEngineKit *)engine {
    PLVF_NORMAL_LOG_WARN(@"BLinkMic", @"%@", NSStringFromSelector(_cmd));
}

- (void)rtcEngine:(AgoraRtcEngineKit *)engine didClientRoleChanged:(AgoraClientRole)oldRole newRole:(AgoraClientRole)newRole{
    PLVF_NORMAL_LOG_INFO(@"BLinkMic", @"didClientRoleChanged %ld %ld",(long)oldRole,(long)newRole);
}

- (void)rtcEngine:(AgoraRtcEngineKit *_Nonnull)engine
   networkQuality:(NSUInteger)uid
        txQuality:(AgoraNetworkQuality)txQuality
        rxQuality:(AgoraNetworkQuality)rxQuality {
    //NSLog(@"POLYVTEST - 网络回调 %lu txQuality %lu rxQuality %lu/// %d %s",(unsigned long)uid,(unsigned long)txQuality,(unsigned long)rxQuality,__LINE__,__FUNCTION__);
    PLVBLinkMicNetworkQuality txNetworkQuality = [self transformNetworkQuality:txQuality];
    PLVBLinkMicNetworkQuality rxNetworkQuality = [self transformNetworkQuality:rxQuality];
    
    PLVBRTCNetworkQuality txNetwork = [self transformPLVNetworkQualityFromAgoraNetworkQuality:txQuality];
    PLVBRTCNetworkQuality rxNetwork = [self transformPLVNetworkQualityFromAgoraNetworkQuality:rxQuality];
    
    BOOL localUser = (uid == 0);
    if (localUser) {
        self.networkQuality = txNetworkQuality;
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:networkQualityDidChanged:)]) {
            [self.delegate plvbLinkMicManager:self networkQualityDidChanged:txNetworkQuality];
        }
        
        self.localNetworkQuality = txNetwork;
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(plvbLinkMicManager:localNetworkQualityDidChanged:)]) {
            [self.delegate plvbLinkMicManager:self localNetworkQualityDidChanged:txNetwork];
        }
    }
    
    NSString * userRTCId = localUser ? self.userLinkMicId : [NSString stringWithFormat:@"%ld",uid];
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:userNetworkQualityDidChanged:txQuality:rxQuality:)]) {
        [self.delegate plvbLinkMicManager:self userNetworkQualityDidChanged:userRTCId txQuality:txNetworkQuality rxQuality:rxNetworkQuality];
    }
    
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvbLinkMicManager:networkQualityChangedWithUserId:txQuality:rxQuality:)]) {
        [self.delegate plvbLinkMicManager:self networkQualityChangedWithUserId:userRTCId txQuality:txNetwork rxQuality:rxNetwork];
    }
}

- (PLVBLinkMicNetworkQuality)transformNetworkQuality:(AgoraNetworkQuality)quality{
    PLVBLinkMicNetworkQuality finalQuality = PLVBLinkMicNetworkQualityUnknown;
    switch (quality) {
        case AgoraNetworkQualityExcellent:
        case AgoraNetworkQualityGood:
            finalQuality = PLVBLinkMicNetworkQualityGood;
            break;
        case AgoraNetworkQualityPoor:
        case AgoraNetworkQualityBad:
            finalQuality = PLVBLinkMicNetworkQualityFine;
            break;
        case AgoraNetworkQualityVBad:
            finalQuality = PLVBLinkMicNetworkQualityBad;
            break;
        case AgoraNetworkQualityDown:
            finalQuality = PLVBLinkMicNetworkQualityDown;
            break;
        case AgoraNetworkQualityUnsupported:
            finalQuality = PLVBLinkMicNetworkQualityUnknown;
            break;
        case AgoraNetworkQualityDetecting:
            finalQuality = PLVBLinkMicNetworkQualityUnknown;
            break;
        default:
            break;
    }
    return finalQuality;
}

- (PLVBRTCNetworkQuality)transformPLVNetworkQualityFromAgoraNetworkQuality:(AgoraNetworkQuality)quality{
    PLVBRTCNetworkQuality finalQuality = PLVBRTCNetworkQuality_Unknown;
    switch (quality) {
        case AgoraNetworkQualityExcellent:
            finalQuality = PLVBRTCNetworkQuality_Excellent;
            break;
        case AgoraNetworkQualityGood:
            finalQuality = PLVBRTCNetworkQuality_Good;
            break;
        case AgoraNetworkQualityPoor:
            finalQuality = PLVBRTCNetworkQuality_Poor;
            break;
        case AgoraNetworkQualityBad:
            finalQuality = PLVBRTCNetworkQuality_Bad;
            break;
        case AgoraNetworkQualityVBad:
            finalQuality = PLVBRTCNetworkQuality_VBad;
            break;
        case AgoraNetworkQualityDown:
            finalQuality = PLVBRTCNetworkQuality_Down;
            break;
        case AgoraNetworkQualityUnsupported:
        case AgoraNetworkQualityDetecting:
            finalQuality = PLVBRTCNetworkQuality_Unknown;
            finalQuality = PLVBRTCNetworkQuality_Unknown;
            break;
        default:
            break;
    }
    return finalQuality;
}

/// 用户 加入/退出 相关
- (void)rtcEngine:(AgoraRtcEngineKit *)engine didJoinedOfUid:(NSUInteger)uid elapsed:(NSInteger)elapsed {
    //NSLog(@"%@", NSStringFromSelector(_cmd));
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didJoinedOfUid:)]) {
        [self.delegate plvbLinkMicManager:self didJoinedOfUid:@(uid).stringValue];
    }
}

- (void)rtcEngine:(AgoraRtcEngineKit *)engine
  didOfflineOfUid:(NSUInteger)uid
           reason:(AgoraUserOfflineReason)reason {
    //NSLog(@"%@", NSStringFromSelector(_cmd));
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didOfflineOfUid:)]) {
        [self.delegate plvbLinkMicManager:self didOfflineOfUid:@(uid).stringValue];
    }
}

/// 流状态相关
- (void)rtcEngine:(AgoraRtcEngineKit *)engine streamPublishedWithUrl:(NSString *)url errorCode:(AgoraErrorCode)errorCode{
    BOOL sucess = errorCode == AgoraErrorCodeNoError;
    if (sucess) {
        self.streamPublishing = YES;
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManager:streamPublishedResult:)]) {
        [self.delegate plvbLinkMicManager:self streamPublishedResult:sucess];
    }
}

- (void)rtcEngine:(AgoraRtcEngineKit * _Nonnull)engine
streamUnpublishedWithUrl:(NSString * _Nonnull)url {
    self.streamPublishing = NO;
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvbLinkMicManagerStreamUnpublished:)]) {
        [self.delegate plvbLinkMicManagerStreamUnpublished:self];
    }
}

- (void)rtcEngine:(AgoraRtcEngineKit *)engine
 didRejoinChannel:(NSString * _Nonnull)channelmanager
          withUid:(NSUInteger)uid
          elapsed:(NSInteger)elapsed {
    PLVF_NORMAL_LOG_INFO(@"BLinkMic", @"%@, uid: %lu", NSStringFromSelector(_cmd), (unsigned long)uid);
}

- (void)rtcEngine:(AgoraRtcEngineKit * _Nonnull)engine
    didAudioMuted:(BOOL)muted
            byUid:(NSUInteger)uid {
    //NSLog(@"%@", NSStringFromSelector(_cmd));
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didAudioMuted:byUid:)]) {
        [self.delegate plvbLinkMicManager:self didAudioMuted:muted byUid:@(uid).stringValue];
    }
}

- (void)rtcEngine:(AgoraRtcEngineKit * _Nonnull)engine
    didVideoMuted:(BOOL)muted
            byUid:(NSUInteger)uid {
    //NSLog(@"%@", NSStringFromSelector(_cmd));
    if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didVideoMuted:byUid:)]) {
        [self.delegate plvbLinkMicManager:self didVideoMuted:muted byUid:@(uid).stringValue];
    }
}


#pragma mark AgoraVideoDataFrameProtocol

// Function to create and populate a CVPixelBufferRef with BGRA data
CVPixelBufferRef createPixelBufferFromBGRAData(const uint8_t *bgraData, size_t width, size_t height) {
    CVPixelBufferRef pixelBuffer = NULL;
    NSDictionary *attributes = @{
        (NSString*)kCVPixelBufferCGImageCompatibilityKey : @YES,
        (NSString*)kCVPixelBufferCGBitmapContextCompatibilityKey : @YES
    };

    CVReturn result = CVPixelBufferCreate(
        kCFAllocatorDefault,
        width,
        height,
        kCVPixelFormatType_32BGRA,
        (__bridge CFDictionaryRef)attributes,
        &pixelBuffer
    );

    if (result != kCVReturnSuccess) {
        NSLog(@"Failed to create CVPixelBuffer: %d", result);
        return NULL;
    }

    CVPixelBufferLockBaseAddress(pixelBuffer, kCVPixelBufferLock_ReadOnly);

    uint8_t *pixelBufferBaseAddress = CVPixelBufferGetBaseAddress(pixelBuffer);

    size_t bytesPerRow = CVPixelBufferGetBytesPerRow(pixelBuffer);
    for (size_t row = 0; row < height; row++) {
        memcpy(pixelBufferBaseAddress + row * bytesPerRow, bgraData + row * width * 4, width * 4);
    }

    CVPixelBufferUnlockBaseAddress(pixelBuffer, kCVPixelBufferLock_ReadOnly);

    return pixelBuffer;
}

/// 摄像头采集数据回调
/// @param videoFrame 视频数据
-(BOOL)onCaptureVideoFrame:(AgoraVideoDataFrame *)videoFrame {
    
    /*
    if (self.isProcessVideoData){
        dispatch_semaphore_t sem1 = dispatch_semaphore_create(0);

        //从 AgoraVideoDataFrame 中得到 CVPixelBufferRef
        size_t uvBufferLength = (size_t)videoFrame.height * (size_t)videoFrame.uStride;
        char* uvBuffer = (char *)malloc(uvBufferLength);
        CVPixelBufferRef pixelBuffer = [self.imageUtils copyI420BufferToNv12:(size_t)videoFrame.width
                                                                       height:(size_t)videoFrame.height
                                                                        yData:videoFrame.yBuffer
                                                                        uData:videoFrame.uBuffer
                                                                        vData:videoFrame.vBuffer
                                                                      yStride:(size_t)videoFrame.yStride
                                                                      uStride:(size_t)videoFrame.uStride
                                                                      vStride:(size_t)videoFrame.vStride
                                                               uvBufferLength:uvBufferLength
                                                                     uvBuffer:uvBuffer];
        
        // 调整数据格式
        PLVPixelBufferInfo *pixelBufferInfo = [self.imageUtils getCVPixelBufferInfo:pixelBuffer];
        if (pixelBufferInfo.format != PLVFormatType_BGRA) {
            CVPixelBufferRef pixelBufferref = [self.imageUtils transforCVPixelBufferToCVPixelBuffer:pixelBuffer outputFormat:PLVFormatType_BGRA];
            CVPixelBufferRelease(pixelBuffer);
            pixelBuffer = pixelBufferref;
        }
        
        // BGRA 格式数据
        CVPixelBufferLockBaseAddress(pixelBuffer, kCVPixelBufferLock_ReadOnly);
        int width = (int)CVPixelBufferGetWidth(pixelBuffer);
        int height = (int)CVPixelBufferGetHeight(pixelBuffer);
        int stride = (int)CVPixelBufferGetBytesPerRow(pixelBuffer)/4;
        
        uint8_t* pixels = (uint8_t *)CVPixelBufferGetBaseAddress(pixelBuffer);
        
        // 同步数据处理 美颜数据回调
        __weak typeof(self) weakSelf = self;
        self.beautyManager.datacallback = ^(const uint8_t * _Nonnull pixel, int width, int height, int64_t ts) {
            // 处理后的 Texture 提取 CVPixelBufferRef，再还原到 AgoraVideoDataFrame 中

            CVPixelBufferRef outputPixelBuffer = createPixelBufferFromBGRAData(pixel, width, height);
            outputPixelBuffer = [self.imageUtils transforCVPixelBufferToCVPixelBuffer:outputPixelBuffer outputFormat:PLVFormatType_YUV420F];
//            
            [weakSelf.imageUtils changePixelBufferRef:outputPixelBuffer
                                      toYUVBuffer:(int)videoFrame.width
                                           height:(int)videoFrame.height
                                            yData:videoFrame.yBuffer
                                            uData:videoFrame.uBuffer
                                            vData:videoFrame.vBuffer
                                          yStride:(int)videoFrame.yStride
                                          uStride:(int)videoFrame.uStride
                                          vStride:(int)videoFrame.vStride];
            
            dispatch_semaphore_signal(sem1);
        };
        

        [self.beautyManager processBytes:width height:height pixels:pixels stride:stride ts:videoFrame.renderTimeMs];

        dispatch_semaphore_wait(sem1, DISPATCH_TIME_FOREVER);
        
        return YES;
    }
    
    return YES;
    
    */
    if (self.isProcessVideoData &&
        self.delegate &&
        [self.delegate respondsToSelector:@selector(plvbLinkMicManager:captureVideoFrameTextureId:videoFrameWidth:videoFrameHeight:videoFrameTimeStamp:processedVideoFrameTextureId:)]) {
        //从 AgoraVideoDataFrame 中得到 CVPixelBufferRef
        size_t uvBufferLength = (size_t)videoFrame.height * (size_t)videoFrame.uStride;
        char* uvBuffer = (char *)malloc(uvBufferLength);
        CVPixelBufferRef pixelBuffer = [self.imageUtils copyI420BufferToNv12:(size_t)videoFrame.width
                                                                       height:(size_t)videoFrame.height
                                                                        yData:videoFrame.yBuffer
                                                                        uData:videoFrame.uBuffer
                                                                        vData:videoFrame.vBuffer
                                                                      yStride:(size_t)videoFrame.yStride
                                                                      uStride:(size_t)videoFrame.uStride
                                                                      vStride:(size_t)videoFrame.vStride
                                                               uvBufferLength:uvBufferLength
                                                                     uvBuffer:uvBuffer];
        
        // 调整数据格式
        PLVPixelBufferInfo *pixelBufferInfo = [self.imageUtils getCVPixelBufferInfo:pixelBuffer];
        if (pixelBufferInfo.format != PLVFormatType_BGRA) {
            CVPixelBufferRef pixelBufferref = [self.imageUtils transforCVPixelBufferToCVPixelBuffer:pixelBuffer outputFormat:PLVFormatType_BGRA];
            CVPixelBufferRelease(pixelBuffer);
            pixelBuffer = pixelBufferref;
        }
        if ([EAGLContext currentContext] != glcontext) {
            [EAGLContext setCurrentContext:glcontext];
        }
        id<PLVGLTexture> texture = (id<PLVGLTexture>)[self.imageUtils transforCVPixelBufferToTexture:pixelBuffer];
        id<PLVGLTexture> outTexture = nil;
        outTexture = [self.imageUtils getOutputPixelBufferGLTextureWithWidth:texture.width
                                                                      height:texture.height
                                                                      format:PLVFormatType_BGRA];
        if (uvBuffer != NULL) {
            free(uvBuffer);
            uvBuffer = NULL;
        }
        // 回调给美颜sdk处理数据
        int ret = [self.delegate plvbLinkMicManager:self
                         captureVideoFrameTextureId:texture.texture
                                    videoFrameWidth:texture.width
                                   videoFrameHeight:texture.height
                                videoFrameTimeStamp:videoFrame.renderTimeMs
                       processedVideoFrameTextureId:outTexture.texture];
        if (ret != 0) {
            outTexture = texture;
        }
        
        // 处理后的 Texture 提取 CVPixelBufferRef，再还原到 AgoraVideoDataFrame 中
        CVPixelBufferRef outputPixelBuffer = [(PLVPixelBufferGLTexture *)outTexture pixelBuffer];
        outputPixelBuffer = [self.imageUtils transforCVPixelBufferToCVPixelBuffer:outputPixelBuffer outputFormat:PLVFormatType_YUV420F];
        [self.imageUtils changePixelBufferRef:outputPixelBuffer
                                  toYUVBuffer:(int)videoFrame.width
                                       height:(int)videoFrame.height
                                        yData:videoFrame.yBuffer
                                        uData:videoFrame.uBuffer
                                        vData:videoFrame.vBuffer
                                      yStride:(int)videoFrame.yStride
                                      uStride:(int)videoFrame.uStride
                                      vStride:(int)videoFrame.vStride];
    }
    return YES;
}

-(AgoraVideoFramePosition)getObservedFramePosition {
    return AgoraVideoFramePositionPostCapture;
}

-(AgoraVideoFrameType)getVideoFormatPreference {
    return AgoraVideoFrameTypeYUV420;
}

-(BOOL)getRotationApplied {
    return NO;
}

-(BOOL)getMirrorApplied {
    return NO;
}

- (BOOL)isMultipleChannelFrameWanted {
    return NO;
}


- (BOOL)onPreEncodeVideoFrame:(AgoraVideoDataFrame *)videoFrame {
    return YES;
}


- (BOOL)onRenderVideoFrame:(AgoraVideoDataFrame *)videoFrame forUid:(unsigned int)uid {
    return YES;
}


- (BOOL)onRenderVideoFrameEx:(AgoraVideoDataFrame *)videoFrame forUid:(unsigned int)uid inChannel:(NSString *)channelId {
    return YES;
}


#endif
@end
