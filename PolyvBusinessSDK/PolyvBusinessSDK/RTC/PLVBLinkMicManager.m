//
//  PLVBLinkMicManager.m
//  PLVBusinessSDK
//
//  Created by <PERSON><PERSON> on 2020/3/20.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVBLinkMicManager.h"
#import "PLVBLinkMicManager+PrivateExtension.h"
#import <PLVFoundationSDK/PLVFConsoleLogger.h>

@implementation PLVBLinkMicManager

#pragma mark - [ Life Period ]
- (void)dealloc{
    PLVF_NORMAL_LOG_DEBUG(@"BLinkMic", @"%s", __FUNCTION__);
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _localCameraFront = YES;
        _localNoiseCancellationLevel = PLVBLinkMicNoiseCancellationLevelAggressive;
    }
    return self;
}

#pragma mark - [ Public Methods ]
+ (instancetype)linkMicManagerWithRTCType:(NSString *)rtcType{
    PLVBLinkMicManager *manager;
    if ([rtcType isEqualToString:@"agora"]) {
        if (NSClassFromString(@"AgoraRtcEngineKit")) {
            manager = [[NSClassFromString(@"PLVBLinkMicAgoraManager") alloc]init];
            manager.rtcType = PLVBLinkMicRTCType_AG;
        }else{
            PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - manager create failed ，need AgoraRtcEngineKit SDK");
        }
    }else if ([rtcType isEqualToString:@"urtc"]){
        if (NSClassFromString(@"UCloudRtcEngine")) {
            manager = [[NSClassFromString(@"PLVBLinkMicUCloudManager") alloc]init];
            manager.rtcType = PLVBLinkMicRTCType_UC;
        }else{
            PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - manager create failed ，need UCloudRtcEngine SDK");
        }
    }else if ([rtcType isEqualToString:@"trtc"]){
        if (NSClassFromString(@"TRTCCloud")) {
            manager = [[NSClassFromString(@"PLVBLinkMicTRTCManager") alloc]init];
            manager.rtcType = PLVBLinkMicRTCType_TX;
        }else{
            PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - manager create failed ，need TRTCCloud SDK");
        }
    }else if ([rtcType isEqualToString:@"volc"]){
        if (NSClassFromString(@"ByteRTCEngineKit")) {
            manager = [[NSClassFromString(@"PLVBLinkMicVOLCManager") alloc]init];
            manager.rtcType = PLVBLinkMicRTCType_BD;
        }else{
            PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - manager create failed ，need VolcEngineRTC SDK");
        }
    }else{
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - linkMicManager create failed, rtcType invalid %@",rtcType);
    }
    
    return manager;
}

- (void)setupAppGroup:(NSString *)appGroup rtcType:(NSString *)rtcType {
    if (![PLVFdUtil checkStringUseable:appGroup] ||
        ![PLVFdUtil checkStringUseable:rtcType]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - %s failed with【params invalid】(appGroup:%@, rtcType:%@)", __FUNCTION__, appGroup, rtcType);
        return;
    }

    self.appGroup = appGroup;
    NSUserDefaults *sharedUserDefaults = [[NSUserDefaults alloc] initWithSuiteName:appGroup];
    [sharedUserDefaults setObject:rtcType forKey:@"PLVUserDefaultAppGroupRTCTypeKey"];
    [sharedUserDefaults synchronize];
}

- (int)updateLinkMicTokenWithStr:(NSString *)linkMicStr{
    if (![PLVFdUtil checkStringUseable:linkMicStr]) {
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - linkmictoken decode failed, linkmicstr invalid %@",linkMicStr);
        return -1;
    }
    NSDictionary *decodeDict = [self linkMicDecodeDictWithStr:linkMicStr];
    [self setupRTCToken:decodeDict];
    
    if (![PLVFdUtil checkStringUseable:self.rtcAppId]) {
        self.rtcTokenAvailable = NO;
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - linkmictoken decode failed, rtc AppId invalid %@",self.rtcAppId);
        return -1;
    }else{
        self.rtcTokenAvailable = YES;
        return 0;
    }
}

/// 创建 RTC引擎
- (void)createRTCEngine{
    // 由子类覆写实现
}

/// 销毁 RTC引擎
- (void)destroyRTCEngine{
    // 由子类覆写实现
}

- (void)unsubscribeStreamWithRTCUserId:(NSString *)rtcUserId{
    /// 若子类支持此方法，则由子类重写
}

- (void)unsubscribeStreamWithRTCUserId:(NSString *)rtcUserId subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode {
    /// 若子类支持此方法，则由子类重写
}

- (void)switchSubscribeStreamMediaTypeWithRTCUserId:(NSString *)rtcUserId mediaType:(PLVBRTCSubscribeStreamMediaType)toMediaType{
    /// 仅urtc支持此方法，并由子类重写
}

- (void)switchPublishStreamSourceType:(PLVBRTCStreamSourceType)streamSourceType{
    // 由子类覆写实现
}

- (void)muteSubscribedRemoteStreamInLocalWithMute:(BOOL)mute{
    /// 仅urtc支持此方法，并由子类重写
}

- (void)setupRTCToken:(NSDictionary *)decodeDict{
    // 由子类覆写实现
}

- (int)joinRtcChannelWithChannelId:(NSString *)channelId
                     userLinkMicId:(NSString *)userLinkMicId{
    return -1; // 由子类覆写实现
}

- (int)leaveRtcChannel{
    return -1; // 由子类覆写实现
}

- (void)setTeacherUserId:(NSString *)teacherId {
    // 由子类覆写实现
}

- (int)setUserRoleTo:(PLVBLinkMicRoleType)roleType{
    return -1; // 由子类覆写实现
}

#pragma mark 本地 摄像头 使用
- (void)setupLocalPreviewWithCanvasModel:(PLVBRTCVideoViewCanvasModel *)canvasModel{
    PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - setupLocalPreviewWithCanvasModel failed");
}

- (int)setupLocalVideoPreviewMirrorMode:(PLVBRTCVideoMirrorMode)mirrorMode{
    return -1; // 由子类覆写实现
}

- (int)switchLocalUserCamera:(BOOL)frontCamera{
    return -1; // 由子类覆写实现
}

- (int)openLocalUserCameraTorch:(BOOL)openCameraTorch{
    return -1; // 由子类覆写实现
}

- (int)openLocalUserCamera:(BOOL)openCamera{
    return -1; // 由子类覆写实现
}

- (int)localVideoStreamOpenPlaceholder:(BOOL)openPlaceholder{
    return -1; // 由子类覆写实现
}

- (int)enableLocalVideo:(BOOL)enabled{
    return -1; // 由子类覆写实现
}

- (int)startLocalPreview:(BOOL)start{
    return -1; // 由子类覆写实现
}

- (int)muteLocalVideoStream:(BOOL)mute{
    return -1; // 由子类覆写实现
}

- (void)enableLocalVideoFrameProcess:(BOOL)enabled {
    // 由子类覆写实现
}

- (void)setStickerImage:(UIImage *)stickerImage{
    // 由子类覆写实现
}

- (void)setAIMattingMode:(PLVBLinkMicAIMattingMode)mode image:(UIImage *)image{
    // 由子类覆写实现

}

#pragma mark 本地 麦克风 使用
- (int)changeLocalMicVolume:(CGFloat)micVolume{
    return -1; // 由子类覆写实现
}

- (int)openLocalUserMic:(BOOL)openMic{
    return -1; // 由子类覆写实现
}

- (int)enableLocalAudio:(BOOL)enabled{
    return -1; // 由子类覆写实现
}

- (int)muteLocalAudioStream:(BOOL)mute{
    return -1; // 由子类覆写实现
}

- (int)switchNoiseCancellationLevelTo:(PLVBLinkMicNoiseCancellationLevel)level {
    return -1; // 由子类覆写实现
}

- (int)enableExternalDevice:(BOOL)enabled {
    return -1; // 由子类覆写实现
}

- (void)setupVideoEncoderConfiguration:(PLVBRTCVideoEncoderConfiguration *)videoEncoderConfiguration {
    self.currentVideoEncoderConfiguration = videoEncoderConfiguration;
    
    CGFloat resolutionWidth = videoEncoderConfiguration.videoResolution.width;
    CGFloat resolutionHeight = videoEncoderConfiguration.videoResolution.height;
    self.videoCDNWidth = resolutionWidth;
    self.videoCDNHeight = resolutionHeight;
    if (videoEncoderConfiguration.videoOutputOrientationMode == PLVBRTCVideoOutputOrientationMode_Portrait) {
        self.videoCDNWidth = resolutionHeight;
        self.videoCDNHeight = resolutionWidth;
    }
    // 由子类覆写实现
}

- (int)addPublishStreamUrl:(NSString *)streamUrl transcodingEnabled:(BOOL)transcodingEnabled{
    return -1; // 由子类覆写实现
}

- (int)removePublishStreamUrl:(NSString *)streamUrl{
    return -1; // 由子类覆写实现
}

- (void)setupLiveTranscoding{
    // 由子类覆写实现
}

- (int)setupLiveTranscodingUser{
    return -1; // 由子类覆写实现
}

- (int)setupLiveTranscodingExtraInfo:(NSString *)extroInfo{
    return -1; // 由子类覆写实现
}

- (int)setupLiveBackgroundImage{
    return -1; // 由子类覆写实现
}


#pragma mark - [ Private Methods ]
- (NSDictionary *)linkMicDecodeDictWithStr:(NSString *)string {
    NSData *encryptedData = [PLVDataUtil dataForHexString:string];
    NSData *base64Data = [PLVDataUtil AES128DecryptData:encryptedData withKey:[PLVKeyUtil getLinkMicKey] iv:[PLVKeyUtil getLinkMicIv]];
    if (base64Data) {
        NSData *decryptedData = [[NSData alloc] initWithBase64EncodedData:base64Data options:NSDataBase64DecodingIgnoreUnknownCharacters];
        if (decryptedData) {
            NSError *parseErr = nil;
            NSDictionary *jsonDict = [NSJSONSerialization JSONObjectWithData:decryptedData options:NSJSONReadingMutableContainers error:&parseErr];
            if (parseErr) {
                PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - parseErr:%@",parseErr.localizedDescription);
            }
            return jsonDict;
        }else{
            PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - token, decrypt failed-2");
            return nil;
        }
    }else{
        PLVF_NORMAL_LOG_ERROR(@"BLinkMic", @"PLVBLinkMicManager - token, decrypt failed-1");
        return nil;
    }
}


#pragma mark - [ For Subclass ]
- (NSError *)errorWithCode:(NSInteger)code errorDescription:(NSString *)errorDes{
    return PLVErrorCreate(@"net.plv.PLVBRTCManager", code, errorDes);
}


#pragma mark - [ For Subclass Callback ]
- (void)callbackForCanSetupLocalHardwareDefaultState{
    plv_dispatch_main_async_safe(^{
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManagerCanSetupLocalHardwareDefaultState:)]) {
            [self.delegate plvbLinkMicManagerCanSetupLocalHardwareDefaultState:self];
        }
    })
}

- (void)callbackForDidOccurError:(NSError *)error{
    plv_dispatch_main_async_safe(^{
        if ([self.delegate respondsToSelector:@selector(plvbLinkMicManager:didOccurError:)]) {
            [self.delegate plvbLinkMicManager:self didOccurError:error];
        }
    })
}

@end
