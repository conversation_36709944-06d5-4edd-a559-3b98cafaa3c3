//
//  PLVBRTCVideoEncoderConfiguration.m
//  PLVBusinessSDK
//
//  Created by Lincal on 2021/6/8.
//  Copyright © 2021 PLV. All rights reserved.
//

#import "PLVBRTCVideoEncoderConfiguration.h"

@implementation PLVBRTCVideoEncoderConfiguration

- (instancetype)init{
    if (self = [super init]) {
        _videoResolution = PLVBRTCVideoResolution320x180;
        _videoBitrate = 280;
        _videoFrameRate = 15;
        _videoOutputOrientationMode = PLVBRTCVideoOutputOrientationMode_Landscape;
        _videoMirrorMode = PLVBRTCVideoMirrorMode_Disabled;
    }
    return self;
}

@end
