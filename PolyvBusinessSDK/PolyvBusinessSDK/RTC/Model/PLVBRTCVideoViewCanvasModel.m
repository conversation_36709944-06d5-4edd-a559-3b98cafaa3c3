//
//  PLVBRTCVideoViewCanvasModel.m
//  PLVBusinessSDK
//
//  Created by <PERSON><PERSON> on 2021/4/13.
//  Copyright © 2021 PLV. All rights reserved.
//

#import "PLVBRTCVideoViewCanvasModel.h"

#import <PLVFoundationSDK/PLVFoundationSDK.h>

@implementation PLVBRTCVideoViewCanvasModel

- (BOOL)checkModelValid{
    BOOL userRTCIdValid = [PLVFdUtil checkStringUseable:self.userRTCId];
    BOOL renderCanvasViewValid = (self.renderCanvasView && [self.renderCanvasView isKindOfClass:UIView.class]);
    return userRTCIdValid && renderCanvasViewValid;
}

@end
