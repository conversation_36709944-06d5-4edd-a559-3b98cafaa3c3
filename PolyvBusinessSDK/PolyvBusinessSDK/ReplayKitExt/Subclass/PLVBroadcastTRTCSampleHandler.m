//
//  PLVBroadcastTRTCSampleHandler.m
//  PLVScreenShareExtension
//
//  Created by <PERSON><PERSON><PERSON> on 2022/2/10.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBroadcastTRTCSampleHandler.h"

#if __has_include(<TXLiteAVSDK_ReplayKitExt/TXLiteAVSDK_ReplayKitExt.h>)
    #import <TXLiteAVSDK_ReplayKitExt/TXLiteAVSDK_ReplayKitExt.h>
    #define TXLiteAVSDK_ReplayKitExt
#endif

#define TXReplayKitExtCLS NSClassFromString(@"TXReplayKitExt")

@interface PLVBroadcastTRTCSampleHandler ()
#ifdef TXLiteAVSDK_ReplayKitExt
<TXReplayKitExtDelegate>

#endif
@end

@implementation PLVBroadcastTRTCSampleHandler
#ifdef TXLiteAVSDK_ReplayKitExt

#pragma mark - [ Override ]

- (void)broadcastStarted {
    [[TXReplayKitExtCLS sharedInstance] setupWithAppGroup:self.appGroup delegate:self];
}

- (void)broadcastPaused {

}

- (void)broadcastResumed {

}

- (void)broadcastFinished {
    [[TXReplayKitExtCLS sharedInstance] broadcastFinished];
}

- (void)sendSampleBuffer:(CMSampleBufferRef)sampleBuffer withType:(RPSampleBufferType)sampleBufferType {
    switch (sampleBufferType) {
        case RPSampleBufferTypeVideo:
            [[TXReplayKitExtCLS sharedInstance] sendSampleBuffer:sampleBuffer withType:sampleBufferType];
            break;
        case RPSampleBufferTypeAudioApp:
            // Handle audio sample buffer for app audio
            [[TXReplayKitExtCLS sharedInstance] sendSampleBuffer:sampleBuffer withType:sampleBufferType];
            break;
        case RPSampleBufferTypeAudioMic:
            break;
        default:
            break;
    }
}

#pragma mark TXReplayKitExtDelegate

- (void)broadcastFinished:(TXReplayKitExt *)broadcast reason:(TXReplayKitExtReason)reason  API_AVAILABLE(ios(11.0)) {
    PLVBroadcastSampleHandlerReason extReason = PLVBroadcastSampleHandlerReasonRequestedByMain;
    switch (reason) {
        case TXReplayKitExtReasonRequestedByMain:
            extReason = PLVBroadcastSampleHandlerReasonRequestedByMain;
            break;
        case TXReplayKitExtReasonDisconnected:
            extReason = PLVBroadcastSampleHandlerReasonDisconnected;
            break;
        case TXReplayKitExtReasonVersionMismatch:
            extReason = PLVBroadcastSampleHandlerReasonVersionMismatch;
            break;
            
        default:
            break;
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvBroadcastSampleHandler:broadcastFinished:)]) {
        [self.delegate plvBroadcastSampleHandler:self broadcastFinished:extReason];
    }
}

#endif
@end
