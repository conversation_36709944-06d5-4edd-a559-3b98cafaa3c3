//
//  PLVBroadcastVOLCSampleHandler.m
//  PLVBusinessSDK
//
//  Created by junotang on 2022/7/12.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBroadcastVOLCSampleHandler.h"

#if __has_include(<VolcEngineRTCScreenCapturer/ByteRtcScreenCapturerExt.h>)
    #import <VolcEngineRTCScreenCapturer/ByteRtcScreenCapturerExt.h>
    #define VOLCScreenCapturerExt
#endif

#define VOLCScreenCapturerExtCLS NSClassFromString(@"ByteRtcScreenCapturerExt")

@interface PLVBroadcastVOLCSampleHandler()
#ifdef VOLCScreenCapturerExt
<ByteRtcScreenCapturerExtDelegate>

@property (nonatomic, strong) ByteRtcScreenCapturerExt *capturerExt;

#endif
@end

@implementation PLVBroadcastVOLCSampleHandler
#ifdef VOLCScreenCapturerExt
- (ByteRtcScreenCapturerExt *)capturerExt {
    if (!_capturerExt) {
        _capturerExt = [[VOLCScreenCapturerExtCLS alloc]init];
    }
    return _capturerExt;
}

#pragma mark - [ Override ]

- (void)broadcastStarted {
    [self.capturerExt startWithDelegate:self groupId:self.appGroup];
}

- (void)broadcastPaused {
    // volc 暂不支持
}

- (void)broadcastResumed {
    // volc 暂不支持
}

- (void)broadcastFinished {
    [self.capturerExt stop];
}

- (void)sendSampleBuffer:(CMSampleBufferRef)sampleBuffer withType:(RPSampleBufferType)sampleBufferType {
    switch (sampleBufferType) {
        case RPSampleBufferTypeVideo:
            [self.capturerExt processSampleBuffer:sampleBuffer withType:sampleBufferType];
            break;
        case RPSampleBufferTypeAudioApp:
            // Handle audio sample buffer for app audio
            break;
        case RPSampleBufferTypeAudioMic:
            // Handle audio sample buffer for mic audio
            break;
            
        default:
            break;
    }
}

#pragma mark ByteRtcScreenCapturerExtDelegate
- (void)onQuitFromApp {
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvBroadcastSampleHandler:broadcastFinished:)]) {
        [self.delegate plvBroadcastSampleHandler:self broadcastFinished:PLVBroadcastSampleHandlerReasonRequestedByMain];
    }
}

- (void)onSocketDisconnect {
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvBroadcastSampleHandler:broadcastFinished:)]) {
        [self.delegate plvBroadcastSampleHandler:self broadcastFinished:PLVBroadcastSampleHandlerReasonDisconnected];
    }
}

- (void)onSocketConnect {
}

- (void)onNotifyAppRunning {
}

- (void)onReceiveMessageFromApp:(NSData *)message {
}
#endif
@end
