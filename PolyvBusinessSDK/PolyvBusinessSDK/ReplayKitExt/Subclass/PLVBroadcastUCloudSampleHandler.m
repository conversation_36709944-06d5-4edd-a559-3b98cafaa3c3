//
//  PLVBroadcastUCloudSampleHandler.m
//  PLVScreenShareExtension
//
//  Created by <PERSON><PERSON><PERSON> on 2022/2/9.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBroadcastUCloudSampleHandler.h"

#if __has_include(<URTCReplayKitExtControl/URTCReplayKitExtControl.h>)
    #import <URTCReplayKitExtControl/URTCReplayKitExtControl.h>
    #define URTCReplayKitExtControl
#endif

#define URTCReplayKitExtControlCLS NSClassFromString(@"URTCReplayKitExtControl")

@interface PLVBroadcastUCloudSampleHandler ()
#ifdef URTCReplayKitExtControl
<URTCReplayKitExtendsionControlDelegate>

#endif
@end

@implementation PLVBroadcastUCloudSampleHandler
#ifdef URTCReplayKitExtControl

#pragma mark - [ Override ]

- (void)broadcastStarted {
    [[URTCReplayKitExtControlCLS shared] setAppGroup:self.appGroup delegate:self];
}

- (void)broadcastPaused {
    [[URTCReplayKitExtControlCLS shared] broadcastPaused];
}

- (void)broadcastResumed {
    [[URTCReplayKitExtControlCLS shared] broadcastResumed];
}

- (void)broadcastFinished {
    [[URTCReplayKitExtControlCLS shared] broadcastFinished];
}

- (void)sendSampleBuffer:(CMSampleBufferRef)sampleBuffer withType:(RPSampleBufferType)sampleBufferType {
    switch (sampleBufferType) {
        case RPSampleBufferTypeVideo:
            [[URTCReplayKitExtControlCLS shared] sendVideoSampleBuffer:sampleBuffer];
            break;
        case RPSampleBufferTypeAudioApp:
            // Handle audio sample buffer for app audio
            break;
        case RPSampleBufferTypeAudioMic:
            // Handle audio sample buffer for mic audio
            break;
            
        default:
            break;
    }
}

#pragma mark URTCReplayKitExtendsionControlDelegate

- (void)urtReplayKitExtControlDidFinishBroadcast:(nonnull PLVBroadcastUCloudSampleHandler *)extension {
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvBroadcastSampleHandler:broadcastFinished:)]) {
        [self.delegate plvBroadcastSampleHandler:self broadcastFinished:PLVBroadcastSampleHandlerReasonRequestedByMain];
    }
}

#endif
@end
