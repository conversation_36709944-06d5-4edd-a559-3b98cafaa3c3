//
//  PLVBroadcastAgoraSampleHandler.m
//  PLVScreenShareExtension
//
//  Created by <PERSON><PERSON><PERSON> on 2022/2/10.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBroadcastAgoraSampleHandler.h"

@interface PLVBroadcastAgoraSampleHandler ()

@end

@implementation PLVBroadcastAgoraSampleHandler

#pragma mark - [ Override ]

- (void)broadcastStarted {

}

- (void)broadcastPaused {

}

- (void)broadcastResumed {

}

- (void)broadcastFinished {
   
}

- (void)sendSampleBuffer:(CMSampleBufferRef)sampleBuffer withType:(RPSampleBufferType)sampleBufferType  API_AVAILABLE(ios(10.0)) {

}

#pragma mark - [ Private Method ]

#pragma mark Getter

- (void)sendVideoBuffer:(CMSampleBufferRef)sampleBuffer {
  
}

- (void)processSampleTimerAction:(NSTimer *)timer {
  
}

@end
