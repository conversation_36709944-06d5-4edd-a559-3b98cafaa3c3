//
//  PLVBroadcastSampleHandler.m
//  PLVScreenShareExtension
//
//  Created by <PERSON><PERSON><PERSON> on 2022/2/9.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBroadcastSampleHandler.h"
#import "PLVBroadcastUCloudSampleHandler.h"
#import "PLVBroadcastTRTCSampleHandler.h"
#import "PLVBroadcastAgoraSampleHandler.h"
#import "PLVBroadcastVOLCSampleHandler.h"

@interface PLVBroadcastSampleHandler ()
 
@property (nonatomic, copy) NSString *appGroup;

@end

@implementation PLVBroadcastSampleHandler

+ (instancetype)broadcastSampleHandlerWithAppGroup:(NSString *)appGroup {
    BOOL isValidAppGroup = appGroup && [appGroup isKindOfClass:NSString.class] && appGroup.length > 0;
    if (!isValidAppGroup) {
        NSLog(@"PLVBroadcastSampleHandler - sampleHandler create failed，AppGroup is invalid");
        return nil;
    }
    
    PLVBroadcastSampleHandler *sampleHandler;
    NSUserDefaults *sharedDefaults = [[NSUserDefaults alloc] initWithSuiteName:appGroup];
    NSString *rtcType = [sharedDefaults objectForKey:@"PLVUserDefaultAppGroupRTCTypeKey"];
    if ([rtcType isEqualToString:@"agora"]) {
        NSLog(@"PLVBroadcastSampleHandler - sampleHandler create failed ，not supported AgoraRtcEngineKit SDK");
    } else if ([rtcType isEqualToString:@"urtc"]){
        if (NSClassFromString(@"URTCReplayKitExtControl")) {
            sampleHandler = [[PLVBroadcastUCloudSampleHandler alloc] init];
        }else{
            NSLog(@"PLVBroadcastSampleHandler - sampleHandler create failed ，need URTCReplayKitExtControl SDK");
        }
    } else if ([rtcType isEqualToString:@"trtc"]){
        if (NSClassFromString(@"TXReplayKitExt")) {
            sampleHandler = [[PLVBroadcastTRTCSampleHandler alloc] init];
        }else{
            NSLog(@"PLVBroadcastSampleHandler - sampleHandler create failed ，need TXReplayKitExt SDK");
        }
    } else if ([rtcType isEqualToString:@"volc"]){
        if (NSClassFromString(@"ByteRtcScreenCapturerExt")) {
            sampleHandler = [[PLVBroadcastVOLCSampleHandler alloc] init];
        }else{
            NSLog(@"PLVBroadcastSampleHandler - sampleHandler create failed ，need URTCReplayKitExtControl SDK");
        }
    } else {
        NSLog(@"PLVBroadcastSampleHandler - sampleHandler create failed, rtcType invalid %@",rtcType);
    }
    if (sampleHandler) {
        sampleHandler.appGroup = appGroup;
    }
    return sampleHandler;
}

- (void)broadcastStarted {
    
}

- (void)broadcastPaused {
    
}

- (void)broadcastResumed {
    
}

- (void)broadcastFinished {
    
}

- (void)sendSampleBuffer:(CMSampleBufferRef)sampleBuffer withType:(RPSampleBufferType)sampleBufferType {

}

@end
