//
//  PLVBSocketClient.m
//  PLVBusinessSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/10.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVBSocketClient.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

@import PLVSocketIOClientSwift;

@interface PLVBSocketClient ()

#pragma mark 外部只读属性
@property (nonatomic, strong) PLVBSocketClientConfiguration *configuration;
@property (nonatomic, assign) PLVBSocketConnectStatus status;
@property (nonatomic, assign) BOOL login;

#pragma mark 外部不可见属性
@property (nonatomic, strong) NSString *roomId; /// 房间号ID，分房间功能关闭时，房间号为频道号
@property (nonatomic, strong) PLVSocketManager *socketManager; /// 第三方socket管理器
@property (nonatomic, strong) PLVSocketIOClient *socketIO; /// 第三方socket管理器对应的业务终端

@end

@implementation PLVBSocketClient

#pragma mark - 初始化

+ (instancetype)clientWithConfiguration:(PLVBSocketClientConfiguration *)configuration {
    if (!configuration || !configuration.account || !configuration.user) {
        return nil;
    }
    
    return [[self alloc] initWithConfiguration:configuration];
}

- (instancetype)initWithConfiguration:(PLVBSocketClientConfiguration *)configuration {
    self = [super init];
    if (self) {
        _configuration = configuration;
    }
    return self;
}

#pragma mark - Getter

- (NSString *)socketId {
    return [PLVSocketObjcBridge clientSidWithClient:self.socketIO];
}

#pragma mark - 登录

- (BOOL)loginWithRoomId:(NSString *)roomId token:(NSString *)token {
    if (!self.configuration || !self.configuration.user) {
        return NO;
    }
    
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0 ||
        !token || ![token isKindOfClass:[NSString class]] || token.length == 0) {
        return NO;
    }
    
    self.roomId = roomId;
    return [self connectWithToken:token];
}

/// 根据 token 进行 socket 连接
- (BOOL)connectWithToken:(NSString *)token {
    if (self.socketIO) {
        [self disconnect];
    }
    
    if (!token || ![token isKindOfClass:[NSString class]] || token.length == 0) {
        return NO;
    }
    
    NSURL *url = [NSURL URLWithString:self.configuration.domain];
    NSString *userAgent = [PLVFUserAgentBuilder sharedBuilder].userAgent ?: @"";
    NSDictionary *configDict = @{
        @"forceWebsockets":@YES,
        @"compress":@YES,
        @"log":@YES,
        @"connectParams":@{
                @"token" : token,
                @"version" : @"2"
        },
        @"version" : @(2),
        @"extraHeaders":@{
                @"User-Agent" : userAgent
        },
    };
    self.socketManager = [PLVSocketObjcBridge managerCreateWithUrl:url config:configDict];
    self.socketIO = [PLVSocketObjcBridge managerDefaultSocketWithManager:self.socketManager];
    [self observeSocketEvent];
    [self connect];
    return YES;
}

/// 对socket的各种事件进行监听，包括PLVBSocketClientConfiguration里配置的事件
- (void)observeSocketEvent {
    __weak typeof(self)weakSelf = self;
    [self observeSocketEvent:@"connect" callback:^(NSArray * _Nonnull data, PLVSocketAckEmitter * _Nonnull ack) {
        [weakSelf updateConnectStatus:PLVBSocketConnectStatusConnected];
        [weakSelf emitLoginEvent];
    }];
    [self observeSocketEvent:@"disconnect" callback:^(NSArray * _Nonnull data, PLVSocketAckEmitter * _Nonnull ack) {
        [weakSelf updateConnectStatus:PLVBSocketConnectStatusDisconnect];
    }];
    [self observeSocketEvent:@"error" callback:^(NSArray * _Nonnull data, PLVSocketAckEmitter * _Nonnull ack) {
          [weakSelf updateConnectStatus:PLVBSocketConnectStatusError];
    }];
    [self observeSocketEvent:@"reconnect" callback:^(NSArray * _Nonnull data, PLVSocketAckEmitter * _Nonnull ack) {
          [weakSelf updateConnectStatus:PLVBSocketConnectStatusReconnect];
    }];
    [self observeSocketEvent:@"message" callback:^(NSArray * _Nonnull data, PLVSocketAckEmitter * _Nonnull ack) {
        [weakSelf didReceiveMessageWithCallbackData:data];
    }];
    [self observeSocketEvent:@"roomPushStatus" callback:^(NSArray * _Nonnull data, PLVSocketAckEmitter * _Nonnull ack) {
        [weakSelf ddidReceiveRoomPushStatusWithCallbackData:data];
    }];
    for (NSString *event in self.configuration.listeningEvents) {
        if (!event || ![event isKindOfClass:[NSString class]] || event.length == 0) {
            continue;
        }
        if ([event isEqualToString:@"connect"] ||
            [event isEqualToString:@"disconnect"] ||
            [event isEqualToString:@"error"] ||
            [event isEqualToString:@"reconnect"] ||
            [event isEqualToString:@"message"]) {
            continue;
        }
        [self observeSocketEvent:event callback:^(NSArray * _Nonnull data, PLVSocketAckEmitter * _Nonnull ack) {
            [weakSelf didReceiveEvent:event callbackData:data];
        }];
    }
}

/// 动态添加socket的事件监听，每个事件只能添加一次
- (void)addObserveSocketEvent:(NSString *)event {
    if (!event || ![event isKindOfClass:[NSString class]] || event.length == 0) {
        return;
    }
    __weak typeof(self)weakSelf = self;
    [self observeSocketEvent:event callback:^(NSArray * _Nonnull data, PLVSocketAckEmitter * _Nonnull ack) {
        [weakSelf didReceiveEvent:event callbackData:data];
    }];
}

#pragma mark - Private
- (void)connect {
    [PLVSocketObjcBridge clientConnectWithClient:self.socketIO];
}

- (void)reconnect {
    [PLVSocketObjcBridge clientReconnectWithClient:self.socketIO];
}

- (void)disconnect {
    [PLVSocketObjcBridge clientDisconnectWithClient:self.socketIO];
}

- (void)removeAllHandlers {
    [PLVSocketObjcBridge clientRemoveAllHandlersWithClient:self.socketIO];
}

/// Socket 监听方法封装
- (void)observeSocketEvent:(NSString *)event callback:(void (^)(NSArray * _Nonnull data, PLVSocketAckEmitter * _Nonnull ack))callback {
    if (!self.socketIO) {
        return;
    }
    if (!event || ![event isKindOfClass:[NSString class]] || event.length == 0) {
        return;
    }
    [PLVSocketObjcBridge clientObserveWithClient:self.socketIO event:event callback:callback];
}

/// 连接成功之后发送login消息进行登录
- (void)emitLoginEvent {
    NSMutableDictionary *loginDict = [NSMutableDictionary dictionary];
    plv_dict_set(loginDict, @"EVENT", @"LOGIN");
    plv_dict_set(loginDict, @"roomId", self.roomId);
    plv_dict_set(loginDict, @"getCup", @(self.configuration.getCup ? 1 : 0));
    
    PLVBSocketAccount *account = self.configuration.account;
    plv_dict_set(loginDict, @"channelId", account.channelId);
    plv_dict_set(loginDict, @"accountId", account.userId);
    plv_dict_set(loginDict, @"sessionId", self.configuration.sessionId);
    
    PLVBSocketUser *user = self.configuration.user;
    plv_dict_set(loginDict, @"type", user.userTypeString);
    plv_dict_set(loginDict, @"actor", user.actor);
    plv_dict_set(loginDict, @"micId", user.linkMicId);
    plv_dict_set(loginDict, @"param4", user.liveParam4);
    plv_dict_set(loginDict, @"param5", user.liveParam5);
    if (user.viewerName && user.avatarUrl && user.viewerId) {
        plv_dict_set(loginDict, @"values", @[user.viewerName, user.avatarUrl, user.viewerId]);
    }
    
    __weak typeof(self)weakSelf = self;
    [self emitEvent:@"message" content:loginDict timeout:12 callback:^(NSArray * _Nonnull ackArray) {
        BOOL success = NO;
        
        NSString *ackStr = nil;
        if (ackArray && [ackArray count] > 0) {
            ackStr = [NSString stringWithFormat:@"%@", ackArray.firstObject];
            if (ackStr && [ackStr isKindOfClass:[NSString class]] && ackStr.length > 4) {
                int status = [[ackStr substringToIndex:1] intValue];
                success = (status == 2);
                if (status == 2) {
                    success = YES;
                }
            }
        }
        if (success) {
            [weakSelf loginSuccess:ackArray];
        } else {
            [weakSelf loginFailureWithCode:PLVBSocketErrorCodeAckError];
        }
    }];
}

#pragma mark - 登出

- (void)logout {
    self.configuration = nil;
    self.roomId = nil;
    self.delegate = nil;
    
    if (self.socketIO) {
        [self disconnect];
        [self removeAllHandlers];
        [self.socketManager removeSocket:self.socketIO];
        self.socketIO = nil;
        self.socketManager = nil;
    }
    
    self.status = PLVBSocketConnectStatusUnconnect;
    self.login = NO;
}

#pragma mark - 触发监听

- (void)updateConnectStatus:(PLVBSocketConnectStatus)status {
    if (status <= PLVBSocketConnectStatusUnconnect || status > PLVBSocketConnectStatusDisconnect) {
        return;
    }
    
    self.status = status;
    if (self.delegate && [self.delegate respondsToSelector:@selector(socketClient:didConnectStatusChange:)]) {
        [self.delegate socketClient:self didConnectStatusChange:self.status];
    }
}

- (void)loginSuccess:(NSArray *)ackArray {
    self.login = YES;
    if (self.delegate && [self.delegate respondsToSelector:@selector(socketClient:didLoginSuccess:)]) {
        [self.delegate socketClient:self didLoginSuccess:ackArray];
    }
}

- (void)loginFailureWithCode:(NSUInteger)code {
    self.login = NO;
    
    if (code == PLVBSocketErrorCodeAckError || code == PLVBSocketErrorCodeTokenExpired) {
        [self disconnect];
    }
    
    NSString *desc = nil;
    switch (code) {
        case PLVBSocketErrorCodeAckError:
            desc = PLVFDLocalizableString(@"登录请求失败");
            break;
        case PLVBSocketErrorCodeTokenExpired:
            desc = PLVFDLocalizableString(@"登录token鉴权失败");
            break;
        case PLVBSocketErrorCodeLoginRefuse:
            desc = PLVFDLocalizableString(@"您未被授权登录本直播间");
            break;
        case PLVBSocketErrorCodeRelogin:
            desc = PLVFDLocalizableString(@"当前账号已在其他地方登录，您将被退出本直播间");
            break;
        case PLVBSocketErrorCodeKick:
            desc = PLVFDLocalizableString(@"您未被授权进入本直播间");
            break;
        default:
            break;
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(socketClient:didLoginFailure:)]) {
        NSError *error = [PLVBSocketClient generateErrorWithCode:code description:desc];
        [self.delegate socketClient:self didLoginFailure:error];
    }
}

- (void)didReceiveMessageWithCallbackData:(NSArray *)data {
    if (!data || [data count] == 0) {
        return;
    }
    
    NSString *jsonString = data.firstObject;
    if (!jsonString || ![jsonString isKindOfClass:NSString.class] || jsonString.length == 0) {
        return;
    }
    
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    id object = [PLVBSocketClient convertJSONDataToArrayOrNSDictionary:jsonData];
    if (!object) {
        return;
    }
    
    NSString *subEvent = PLV_SafeStringForDictKey(object, @"EVENT");
    if (!subEvent || subEvent.length == 0) {
        return;
    }
    
    if ([subEvent isEqualToString:@"TOKEN_EXPIRED"]) { // token过期
        [self loginFailureWithCode:PLVBSocketErrorCodeTokenExpired];
    } else if ([subEvent isEqualToString:@"LOGIN_REFUSE"]) { // 未被授权观看本直播
        [self loginFailureWithCode:PLVBSocketErrorCodeLoginRefuse];
    } else if ([subEvent isEqualToString:@"RELOGIN"]) { // 账号已在其他地方登录
        [self loginFailureWithCode:PLVBSocketErrorCodeRelogin];
    } else if ([subEvent isEqualToString:@"KICK"]) { // 被踢出
        NSDictionary *userDict = PLV_SafeDictionaryForDictKey(object, @"user");
        NSString *viewerId = self.configuration.user.viewerId;
        if (userDict && viewerId) {
            NSString *userId = PLV_SafeStringForDictKey(userDict, @"userId");
            if ([viewerId isEqualToString:userId]) {
                [self loginFailureWithCode:PLVBSocketErrorCodeKick];
            }
        }
    } else {
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(socketClient:didReceiveMessage:json:jsonObject:)]) {
            [self.delegate socketClient:self didReceiveMessage:subEvent json:jsonString jsonObject:object];
        }
    }
}

- (void)ddidReceiveRoomPushStatusWithCallbackData:(NSArray *)data {
    if (!data || [data count] == 0) {
        return;
    }
    
    NSString *jsonString = data.firstObject;
    if (!jsonString || ![jsonString isKindOfClass:NSString.class] || jsonString.length == 0) {
        return;
    }
    
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    id object = [PLVBSocketClient convertJSONDataToArrayOrNSDictionary:jsonData];
    if (!object) {
        return;
    }
    
    NSString *status = PLV_SafeStringForDictKey(object, @"status");
    if (!status || status.length == 0) {
        return;
    }
    
    if ([status isEqualToString:@"forbid"]) { // 当前直播间被禁播
        [self loginFailureWithCode:PLVBSocketErrorCodeKick];
    }
}

- (void)didReceiveEvent:(NSString *)event callbackData:(NSArray *)data {
    if (!data || [data count] == 0) {
        return;
    }
    
    NSString *jsonString = data.firstObject;
    if (!jsonString || ![jsonString isKindOfClass:NSString.class] || jsonString.length == 0) {
        return;
    }
    
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    id object = [PLVBSocketClient convertJSONDataToArrayOrNSDictionary:jsonData];
    if (!object) {
        return;
    }
    
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(socketClient:didReceiveEvent:subEvent:json:jsonObject:)]) {
        NSString *subEvent = PLV_SafeStringForDictKey(object, @"EVENT");
        [self.delegate socketClient:self didReceiveEvent:event subEvent:subEvent json:jsonString jsonObject:object];
    }
}

#pragma mark - Message Emit

- (BOOL)emitEvent:(NSString *)event
          content:(id)content
          timeout:(double)timeout
         callback:(void (^ _Nullable )(NSArray *ackArray))callback {
    if (!event || ![event isKindOfClass:[NSString class]] || event.length == 0 || !content) {
        return NO;
    }
    
    if (![content isKindOfClass:[NSString class]] && ![content isKindOfClass:[NSDictionary class]]) {
        return NO;
    }
    
    if (self.status != PLVBSocketConnectStatusConnected) {
        return NO;
    }
    
    NSString *emitContent = nil;
    if ([content isKindOfClass:[NSString class]]) {
        emitContent = content;
    } else if ([content isKindOfClass:[NSDictionary class]]) {
        NSData *jsonData = [PLVBSocketClient convertArrayOrDictionaryToJSONData:content];
        if (jsonData) {
            emitContent = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        }
    }
    
    if (emitContent) {
        timeout = MAX(0, timeout);
        dispatch_queue_t handleQueue = [PLVSocketObjcBridge managerHandleQueueWithManager:self.socketManager];
        if (handleQueue) {
            dispatch_async(handleQueue, ^{
                if (!self.socketIO) {
                    return;
                }
                [PLVSocketObjcBridge clientEmitAckWithClient:self.socketIO event:event items:@[emitContent] after:timeout callback:^(NSArray * _Nonnull ackArray) {
                    if (callback) {
                        callback(ackArray);
                    }
                }];
            });
        } else {
            [PLVSocketObjcBridge clientEmitAckWithClient:self.socketIO event:event items:@[emitContent] after:timeout callback:^(NSArray * _Nonnull ackArray) {
                if (callback) {
                    callback(ackArray);
                }
            }];
        }
        return YES;
    } else {
        return NO;
    }
}

#pragma mark - Utils

/// 将 JSON 数据转化为字典或数组
+ (id)convertJSONDataToArrayOrNSDictionary:(NSData *)jsonData{
    if (!jsonData || !jsonData.length) {
        return nil;
    }
    
    id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
    return jsonObject;
}

/// 将字典或数组转化为 JSON 数据
+ (NSData *)convertArrayOrDictionaryToJSONData:(id)object {
    if (!object) {
        return nil;
    }
    if (![NSJSONSerialization isValidJSONObject:object]) {
        return nil;
    }
    
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:object options:NSJSONWritingPrettyPrinted error:nil];
    return jsonData;
}

/// 根据code和description生成NSError对象
+ (NSError *)generateErrorWithCode:(NSInteger)code description:(NSString *)description {
    NSErrorDomain errorDomain = @"PLVBSocketDomain";
    NSDictionary *userInfo = nil;
    if (description && [description isKindOfClass:[NSString class]] && description.length > 0) {
        userInfo = @{NSLocalizedDescriptionKey : description};
    }
    NSError *error = [NSError errorWithDomain:errorDomain code:code userInfo:userInfo];
    return error;
}

@end
