//
//  PLVBSocketClientConfiguration.m
//  PLVBusinessSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/10.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVBSocketClientConfiguration.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

static NSString *const kSocketServerUrl = @"https://chat.polyv.net"; // 正式环境
//static NSString *const kSocketServerUrl = @"http://112.74.30.89:8012"; // 测试环境

#pragma mark - PLVBSocketAccount

@interface PLVBSocketAccount ()

@property (nonatomic, strong) NSString *userId;
@property (nonatomic, strong) NSString *channelId;

@end

@implementation PLVBSocketAccount

@end

#pragma mark - PLVBSocketUser

@interface PLVBSocketUser ()

@property (nonatomic, assign) PLVBSocketUserType userType;
@property (nonatomic, strong) NSString *userTypeString;
@property (nonatomic, strong) NSString *linkMicId;
@property (nonatomic, strong) NSString *viewerId;
@property (nonatomic, strong) NSString *viewerName;
@property (nonatomic, strong) NSString *avatarUrl;
@property (nonatomic, strong) NSString * _Nullable actor;
@property (nonatomic, copy) NSString * _Nullable liveParam4;
@property (nonatomic, copy) NSString * _Nullable liveParam5;

@end

@implementation PLVBSocketUser

+ (NSString *)userTypeStringWithUserType:(PLVBSocketUserType)userType english:(BOOL)english {
    switch (userType) {
        case PLVBSocketUserTypeStudent:
            return english ? @"student" : @"普通观众";
        case PLVBSocketUserTypeSlice:
            return english ? @"slice" : @"云课堂学员";
        case PLVBSocketUserTypeViewer:
            return english ? @"viewer" : @"客户端的参与者";
        case PLVBSocketUserTypeGuest:
            return english ? @"guest" : @"嘉宾";
        case PLVBSocketUserTypeTeacher:
            return english ? @"teacher" : @"讲师";
        case PLVBSocketUserTypeAssistant:
            return english ?  @"assistant" : @"助教";
        case PLVBSocketUserTypeManager:
            return english ? @"manager" : @"管理员";
        case PLVBSocketUserTypeSCStudent:
            return english ? @"SCStudent" : @"互动学堂学员";
        default:
            return @"";
    }
}

@end

#pragma mark - PLVBSocketClientConfiguration

@interface PLVBSocketClientConfiguration ()

@property (nonatomic, strong) PLVBSocketAccount *account;
@property (nonatomic, strong) PLVBSocketUser *user;
@property (nonatomic, strong) NSSet <NSString *> *listeningEvents;
@property (nonatomic, strong) NSString *domain;

@end

@implementation PLVBSocketClientConfiguration

#pragma mark 初始化时设置默认值

- (instancetype)init {
    self = [super init];
    if (self) {
        self.domain = kSocketServerUrl;
    }
    return self;
}

#pragma mark 配置属性

- (void)setAccountWithChannelId:(NSString *)channelId
                         userId:(NSString *)userId {
    if (!channelId || ![channelId isKindOfClass:[NSString class]] || channelId.length == 0) {
        return;
    }
    
    PLVBSocketAccount *account = [[PLVBSocketAccount alloc] init];
    account.channelId = channelId;
    account.userId = userId;
    
    self.account = account;
}

- (void)setUserWithViewerId:(NSString * _Nullable)viewerId
                 viewerName:(NSString * _Nullable)viewerName
                  avatarUrl:(NSString * _Nullable)avatarUrl
                 extraParam:(NSDictionary * _Nullable)extraParam
                      actor:(NSString * _Nullable)actor
                   userType:(PLVBSocketUserType)userType {
    if (userType < PLVBSocketUserTypeStudent || userType > PLVBSocketUserTypeSCStudent) {
        return;
    }
    
    NSInteger timeInterval = (NSInteger)[[NSDate date] timeIntervalSince1970];
    NSString *processedViewerId = viewerId;
    if (!viewerId || ![viewerId isKindOfClass:[NSString class]] || viewerId.length == 0) {
        processedViewerId = @(timeInterval).stringValue;
    }
    
    /// 210824 因linkMicId在不同场景生成规则不一，之后将迁移至对应Presenter进行管理
    /// 从1.6.0开始，StreamerPresenter将不再依赖于Business层生成 linkMicId
    NSString *linkMicId = @(timeInterval).stringValue;
    if (userType == PLVBSocketUserTypeViewer ||
        userType == PLVBSocketUserTypeTeacher ||
        userType == PLVBSocketUserTypeSCStudent) { // linkMicId与viewerId一致
        linkMicId = processedViewerId;
    }
    
    NSString *processedViewerName = viewerName;
    if (!viewerName || ![viewerName isKindOfClass:[NSString class]] || viewerName.length == 0) {
        processedViewerName = [NSString stringWithFormat:@"%05d",arc4random() % 100000];
    }
    
    NSString *processedAvatarUrl = avatarUrl;
    if (!avatarUrl || ![avatarUrl isKindOfClass:[NSString class]] || avatarUrl.length == 0) {
        if (userType == PLVBSocketUserTypeTeacher) {
            processedAvatarUrl = @"https://livestatic.videocc.net/uploaded/images/webapp/avatar/default-teacher.png";
        } else {
            processedAvatarUrl = @"https://livestatic.videocc.net/v_434/assets/wimages/missing_face.png";
        }
    }
    
    NSString *processedActor = actor;
    if (![actor isKindOfClass:[NSString class]] || avatarUrl.length == 0) {
        processedActor = nil;
    }
    NSString *liveParam4 = PLV_SafeStringForDictKey(extraParam,@"liveParam4") ?:@"";
    NSString *liveParam5 = PLV_SafeStringForDictKey(extraParam,@"liveParam5") ?:@"";
    
    PLVBSocketUser *user = [[PLVBSocketUser alloc] init];
    user.userType = userType;
    user.userTypeString = [PLVBSocketUser userTypeStringWithUserType:userType english:YES];
    user.linkMicId = linkMicId;
    user.viewerId = processedViewerId;
    user.viewerName = processedViewerName;
    user.avatarUrl = processedAvatarUrl;
    user.actor = processedActor;
    user.liveParam4 = liveParam4;
    user.liveParam5 = liveParam5;
    
    self.user = user;
}

- (void)configureListeningEvents:(NSSet <NSString *> *)listeningEvents {
    if (!listeningEvents || ![listeningEvents isKindOfClass:[NSSet class]] || listeningEvents.count == 0) {
        return;
    }
    
    NSMutableSet *muArray = [[NSMutableSet alloc] initWithCapacity:listeningEvents.count];
    for (NSString *event in listeningEvents) {
        if (![event isKindOfClass:[NSString class]] || event.length == 0) {
            continue;
        }
        
        [muArray addObject:event];
    }
    self.listeningEvents = [muArray copy];
}

- (void)configureDomain:(NSString *)domain {
    if (!domain || ![domain isKindOfClass:[NSString class]] || domain.length == 0) {
        return;
    }
    
    NSString *originDomain = nil;
    if ([_domain hasPrefix:@"https://"]) {
        originDomain = [_domain substringFromIndex:8];
    } else if ([_domain hasPrefix:@"http://"]) {
        originDomain = [_domain substringFromIndex:7];
    }
    
    // 此处应使用'-hasPrefix:',而不是'-containsString:', 避免类似 apichat.polyv.net 包含 chat.polyv.net 当成同一个域名
    if ([originDomain hasPrefix:domain]) {
        return;
    }
    
    if (![domain hasPrefix:@"http"]) { // 若无协议，加上协议
        domain = [@"https://" stringByAppendingString:domain];
    }
    
    self.domain = domain;
}

@end
