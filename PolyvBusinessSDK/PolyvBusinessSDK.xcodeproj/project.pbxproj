// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXAggregateTarget section */
		22E42BC92193DBFC007CE190 /* PolyvBusinessSDKBuild */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 22E42BCD2193DBFC007CE190 /* Build configuration list for PBXAggregateTarget "PolyvBusinessSDKBuild" */;
			buildPhases = (
				22E42BCE2193DC26007CE190 /* ShellScript */,
			);
			dependencies = (
			);
			name = PolyvBusinessSDKBuild;
			productName = PolyvBusinessSDKBuild;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		00EC008B2581B5CB003DF1EC /* PLVBSocketClientConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = 00EC00892581B5CB003DF1EC /* PLVBSocketClientConfiguration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00EC008C2581B5CB003DF1EC /* PLVBSocketClientConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = 00EC008A2581B5CB003DF1EC /* PLVBSocketClientConfiguration.m */; };
		00EC008F25820BA3003DF1EC /* PLVBSocketClient.h in Headers */ = {isa = PBXBuildFile; fileRef = 00EC008D25820BA3003DF1EC /* PLVBSocketClient.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00EC009025820BA3003DF1EC /* PLVBSocketClient.m in Sources */ = {isa = PBXBuildFile; fileRef = 00EC008E25820BA3003DF1EC /* PLVBSocketClient.m */; };
		04297741289FA55000C86997 /* PLVBLinkMicVOLCManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 0429773F289FA55000C86997 /* PLVBLinkMicVOLCManager.h */; };
		04297742289FA55000C86997 /* PLVBLinkMicVOLCManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 04297740289FA55000C86997 /* PLVBLinkMicVOLCManager.m */; };
		043FD12E28D070040005FA82 /* PLVBPublicStreamVOLCPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 043FD12C28D070040005FA82 /* PLVBPublicStreamVOLCPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		043FD12F28D070040005FA82 /* PLVBPublicStreamVOLCPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 043FD12D28D070040005FA82 /* PLVBPublicStreamVOLCPlayer.m */; };
		045163862D68049E0038F591 /* PLVMattingVideoProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = 045163842D68049E0038F591 /* PLVMattingVideoProcessor.m */; };
		045163872D68049E0038F591 /* PLVMattingVideoProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = 045163852D68049E0038F591 /* PLVMattingVideoProcessor.h */; };
		047C31062C9194AF00C5A70E /* PLVBBeautyGPUPixelManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 047C31042C9194AF00C5A70E /* PLVBBeautyGPUPixelManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		047C31072C9194AF00C5A70E /* PLVBBeautyGPUPixelManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 047C31052C9194AF00C5A70E /* PLVBBeautyGPUPixelManager.mm */; };
		047C317A2C91A74B00C5A70E /* Pods_business_PLVBusinessSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A948D912941A7D320DDE12B6 /* Pods_business_PLVBusinessSDK.framework */; };
		21BA5ADA29F7D4910090D153 /* PLVBRTCStatistics.h in Headers */ = {isa = PBXBuildFile; fileRef = 21BA5AD829F7D4910090D153 /* PLVBRTCStatistics.h */; settings = {ATTRIBUTES = (Public, ); }; };
		21BA5ADB29F7D4910090D153 /* PLVBRTCStatistics.m in Sources */ = {isa = PBXBuildFile; fileRef = 21BA5AD929F7D4910090D153 /* PLVBRTCStatistics.m */; };
		22142F8C21130A3F00062C6E /* PLVBusinessSDK.h in Headers */ = {isa = PBXBuildFile; fileRef = 22142F8A21130A3F00062C6E /* PLVBusinessSDK.h */; settings = {ATTRIBUTES = (Public, ); }; };
		365A8DBD27DF13E000F8EFC8 /* PLVBroadcastSampleHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 365A8DB427DF13DF00F8EFC8 /* PLVBroadcastSampleHandler.m */; };
		365A8DBE27DF13E000F8EFC8 /* PLVBroadcastAgoraSampleHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 365A8DB627DF13DF00F8EFC8 /* PLVBroadcastAgoraSampleHandler.m */; };
		365A8DBF27DF13E000F8EFC8 /* PLVBroadcastUCloudSampleHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 365A8DB727DF13DF00F8EFC8 /* PLVBroadcastUCloudSampleHandler.m */; };
		365A8DC027DF13E000F8EFC8 /* PLVBroadcastTRTCSampleHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 365A8DB827DF13DF00F8EFC8 /* PLVBroadcastTRTCSampleHandler.m */; };
		365A8DC127DF13E000F8EFC8 /* PLVBroadcastUCloudSampleHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 365A8DB927DF13DF00F8EFC8 /* PLVBroadcastUCloudSampleHandler.h */; };
		365A8DC227DF13E000F8EFC8 /* PLVBroadcastAgoraSampleHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 365A8DBA27DF13DF00F8EFC8 /* PLVBroadcastAgoraSampleHandler.h */; };
		365A8DC327DF13E000F8EFC8 /* PLVBroadcastTRTCSampleHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 365A8DBB27DF13DF00F8EFC8 /* PLVBroadcastTRTCSampleHandler.h */; };
		365A8DC427DF13E000F8EFC8 /* PLVBroadcastSampleHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 365A8DBC27DF13DF00F8EFC8 /* PLVBroadcastSampleHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		36E8E39D26DCDF3E00DAEE02 /* PLVFoundationSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 36E8E39C26DCDF3E00DAEE02 /* PLVFoundationSDK.framework */; };
		633A8C78266FA185007CE1C8 /* PLVBRTCVideoEncoderConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = 633A8C76266FA185007CE1C8 /* PLVBRTCVideoEncoderConfiguration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		633A8C79266FA185007CE1C8 /* PLVBRTCVideoEncoderConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = 633A8C77266FA185007CE1C8 /* PLVBRTCVideoEncoderConfiguration.m */; };
		633A8C7C2671A424007CE1C8 /* PLVBRTCConstants.h in Headers */ = {isa = PBXBuildFile; fileRef = 633A8C7A2671A424007CE1C8 /* PLVBRTCConstants.h */; settings = {ATTRIBUTES = (Public, ); }; };
		633A8C7D2671A424007CE1C8 /* PLVBRTCConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 633A8C7B2671A424007CE1C8 /* PLVBRTCConstants.m */; };
		637C27DB2424BD0900AB5E94 /* PLVBLinkMicManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 637C27D92424BD0900AB5E94 /* PLVBLinkMicManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		637C27DC2424BD0900AB5E94 /* PLVBLinkMicManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 637C27DA2424BD0900AB5E94 /* PLVBLinkMicManager.m */; };
		6395DE5926253B8A000D0A44 /* PLVBLinkMicUCloudManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6395DE5226253B8A000D0A44 /* PLVBLinkMicUCloudManager.m */; };
		6395DE5B26253B8A000D0A44 /* PLVBLinkMicAgoraManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6395DE5426253B8A000D0A44 /* PLVBLinkMicAgoraManager.m */; };
		6395DE5C26253B8A000D0A44 /* PLVBLinkMicUCloudManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 6395DE5526253B8A000D0A44 /* PLVBLinkMicUCloudManager.h */; };
		6395DE5D26253B8A000D0A44 /* PLVBLinkMicManager+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = 6395DE5626253B8A000D0A44 /* PLVBLinkMicManager+PrivateExtension.h */; };
		6395DE5F26253B8A000D0A44 /* PLVBLinkMicAgoraManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 6395DE5826253B8A000D0A44 /* PLVBLinkMicAgoraManager.h */; };
		6395DE63262547F5000D0A44 /* PLVBRTCVideoViewCanvasModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 6395DE61262547F5000D0A44 /* PLVBRTCVideoViewCanvasModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6395DE64262547F5000D0A44 /* PLVBRTCVideoViewCanvasModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6395DE62262547F5000D0A44 /* PLVBRTCVideoViewCanvasModel.m */; };
		6395DE662625484D000D0A44 /* PLVBRTCDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 6395DE652625484D000D0A44 /* PLVBRTCDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
		63C0CAB32636F22E00664CB5 /* PLVBLinkMicTRTCManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 63C0CAB12636F22E00664CB5 /* PLVBLinkMicTRTCManager.h */; };
		63C0CAB42636F22E00664CB5 /* PLVBLinkMicTRTCManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 63C0CAB22636F22E00664CB5 /* PLVBLinkMicTRTCManager.m */; };
		FA4F04BE27FE866E000EBBA0 /* PLVBBytedHttpRequestProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = FA4F04BC27FE866E000EBBA0 /* PLVBBytedHttpRequestProvider.h */; };
		FA4F04BF27FE866E000EBBA0 /* PLVBBytedHttpRequestProvider.mm in Sources */ = {isa = PBXBuildFile; fileRef = FA4F04BD27FE866E000EBBA0 /* PLVBBytedHttpRequestProvider.mm */; };
		FA574861287D663A00863363 /* PLVBroadcastVOLCSampleHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = FA57485F287D663A00863363 /* PLVBroadcastVOLCSampleHandler.h */; };
		FA574862287D663A00863363 /* PLVBroadcastVOLCSampleHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = FA574860287D663A00863363 /* PLVBroadcastVOLCSampleHandler.m */; };
		FA5899832796884400BDC808 /* PLVBFilterOption.h in Headers */ = {isa = PBXBuildFile; fileRef = FA5899812796884400BDC808 /* PLVBFilterOption.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA5899842796884400BDC808 /* PLVBFilterOption.m in Sources */ = {isa = PBXBuildFile; fileRef = FA5899822796884400BDC808 /* PLVBFilterOption.m */; };
		FAB4570927951CEF00816F8B /* PLVBBeautyManager.h in Headers */ = {isa = PBXBuildFile; fileRef = FAB4570727951CEF00816F8B /* PLVBBeautyManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FAB4570A27951CEF00816F8B /* PLVBBeautyManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FAB4570827951CEF00816F8B /* PLVBBeautyManager.m */; };
		FAB4570E27951F2D00816F8B /* PLVBBeautyBytedManager.h in Headers */ = {isa = PBXBuildFile; fileRef = FAB4570C27951F2D00816F8B /* PLVBBeautyBytedManager.h */; };
		FAB4570F27951F2D00816F8B /* PLVBBeautyBytedManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = FAB4570D27951F2D00816F8B /* PLVBBeautyBytedManager.mm */; };
		FAB457112795204700816F8B /* PLVBBeautyDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = FAB457102795203C00816F8B /* PLVBBeautyDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		047C31182C91974C00C5A70E /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00EC00892581B5CB003DF1EC /* PLVBSocketClientConfiguration.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBSocketClientConfiguration.h; sourceTree = "<group>"; };
		00EC008A2581B5CB003DF1EC /* PLVBSocketClientConfiguration.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBSocketClientConfiguration.m; sourceTree = "<group>"; };
		00EC008D25820BA3003DF1EC /* PLVBSocketClient.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBSocketClient.h; sourceTree = "<group>"; };
		00EC008E25820BA3003DF1EC /* PLVBSocketClient.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBSocketClient.m; sourceTree = "<group>"; };
		0429773F289FA55000C86997 /* PLVBLinkMicVOLCManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBLinkMicVOLCManager.h; sourceTree = "<group>"; };
		04297740289FA55000C86997 /* PLVBLinkMicVOLCManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBLinkMicVOLCManager.m; sourceTree = "<group>"; };
		043FD12C28D070040005FA82 /* PLVBPublicStreamVOLCPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBPublicStreamVOLCPlayer.h; sourceTree = "<group>"; };
		043FD12D28D070040005FA82 /* PLVBPublicStreamVOLCPlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBPublicStreamVOLCPlayer.m; sourceTree = "<group>"; };
		045163842D68049E0038F591 /* PLVMattingVideoProcessor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVMattingVideoProcessor.m; sourceTree = "<group>"; };
		045163852D68049E0038F591 /* PLVMattingVideoProcessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVMattingVideoProcessor.h; sourceTree = "<group>"; };
		047C31042C9194AF00C5A70E /* PLVBBeautyGPUPixelManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBBeautyGPUPixelManager.h; sourceTree = "<group>"; };
		047C31052C9194AF00C5A70E /* PLVBBeautyGPUPixelManager.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVBBeautyGPUPixelManager.mm; sourceTree = "<group>"; };
		047C31122C9196AC00C5A70E /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.2.sdk/System/Library/Frameworks/AVFoundation.framework; sourceTree = DEVELOPER_DIR; };
		047C31142C9196B400C5A70E /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = Platforms/MacOSX.platform/Developer/SDKs/MacOSX14.2.sdk/System/Library/Frameworks/CoreVideo.framework; sourceTree = DEVELOPER_DIR; };
		1DCDAFC799ABB9620F71EFB9 /* Pods-business-PolyvBusinessSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-PolyvBusinessSDK.debug.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-PolyvBusinessSDK/Pods-business-PolyvBusinessSDK.debug.xcconfig"; sourceTree = "<group>"; };
		21BA5AD829F7D4910090D153 /* PLVBRTCStatistics.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBRTCStatistics.h; sourceTree = "<group>"; };
		21BA5AD929F7D4910090D153 /* PLVBRTCStatistics.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBRTCStatistics.m; sourceTree = "<group>"; };
		22142F8721130A3F00062C6E /* PLVBusinessSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = PLVBusinessSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		22142F8A21130A3F00062C6E /* PLVBusinessSDK.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBusinessSDK.h; sourceTree = "<group>"; };
		22142F8B21130A3F00062C6E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		229F827021C79C0E00446B9A /* PolyvFoundationSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = PolyvFoundationSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		282848E4774C2044A7657233 /* Pods-business-PolyvBusinessSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-PolyvBusinessSDK.release.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-PolyvBusinessSDK/Pods-business-PolyvBusinessSDK.release.xcconfig"; sourceTree = "<group>"; };
		365A8DB427DF13DF00F8EFC8 /* PLVBroadcastSampleHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVBroadcastSampleHandler.m; sourceTree = "<group>"; };
		365A8DB627DF13DF00F8EFC8 /* PLVBroadcastAgoraSampleHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVBroadcastAgoraSampleHandler.m; sourceTree = "<group>"; };
		365A8DB727DF13DF00F8EFC8 /* PLVBroadcastUCloudSampleHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVBroadcastUCloudSampleHandler.m; sourceTree = "<group>"; };
		365A8DB827DF13DF00F8EFC8 /* PLVBroadcastTRTCSampleHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVBroadcastTRTCSampleHandler.m; sourceTree = "<group>"; };
		365A8DB927DF13DF00F8EFC8 /* PLVBroadcastUCloudSampleHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVBroadcastUCloudSampleHandler.h; sourceTree = "<group>"; };
		365A8DBA27DF13DF00F8EFC8 /* PLVBroadcastAgoraSampleHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVBroadcastAgoraSampleHandler.h; sourceTree = "<group>"; };
		365A8DBB27DF13DF00F8EFC8 /* PLVBroadcastTRTCSampleHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVBroadcastTRTCSampleHandler.h; sourceTree = "<group>"; };
		365A8DBC27DF13DF00F8EFC8 /* PLVBroadcastSampleHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVBroadcastSampleHandler.h; sourceTree = "<group>"; };
		36E8E39C26DCDF3E00DAEE02 /* PLVFoundationSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = PLVFoundationSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		633A8C76266FA185007CE1C8 /* PLVBRTCVideoEncoderConfiguration.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBRTCVideoEncoderConfiguration.h; sourceTree = "<group>"; };
		633A8C77266FA185007CE1C8 /* PLVBRTCVideoEncoderConfiguration.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBRTCVideoEncoderConfiguration.m; sourceTree = "<group>"; };
		633A8C7A2671A424007CE1C8 /* PLVBRTCConstants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBRTCConstants.h; sourceTree = "<group>"; };
		633A8C7B2671A424007CE1C8 /* PLVBRTCConstants.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBRTCConstants.m; sourceTree = "<group>"; };
		637C27D92424BD0900AB5E94 /* PLVBLinkMicManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBLinkMicManager.h; sourceTree = "<group>"; };
		637C27DA2424BD0900AB5E94 /* PLVBLinkMicManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBLinkMicManager.m; sourceTree = "<group>"; };
		6395DE5226253B8A000D0A44 /* PLVBLinkMicUCloudManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVBLinkMicUCloudManager.m; sourceTree = "<group>"; };
		6395DE5426253B8A000D0A44 /* PLVBLinkMicAgoraManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVBLinkMicAgoraManager.m; sourceTree = "<group>"; };
		6395DE5526253B8A000D0A44 /* PLVBLinkMicUCloudManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVBLinkMicUCloudManager.h; sourceTree = "<group>"; };
		6395DE5626253B8A000D0A44 /* PLVBLinkMicManager+PrivateExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "PLVBLinkMicManager+PrivateExtension.h"; sourceTree = "<group>"; };
		6395DE5826253B8A000D0A44 /* PLVBLinkMicAgoraManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVBLinkMicAgoraManager.h; sourceTree = "<group>"; };
		6395DE61262547F5000D0A44 /* PLVBRTCVideoViewCanvasModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBRTCVideoViewCanvasModel.h; sourceTree = "<group>"; };
		6395DE62262547F5000D0A44 /* PLVBRTCVideoViewCanvasModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBRTCVideoViewCanvasModel.m; sourceTree = "<group>"; };
		6395DE652625484D000D0A44 /* PLVBRTCDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBRTCDefine.h; sourceTree = "<group>"; };
		63C0CAB12636F22E00664CB5 /* PLVBLinkMicTRTCManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVBLinkMicTRTCManager.h; sourceTree = "<group>"; };
		63C0CAB22636F22E00664CB5 /* PLVBLinkMicTRTCManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVBLinkMicTRTCManager.m; sourceTree = "<group>"; };
		7D5CBA4A9584D084BD71FDA2 /* Pods-business-PLVBusinessSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-PLVBusinessSDK.debug.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-PLVBusinessSDK/Pods-business-PLVBusinessSDK.debug.xcconfig"; sourceTree = "<group>"; };
		A948D912941A7D320DDE12B6 /* Pods_business_PLVBusinessSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_business_PLVBusinessSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		EA0EBF6EF3E2EDBFED20A898 /* Pods-business-PLVBusinessSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-PLVBusinessSDK.release.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-PLVBusinessSDK/Pods-business-PLVBusinessSDK.release.xcconfig"; sourceTree = "<group>"; };
		FA4F04BC27FE866E000EBBA0 /* PLVBBytedHttpRequestProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVBBytedHttpRequestProvider.h; sourceTree = "<group>"; };
		FA4F04BD27FE866E000EBBA0 /* PLVBBytedHttpRequestProvider.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVBBytedHttpRequestProvider.mm; sourceTree = "<group>"; };
		FA57485F287D663A00863363 /* PLVBroadcastVOLCSampleHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBroadcastVOLCSampleHandler.h; sourceTree = "<group>"; };
		FA574860287D663A00863363 /* PLVBroadcastVOLCSampleHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBroadcastVOLCSampleHandler.m; sourceTree = "<group>"; };
		FA5899812796884400BDC808 /* PLVBFilterOption.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBFilterOption.h; sourceTree = "<group>"; };
		FA5899822796884400BDC808 /* PLVBFilterOption.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBFilterOption.m; sourceTree = "<group>"; };
		FAB4570727951CEF00816F8B /* PLVBBeautyManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBBeautyManager.h; sourceTree = "<group>"; };
		FAB4570827951CEF00816F8B /* PLVBBeautyManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBBeautyManager.m; sourceTree = "<group>"; };
		FAB4570C27951F2D00816F8B /* PLVBBeautyBytedManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBBeautyBytedManager.h; sourceTree = "<group>"; };
		FAB4570D27951F2D00816F8B /* PLVBBeautyBytedManager.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVBBeautyBytedManager.mm; sourceTree = "<group>"; };
		FAB457102795203C00816F8B /* PLVBBeautyDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBBeautyDefine.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		22142F8321130A3F00062C6E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				047C317A2C91A74B00C5A70E /* Pods_business_PLVBusinessSDK.framework in Frameworks */,
				36E8E39D26DCDF3E00DAEE02 /* PLVFoundationSDK.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00BCBC442339FAA9003BFCF1 /* RTC */ = {
			isa = PBXGroup;
			children = (
				637C27D92424BD0900AB5E94 /* PLVBLinkMicManager.h */,
				637C27DA2424BD0900AB5E94 /* PLVBLinkMicManager.m */,
				6395DE652625484D000D0A44 /* PLVBRTCDefine.h */,
				633A8C7B2671A424007CE1C8 /* PLVBRTCConstants.m */,
				633A8C7A2671A424007CE1C8 /* PLVBRTCConstants.h */,
				6395DE602625472E000D0A44 /* Model */,
				6395DE5126253B8A000D0A44 /* Subclass */,
			);
			path = RTC;
			sourceTree = "<group>";
		};
		00EC00882581B599003DF1EC /* Socket */ = {
			isa = PBXGroup;
			children = (
				00EC00892581B5CB003DF1EC /* PLVBSocketClientConfiguration.h */,
				00EC008A2581B5CB003DF1EC /* PLVBSocketClientConfiguration.m */,
				00EC008D25820BA3003DF1EC /* PLVBSocketClient.h */,
				00EC008E25820BA3003DF1EC /* PLVBSocketClient.m */,
			);
			path = Socket;
			sourceTree = "<group>";
		};
		043FD12B28D06F6A0005FA82 /* Player */ = {
			isa = PBXGroup;
			children = (
				043FD12C28D070040005FA82 /* PLVBPublicStreamVOLCPlayer.h */,
				043FD12D28D070040005FA82 /* PLVBPublicStreamVOLCPlayer.m */,
			);
			path = Player;
			sourceTree = "<group>";
		};
		047C31032C91945000C5A70E /* GPUPixel */ = {
			isa = PBXGroup;
			children = (
				047C31042C9194AF00C5A70E /* PLVBBeautyGPUPixelManager.h */,
				047C31052C9194AF00C5A70E /* PLVBBeautyGPUPixelManager.mm */,
			);
			path = GPUPixel;
			sourceTree = "<group>";
		};
		10A111BCB19E40FB5F945C0F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				047C31142C9196B400C5A70E /* CoreVideo.framework */,
				047C31122C9196AC00C5A70E /* AVFoundation.framework */,
				36E8E39C26DCDF3E00DAEE02 /* PLVFoundationSDK.framework */,
				229F827021C79C0E00446B9A /* PolyvFoundationSDK.framework */,
				A948D912941A7D320DDE12B6 /* Pods_business_PLVBusinessSDK.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		22142F7D21130A3F00062C6E = {
			isa = PBXGroup;
			children = (
				22142F8921130A3F00062C6E /* PolyvBusinessSDK */,
				22142F8821130A3F00062C6E /* Products */,
				A480683CAE854F8735574FCF /* Pods */,
				10A111BCB19E40FB5F945C0F /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		22142F8821130A3F00062C6E /* Products */ = {
			isa = PBXGroup;
			children = (
				22142F8721130A3F00062C6E /* PLVBusinessSDK.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		22142F8921130A3F00062C6E /* PolyvBusinessSDK */ = {
			isa = PBXGroup;
			children = (
				22142F8A21130A3F00062C6E /* PLVBusinessSDK.h */,
				22142F8B21130A3F00062C6E /* Info.plist */,
				FA536258278EBBB30001A4F8 /* Beauty */,
				00EC00882581B599003DF1EC /* Socket */,
				00BCBC442339FAA9003BFCF1 /* RTC */,
				365A8DB327DF13DF00F8EFC8 /* ReplayKitExt */,
				043FD12B28D06F6A0005FA82 /* Player */,
			);
			path = PolyvBusinessSDK;
			sourceTree = "<group>";
		};
		365A8DB327DF13DF00F8EFC8 /* ReplayKitExt */ = {
			isa = PBXGroup;
			children = (
				365A8DBC27DF13DF00F8EFC8 /* PLVBroadcastSampleHandler.h */,
				365A8DB427DF13DF00F8EFC8 /* PLVBroadcastSampleHandler.m */,
				365A8DB527DF13DF00F8EFC8 /* Subclass */,
			);
			path = ReplayKitExt;
			sourceTree = "<group>";
		};
		365A8DB527DF13DF00F8EFC8 /* Subclass */ = {
			isa = PBXGroup;
			children = (
				365A8DBA27DF13DF00F8EFC8 /* PLVBroadcastAgoraSampleHandler.h */,
				365A8DB627DF13DF00F8EFC8 /* PLVBroadcastAgoraSampleHandler.m */,
				365A8DB927DF13DF00F8EFC8 /* PLVBroadcastUCloudSampleHandler.h */,
				365A8DB727DF13DF00F8EFC8 /* PLVBroadcastUCloudSampleHandler.m */,
				365A8DBB27DF13DF00F8EFC8 /* PLVBroadcastTRTCSampleHandler.h */,
				365A8DB827DF13DF00F8EFC8 /* PLVBroadcastTRTCSampleHandler.m */,
				FA57485F287D663A00863363 /* PLVBroadcastVOLCSampleHandler.h */,
				FA574860287D663A00863363 /* PLVBroadcastVOLCSampleHandler.m */,
			);
			path = Subclass;
			sourceTree = "<group>";
		};
		6395DE5126253B8A000D0A44 /* Subclass */ = {
			isa = PBXGroup;
			children = (
				045163852D68049E0038F591 /* PLVMattingVideoProcessor.h */,
				045163842D68049E0038F591 /* PLVMattingVideoProcessor.m */,
				6395DE5626253B8A000D0A44 /* PLVBLinkMicManager+PrivateExtension.h */,
				6395DE5826253B8A000D0A44 /* PLVBLinkMicAgoraManager.h */,
				6395DE5426253B8A000D0A44 /* PLVBLinkMicAgoraManager.m */,
				6395DE5526253B8A000D0A44 /* PLVBLinkMicUCloudManager.h */,
				6395DE5226253B8A000D0A44 /* PLVBLinkMicUCloudManager.m */,
				63C0CAB12636F22E00664CB5 /* PLVBLinkMicTRTCManager.h */,
				63C0CAB22636F22E00664CB5 /* PLVBLinkMicTRTCManager.m */,
				0429773F289FA55000C86997 /* PLVBLinkMicVOLCManager.h */,
				04297740289FA55000C86997 /* PLVBLinkMicVOLCManager.m */,
			);
			path = Subclass;
			sourceTree = "<group>";
		};
		6395DE602625472E000D0A44 /* Model */ = {
			isa = PBXGroup;
			children = (
				21BA5AD829F7D4910090D153 /* PLVBRTCStatistics.h */,
				21BA5AD929F7D4910090D153 /* PLVBRTCStatistics.m */,
				6395DE61262547F5000D0A44 /* PLVBRTCVideoViewCanvasModel.h */,
				6395DE62262547F5000D0A44 /* PLVBRTCVideoViewCanvasModel.m */,
				633A8C76266FA185007CE1C8 /* PLVBRTCVideoEncoderConfiguration.h */,
				633A8C77266FA185007CE1C8 /* PLVBRTCVideoEncoderConfiguration.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		A480683CAE854F8735574FCF /* Pods */ = {
			isa = PBXGroup;
			children = (
				1DCDAFC799ABB9620F71EFB9 /* Pods-business-PolyvBusinessSDK.debug.xcconfig */,
				282848E4774C2044A7657233 /* Pods-business-PolyvBusinessSDK.release.xcconfig */,
				7D5CBA4A9584D084BD71FDA2 /* Pods-business-PLVBusinessSDK.debug.xcconfig */,
				EA0EBF6EF3E2EDBFED20A898 /* Pods-business-PLVBusinessSDK.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		FA4F04C027FEAEC2000EBBA0 /* Byted */ = {
			isa = PBXGroup;
			children = (
				FAB4570C27951F2D00816F8B /* PLVBBeautyBytedManager.h */,
				FAB4570D27951F2D00816F8B /* PLVBBeautyBytedManager.mm */,
				FA4F04BC27FE866E000EBBA0 /* PLVBBytedHttpRequestProvider.h */,
				FA4F04BD27FE866E000EBBA0 /* PLVBBytedHttpRequestProvider.mm */,
			);
			path = Byted;
			sourceTree = "<group>";
		};
		FA536258278EBBB30001A4F8 /* Beauty */ = {
			isa = PBXGroup;
			children = (
				FAB4570727951CEF00816F8B /* PLVBBeautyManager.h */,
				FAB4570827951CEF00816F8B /* PLVBBeautyManager.m */,
				FAB457102795203C00816F8B /* PLVBBeautyDefine.h */,
				FA5899802796882700BDC808 /* Model */,
				FAB4570B27951EB500816F8B /* Subclass */,
			);
			path = Beauty;
			sourceTree = "<group>";
		};
		FA5899802796882700BDC808 /* Model */ = {
			isa = PBXGroup;
			children = (
				FA5899812796884400BDC808 /* PLVBFilterOption.h */,
				FA5899822796884400BDC808 /* PLVBFilterOption.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		FAB4570B27951EB500816F8B /* Subclass */ = {
			isa = PBXGroup;
			children = (
				047C31032C91945000C5A70E /* GPUPixel */,
				FA4F04C027FEAEC2000EBBA0 /* Byted */,
			);
			path = Subclass;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		22142F8421130A3F00062C6E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6395DE63262547F5000D0A44 /* PLVBRTCVideoViewCanvasModel.h in Headers */,
				6395DE5C26253B8A000D0A44 /* PLVBLinkMicUCloudManager.h in Headers */,
				6395DE5D26253B8A000D0A44 /* PLVBLinkMicManager+PrivateExtension.h in Headers */,
				FAB457112795204700816F8B /* PLVBBeautyDefine.h in Headers */,
				00EC008F25820BA3003DF1EC /* PLVBSocketClient.h in Headers */,
				21BA5ADA29F7D4910090D153 /* PLVBRTCStatistics.h in Headers */,
				633A8C7C2671A424007CE1C8 /* PLVBRTCConstants.h in Headers */,
				63C0CAB32636F22E00664CB5 /* PLVBLinkMicTRTCManager.h in Headers */,
				365A8DC327DF13E000F8EFC8 /* PLVBroadcastTRTCSampleHandler.h in Headers */,
				FA574861287D663A00863363 /* PLVBroadcastVOLCSampleHandler.h in Headers */,
				365A8DC427DF13E000F8EFC8 /* PLVBroadcastSampleHandler.h in Headers */,
				FA5899832796884400BDC808 /* PLVBFilterOption.h in Headers */,
				043FD12E28D070040005FA82 /* PLVBPublicStreamVOLCPlayer.h in Headers */,
				047C31062C9194AF00C5A70E /* PLVBBeautyGPUPixelManager.h in Headers */,
				22142F8C21130A3F00062C6E /* PLVBusinessSDK.h in Headers */,
				FA4F04BE27FE866E000EBBA0 /* PLVBBytedHttpRequestProvider.h in Headers */,
				637C27DB2424BD0900AB5E94 /* PLVBLinkMicManager.h in Headers */,
				633A8C78266FA185007CE1C8 /* PLVBRTCVideoEncoderConfiguration.h in Headers */,
				FAB4570927951CEF00816F8B /* PLVBBeautyManager.h in Headers */,
				045163872D68049E0038F591 /* PLVMattingVideoProcessor.h in Headers */,
				FAB4570E27951F2D00816F8B /* PLVBBeautyBytedManager.h in Headers */,
				04297741289FA55000C86997 /* PLVBLinkMicVOLCManager.h in Headers */,
				6395DE662625484D000D0A44 /* PLVBRTCDefine.h in Headers */,
				00EC008B2581B5CB003DF1EC /* PLVBSocketClientConfiguration.h in Headers */,
				365A8DC127DF13E000F8EFC8 /* PLVBroadcastUCloudSampleHandler.h in Headers */,
				365A8DC227DF13E000F8EFC8 /* PLVBroadcastAgoraSampleHandler.h in Headers */,
				6395DE5F26253B8A000D0A44 /* PLVBLinkMicAgoraManager.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		22142F8621130A3F00062C6E /* PLVBusinessSDK */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 22142F8F21130A3F00062C6E /* Build configuration list for PBXNativeTarget "PLVBusinessSDK" */;
			buildPhases = (
				1113D2AE6D071F58ED1ED901 /* [CP] Check Pods Manifest.lock */,
				22142F8221130A3F00062C6E /* Sources */,
				22142F8321130A3F00062C6E /* Frameworks */,
				22142F8421130A3F00062C6E /* Headers */,
				22142F8521130A3F00062C6E /* Resources */,
				63F7FE70D42BE217AF628760 /* [CP] Copy Pods Resources */,
				047C31182C91974C00C5A70E /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PLVBusinessSDK;
			productName = PolyvBusinessSDK;
			productReference = 22142F8721130A3F00062C6E /* PLVBusinessSDK.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		22142F7E21130A3F00062C6E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1000;
				ORGANIZATIONNAME = PLV;
				TargetAttributes = {
					22142F8621130A3F00062C6E = {
						CreatedOnToolsVersion = 9.4.1;
						ProvisioningStyle = Automatic;
					};
					22E42BC92193DBFC007CE190 = {
						CreatedOnToolsVersion = 10.0;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 22142F8121130A3F00062C6E /* Build configuration list for PBXProject "PolyvBusinessSDK" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 22142F7D21130A3F00062C6E;
			productRefGroup = 22142F8821130A3F00062C6E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				22142F8621130A3F00062C6E /* PLVBusinessSDK */,
				22E42BC92193DBFC007CE190 /* PolyvBusinessSDKBuild */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		22142F8521130A3F00062C6E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1113D2AE6D071F58ED1ED901 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-business-PLVBusinessSDK-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		22E42BCE2193DC26007CE190 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/sh\n#要build的target名\nTARGET_NAME=${PROJECT_NAME}\nif [[ $1 ]]\nthen\nTARGET_NAME=$1\nfi\n\nUNIVERSAL_OUTPUT_FOLDER=\"${SRCROOT}/Products/\"\n\n#创建输出目录，并删除之前的framework文件\nmkdir -p \"${UNIVERSAL_OUTPUT_FOLDER}\"\nrm -rf \"${UNIVERSAL_OUTPUT_FOLDER}/${TARGET_NAME}.framework\"\n\n#分别编译模拟器和真机的Framework\nxcodebuild -target \"${TARGET_NAME}\" ONLY_ACTIVE_ARCH=NO -configuration ${CONFIGURATION} -sdk iphoneos BUILD_DIR=\"${BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\" clean build\nxcodebuild -target \"${TARGET_NAME}\" ONLY_ACTIVE_ARCH=NO -configuration ${CONFIGURATION} -sdk iphonesimulator BUILD_DIR=\"${BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\" clean build\n\n#拷贝framework到univer目录\ncp -R \"${BUILD_DIR}/${CONFIGURATION}-iphoneos/${TARGET_NAME}.framework\" \"${UNIVERSAL_OUTPUT_FOLDER}\"\n\n#合并framework，输出最终的framework到build目录\nlipo -create -output \"${UNIVERSAL_OUTPUT_FOLDER}/${TARGET_NAME}.framework/${TARGET_NAME}\" \"${BUILD_DIR}/${CONFIGURATION}-iphonesimulator/${TARGET_NAME}.framework/${TARGET_NAME}\" \"${BUILD_DIR}/${CONFIGURATION}-iphoneos/${TARGET_NAME}.framework/${TARGET_NAME}\"\n\n#删除编译之后生成的无关的配置文件\ndir_path=\"${UNIVERSAL_OUTPUT_FOLDER}/${TARGET_NAME}.framework/\"\nfor file in ls $dir_path\ndo\nif [[ ${file} =~ \".xcconfig\" ]]\nthen\nrm -f \"${dir_path}/${file}\"\nfi\ndone\n#判断build文件夹是否存在，存在则删除\nif [ -d \"${SRCROOT}/build\" ]\nthen\nrm -rf \"${SRCROOT}/build\"\nfi\n#rm -rf \"${BUILD_DIR}/${CONFIGURATION}-iphonesimulator\" \"${BUILD_DIR}/${CONFIGURATION}-iphoneos\"\n#打开合并后的文件夹\nopen \"${UNIVERSAL_OUTPUT_FOLDER}\"\n";
		};
		63F7FE70D42BE217AF628760 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-business-PLVBusinessSDK/Pods-business-PLVBusinessSDK-resources.sh",
				"${PODS_ROOT}/TXLiteAVSDK_TRTC/TXLiteAVSDK_TRTC/TXLiteAVSDK_TRTC.xcframework/ios-arm64_armv7/TXLiteAVSDK_TRTC.framework/TXLiteAVSDK_TRTC.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TXLiteAVSDK_TRTC.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-business-PLVBusinessSDK/Pods-business-PLVBusinessSDK-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		22142F8221130A3F00062C6E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				365A8DC027DF13E000F8EFC8 /* PLVBroadcastTRTCSampleHandler.m in Sources */,
				045163862D68049E0038F591 /* PLVMattingVideoProcessor.m in Sources */,
				6395DE64262547F5000D0A44 /* PLVBRTCVideoViewCanvasModel.m in Sources */,
				633A8C7D2671A424007CE1C8 /* PLVBRTCConstants.m in Sources */,
				365A8DBF27DF13E000F8EFC8 /* PLVBroadcastUCloudSampleHandler.m in Sources */,
				043FD12F28D070040005FA82 /* PLVBPublicStreamVOLCPlayer.m in Sources */,
				00EC009025820BA3003DF1EC /* PLVBSocketClient.m in Sources */,
				365A8DBD27DF13E000F8EFC8 /* PLVBroadcastSampleHandler.m in Sources */,
				6395DE5B26253B8A000D0A44 /* PLVBLinkMicAgoraManager.m in Sources */,
				633A8C79266FA185007CE1C8 /* PLVBRTCVideoEncoderConfiguration.m in Sources */,
				00EC008C2581B5CB003DF1EC /* PLVBSocketClientConfiguration.m in Sources */,
				365A8DBE27DF13E000F8EFC8 /* PLVBroadcastAgoraSampleHandler.m in Sources */,
				21BA5ADB29F7D4910090D153 /* PLVBRTCStatistics.m in Sources */,
				637C27DC2424BD0900AB5E94 /* PLVBLinkMicManager.m in Sources */,
				FAB4570A27951CEF00816F8B /* PLVBBeautyManager.m in Sources */,
				FA5899842796884400BDC808 /* PLVBFilterOption.m in Sources */,
				63C0CAB42636F22E00664CB5 /* PLVBLinkMicTRTCManager.m in Sources */,
				047C31072C9194AF00C5A70E /* PLVBBeautyGPUPixelManager.mm in Sources */,
				FAB4570F27951F2D00816F8B /* PLVBBeautyBytedManager.mm in Sources */,
				04297742289FA55000C86997 /* PLVBLinkMicVOLCManager.m in Sources */,
				6395DE5926253B8A000D0A44 /* PLVBLinkMicUCloudManager.m in Sources */,
				FA574862287D663A00863363 /* PLVBroadcastVOLCSampleHandler.m in Sources */,
				FA4F04BF27FE866E000EBBA0 /* PLVBBytedHttpRequestProvider.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		22142F8D21130A3F00062C6E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.4;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		22142F8E21130A3F00062C6E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		22142F9021130A3F00062C6E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7D5CBA4A9584D084BD71FDA2 /* Pods-business-PLVBusinessSDK.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/PolyvBusinessSDK/RTC",
					"$(PROJECT_DIR)/PolyvBusinessSDK/Libs",
					"$(PROJECT_DIR)/PolyvBusinessSDK/Libs/vnn",
				);
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = PolyvBusinessSDK/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/../../Frameworks";
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.1.0;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-isystem",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Socket.IO-Client-Swift/SocketIO.framework/Headers\"",
					"-isystem",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Starscream/Starscream.framework/Headers\"",
					"-iframework",
					"\"${PODS_ROOT}/PolyvIJKPlayer\"",
					"-iframework",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Socket.IO-Client-Swift\"",
					"-iframework",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Starscream\"",
					"-fembed-bitcode",
				);
				PRODUCT_BUNDLE_IDENTIFIER = polyv.PolyvBusinessSDK;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = "arm64 armv7 x86_64 i386";
			};
			name = Debug;
		};
		22142F9121130A3F00062C6E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EA0EBF6EF3E2EDBFED20A898 /* Pods-business-PLVBusinessSDK.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = ETRBR9HA5V;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/PolyvBusinessSDK/RTC",
					"$(PROJECT_DIR)/PolyvBusinessSDK/Libs",
					"$(PROJECT_DIR)/PolyvBusinessSDK/Libs/vnn",
				);
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = PolyvBusinessSDK/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/../../Frameworks";
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.1.0;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-isystem",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Socket.IO-Client-Swift/SocketIO.framework/Headers\"",
					"-isystem",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Starscream/Starscream.framework/Headers\"",
					"-iframework",
					"\"${PODS_ROOT}/PolyvIJKPlayer\"",
					"-iframework",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Socket.IO-Client-Swift\"",
					"-iframework",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Starscream\"",
					"-fembed-bitcode",
				);
				PRODUCT_BUNDLE_IDENTIFIER = polyv.PolyvBusinessSDK;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = "arm64 armv7 x86_64 i386";
			};
			name = Release;
		};
		22E42BCA2193DBFC007CE190 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 8BMZBNN3Q5;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		22E42BCB2193DBFC007CE190 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 8BMZBNN3Q5;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		22142F8121130A3F00062C6E /* Build configuration list for PBXProject "PolyvBusinessSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22142F8D21130A3F00062C6E /* Debug */,
				22142F8E21130A3F00062C6E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		22142F8F21130A3F00062C6E /* Build configuration list for PBXNativeTarget "PLVBusinessSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22142F9021130A3F00062C6E /* Debug */,
				22142F9121130A3F00062C6E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		22E42BCD2193DBFC007CE190 /* Build configuration list for PBXAggregateTarget "PolyvBusinessSDKBuild" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22E42BCA2193DBFC007CE190 /* Debug */,
				22E42BCB2193DBFC007CE190 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 22142F7E21130A3F00062C6E /* Project object */;
}
