//
//  PLVStickerTextModel.m
//  PolyvLiveScenesDemo
//
//  Created by polyv on 2023/9/15.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVStickerTextModel.h"

@implementation PLVStickerTextModel

- (instancetype)initWithText:(NSString *)text
                    position:(CGPoint)position
                        size:(CGSize)size
                    rotation:(CGFloat)rotation
                   textColor:(UIColor *)textColor
                    fontName:(NSString *)fontName
                    fontSize:(CGFloat)fontSize {
    self = [super init];
    if (self) {
        _text = [text copy];
        _position = position;
        _size = size;
        _rotation = rotation;
        _textColor = textColor;
        _fontName = [fontName copy];
        _fontSize = fontSize;
        
        // 设置默认值
        _backgroundColor = [UIColor clearColor];
        _borderColor = [UIColor clearColor];
        _borderWidth = 0.0;
        _textAlignment = NSTextAlignmentCenter;
    }
    return self;
}

+ (instancetype)defaultTextModelWithText:(NSString *)text position:(CGPoint)position {
    CGSize defaultSize = CGSizeMake(150, 50);
    
    return [[PLVStickerTextModel alloc] initWithText:text
                                            position:position
                                                size:defaultSize
                                            rotation:0.0
                                           textColor:[UIColor whiteColor]
                                            fontName:@"PingFangSC-Medium"
                                            fontSize:18.0];
}

@end 