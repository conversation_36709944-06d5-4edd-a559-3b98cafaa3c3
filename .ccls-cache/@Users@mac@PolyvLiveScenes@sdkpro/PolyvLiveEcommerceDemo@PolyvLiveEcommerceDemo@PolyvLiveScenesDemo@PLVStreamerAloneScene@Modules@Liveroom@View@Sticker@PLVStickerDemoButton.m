//
//  PLVStickerDemoButton.m
//  PolyvLiveScenesDemo
//
//  Created by polyv on 2023/9/15.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVStickerDemoButton.h"
#import <PLVFoundationSDK/PLVColorUtil.h>

@implementation PLVStickerDemoButton

+ (instancetype)stickerButton {
    PLVStickerDemoButton *button = [PLVStickerDemoButton buttonWithType:UIButtonTypeCustom];
    [button setTitle:@"贴图" forState:UIControlStateNormal];
    [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    button.backgroundColor = [PLVColorUtil colorFromHexString:@"#3E8AFF"];
    button.layer.cornerRadius = 20;
    button.titleLabel.font = [UIFont systemFontOfSize:14];
    button.frame = CGRectMake(0, 0, 60, 40);
    return button;
}

@end 