//
//  PLVStickerTypeSelectionView.m
//  PolyvLiveScenesDemo
//
//  Created by polyv on 2023/9/15.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVStickerTypeSelectionView.h"
#import <PLVFoundationSDK/PLVColorUtil.h>
#import "PLVSAUtils.h"

@interface PLVStickerTypeSelectionView ()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *textButton;
@property (nonatomic, strong) UIButton *imageButton;

@end

@implementation PLVStickerTypeSelectionView

#pragma mark - Life Cycle

- (instancetype)init {
    return [self initWithSheetHeight:200];
}

- (instancetype)initWithSheetHeight:(CGFloat)sheetHeight {
    self = [super initWithSheetHeight:sheetHeight];
    if (self) {
        [self setupUI];
    }
    return self;
}

#pragma mark - UI Setup

- (void)setupUI {
    // 设置圆角
    [self setSheetCornerRadius:16];
    self.contentView.backgroundColor = [PLVColorUtil colorFromHexString:@"#191A1F"];
    
    // 添加标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"贴图";
    self.titleLabel.textAlignment = NSTextAlignmentLeft;
    self.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    self.titleLabel.textColor = [UIColor whiteColor];
    [self.contentView addSubview:self.titleLabel];
    
    // 添加文字贴图按钮
    self.textButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.textButton setTitle:@"文字" forState:UIControlStateNormal];
    [self.textButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.textButton.titleLabel.font = [UIFont systemFontOfSize:16];
    self.textButton.backgroundColor = [PLVColorUtil colorFromHexString:@"#2B2C32"];
    self.textButton.layer.cornerRadius = 8;
    [self.textButton addTarget:self action:@selector(textButtonAction) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.textButton];
    
    // 添加图片贴图按钮
    self.imageButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.imageButton setTitle:@"手机相册图片" forState:UIControlStateNormal];
    [self.imageButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.imageButton.titleLabel.font = [UIFont systemFontOfSize:16];
    self.imageButton.backgroundColor = [PLVColorUtil colorFromHexString:@"#2B2C32"];
    self.imageButton.layer.cornerRadius = 8;
    [self.imageButton addTarget:self action:@selector(imageButtonAction) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.imageButton];
    
    // 设置关闭回调
    __weak typeof(self) weakSelf = self;
    self.didCloseSheet = ^{
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(stickerTypeSelectionViewDidCancel:)]) {
            [weakSelf.delegate stickerTypeSelectionViewDidCancel:weakSelf];
        }
    };
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    CGFloat contentWidth = self.contentView.bounds.size.width;
    CGFloat buttonHeight = 50;
    CGFloat buttonMargin = 20;
    CGFloat buttonWidth = contentWidth - buttonMargin * 2;
    
    // 标题布局
    self.titleLabel.frame = CGRectMake(buttonMargin, 20, buttonWidth, 30);
    
    // 文字贴图按钮布局
    self.textButton.frame = CGRectMake(buttonMargin, 70, buttonWidth, buttonHeight);
    
    // 图片贴图按钮布局
    self.imageButton.frame = CGRectMake(buttonMargin, 70 + buttonHeight + 20, buttonWidth, buttonHeight);
}

#pragma mark - Actions

- (void)textButtonAction {
    if (self.delegate && [self.delegate respondsToSelector:@selector(stickerTypeSelectionView:didSelectType:)]) {
        [self.delegate stickerTypeSelectionView:self didSelectType:PLVStickerTypeText];
    }
    [self dismiss];
}

- (void)imageButtonAction {
    if (self.delegate && [self.delegate respondsToSelector:@selector(stickerTypeSelectionView:didSelectType:)]) {
        [self.delegate stickerTypeSelectionView:self didSelectType:PLVStickerTypeImage];
    }
    [self dismiss];
}

@end 