//
//  PLVStickerTextModel.h
//  PolyvLiveScenesDemo
//
//  Created by polyv on 2023/9/15.
//  Copyright © 2023 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface PLVStickerTextModel : NSObject

/// 文本内容
@property (nonatomic, copy) NSString *text;

/// 位置 (相对于画布的坐标)
@property (nonatomic, assign) CGPoint position;

/// 尺寸
@property (nonatomic, assign) CGSize size;

/// 旋转角度 (弧度)
@property (nonatomic, assign) CGFloat rotation;

/// 文本颜色
@property (nonatomic, strong) UIColor *textColor;

/// 字体名称
@property (nonatomic, copy) NSString *fontName;

/// 字体大小
@property (nonatomic, assign) CGFloat fontSize;

/// 背景颜色 (可选)
@property (nonatomic, strong, nullable) UIColor *backgroundColor;

/// 边框颜色 (可选)
@property (nonatomic, strong, nullable) UIColor *borderColor;

/// 边框宽度
@property (nonatomic, assign) CGFloat borderWidth;

/// 文本对齐方式
@property (nonatomic, assign) NSTextAlignment textAlignment;

/// 初始化方法
- (instancetype)initWithText:(NSString *)text
                    position:(CGPoint)position
                        size:(CGSize)size
                    rotation:(CGFloat)rotation
                   textColor:(UIColor *)textColor
                    fontName:(NSString *)fontName
                    fontSize:(CGFloat)fontSize;

/// 创建默认文本模板
+ (instancetype)defaultTextModelWithText:(NSString *)text position:(CGPoint)position;

@end

NS_ASSUME_NONNULL_END 