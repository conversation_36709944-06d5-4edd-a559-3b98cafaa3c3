//
//  PLVStickerTextView.h
//  PolyvLiveScenesDemo
//
//  Created by polyv on 2023/9/15.
//  Copyright © 2023 PLV. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PLVStickerTextModel.h"

NS_ASSUME_NONNULL_BEGIN

@class PLVStickerTextView;

@protocol PLVStickerTextViewDelegate <NSObject>

@optional
/// 点击文本贴图内容视图的回调
- (void)plv_StickerTextViewDidTapContentView:(PLVStickerTextView *)stickerTextView;

/// 移动文本贴图的回调
- (void)plv_StickerTextViewHandleMove:(PLVStickerTextView *)stickerTextView point:(CGPoint)point gestureEnded:(BOOL)ended;

/// 开始编辑文本内容的回调
- (void)plv_StickerTextViewDidBeginEditing:(PLVStickerTextView *)stickerTextView;

/// 结束编辑文本内容的回调
- (void)plv_StickerTextViewDidEndEditing:(PLVStickerTextView *)stickerTextView;

@end

@interface PLVStickerTextView : UIView <UIGestureRecognizerDelegate>

/// 代理对象
@property (nonatomic, weak, nullable) id<PLVStickerTextViewDelegate> delegate;

/// 最小缩放比例
@property (nonatomic, assign) CGFloat stickerMinScale;

/// 最大缩放比例
@property (nonatomic, assign) CGFloat stickerMaxScale;

/// 是否启用控制功能
@property (nonatomic, assign) BOOL enabledControl;

/// 是否启用抖动动画
@property (nonatomic, assign) BOOL enabledShakeAnimation;

/// 是否显示边框
@property (nonatomic, assign) BOOL enabledBorder;

/// 是否启用编辑模式
@property (nonatomic, assign) BOOL enableEdit;

/// 文本数据模型
@property (nonatomic, strong) PLVStickerTextModel *textModel;

/// 初始化方法
- (instancetype)initWithFrame:(CGRect)frame textModel:(PLVStickerTextModel *)textModel;

/// 执行点击操作
- (void)performTapOperation;

/// 更新文本内容
- (void)updateText:(NSString *)text;

@end

NS_ASSUME_NONNULL_END 