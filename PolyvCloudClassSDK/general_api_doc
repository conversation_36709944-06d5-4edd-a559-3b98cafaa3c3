#!/bin/bash
# -------------------------------------------------------------------------------
# Date:     2021/06/26
# Author:   MissYasiky
# Abstract: 该脚本用于生成 PLVLiveScenesSDK 的 API 文档
# 操作说明: 
# 1. 双击执行文件运行脚本；
# 2. 按照脚本要求输入当前SDK版本号；
# 3. 生成以"版本号+运行脚本日期"命名的API文档，存放与于脚本同级目录下的documents文件夹中；
# 4. 手动将API文档上传到公司FTP的documents/PLVLiveScenesSDK目录下，
# 对应API文档链接为"http://repo.polyv.net/ios/documents/PLVLiveScenesSDK/1.5.0-20210623/index.html", 
# 将"1.5.0-20210623"替换成响应的API文档文件夹名称；
# 5. 更新github仓库README.md文件中的API文档链接。
# 注意：
# 1. 该脚本必须存放在SDK项目的根目录下;
# 2. 生成API文档时需手动切换代码仓库到所需要生成文档的版本分支/标签;
# 3. SDK项目根目录下的README.md文档会变成API文档的首页内容。
# -------------------------------------------------------------------------------

# 获取脚本所在路径CURRENT_PATH
SOURCE="$0"
while [ -h "$SOURCE"  ]; do # resolve $SOURCE until the file is no longer a symlink
    DIR="$( cd -P "$( dirname "$SOURCE"  )" && pwd  )"
    SOURCE="$(readlink "$SOURCE")"
    [[ $SOURCE != /*  ]] && SOURCE="$DIR/$SOURCE" # if $SOURCE was a relative symlink, we need to resolve it relative to the path where the symlink file was located
done
CURRENT_PATH="$( cd -P "$( dirname "$SOURCE"  )" && pwd  )"


# 进入脚本所在路径下
cd ${CURRENT_PATH}


# 设置待生成的API文档的SDK版本号
read -p "⌨️  请输入 SDK 版本号（格式：x.x.x): " sdk_version
time=$(date "+%Y%m%d")
api_dir_name="${sdk_version}-${time}"


# 拷贝SDK头文件PLVLiveScenesSDK.h到根目录下
echo "拷贝SDK头文件PLVLiveScenesSDK.h到根目录下"
source_path="${CURRENT_PATH}/PolyvCloudClassSDK/PLVLiveScenesSDK.h"
destine_path="${CURRENT_PATH}/PLVLiveScenesSDK.h"
cp ${source_path} ${destine_path}


# 修改头文件PLVLiveScenesSDK.h做好生成API文档的准备
echo "修改头文件PLVLiveScenesSDK.h做好生成API文档的准备"
# 1.注释掉不需要的内容
sed -i '' 's$#import <UIKit/UIKit.h>$//#import <UIKit/UIKit.h>$' ${destine_path}
sed -i '' 's$FOUNDATION_EXPORT$//FOUNDATION_EXPORT$' ${destine_path}
# 2.把<PLVLiveScenesSDK/替换成"
sed -i '' 's$<PLVLiveScenesSDK/$"$' ${destine_path}
# 3.把>替换成"
sed -i '' 's$>$"$' ${destine_path}


# 生成API文档到document文件夹
echo "生成API文档到documents文件夹"
sdk_name="PLVLiveScenesSDK"
sdk_header="PLVLiveScenesSDK.h"
github_url="https://github.com/polyv/polyv-ios-livescenes-sdk-demo"
output_path="documents/${api_dir_name}/"
author="polyv"
jazzy --objc --clean --umbrella-header ${sdk_header} --framework-root . --github_url ${github_url} --module ${sdk_name} --module-version ${sdk_version} --output ${output_path} --author ${author}


# 清理中间文件
echo "清理中间文件"
rm ${destine_path}
