// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXAggregateTarget section */
		2299AA5321142D7200BE675D /* PolyvCloudClassSDKBuild */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 2299AA5421142D7200BE675D /* Build configuration list for PBXAggregateTarget "PolyvCloudClassSDKBuild" */;
			buildPhases = (
				2299AA5B21142DFC00BE675D /* ShellScript */,
			);
			dependencies = (
			);
			name = PolyvCloudClassSDKBuild;
			productName = PolyvLiveSDk;
		};
		632F43F723CA333A005558F1 /* AllFrameworksBuild */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 632F43FA23CA333A005558F1 /* Build configuration list for PBXAggregateTarget "AllFrameworksBuild" */;
			buildPhases = (
				632F43FB23CA333F005558F1 /* ShellScript */,
			);
			dependencies = (
			);
			name = AllFrameworksBuild;
			productName = AllFrameworksBuild;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		0003CCA623F25E4700DB26B4 /* PLVWErrorManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 0003CCA423F25E4700DB26B4 /* PLVWErrorManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0003CCA723F25E4700DB26B4 /* PLVWErrorManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0003CCA523F25E4700DB26B4 /* PLVWErrorManager.m */; };
		002E89BA23B1F1F7006F08D4 /* PLVWELogEventDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 002E89B823B1F1F7006F08D4 /* PLVWELogEventDefine.h */; };
		002E89BB23B1F1F7006F08D4 /* PLVWELogEventDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 002E89B923B1F1F7006F08D4 /* PLVWELogEventDefine.m */; };
		00340F4D22E1BB9700336ACE /* PLVImageUpload.h in Headers */ = {isa = PBXBuildFile; fileRef = 00340F4B22E1BB9700336ACE /* PLVImageUpload.h */; };
		00340F4E22E1BB9700336ACE /* PLVImageUpload.m in Sources */ = {isa = PBXBuildFile; fileRef = 00340F4C22E1BB9700336ACE /* PLVImageUpload.m */; };
		00466F8B23B0A20400F968DC /* PLVWLogReporterManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 00466F8923B0A20400F968DC /* PLVWLogReporterManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00466F8C23B0A20400F968DC /* PLVWLogReporterManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 00466F8A23B0A20400F968DC /* PLVWLogReporterManager.m */; };
		00485BEB256CE00400616C65 /* PLVSpeakMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 00485BE9256CE00400616C65 /* PLVSpeakMessage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00485BEC256CE00400616C65 /* PLVSpeakMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 00485BEA256CE00400616C65 /* PLVSpeakMessage.m */; };
		00485BEF256CE01700616C65 /* PLVImageMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 00485BED256CE01700616C65 /* PLVImageMessage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00485BF0256CE01700616C65 /* PLVImageMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 00485BEE256CE01700616C65 /* PLVImageMessage.m */; };
		00485BF3256CE03900616C65 /* PLVChatroomManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 00485BF1256CE03900616C65 /* PLVChatroomManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00485BF4256CE03900616C65 /* PLVChatroomManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 00485BF2256CE03900616C65 /* PLVChatroomManager.m */; };
		00485BF7256CE10400616C65 /* PLVCustomMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 00485BF5256CE10400616C65 /* PLVCustomMessage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00485BF8256CE10400616C65 /* PLVCustomMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 00485BF6256CE10400616C65 /* PLVCustomMessage.m */; };
		00485C0B256E52CC00616C65 /* PLVQuoteMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 00485C09256E52CC00616C65 /* PLVQuoteMessage.m */; };
		00485C0C256E52CC00616C65 /* PLVQuoteMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 00485C0A256E52CC00616C65 /* PLVQuoteMessage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		006002A3284DDDC800D5FD51 /* PLVPlaybackMsgIndex.h in Headers */ = {isa = PBXBuildFile; fileRef = 006002A1284DDDC800D5FD51 /* PLVPlaybackMsgIndex.h */; };
		006002A4284DDDC800D5FD51 /* PLVPlaybackMsgIndex.m in Sources */ = {isa = PBXBuildFile; fileRef = 006002A2284DDDC800D5FD51 /* PLVPlaybackMsgIndex.m */; };
		006002A7284DDEAF00D5FD51 /* PLVPlaybackMsgUser.h in Headers */ = {isa = PBXBuildFile; fileRef = 006002A5284DDEAF00D5FD51 /* PLVPlaybackMsgUser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		006002A8284DDEAF00D5FD51 /* PLVPlaybackMsgUser.m in Sources */ = {isa = PBXBuildFile; fileRef = 006002A6284DDEAF00D5FD51 /* PLVPlaybackMsgUser.m */; };
		006002AB284DDF0400D5FD51 /* PLVPlaybackMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 006002A9284DDF0400D5FD51 /* PLVPlaybackMessage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		006002AC284DDF0400D5FD51 /* PLVPlaybackMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 006002AA284DDF0400D5FD51 /* PLVPlaybackMessage.m */; };
		006002AF284DE4A800D5FD51 /* PLVPlaybackMessageManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 006002AD284DE4A800D5FD51 /* PLVPlaybackMessageManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		006002B0284DE4A800D5FD51 /* PLVPlaybackMessageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 006002AE284DE4A800D5FD51 /* PLVPlaybackMessageManager.m */; };
		0060354C268D577E00147A78 /* PLVLiveVClassAPI.h in Headers */ = {isa = PBXBuildFile; fileRef = 0060354A268D577E00147A78 /* PLVLiveVClassAPI.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0060354D268D577E00147A78 /* PLVLiveVClassAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 0060354B268D577E00147A78 /* PLVLiveVClassAPI.m */; };
		006398D126C1122D0024D084 /* PLVLiveVClassAPI+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 006398D026C0D9AF0024D084 /* PLVLiveVClassAPI+Private.h */; };
		0067AEA42727E73000CFA7D0 /* PLVHiClassManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 0067AEA22727E73000CFA7D0 /* PLVHiClassManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0067AEA52727E73000CFA7D0 /* PLVHiClassManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 0067AEA32727E73000CFA7D0 /* PLVHiClassManager.m */; };
		007279EE25CBFA91001CE398 /* PLVWebViewBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 007279EC25CBFA91001CE398 /* PLVWebViewBridge.m */; };
		007279EF25CBFA91001CE398 /* PLVWebViewBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 007279ED25CBFA91001CE398 /* PLVWebViewBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0075EC862646331200116271 /* PLVConsoleLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = 0075EC842646331200116271 /* PLVConsoleLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0075EC872646331200116271 /* PLVConsoleLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 0075EC852646331200116271 /* PLVConsoleLogger.m */; };
		00C75BC3261C3C7400F141C5 /* PLVDocumentUploader.h in Headers */ = {isa = PBXBuildFile; fileRef = 00C75BB8261C3C7400F141C5 /* PLVDocumentUploader.h */; };
		00C75BC4261C3C7400F141C5 /* PLVFileManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 00C75BB9261C3C7400F141C5 /* PLVFileManager.m */; };
		00C75BC5261C3C7400F141C5 /* PLVDocumentUploadClient.h in Headers */ = {isa = PBXBuildFile; fileRef = 00C75BBA261C3C7400F141C5 /* PLVDocumentUploadClient.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00C75BC6261C3C7400F141C5 /* PLVDocumentUploader.m in Sources */ = {isa = PBXBuildFile; fileRef = 00C75BBB261C3C7400F141C5 /* PLVDocumentUploader.m */; };
		00C75BC7261C3C7400F141C5 /* PLVFileManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 00C75BBC261C3C7400F141C5 /* PLVFileManager.h */; };
		00C75BC8261C3C7400F141C5 /* PLVDocumentTokenModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 00C75BBE261C3C7400F141C5 /* PLVDocumentTokenModel.h */; };
		00C75BC9261C3C7400F141C5 /* PLVDocumentUploadModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 00C75BBF261C3C7400F141C5 /* PLVDocumentUploadModel.m */; };
		00C75BCA261C3C7400F141C5 /* PLVDocumentTokenModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 00C75BC0261C3C7400F141C5 /* PLVDocumentTokenModel.m */; };
		00C75BCB261C3C7400F141C5 /* PLVDocumentUploadModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 00C75BC1261C3C7400F141C5 /* PLVDocumentUploadModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00C75BCC261C3C7400F141C5 /* PLVDocumentUploadClient.m in Sources */ = {isa = PBXBuildFile; fileRef = 00C75BC2261C3C7400F141C5 /* PLVDocumentUploadClient.m */; };
		00C75BD9261C3E3100F141C5 /* PLVLivePrivateAPI.h in Headers */ = {isa = PBXBuildFile; fileRef = 22142FC72113119600062C6E /* PLVLivePrivateAPI.h */; };
		00E48A352589B2100068C884 /* PLVViewLogCustomParam.h in Headers */ = {isa = PBXBuildFile; fileRef = 00E48A332589B2100068C884 /* PLVViewLogCustomParam.h */; settings = {ATTRIBUTES = (Public, ); }; };
		00E48A362589B2100068C884 /* PLVViewLogCustomParam.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E48A342589B2100068C884 /* PLVViewLogCustomParam.m */; };
		043F13F928E199AC003E72F4 /* PLVPublicStreamGetInfoModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 043F13F728E199AC003E72F4 /* PLVPublicStreamGetInfoModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		043F13FA28E199AC003E72F4 /* PLVPublicStreamGetInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 043F13F828E199AC003E72F4 /* PLVPublicStreamGetInfoModel.m */; };
		043F13FC28E19F43003E72F4 /* PLVPublicStreamGetInfoModel+PrivateInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 043F13FB28E19F43003E72F4 /* PLVPublicStreamGetInfoModel+PrivateInfo.h */; };
		043FD13228D078270005FA82 /* PLVPublicStreamPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 043FD13028D078260005FA82 /* PLVPublicStreamPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		043FD13328D078270005FA82 /* PLVPublicStreamPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 043FD13128D078270005FA82 /* PLVPublicStreamPlayer.m */; };
		213973D629FA45000052F584 /* PLVRTCStatistics.m in Sources */ = {isa = PBXBuildFile; fileRef = 213973D429FA45000052F584 /* PLVRTCStatistics.m */; };
		213973D729FA45000052F584 /* PLVRTCStatistics.h in Headers */ = {isa = PBXBuildFile; fileRef = 213973D529FA45000052F584 /* PLVRTCStatistics.h */; settings = {ATTRIBUTES = (Public, ); }; };
		21C23DF9258602C000F2A5D3 /* PLVSocketManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 21C23DF7258602C000F2A5D3 /* PLVSocketManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		21C23DFA258602C000F2A5D3 /* PLVSocketManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 21C23DF8258602C000F2A5D3 /* PLVSocketManager.m */; };
		21C23E012586432100F2A5D3 /* PLVSocketEventDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 21C23DFF2586432100F2A5D3 /* PLVSocketEventDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
		21C23E022586432100F2A5D3 /* PLVSocketEventDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 21C23E002586432100F2A5D3 /* PLVSocketEventDefine.m */; };
		21DDF88D2966D01D00A00202 /* PLVRedpackMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 21DDF88B2966D01D00A00202 /* PLVRedpackMessage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		21DDF88E2966D01D00A00202 /* PLVRedpackMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 21DDF88C2966D01D00A00202 /* PLVRedpackMessage.m */; };
		22142F77211309AD00062C6E /* PLVLiveScenesSDK.h in Headers */ = {isa = PBXBuildFile; fileRef = 22142F75211309AD00062C6E /* PLVLiveScenesSDK.h */; settings = {ATTRIBUTES = (Public, ); }; };
		22142FD42113119600062C6E /* PLVLiveDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = 22142FC32113119600062C6E /* PLVLiveDefine.h */; settings = {ATTRIBUTES = (Public, ); }; };
		22142FD52113119600062C6E /* PLVLivePrivateAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 22142FC42113119600062C6E /* PLVLivePrivateAPI.m */; };
		22142FDB2113119600062C6E /* PLVLiveVideoChannelMenuInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 22142FCB2113119600062C6E /* PLVLiveVideoChannelMenuInfo.m */; };
		22142FE02113119600062C6E /* PLVLiveVideoChannelMenuInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 22142FD02113119600062C6E /* PLVLiveVideoChannelMenuInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		228FFC0A21997AA5007E8413 /* PLVLiveVideoAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 228FFC0821997AA5007E8413 /* PLVLiveVideoAPI.m */; };
		228FFC0B21997AA5007E8413 /* PLVLiveVideoAPI.h in Headers */ = {isa = PBXBuildFile; fileRef = 228FFC0921997AA5007E8413 /* PLVLiveVideoAPI.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2917AB6B26C0DB8600054BCF /* PLVChannelClassManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 2917AB6926C0DB8600054BCF /* PLVChannelClassManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2917AB6C26C0DB8600054BCF /* PLVChannelClassManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 2917AB6A26C0DB8600054BCF /* PLVChannelClassManager.m */; };
		31749BE6012A161865EF893E /* Pods_business_liveScenes_PLVLiveScenesSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2895D49A8ECCE607CB987E94 /* Pods_business_liveScenes_PLVLiveScenesSDK.framework */; };
		3607013D2BA14F7900E1E1B9 /* PLVLivePlayerPictureInPictureProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 3607013C2BA14F7900E1E1B9 /* PLVLivePlayerPictureInPictureProtocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		36237D342772D8DB00C7831A /* PLVSocketWebViewBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 36237D322772D8DB00C7831A /* PLVSocketWebViewBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		36237D352772D8DB00C7831A /* PLVSocketWebViewBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 36237D332772D8DB00C7831A /* PLVSocketWebViewBridge.m */; };
		362E3B5428F426D200384FC6 /* PLVStreamerCommodityWebViewBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 362E3B5228F426D200384FC6 /* PLVStreamerCommodityWebViewBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		362E3B5528F426D200384FC6 /* PLVStreamerCommodityWebViewBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 362E3B5328F426D200384FC6 /* PLVStreamerCommodityWebViewBridge.m */; };
		363D84E42BECDD9000BDCE36 /* PLVLiveSampleBufferDisplayView.m in Sources */ = {isa = PBXBuildFile; fileRef = 363D84E22BECDD9000BDCE36 /* PLVLiveSampleBufferDisplayView.m */; };
		363D84E52BECDD9000BDCE36 /* PLVLiveSampleBufferDisplayView.h in Headers */ = {isa = PBXBuildFile; fileRef = 363D84E32BECDD9000BDCE36 /* PLVLiveSampleBufferDisplayView.h */; };
		364FBDC02A2D79110087DE53 /* PLVTuWenWebViewBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 364FBDBE2A2D79110087DE53 /* PLVTuWenWebViewBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		364FBDC12A2D79110087DE53 /* PLVTuWenWebViewBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 364FBDBF2A2D79110087DE53 /* PLVTuWenWebViewBridge.m */; };
		3674693F2769D2F700235819 /* PLVInteractWebViewBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 3674693D2769D2F700235819 /* PLVInteractWebViewBridge.m */; };
		367469402769D2F700235819 /* PLVInteractWebViewBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 3674693E2769D2F700235819 /* PLVInteractWebViewBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		367AD2AF2B8DD972006244FB /* PLVQAWebViewBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 367AD2AD2B8DD972006244FB /* PLVQAWebViewBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		367AD2B02B8DD972006244FB /* PLVQAWebViewBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 367AD2AE2B8DD972006244FB /* PLVQAWebViewBridge.m */; };
		369C2A562C339B9A00389D5B /* PLVSpeakTopMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 369C2A542C339B9A00389D5B /* PLVSpeakTopMessage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		369C2A572C339B9A00389D5B /* PLVSpeakTopMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 369C2A552C339B9A00389D5B /* PLVSpeakTopMessage.m */; };
		36A203432BF4B6E5009BBC3A /* PLVStreamerInteractWebViewBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 36A203412BF4B6E5009BBC3A /* PLVStreamerInteractWebViewBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		36A203442BF4B6E5009BBC3A /* PLVStreamerInteractWebViewBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 36A203422BF4B6E5009BBC3A /* PLVStreamerInteractWebViewBridge.m */; };
		36BACEFA26959E0C00C8CA31 /* PLVImageEmotionMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 36BACEF826959E0C00C8CA31 /* PLVImageEmotionMessage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		36BACEFB26959E0C00C8CA31 /* PLVImageEmotionMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 36BACEF926959E0C00C8CA31 /* PLVImageEmotionMessage.m */; };
		36E8E1A826DCD62200DAEE02 /* PLVLiveConstants.h in Headers */ = {isa = PBXBuildFile; fileRef = 36E8E1A626DCD62200DAEE02 /* PLVLiveConstants.h */; settings = {ATTRIBUTES = (Public, ); }; };
		36E8E1A926DCD62200DAEE02 /* PLVLiveConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 36E8E1A726DCD62200DAEE02 /* PLVLiveConstants.m */; };
		36E8E43126DCE00900DAEE02 /* PLVFoundationSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 36E8E43026DCE00900DAEE02 /* PLVFoundationSDK.framework */; };
		36E8E43526DCE01400DAEE02 /* PLVBusinessSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 36E8E43426DCE01400DAEE02 /* PLVBusinessSDK.framework */; };
		36EB53382831EED000DEF138 /* PLVProductWebViewBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 36EB53362831EED000DEF138 /* PLVProductWebViewBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		36EB53392831EED000DEF138 /* PLVProductWebViewBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 36EB53372831EED000DEF138 /* PLVProductWebViewBridge.m */; };
		4D1E74C9275DC1BF008B7D3A /* PLVLivePlaybackSectionModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D1E74C7275DC1BF008B7D3A /* PLVLivePlaybackSectionModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4D1E74CA275DC1BF008B7D3A /* PLVLivePlaybackSectionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D1E74C8275DC1BF008B7D3A /* PLVLivePlaybackSectionModel.m */; };
		4D3E417A27CDCAB300009706 /* PLVLiveHttpDnsManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D3E417827CDCAB300009706 /* PLVLiveHttpDnsManager.h */; };
		4D3E417B27CDCAB300009706 /* PLVLiveHttpDnsManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D3E417927CDCAB300009706 /* PLVLiveHttpDnsManager.m */; };
		4D46919528868CE000043A4E /* PLVFileMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D46919328868CE000043A4E /* PLVFileMessage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4D46919628868CE000043A4E /* PLVFileMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D46919428868CE000043A4E /* PLVFileMessage.m */; };
		4DE9DD8E28194812003EC5BF /* PLVChannelPlaybackInfoModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DE9DD8C28194812003EC5BF /* PLVChannelPlaybackInfoModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4DE9DD8F28194812003EC5BF /* PLVChannelPlaybackInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 4DE9DD8D28194812003EC5BF /* PLVChannelPlaybackInfoModel.m */; };
		633E4256257B91090039D35D /* PLVLivePlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 633E4254257B91090039D35D /* PLVLivePlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		633E4257257B91090039D35D /* PLVLivePlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 633E4255257B91090039D35D /* PLVLivePlayer.m */; };
		633E425A257B91220039D35D /* PLVLivePlaybackPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 633E4258257B91220039D35D /* PLVLivePlaybackPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		633E425B257B91220039D35D /* PLVLivePlaybackPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 633E4259257B91220039D35D /* PLVLivePlaybackPlayer.m */; };
		636144D62339CB1C002A44F3 /* PLVLiveAPIUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 636144D42339CB1C002A44F3 /* PLVLiveAPIUtils.h */; };
		636144D72339CB1C002A44F3 /* PLVLiveAPIUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 636144D52339CB1C002A44F3 /* PLVLiveAPIUtils.m */; };
		6390DBBE258AFD1E0031A29B /* PLVChannelInfoModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 6390DBBC258AFD1D0031A29B /* PLVChannelInfoModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6390DBBF258AFD1E0031A29B /* PLVChannelInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6390DBBD258AFD1E0031A29B /* PLVChannelInfoModel.m */; };
		6390DBC1258B47690031A29B /* PLVChannelInfoModel+PrivateInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 6390DBC0258B47690031A29B /* PLVChannelInfoModel+PrivateInfo.h */; };
		63976732250A20C100D1F16B /* PLVInteractWebview.h in Headers */ = {isa = PBXBuildFile; fileRef = 63976730250A20C100D1F16B /* PLVInteractWebview.h */; settings = {ATTRIBUTES = (Public, ); }; };
		63976733250A20C100D1F16B /* PLVInteractWebview.m in Sources */ = {isa = PBXBuildFile; fileRef = 63976731250A20C100D1F16B /* PLVInteractWebview.m */; };
		6397673A250A365900D1F16B /* PLVInteractBaseApp.h in Headers */ = {isa = PBXBuildFile; fileRef = 63976738250A365900D1F16B /* PLVInteractBaseApp.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6397673B250A365900D1F16B /* PLVInteractBaseApp.m in Sources */ = {isa = PBXBuildFile; fileRef = 63976739250A365900D1F16B /* PLVInteractBaseApp.m */; };
		63C0CAB72636F5D400664CB5 /* PLVRTCStreamerManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 63C0CAB52636F5D400664CB5 /* PLVRTCStreamerManager.m */; };
		63C0CAB82636F5D400664CB5 /* PLVRTCStreamerManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 63C0CAB62636F5D400664CB5 /* PLVRTCStreamerManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		63C0CABB2636F5DB00664CB5 /* PLVLinkMicManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 63C0CAB92636F5DB00664CB5 /* PLVLinkMicManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		63C0CABC2636F5DB00664CB5 /* PLVLinkMicManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 63C0CABA2636F5DB00664CB5 /* PLVLinkMicManager.m */; };
		63C0CAC02636F5E100664CB5 /* PLVLinkMicGetTokenModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 63C0CABD2636F5E000664CB5 /* PLVLinkMicGetTokenModel.m */; };
		63C0CAC12636F5E100664CB5 /* PLVLinkMicGetTokenModel+PrivateInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 63C0CABE2636F5E100664CB5 /* PLVLinkMicGetTokenModel+PrivateInfo.h */; };
		63C0CAC22636F5E100664CB5 /* PLVLinkMicGetTokenModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 63C0CABF2636F5E100664CB5 /* PLVLinkMicGetTokenModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		63C284BD266B71B200DBF84D /* PLVRTCStreamerMixUser.h in Headers */ = {isa = PBXBuildFile; fileRef = 63C284BB266B71B200DBF84D /* PLVRTCStreamerMixUser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		63C284BE266B71B200DBF84D /* PLVRTCStreamerMixUser.m in Sources */ = {isa = PBXBuildFile; fileRef = 63C284BC266B71B200DBF84D /* PLVRTCStreamerMixUser.m */; };
		63CBC73A2589B1D700E80897 /* PLVPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 63CBC7372589B1D700E80897 /* PLVPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		63CBC73B2589B1D700E80897 /* PLVPlayer+SubClassExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = 63CBC7382589B1D700E80897 /* PLVPlayer+SubClassExtension.h */; settings = {ATTRIBUTES = (Public, ); }; };
		63CBC73C2589B1D700E80897 /* PLVPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 63CBC7392589B1D700E80897 /* PLVPlayer.m */; };
		63F18F63251356FE0057A8B7 /* PLVPPTWebview.h in Headers */ = {isa = PBXBuildFile; fileRef = 63F18F61251356FD0057A8B7 /* PLVPPTWebview.h */; settings = {ATTRIBUTES = (Public, ); }; };
		63F18F64251356FE0057A8B7 /* PLVPPTWebview.m in Sources */ = {isa = PBXBuildFile; fileRef = 63F18F62251356FE0057A8B7 /* PLVPPTWebview.m */; };
		6509194A2B61F448009F50A0 /* PLVSocketManager+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 650919492B61F448009F50A0 /* PLVSocketManager+Private.h */; };
		651B05EB2941E1DC00F78E8E /* PLVLoganUploadManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 651B05E92941E1DC00F78E8E /* PLVLoganUploadManager.h */; };
		651B05EC2941E1DC00F78E8E /* PLVLoganUploadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 651B05EA2941E1DC00F78E8E /* PLVLoganUploadManager.m */; };
		6524811529519FC20039F997 /* PLVLoganManager+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 6524811429519FC20039F997 /* PLVLoganManager+Private.h */; };
		6526517E2948803A007563AC /* PLVLoganUploadModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 6526517C2948803A007563AC /* PLVLoganUploadModel.h */; };
		6526517F2948803A007563AC /* PLVLoganUploadModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6526517D2948803A007563AC /* PLVLoganUploadModel.m */; };
		65560BC22A274E0F0006EEE9 /* PLVQosLoadingTracerModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 65560BC02A274E0F0006EEE9 /* PLVQosLoadingTracerModel.h */; };
		65560BC32A274E0F0006EEE9 /* PLVQosLoadingTracerModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 65560BC12A274E0F0006EEE9 /* PLVQosLoadingTracerModel.m */; };
		655AED902950694800955B33 /* PLVLoganManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 655AED8E2950694800955B33 /* PLVLoganManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		655AED912950694800955B33 /* PLVLoganManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 655AED8F2950694800955B33 /* PLVLoganManager.m */; };
		658F9B162949B503003B031A /* PLVLoganUploadTokenModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 658F9B142949B503003B031A /* PLVLoganUploadTokenModel.h */; };
		658F9B172949B503003B031A /* PLVLoganUploadTokenModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 658F9B152949B503003B031A /* PLVLoganUploadTokenModel.m */; };
		67116BA424E2A27400F4213F /* PLVPlaybackListModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 67116BA224E2A27400F4213F /* PLVPlaybackListModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		67116BA524E2A27400F4213F /* PLVPlaybackListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 67116BA324E2A27400F4213F /* PLVPlaybackListModel.m */; };
		6727C36928084C6A00965384 /* PLVLiveVideoConfig+PrivateInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 6727C36828084C6A00965384 /* PLVLiveVideoConfig+PrivateInfo.h */; };
		6730B5F6212EA47E0086CFC0 /* PLVLiveVideoConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 6730B5F4212EA47E0086CFC0 /* PLVLiveVideoConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6730B5F7212EA47E0086CFC0 /* PLVLiveVideoConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 6730B5F5212EA47E0086CFC0 /* PLVLiveVideoConfig.m */; };
		8F789515267B806900A7C2A7 /* PLVRewardMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = 8F789513267B806900A7C2A7 /* PLVRewardMessage.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8F789516267B806900A7C2A7 /* PLVRewardMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 8F789514267B806900A7C2A7 /* PLVRewardMessage.m */; };
		8F78D375269D7B360005AB76 /* PLVContainerWebViewBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 8F78D373269D7B360005AB76 /* PLVContainerWebViewBridge.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8F78D376269D7B360005AB76 /* PLVContainerWebViewBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = 8F78D374269D7B360005AB76 /* PLVContainerWebViewBridge.m */; };
		8FB1BCF727D6022F0033A7BD /* PLVBeautyResourceManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 8FB1BCF527D6022F0033A7BD /* PLVBeautyResourceManager.h */; };
		8FB1BCF827D6022F0033A7BD /* PLVBeautyResourceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 8FB1BCF627D6022F0033A7BD /* PLVBeautyResourceManager.m */; };
		BF7CFC7025BA64F70084C160 /* PLVCommodityModel.h in Headers */ = {isa = PBXBuildFile; fileRef = BF7CFC6E25BA64F70084C160 /* PLVCommodityModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BF7CFC7125BA64F70084C160 /* PLVCommodityModel.m in Sources */ = {isa = PBXBuildFile; fileRef = BF7CFC6F25BA64F70084C160 /* PLVCommodityModel.m */; };
		FA1B8838283B74DA00BFDE7D /* PLVDownloadManager.h in Headers */ = {isa = PBXBuildFile; fileRef = FA1B8836283B74DA00BFDE7D /* PLVDownloadManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA1B8839283B74DA00BFDE7D /* PLVDownloadManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FA1B8837283B74DA00BFDE7D /* PLVDownloadManager.m */; };
		FA1B883C283B774600BFDE7D /* PLVDownloadTaskInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = FA1B883A283B774600BFDE7D /* PLVDownloadTaskInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA1B883D283B774600BFDE7D /* PLVDownloadTaskInfo.mm in Sources */ = {isa = PBXBuildFile; fileRef = FA1B883B283B774600BFDE7D /* PLVDownloadTaskInfo.mm */; };
		FA1B883F283B7A5700BFDE7D /* PLVDownloadTaskInfo+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = FA1B883E283B7A5700BFDE7D /* PLVDownloadTaskInfo+PrivateExtension.h */; };
		FA1B8842283B7F7C00BFDE7D /* PLVDownloadPathManager.h in Headers */ = {isa = PBXBuildFile; fileRef = FA1B8840283B7F7C00BFDE7D /* PLVDownloadPathManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA1B8843283B7F7C00BFDE7D /* PLVDownloadPathManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FA1B8841283B7F7C00BFDE7D /* PLVDownloadPathManager.m */; };
		FA1B8848283B847900BFDE7D /* PLVDownloadPlaybackTaskInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = FA1B8846283B847900BFDE7D /* PLVDownloadPlaybackTaskInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA1B8849283B847900BFDE7D /* PLVDownloadPlaybackTaskInfo.mm in Sources */ = {isa = PBXBuildFile; fileRef = FA1B8847283B847900BFDE7D /* PLVDownloadPlaybackTaskInfo.mm */; };
		FA1B884B283B866100BFDE7D /* PLVDownloadPlaybackTaskInfo+PrivateExtension.h in Headers */ = {isa = PBXBuildFile; fileRef = FA1B884A283B866100BFDE7D /* PLVDownloadPlaybackTaskInfo+PrivateExtension.h */; };
		FA1B8850283B86B500BFDE7D /* PLVDownloadDatabaseManager.h in Headers */ = {isa = PBXBuildFile; fileRef = FA1B884E283B86B500BFDE7D /* PLVDownloadDatabaseManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA1B8851283B86B500BFDE7D /* PLVDownloadDatabaseManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = FA1B884F283B86B500BFDE7D /* PLVDownloadDatabaseManager.mm */; };
		FA1B8853283B885400BFDE7D /* PLVDownloadDatabaseManager+Playback.h in Headers */ = {isa = PBXBuildFile; fileRef = FA1B8852283B885400BFDE7D /* PLVDownloadDatabaseManager+Playback.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA1B8856283B8C6D00BFDE7D /* PLVDownloadCommonOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = FA1B8854283B8C6D00BFDE7D /* PLVDownloadCommonOperation.h */; };
		FA1B8857283B8C6D00BFDE7D /* PLVDownloadCommonOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = FA1B8855283B8C6D00BFDE7D /* PLVDownloadCommonOperation.m */; };
		FA1B885A283B8C7C00BFDE7D /* PLVDownloadPlaybackOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = FA1B8858283B8C7C00BFDE7D /* PLVDownloadPlaybackOperation.h */; };
		FA1B885B283B8C7C00BFDE7D /* PLVDownloadPlaybackOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = FA1B8859283B8C7C00BFDE7D /* PLVDownloadPlaybackOperation.m */; };
		FA1B885E283B8D1600BFDE7D /* PLVDownloadURLSession.h in Headers */ = {isa = PBXBuildFile; fileRef = FA1B885C283B8D1600BFDE7D /* PLVDownloadURLSession.h */; };
		FA1B885F283B8D1600BFDE7D /* PLVDownloadURLSession.m in Sources */ = {isa = PBXBuildFile; fileRef = FA1B885D283B8D1600BFDE7D /* PLVDownloadURLSession.m */; };
		FA1B8862283B8D4400BFDE7D /* PLVDownloadDataTasker.h in Headers */ = {isa = PBXBuildFile; fileRef = FA1B8860283B8D4400BFDE7D /* PLVDownloadDataTasker.h */; };
		FA1B8863283B8D4400BFDE7D /* PLVDownloadDataTasker.m in Sources */ = {isa = PBXBuildFile; fileRef = FA1B8861283B8D4400BFDE7D /* PLVDownloadDataTasker.m */; };
		FA4FD38427B4B5BD004F4889 /* PLVLivePictureInPictureManager+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = FA4FD38327B4B5BD004F4889 /* PLVLivePictureInPictureManager+Private.h */; };
		FA51FF3927B35B1D004C3B4B /* PLVLivePictureInPictureManager.h in Headers */ = {isa = PBXBuildFile; fileRef = FA51FF3727B35B1D004C3B4B /* PLVLivePictureInPictureManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA51FF3A27B35B1D004C3B4B /* PLVLivePictureInPictureManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FA51FF3827B35B1D004C3B4B /* PLVLivePictureInPictureManager.m */; };
		FA658DB8283C8D1C00AFBC4E /* PLVPlaybackVideoInfoModel.h in Headers */ = {isa = PBXBuildFile; fileRef = FA658DB6283C8D1C00AFBC4E /* PLVPlaybackVideoInfoModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA658DB9283C8D1C00AFBC4E /* PLVPlaybackVideoInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = FA658DB7283C8D1C00AFBC4E /* PLVPlaybackVideoInfoModel.m */; };
		FA658DBE283C8F8800AFBC4E /* PLVPlaybackCacheManager.h in Headers */ = {isa = PBXBuildFile; fileRef = FA658DBC283C8F8800AFBC4E /* PLVPlaybackCacheManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FA658DBF283C8F8800AFBC4E /* PLVPlaybackCacheManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FA658DBD283C8F8800AFBC4E /* PLVPlaybackCacheManager.m */; };
		FAB4571E27957B6200816F8B /* PLVBeautyManager.h in Headers */ = {isa = PBXBuildFile; fileRef = FAB4571C27957B6200816F8B /* PLVBeautyManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		FAB4571F27957B6200816F8B /* PLVBeautyManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FAB4571D27957B6200816F8B /* PLVBeautyManager.m */; };
		FAE8DE9127D0A1ED007343FD /* PLVBeautyManager+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = FAE8DE9027D0A1ED007343FD /* PLVBeautyManager+Private.h */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0003CCA423F25E4700DB26B4 /* PLVWErrorManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVWErrorManager.h; sourceTree = "<group>"; };
		0003CCA523F25E4700DB26B4 /* PLVWErrorManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVWErrorManager.m; sourceTree = "<group>"; };
		002E89B823B1F1F7006F08D4 /* PLVWELogEventDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVWELogEventDefine.h; sourceTree = "<group>"; };
		002E89B923B1F1F7006F08D4 /* PLVWELogEventDefine.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVWELogEventDefine.m; sourceTree = "<group>"; };
		00340F4B22E1BB9700336ACE /* PLVImageUpload.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVImageUpload.h; sourceTree = "<group>"; };
		00340F4C22E1BB9700336ACE /* PLVImageUpload.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVImageUpload.m; sourceTree = "<group>"; };
		00466F8923B0A20400F968DC /* PLVWLogReporterManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVWLogReporterManager.h; sourceTree = "<group>"; };
		00466F8A23B0A20400F968DC /* PLVWLogReporterManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVWLogReporterManager.m; sourceTree = "<group>"; };
		00485BE9256CE00400616C65 /* PLVSpeakMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVSpeakMessage.h; sourceTree = "<group>"; };
		00485BEA256CE00400616C65 /* PLVSpeakMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVSpeakMessage.m; sourceTree = "<group>"; };
		00485BED256CE01700616C65 /* PLVImageMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVImageMessage.h; sourceTree = "<group>"; };
		00485BEE256CE01700616C65 /* PLVImageMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVImageMessage.m; sourceTree = "<group>"; };
		00485BF1256CE03900616C65 /* PLVChatroomManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVChatroomManager.h; sourceTree = "<group>"; };
		00485BF2256CE03900616C65 /* PLVChatroomManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVChatroomManager.m; sourceTree = "<group>"; };
		00485BF5256CE10400616C65 /* PLVCustomMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVCustomMessage.h; sourceTree = "<group>"; };
		00485BF6256CE10400616C65 /* PLVCustomMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVCustomMessage.m; sourceTree = "<group>"; };
		00485C09256E52CC00616C65 /* PLVQuoteMessage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVQuoteMessage.m; sourceTree = "<group>"; };
		00485C0A256E52CC00616C65 /* PLVQuoteMessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVQuoteMessage.h; sourceTree = "<group>"; };
		006002A1284DDDC800D5FD51 /* PLVPlaybackMsgIndex.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPlaybackMsgIndex.h; sourceTree = "<group>"; };
		006002A2284DDDC800D5FD51 /* PLVPlaybackMsgIndex.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPlaybackMsgIndex.m; sourceTree = "<group>"; };
		006002A5284DDEAF00D5FD51 /* PLVPlaybackMsgUser.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPlaybackMsgUser.h; sourceTree = "<group>"; };
		006002A6284DDEAF00D5FD51 /* PLVPlaybackMsgUser.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPlaybackMsgUser.m; sourceTree = "<group>"; };
		006002A9284DDF0400D5FD51 /* PLVPlaybackMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPlaybackMessage.h; sourceTree = "<group>"; };
		006002AA284DDF0400D5FD51 /* PLVPlaybackMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPlaybackMessage.m; sourceTree = "<group>"; };
		006002AD284DE4A800D5FD51 /* PLVPlaybackMessageManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPlaybackMessageManager.h; sourceTree = "<group>"; };
		006002AE284DE4A800D5FD51 /* PLVPlaybackMessageManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPlaybackMessageManager.m; sourceTree = "<group>"; };
		0060354A268D577E00147A78 /* PLVLiveVClassAPI.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLiveVClassAPI.h; sourceTree = "<group>"; };
		0060354B268D577E00147A78 /* PLVLiveVClassAPI.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVLiveVClassAPI.m; sourceTree = "<group>"; };
		006398D026C0D9AF0024D084 /* PLVLiveVClassAPI+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVLiveVClassAPI+Private.h"; sourceTree = "<group>"; };
		0067AEA22727E73000CFA7D0 /* PLVHiClassManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVHiClassManager.h; sourceTree = "<group>"; };
		0067AEA32727E73000CFA7D0 /* PLVHiClassManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVHiClassManager.m; sourceTree = "<group>"; };
		007279EC25CBFA91001CE398 /* PLVWebViewBridge.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVWebViewBridge.m; sourceTree = "<group>"; };
		007279ED25CBFA91001CE398 /* PLVWebViewBridge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVWebViewBridge.h; sourceTree = "<group>"; };
		0075EC842646331200116271 /* PLVConsoleLogger.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVConsoleLogger.h; sourceTree = "<group>"; };
		0075EC852646331200116271 /* PLVConsoleLogger.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVConsoleLogger.m; sourceTree = "<group>"; };
		00C75BB8261C3C7400F141C5 /* PLVDocumentUploader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVDocumentUploader.h; sourceTree = "<group>"; };
		00C75BB9261C3C7400F141C5 /* PLVFileManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVFileManager.m; sourceTree = "<group>"; };
		00C75BBA261C3C7400F141C5 /* PLVDocumentUploadClient.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVDocumentUploadClient.h; sourceTree = "<group>"; };
		00C75BBB261C3C7400F141C5 /* PLVDocumentUploader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVDocumentUploader.m; sourceTree = "<group>"; };
		00C75BBC261C3C7400F141C5 /* PLVFileManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVFileManager.h; sourceTree = "<group>"; };
		00C75BBE261C3C7400F141C5 /* PLVDocumentTokenModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVDocumentTokenModel.h; sourceTree = "<group>"; };
		00C75BBF261C3C7400F141C5 /* PLVDocumentUploadModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVDocumentUploadModel.m; sourceTree = "<group>"; };
		00C75BC0261C3C7400F141C5 /* PLVDocumentTokenModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVDocumentTokenModel.m; sourceTree = "<group>"; };
		00C75BC1261C3C7400F141C5 /* PLVDocumentUploadModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVDocumentUploadModel.h; sourceTree = "<group>"; };
		00C75BC2261C3C7400F141C5 /* PLVDocumentUploadClient.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVDocumentUploadClient.m; sourceTree = "<group>"; };
		00E48A332589B2100068C884 /* PLVViewLogCustomParam.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVViewLogCustomParam.h; sourceTree = "<group>"; };
		00E48A342589B2100068C884 /* PLVViewLogCustomParam.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVViewLogCustomParam.m; sourceTree = "<group>"; };
		043F13F728E199AC003E72F4 /* PLVPublicStreamGetInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPublicStreamGetInfoModel.h; sourceTree = "<group>"; };
		043F13F828E199AC003E72F4 /* PLVPublicStreamGetInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPublicStreamGetInfoModel.m; sourceTree = "<group>"; };
		043F13FB28E19F43003E72F4 /* PLVPublicStreamGetInfoModel+PrivateInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVPublicStreamGetInfoModel+PrivateInfo.h"; sourceTree = "<group>"; };
		043FD13028D078260005FA82 /* PLVPublicStreamPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPublicStreamPlayer.h; sourceTree = "<group>"; };
		043FD13128D078270005FA82 /* PLVPublicStreamPlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPublicStreamPlayer.m; sourceTree = "<group>"; };
		213973D429FA45000052F584 /* PLVRTCStatistics.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVRTCStatistics.m; sourceTree = "<group>"; };
		213973D529FA45000052F584 /* PLVRTCStatistics.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVRTCStatistics.h; sourceTree = "<group>"; };
		21C23DF7258602C000F2A5D3 /* PLVSocketManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVSocketManager.h; sourceTree = "<group>"; };
		21C23DF8258602C000F2A5D3 /* PLVSocketManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVSocketManager.m; sourceTree = "<group>"; };
		21C23DFF2586432100F2A5D3 /* PLVSocketEventDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVSocketEventDefine.h; sourceTree = "<group>"; };
		21C23E002586432100F2A5D3 /* PLVSocketEventDefine.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVSocketEventDefine.m; sourceTree = "<group>"; };
		21DDF88B2966D01D00A00202 /* PLVRedpackMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVRedpackMessage.h; sourceTree = "<group>"; };
		21DDF88C2966D01D00A00202 /* PLVRedpackMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVRedpackMessage.m; sourceTree = "<group>"; };
		22142F72211309AD00062C6E /* PLVLiveScenesSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = PLVLiveScenesSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		22142F75211309AD00062C6E /* PLVLiveScenesSDK.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLiveScenesSDK.h; sourceTree = "<group>"; };
		22142F76211309AD00062C6E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		22142FC32113119600062C6E /* PLVLiveDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVLiveDefine.h; sourceTree = "<group>"; };
		22142FC42113119600062C6E /* PLVLivePrivateAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVLivePrivateAPI.m; sourceTree = "<group>"; };
		22142FC72113119600062C6E /* PLVLivePrivateAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVLivePrivateAPI.h; sourceTree = "<group>"; };
		22142FCB2113119600062C6E /* PLVLiveVideoChannelMenuInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVLiveVideoChannelMenuInfo.m; sourceTree = "<group>"; };
		22142FD02113119600062C6E /* PLVLiveVideoChannelMenuInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVLiveVideoChannelMenuInfo.h; sourceTree = "<group>"; };
		228FFC0821997AA5007E8413 /* PLVLiveVideoAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVLiveVideoAPI.m; sourceTree = "<group>"; };
		228FFC0921997AA5007E8413 /* PLVLiveVideoAPI.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVLiveVideoAPI.h; sourceTree = "<group>"; };
		22D7915921A68E0400835ADD /* PolyvBusinessSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = PolyvBusinessSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		22D7915B21A68E0400835ADD /* PolyvFoundationSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = PolyvFoundationSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		24668DAA6C51206A7C65EF6E /* Pods-business-cloudClass-PolyvCloudClassSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-cloudClass-PolyvCloudClassSDK.debug.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-cloudClass-PolyvCloudClassSDK/Pods-business-cloudClass-PolyvCloudClassSDK.debug.xcconfig"; sourceTree = "<group>"; };
		2895D49A8ECCE607CB987E94 /* Pods_business_liveScenes_PLVLiveScenesSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_business_liveScenes_PLVLiveScenesSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2917AB6926C0DB8600054BCF /* PLVChannelClassManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVChannelClassManager.h; sourceTree = "<group>"; };
		2917AB6A26C0DB8600054BCF /* PLVChannelClassManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVChannelClassManager.m; sourceTree = "<group>"; };
		356A9E9A1185509BDF604779 /* Pods-common-PolyvCloudClassSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-common-PolyvCloudClassSDK.release.xcconfig"; path = "../PolyvCloudClassDemo/Pods/Target Support Files/Pods-common-PolyvCloudClassSDK/Pods-common-PolyvCloudClassSDK.release.xcconfig"; sourceTree = "<group>"; };
		3607013C2BA14F7900E1E1B9 /* PLVLivePlayerPictureInPictureProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLivePlayerPictureInPictureProtocol.h; sourceTree = "<group>"; };
		36237D322772D8DB00C7831A /* PLVSocketWebViewBridge.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVSocketWebViewBridge.h; sourceTree = "<group>"; };
		36237D332772D8DB00C7831A /* PLVSocketWebViewBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVSocketWebViewBridge.m; sourceTree = "<group>"; };
		362E3B5228F426D200384FC6 /* PLVStreamerCommodityWebViewBridge.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVStreamerCommodityWebViewBridge.h; sourceTree = "<group>"; };
		362E3B5328F426D200384FC6 /* PLVStreamerCommodityWebViewBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVStreamerCommodityWebViewBridge.m; sourceTree = "<group>"; };
		363D84E22BECDD9000BDCE36 /* PLVLiveSampleBufferDisplayView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVLiveSampleBufferDisplayView.m; sourceTree = "<group>"; };
		363D84E32BECDD9000BDCE36 /* PLVLiveSampleBufferDisplayView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVLiveSampleBufferDisplayView.h; sourceTree = "<group>"; };
		364FBDBE2A2D79110087DE53 /* PLVTuWenWebViewBridge.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVTuWenWebViewBridge.h; sourceTree = "<group>"; };
		364FBDBF2A2D79110087DE53 /* PLVTuWenWebViewBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVTuWenWebViewBridge.m; sourceTree = "<group>"; };
		3674693D2769D2F700235819 /* PLVInteractWebViewBridge.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVInteractWebViewBridge.m; sourceTree = "<group>"; };
		3674693E2769D2F700235819 /* PLVInteractWebViewBridge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVInteractWebViewBridge.h; sourceTree = "<group>"; };
		367AD2AD2B8DD972006244FB /* PLVQAWebViewBridge.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVQAWebViewBridge.h; sourceTree = "<group>"; };
		367AD2AE2B8DD972006244FB /* PLVQAWebViewBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVQAWebViewBridge.m; sourceTree = "<group>"; };
		369C2A542C339B9A00389D5B /* PLVSpeakTopMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVSpeakTopMessage.h; sourceTree = "<group>"; };
		369C2A552C339B9A00389D5B /* PLVSpeakTopMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVSpeakTopMessage.m; sourceTree = "<group>"; };
		36A203412BF4B6E5009BBC3A /* PLVStreamerInteractWebViewBridge.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVStreamerInteractWebViewBridge.h; sourceTree = "<group>"; };
		36A203422BF4B6E5009BBC3A /* PLVStreamerInteractWebViewBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVStreamerInteractWebViewBridge.m; sourceTree = "<group>"; };
		36BACEF826959E0C00C8CA31 /* PLVImageEmotionMessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVImageEmotionMessage.h; sourceTree = "<group>"; };
		36BACEF926959E0C00C8CA31 /* PLVImageEmotionMessage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVImageEmotionMessage.m; sourceTree = "<group>"; };
		36E8E1A626DCD62200DAEE02 /* PLVLiveConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVLiveConstants.h; sourceTree = "<group>"; };
		36E8E1A726DCD62200DAEE02 /* PLVLiveConstants.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVLiveConstants.m; sourceTree = "<group>"; };
		36E8E43026DCE00900DAEE02 /* PLVFoundationSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = PLVFoundationSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		36E8E43426DCE01400DAEE02 /* PLVBusinessSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = PLVBusinessSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		36EB53362831EED000DEF138 /* PLVProductWebViewBridge.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVProductWebViewBridge.h; sourceTree = "<group>"; };
		36EB53372831EED000DEF138 /* PLVProductWebViewBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVProductWebViewBridge.m; sourceTree = "<group>"; };
		3A1280D9E678C44B22A923FA /* Pods-business-liveScenes-PLVLiveScenesSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-liveScenes-PLVLiveScenesSDK.debug.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-liveScenes-PLVLiveScenesSDK/Pods-business-liveScenes-PLVLiveScenesSDK.debug.xcconfig"; sourceTree = "<group>"; };
		4D1E74C7275DC1BF008B7D3A /* PLVLivePlaybackSectionModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLivePlaybackSectionModel.h; sourceTree = "<group>"; };
		4D1E74C8275DC1BF008B7D3A /* PLVLivePlaybackSectionModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVLivePlaybackSectionModel.m; sourceTree = "<group>"; };
		4D3E417827CDCAB300009706 /* PLVLiveHttpDnsManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLiveHttpDnsManager.h; sourceTree = "<group>"; };
		4D3E417927CDCAB300009706 /* PLVLiveHttpDnsManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVLiveHttpDnsManager.m; sourceTree = "<group>"; };
		4D46919328868CE000043A4E /* PLVFileMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVFileMessage.h; sourceTree = "<group>"; };
		4D46919428868CE000043A4E /* PLVFileMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVFileMessage.m; sourceTree = "<group>"; };
		4DE9DD8C28194812003EC5BF /* PLVChannelPlaybackInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVChannelPlaybackInfoModel.h; sourceTree = "<group>"; };
		4DE9DD8D28194812003EC5BF /* PLVChannelPlaybackInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVChannelPlaybackInfoModel.m; sourceTree = "<group>"; };
		506C38BB4D862A9456F4C096 /* Pods-common-sdk_common-PolyvCloudClassSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-common-sdk_common-PolyvCloudClassSDK.release.xcconfig"; path = "../PolyvCloudClassDemo/Pods/Target Support Files/Pods-common-sdk_common-PolyvCloudClassSDK/Pods-common-sdk_common-PolyvCloudClassSDK.release.xcconfig"; sourceTree = "<group>"; };
		633E4254257B91090039D35D /* PLVLivePlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLivePlayer.h; sourceTree = "<group>"; };
		633E4255257B91090039D35D /* PLVLivePlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVLivePlayer.m; sourceTree = "<group>"; };
		633E4258257B91220039D35D /* PLVLivePlaybackPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLivePlaybackPlayer.h; sourceTree = "<group>"; };
		633E4259257B91220039D35D /* PLVLivePlaybackPlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVLivePlaybackPlayer.m; sourceTree = "<group>"; };
		636144D42339CB1C002A44F3 /* PLVLiveAPIUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLiveAPIUtils.h; sourceTree = "<group>"; };
		636144D52339CB1C002A44F3 /* PLVLiveAPIUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVLiveAPIUtils.m; sourceTree = "<group>"; };
		6390DBBC258AFD1D0031A29B /* PLVChannelInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVChannelInfoModel.h; sourceTree = "<group>"; };
		6390DBBD258AFD1E0031A29B /* PLVChannelInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVChannelInfoModel.m; sourceTree = "<group>"; };
		6390DBC0258B47690031A29B /* PLVChannelInfoModel+PrivateInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVChannelInfoModel+PrivateInfo.h"; sourceTree = "<group>"; };
		63976730250A20C100D1F16B /* PLVInteractWebview.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVInteractWebview.h; sourceTree = "<group>"; };
		63976731250A20C100D1F16B /* PLVInteractWebview.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVInteractWebview.m; sourceTree = "<group>"; };
		63976738250A365900D1F16B /* PLVInteractBaseApp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVInteractBaseApp.h; sourceTree = "<group>"; };
		63976739250A365900D1F16B /* PLVInteractBaseApp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVInteractBaseApp.m; sourceTree = "<group>"; };
		63C0CAB52636F5D400664CB5 /* PLVRTCStreamerManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVRTCStreamerManager.m; sourceTree = "<group>"; };
		63C0CAB62636F5D400664CB5 /* PLVRTCStreamerManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVRTCStreamerManager.h; sourceTree = "<group>"; };
		63C0CAB92636F5DB00664CB5 /* PLVLinkMicManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVLinkMicManager.h; sourceTree = "<group>"; };
		63C0CABA2636F5DB00664CB5 /* PLVLinkMicManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVLinkMicManager.m; sourceTree = "<group>"; };
		63C0CABD2636F5E000664CB5 /* PLVLinkMicGetTokenModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVLinkMicGetTokenModel.m; sourceTree = "<group>"; };
		63C0CABE2636F5E100664CB5 /* PLVLinkMicGetTokenModel+PrivateInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "PLVLinkMicGetTokenModel+PrivateInfo.h"; sourceTree = "<group>"; };
		63C0CABF2636F5E100664CB5 /* PLVLinkMicGetTokenModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVLinkMicGetTokenModel.h; sourceTree = "<group>"; };
		63C284BB266B71B200DBF84D /* PLVRTCStreamerMixUser.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVRTCStreamerMixUser.h; sourceTree = "<group>"; };
		63C284BC266B71B200DBF84D /* PLVRTCStreamerMixUser.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVRTCStreamerMixUser.m; sourceTree = "<group>"; };
		63CBC7372589B1D700E80897 /* PLVPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVPlayer.h; sourceTree = "<group>"; };
		63CBC7382589B1D700E80897 /* PLVPlayer+SubClassExtension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "PLVPlayer+SubClassExtension.h"; sourceTree = "<group>"; };
		63CBC7392589B1D700E80897 /* PLVPlayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVPlayer.m; sourceTree = "<group>"; };
		63F18F61251356FD0057A8B7 /* PLVPPTWebview.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVPPTWebview.h; sourceTree = "<group>"; };
		63F18F62251356FE0057A8B7 /* PLVPPTWebview.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVPPTWebview.m; sourceTree = "<group>"; };
		650919492B61F448009F50A0 /* PLVSocketManager+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVSocketManager+Private.h"; sourceTree = "<group>"; };
		651B05E92941E1DC00F78E8E /* PLVLoganUploadManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLoganUploadManager.h; sourceTree = "<group>"; };
		651B05EA2941E1DC00F78E8E /* PLVLoganUploadManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVLoganUploadManager.m; sourceTree = "<group>"; };
		6524811429519FC20039F997 /* PLVLoganManager+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVLoganManager+Private.h"; sourceTree = "<group>"; };
		6526517C2948803A007563AC /* PLVLoganUploadModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLoganUploadModel.h; sourceTree = "<group>"; };
		6526517D2948803A007563AC /* PLVLoganUploadModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVLoganUploadModel.m; sourceTree = "<group>"; };
		653B18CC28DC4FE500175F49 /* PLVLogan.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = PLVLogan.framework; path = ../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/PLVLogan.framework; sourceTree = "<group>"; };
		65560BC02A274E0F0006EEE9 /* PLVQosLoadingTracerModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVQosLoadingTracerModel.h; sourceTree = "<group>"; };
		65560BC12A274E0F0006EEE9 /* PLVQosLoadingTracerModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVQosLoadingTracerModel.m; sourceTree = "<group>"; };
		655AED8E2950694800955B33 /* PLVLoganManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLoganManager.h; sourceTree = "<group>"; };
		655AED8F2950694800955B33 /* PLVLoganManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVLoganManager.m; sourceTree = "<group>"; };
		658F9B142949B503003B031A /* PLVLoganUploadTokenModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLoganUploadTokenModel.h; sourceTree = "<group>"; };
		658F9B152949B503003B031A /* PLVLoganUploadTokenModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVLoganUploadTokenModel.m; sourceTree = "<group>"; };
		67116BA224E2A27400F4213F /* PLVPlaybackListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPlaybackListModel.h; sourceTree = "<group>"; };
		67116BA324E2A27400F4213F /* PLVPlaybackListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPlaybackListModel.m; sourceTree = "<group>"; };
		6727C36828084C6A00965384 /* PLVLiveVideoConfig+PrivateInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVLiveVideoConfig+PrivateInfo.h"; sourceTree = "<group>"; };
		6730B5F4212EA47E0086CFC0 /* PLVLiveVideoConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVLiveVideoConfig.h; sourceTree = "<group>"; };
		6730B5F5212EA47E0086CFC0 /* PLVLiveVideoConfig.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVLiveVideoConfig.m; sourceTree = "<group>"; };
		71BCA6F80C42AD25617A21C4 /* Pods-business-livescenes-PLVLiveScenesSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-livescenes-PLVLiveScenesSDK.debug.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-livescenes-PLVLiveScenesSDK/Pods-business-livescenes-PLVLiveScenesSDK.debug.xcconfig"; sourceTree = "<group>"; };
		84B4C2A83C9E73343C9AC159 /* Pods-business-cloudClass-PLVLiveScenesSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-cloudClass-PLVLiveScenesSDK.release.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-cloudClass-PLVLiveScenesSDK/Pods-business-cloudClass-PLVLiveScenesSDK.release.xcconfig"; sourceTree = "<group>"; };
		8F789513267B806900A7C2A7 /* PLVRewardMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVRewardMessage.h; sourceTree = "<group>"; };
		8F789514267B806900A7C2A7 /* PLVRewardMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVRewardMessage.m; sourceTree = "<group>"; };
		8F78D373269D7B360005AB76 /* PLVContainerWebViewBridge.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVContainerWebViewBridge.h; sourceTree = "<group>"; };
		8F78D374269D7B360005AB76 /* PLVContainerWebViewBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVContainerWebViewBridge.m; sourceTree = "<group>"; };
		8FB1BCF527D6022F0033A7BD /* PLVBeautyResourceManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBeautyResourceManager.h; sourceTree = "<group>"; };
		8FB1BCF627D6022F0033A7BD /* PLVBeautyResourceManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBeautyResourceManager.m; sourceTree = "<group>"; };
		97C1FEEFECFD45A38CF2A685 /* Pods-business-liveScenes-PLVLiveScenesSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-liveScenes-PLVLiveScenesSDK.release.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-liveScenes-PLVLiveScenesSDK/Pods-business-liveScenes-PLVLiveScenesSDK.release.xcconfig"; sourceTree = "<group>"; };
		988F60274207A623C5179416 /* Pods-common-sdk_common-PolyvCloudClassSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-common-sdk_common-PolyvCloudClassSDK.debug.xcconfig"; path = "../PolyvCloudClassDemo/Pods/Target Support Files/Pods-common-sdk_common-PolyvCloudClassSDK/Pods-common-sdk_common-PolyvCloudClassSDK.debug.xcconfig"; sourceTree = "<group>"; };
		A3F6D4753D3CD788A41BBA50 /* Pods-common-PolyvCloudClassSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-common-PolyvCloudClassSDK.debug.xcconfig"; path = "../PolyvCloudClassDemo/Pods/Target Support Files/Pods-common-PolyvCloudClassSDK/Pods-common-PolyvCloudClassSDK.debug.xcconfig"; sourceTree = "<group>"; };
		B090A7BDC0E2C04A6653BEBF /* Pods-PolyvCloudClassSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PolyvCloudClassSDK.debug.xcconfig"; path = "../PolyvCloudClassDemo/Pods/Target Support Files/Pods-PolyvCloudClassSDK/Pods-PolyvCloudClassSDK.debug.xcconfig"; sourceTree = "<group>"; };
		BF7CFC6E25BA64F70084C160 /* PLVCommodityModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PLVCommodityModel.h; sourceTree = "<group>"; };
		BF7CFC6F25BA64F70084C160 /* PLVCommodityModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PLVCommodityModel.m; sourceTree = "<group>"; };
		C887B9BFA0114406E26E13AF /* Pods-PolyvCloudClassSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PolyvCloudClassSDK.release.xcconfig"; path = "../PolyvCloudClassDemo/Pods/Target Support Files/Pods-PolyvCloudClassSDK/Pods-PolyvCloudClassSDK.release.xcconfig"; sourceTree = "<group>"; };
		D2050A47E169D3866E69A43D /* Pods-business-cloudClass-PolyvCloudClassSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-cloudClass-PolyvCloudClassSDK.release.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-cloudClass-PolyvCloudClassSDK/Pods-business-cloudClass-PolyvCloudClassSDK.release.xcconfig"; sourceTree = "<group>"; };
		F2F3F21C91998B8F2B4C0306 /* Pods-business-cloudClass-PLVLiveScenesSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-cloudClass-PLVLiveScenesSDK.debug.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-cloudClass-PLVLiveScenesSDK/Pods-business-cloudClass-PLVLiveScenesSDK.debug.xcconfig"; sourceTree = "<group>"; };
		FA1B8836283B74DA00BFDE7D /* PLVDownloadManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadManager.h; sourceTree = "<group>"; };
		FA1B8837283B74DA00BFDE7D /* PLVDownloadManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadManager.m; sourceTree = "<group>"; };
		FA1B883A283B774600BFDE7D /* PLVDownloadTaskInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadTaskInfo.h; sourceTree = "<group>"; };
		FA1B883B283B774600BFDE7D /* PLVDownloadTaskInfo.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVDownloadTaskInfo.mm; sourceTree = "<group>"; };
		FA1B883E283B7A5700BFDE7D /* PLVDownloadTaskInfo+PrivateExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVDownloadTaskInfo+PrivateExtension.h"; sourceTree = "<group>"; };
		FA1B8840283B7F7C00BFDE7D /* PLVDownloadPathManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadPathManager.h; sourceTree = "<group>"; };
		FA1B8841283B7F7C00BFDE7D /* PLVDownloadPathManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadPathManager.m; sourceTree = "<group>"; };
		FA1B8846283B847900BFDE7D /* PLVDownloadPlaybackTaskInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadPlaybackTaskInfo.h; sourceTree = "<group>"; };
		FA1B8847283B847900BFDE7D /* PLVDownloadPlaybackTaskInfo.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVDownloadPlaybackTaskInfo.mm; sourceTree = "<group>"; };
		FA1B884A283B866100BFDE7D /* PLVDownloadPlaybackTaskInfo+PrivateExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVDownloadPlaybackTaskInfo+PrivateExtension.h"; sourceTree = "<group>"; };
		FA1B884E283B86B500BFDE7D /* PLVDownloadDatabaseManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadDatabaseManager.h; sourceTree = "<group>"; };
		FA1B884F283B86B500BFDE7D /* PLVDownloadDatabaseManager.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = PLVDownloadDatabaseManager.mm; sourceTree = "<group>"; };
		FA1B8852283B885400BFDE7D /* PLVDownloadDatabaseManager+Playback.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVDownloadDatabaseManager+Playback.h"; sourceTree = "<group>"; };
		FA1B8854283B8C6D00BFDE7D /* PLVDownloadCommonOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadCommonOperation.h; sourceTree = "<group>"; };
		FA1B8855283B8C6D00BFDE7D /* PLVDownloadCommonOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadCommonOperation.m; sourceTree = "<group>"; };
		FA1B8858283B8C7C00BFDE7D /* PLVDownloadPlaybackOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadPlaybackOperation.h; sourceTree = "<group>"; };
		FA1B8859283B8C7C00BFDE7D /* PLVDownloadPlaybackOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadPlaybackOperation.m; sourceTree = "<group>"; };
		FA1B885C283B8D1600BFDE7D /* PLVDownloadURLSession.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadURLSession.h; sourceTree = "<group>"; };
		FA1B885D283B8D1600BFDE7D /* PLVDownloadURLSession.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadURLSession.m; sourceTree = "<group>"; };
		FA1B8860283B8D4400BFDE7D /* PLVDownloadDataTasker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVDownloadDataTasker.h; sourceTree = "<group>"; };
		FA1B8861283B8D4400BFDE7D /* PLVDownloadDataTasker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVDownloadDataTasker.m; sourceTree = "<group>"; };
		FA4FD38327B4B5BD004F4889 /* PLVLivePictureInPictureManager+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVLivePictureInPictureManager+Private.h"; sourceTree = "<group>"; };
		FA51FF3727B35B1D004C3B4B /* PLVLivePictureInPictureManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVLivePictureInPictureManager.h; sourceTree = "<group>"; };
		FA51FF3827B35B1D004C3B4B /* PLVLivePictureInPictureManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVLivePictureInPictureManager.m; sourceTree = "<group>"; };
		FA658DB6283C8D1C00AFBC4E /* PLVPlaybackVideoInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPlaybackVideoInfoModel.h; sourceTree = "<group>"; };
		FA658DB7283C8D1C00AFBC4E /* PLVPlaybackVideoInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPlaybackVideoInfoModel.m; sourceTree = "<group>"; };
		FA658DBC283C8F8800AFBC4E /* PLVPlaybackCacheManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVPlaybackCacheManager.h; sourceTree = "<group>"; };
		FA658DBD283C8F8800AFBC4E /* PLVPlaybackCacheManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVPlaybackCacheManager.m; sourceTree = "<group>"; };
		FAB4571C27957B6200816F8B /* PLVBeautyManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PLVBeautyManager.h; sourceTree = "<group>"; };
		FAB4571D27957B6200816F8B /* PLVBeautyManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PLVBeautyManager.m; sourceTree = "<group>"; };
		FAE8DE9027D0A1ED007343FD /* PLVBeautyManager+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "PLVBeautyManager+Private.h"; sourceTree = "<group>"; };
		FE0FCF5F60228C05697F446C /* Pods-business-livescenes-PLVLiveScenesSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-business-livescenes-PLVLiveScenesSDK.release.xcconfig"; path = "../PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/Pods/Target Support Files/Pods-business-livescenes-PLVLiveScenesSDK/Pods-business-livescenes-PLVLiveScenesSDK.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		22142F6E211309AD00062C6E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				36E8E43526DCE01400DAEE02 /* PLVBusinessSDK.framework in Frameworks */,
				36E8E43126DCE00900DAEE02 /* PLVFoundationSDK.framework in Frameworks */,
				31749BE6012A161865EF893E /* Pods_business_liveScenes_PLVLiveScenesSDK.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00466F8823B0A1DC00F968DC /* Log */ = {
			isa = PBXGroup;
			children = (
				0075EC842646331200116271 /* PLVConsoleLogger.h */,
				0075EC852646331200116271 /* PLVConsoleLogger.m */,
				00E48A332589B2100068C884 /* PLVViewLogCustomParam.h */,
				00E48A342589B2100068C884 /* PLVViewLogCustomParam.m */,
				002E89B823B1F1F7006F08D4 /* PLVWELogEventDefine.h */,
				002E89B923B1F1F7006F08D4 /* PLVWELogEventDefine.m */,
				00466F8923B0A20400F968DC /* PLVWLogReporterManager.h */,
				00466F8A23B0A20400F968DC /* PLVWLogReporterManager.m */,
				651B05E92941E1DC00F78E8E /* PLVLoganUploadManager.h */,
				651B05EA2941E1DC00F78E8E /* PLVLoganUploadManager.m */,
				6526517C2948803A007563AC /* PLVLoganUploadModel.h */,
				6526517D2948803A007563AC /* PLVLoganUploadModel.m */,
				658F9B142949B503003B031A /* PLVLoganUploadTokenModel.h */,
				658F9B152949B503003B031A /* PLVLoganUploadTokenModel.m */,
				655AED8E2950694800955B33 /* PLVLoganManager.h */,
				655AED8F2950694800955B33 /* PLVLoganManager.m */,
				6524811429519FC20039F997 /* PLVLoganManager+Private.h */,
			);
			path = Log;
			sourceTree = "<group>";
		};
		00485BE0256CB89500616C65 /* Model */ = {
			isa = PBXGroup;
			children = (
				00485BE9256CE00400616C65 /* PLVSpeakMessage.h */,
				00485BEA256CE00400616C65 /* PLVSpeakMessage.m */,
				00485C0A256E52CC00616C65 /* PLVQuoteMessage.h */,
				00485C09256E52CC00616C65 /* PLVQuoteMessage.m */,
				00485BED256CE01700616C65 /* PLVImageMessage.h */,
				00485BEE256CE01700616C65 /* PLVImageMessage.m */,
				36BACEF826959E0C00C8CA31 /* PLVImageEmotionMessage.h */,
				36BACEF926959E0C00C8CA31 /* PLVImageEmotionMessage.m */,
				00485BF5256CE10400616C65 /* PLVCustomMessage.h */,
				00485BF6256CE10400616C65 /* PLVCustomMessage.m */,
				8F789513267B806900A7C2A7 /* PLVRewardMessage.h */,
				8F789514267B806900A7C2A7 /* PLVRewardMessage.m */,
				006002A1284DDDC800D5FD51 /* PLVPlaybackMsgIndex.h */,
				006002A2284DDDC800D5FD51 /* PLVPlaybackMsgIndex.m */,
				006002A5284DDEAF00D5FD51 /* PLVPlaybackMsgUser.h */,
				006002A6284DDEAF00D5FD51 /* PLVPlaybackMsgUser.m */,
				006002A9284DDF0400D5FD51 /* PLVPlaybackMessage.h */,
				006002AA284DDF0400D5FD51 /* PLVPlaybackMessage.m */,
				4D46919328868CE000043A4E /* PLVFileMessage.h */,
				4D46919428868CE000043A4E /* PLVFileMessage.m */,
				21DDF88B2966D01D00A00202 /* PLVRedpackMessage.h */,
				21DDF88C2966D01D00A00202 /* PLVRedpackMessage.m */,
				369C2A542C339B9A00389D5B /* PLVSpeakTopMessage.h */,
				369C2A552C339B9A00389D5B /* PLVSpeakTopMessage.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		0053BCF223F25D5B00BD03CB /* ErrorCode */ = {
			isa = PBXGroup;
			children = (
				0003CCA423F25E4700DB26B4 /* PLVWErrorManager.h */,
				0003CCA523F25E4700DB26B4 /* PLVWErrorManager.m */,
			);
			path = ErrorCode;
			sourceTree = "<group>";
		};
		007279DE25CBE5FB001CE398 /* JSBridge */ = {
			isa = PBXGroup;
			children = (
				007279ED25CBFA91001CE398 /* PLVWebViewBridge.h */,
				007279EC25CBFA91001CE398 /* PLVWebViewBridge.m */,
				8F78D373269D7B360005AB76 /* PLVContainerWebViewBridge.h */,
				8F78D374269D7B360005AB76 /* PLVContainerWebViewBridge.m */,
				3674693E2769D2F700235819 /* PLVInteractWebViewBridge.h */,
				3674693D2769D2F700235819 /* PLVInteractWebViewBridge.m */,
				36EB53362831EED000DEF138 /* PLVProductWebViewBridge.h */,
				36EB53372831EED000DEF138 /* PLVProductWebViewBridge.m */,
				362E3B5228F426D200384FC6 /* PLVStreamerCommodityWebViewBridge.h */,
				362E3B5328F426D200384FC6 /* PLVStreamerCommodityWebViewBridge.m */,
				36237D322772D8DB00C7831A /* PLVSocketWebViewBridge.h */,
				36237D332772D8DB00C7831A /* PLVSocketWebViewBridge.m */,
				36A203412BF4B6E5009BBC3A /* PLVStreamerInteractWebViewBridge.h */,
				36A203422BF4B6E5009BBC3A /* PLVStreamerInteractWebViewBridge.m */,
				364FBDBE2A2D79110087DE53 /* PLVTuWenWebViewBridge.h */,
				364FBDBF2A2D79110087DE53 /* PLVTuWenWebViewBridge.m */,
				367AD2AD2B8DD972006244FB /* PLVQAWebViewBridge.h */,
				367AD2AE2B8DD972006244FB /* PLVQAWebViewBridge.m */,
			);
			path = JSBridge;
			sourceTree = "<group>";
		};
		00C75BB7261C3C7400F141C5 /* Upload */ = {
			isa = PBXGroup;
			children = (
				00C75BBD261C3C7400F141C5 /* Model */,
				00C75BBA261C3C7400F141C5 /* PLVDocumentUploadClient.h */,
				00C75BC2261C3C7400F141C5 /* PLVDocumentUploadClient.m */,
				00C75BB8261C3C7400F141C5 /* PLVDocumentUploader.h */,
				00C75BBB261C3C7400F141C5 /* PLVDocumentUploader.m */,
				00C75BBC261C3C7400F141C5 /* PLVFileManager.h */,
				00C75BB9261C3C7400F141C5 /* PLVFileManager.m */,
			);
			path = Upload;
			sourceTree = "<group>";
		};
		00C75BBD261C3C7400F141C5 /* Model */ = {
			isa = PBXGroup;
			children = (
				00C75BBE261C3C7400F141C5 /* PLVDocumentTokenModel.h */,
				00C75BC0261C3C7400F141C5 /* PLVDocumentTokenModel.m */,
				00C75BC1261C3C7400F141C5 /* PLVDocumentUploadModel.h */,
				00C75BBF261C3C7400F141C5 /* PLVDocumentUploadModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		21C23DF62586002900F2A5D3 /* Socket */ = {
			isa = PBXGroup;
			children = (
				21C23DF7258602C000F2A5D3 /* PLVSocketManager.h */,
				650919492B61F448009F50A0 /* PLVSocketManager+Private.h */,
				21C23DF8258602C000F2A5D3 /* PLVSocketManager.m */,
				21C23DFF2586432100F2A5D3 /* PLVSocketEventDefine.h */,
				21C23E002586432100F2A5D3 /* PLVSocketEventDefine.m */,
			);
			path = Socket;
			sourceTree = "<group>";
		};
		22142F68211309AD00062C6E = {
			isa = PBXGroup;
			children = (
				22142F74211309AD00062C6E /* PolyvCloudClassSDK */,
				22142F73211309AD00062C6E /* Products */,
				558B00072A806E964EA770EC /* Pods */,
				DF162DE7DDA0D3C78DA52D60 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		22142F73211309AD00062C6E /* Products */ = {
			isa = PBXGroup;
			children = (
				22142F72211309AD00062C6E /* PLVLiveScenesSDK.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		22142F74211309AD00062C6E /* PolyvCloudClassSDK */ = {
			isa = PBXGroup;
			children = (
				22142F75211309AD00062C6E /* PLVLiveScenesSDK.h */,
				22142F76211309AD00062C6E /* Info.plist */,
				22142FC32113119600062C6E /* PLVLiveDefine.h */,
				36E8E1A526DCD5EC00DAEE02 /* PLVLiveConstants */,
				2917AB6826C0DB7000054BCF /* Class */,
				6395DE13261F438C000D0A44 /* RTC */,
				FAB4571B27957B3100816F8B /* Beauty */,
				6719EDFE24DA4A9B0068A3E9 /* PublicConfig */,
				22142FD12113119600062C6E /* Player */,
				0053BCF223F25D5B00BD03CB /* ErrorCode */,
				00466F8823B0A1DC00F968DC /* Log */,
				6790C27524226F2F004B3AC4 /* PPT */,
				007279DE25CBE5FB001CE398 /* JSBridge */,
				FA658DBB283C8F7100AFBC4E /* Playback */,
				FA1B8831283B711000BFDE7D /* Download */,
				00C75BB7261C3C7400F141C5 /* Upload */,
				671ED4AE24D2A70F008D2976 /* Chatroom */,
				21C23DF62586002900F2A5D3 /* Socket */,
				6790C27624226F85004B3AC4 /* Interaction */,
				22142FC12113119600062C6E /* Network */,
			);
			path = PolyvCloudClassSDK;
			sourceTree = "<group>";
		};
		22142FC12113119600062C6E /* Network */ = {
			isa = PBXGroup;
			children = (
				4D3E417727CDCA5000009706 /* LiveHttpDns */,
				22142FC82113119600062C6E /* LiveModels */,
				22142FC22113119600062C6E /* LiveAPI */,
			);
			path = Network;
			sourceTree = "<group>";
		};
		22142FC22113119600062C6E /* LiveAPI */ = {
			isa = PBXGroup;
			children = (
				0060354A268D577E00147A78 /* PLVLiveVClassAPI.h */,
				0060354B268D577E00147A78 /* PLVLiveVClassAPI.m */,
				006398D026C0D9AF0024D084 /* PLVLiveVClassAPI+Private.h */,
				228FFC0921997AA5007E8413 /* PLVLiveVideoAPI.h */,
				228FFC0821997AA5007E8413 /* PLVLiveVideoAPI.m */,
				22142FC72113119600062C6E /* PLVLivePrivateAPI.h */,
				22142FC42113119600062C6E /* PLVLivePrivateAPI.m */,
				636144D42339CB1C002A44F3 /* PLVLiveAPIUtils.h */,
				636144D52339CB1C002A44F3 /* PLVLiveAPIUtils.m */,
				00340F4B22E1BB9700336ACE /* PLVImageUpload.h */,
				00340F4C22E1BB9700336ACE /* PLVImageUpload.m */,
			);
			path = LiveAPI;
			sourceTree = "<group>";
		};
		22142FC82113119600062C6E /* LiveModels */ = {
			isa = PBXGroup;
			children = (
				BF7CFC6E25BA64F70084C160 /* PLVCommodityModel.h */,
				BF7CFC6F25BA64F70084C160 /* PLVCommodityModel.m */,
				22142FD02113119600062C6E /* PLVLiveVideoChannelMenuInfo.h */,
				22142FCB2113119600062C6E /* PLVLiveVideoChannelMenuInfo.m */,
				67116BA224E2A27400F4213F /* PLVPlaybackListModel.h */,
				67116BA324E2A27400F4213F /* PLVPlaybackListModel.m */,
				6390DBBC258AFD1D0031A29B /* PLVChannelInfoModel.h */,
				6390DBBD258AFD1E0031A29B /* PLVChannelInfoModel.m */,
				6390DBC0258B47690031A29B /* PLVChannelInfoModel+PrivateInfo.h */,
				4D1E74C7275DC1BF008B7D3A /* PLVLivePlaybackSectionModel.h */,
				4D1E74C8275DC1BF008B7D3A /* PLVLivePlaybackSectionModel.m */,
				4DE9DD8C28194812003EC5BF /* PLVChannelPlaybackInfoModel.h */,
				4DE9DD8D28194812003EC5BF /* PLVChannelPlaybackInfoModel.m */,
			);
			path = LiveModels;
			sourceTree = "<group>";
		};
		22142FD12113119600062C6E /* Player */ = {
			isa = PBXGroup;
			children = (
				363D84E12BECDD9000BDCE36 /* View */,
				FA658DB5283C8D0900AFBC4E /* Model */,
				63CBC7362589B1D700E80897 /* BasePlayer */,
				633E4254257B91090039D35D /* PLVLivePlayer.h */,
				633E4255257B91090039D35D /* PLVLivePlayer.m */,
				633E4258257B91220039D35D /* PLVLivePlaybackPlayer.h */,
				633E4259257B91220039D35D /* PLVLivePlaybackPlayer.m */,
				FA51FF3727B35B1D004C3B4B /* PLVLivePictureInPictureManager.h */,
				FA51FF3827B35B1D004C3B4B /* PLVLivePictureInPictureManager.m */,
				3607013C2BA14F7900E1E1B9 /* PLVLivePlayerPictureInPictureProtocol.h */,
				FA4FD38327B4B5BD004F4889 /* PLVLivePictureInPictureManager+Private.h */,
				043FD13028D078260005FA82 /* PLVPublicStreamPlayer.h */,
				043FD13128D078270005FA82 /* PLVPublicStreamPlayer.m */,
			);
			path = Player;
			sourceTree = "<group>";
		};
		2917AB6826C0DB7000054BCF /* Class */ = {
			isa = PBXGroup;
			children = (
				2917AB6926C0DB8600054BCF /* PLVChannelClassManager.h */,
				2917AB6A26C0DB8600054BCF /* PLVChannelClassManager.m */,
				0067AEA22727E73000CFA7D0 /* PLVHiClassManager.h */,
				0067AEA32727E73000CFA7D0 /* PLVHiClassManager.m */,
			);
			path = Class;
			sourceTree = "<group>";
		};
		363D84E12BECDD9000BDCE36 /* View */ = {
			isa = PBXGroup;
			children = (
				363D84E32BECDD9000BDCE36 /* PLVLiveSampleBufferDisplayView.h */,
				363D84E22BECDD9000BDCE36 /* PLVLiveSampleBufferDisplayView.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		36E8E1A526DCD5EC00DAEE02 /* PLVLiveConstants */ = {
			isa = PBXGroup;
			children = (
				36E8E1A626DCD62200DAEE02 /* PLVLiveConstants.h */,
				36E8E1A726DCD62200DAEE02 /* PLVLiveConstants.m */,
			);
			path = PLVLiveConstants;
			sourceTree = "<group>";
		};
		4D3E417727CDCA5000009706 /* LiveHttpDns */ = {
			isa = PBXGroup;
			children = (
				4D3E417827CDCAB300009706 /* PLVLiveHttpDnsManager.h */,
				4D3E417927CDCAB300009706 /* PLVLiveHttpDnsManager.m */,
			);
			path = LiveHttpDns;
			sourceTree = "<group>";
		};
		558B00072A806E964EA770EC /* Pods */ = {
			isa = PBXGroup;
			children = (
				B090A7BDC0E2C04A6653BEBF /* Pods-PolyvCloudClassSDK.debug.xcconfig */,
				C887B9BFA0114406E26E13AF /* Pods-PolyvCloudClassSDK.release.xcconfig */,
				A3F6D4753D3CD788A41BBA50 /* Pods-common-PolyvCloudClassSDK.debug.xcconfig */,
				356A9E9A1185509BDF604779 /* Pods-common-PolyvCloudClassSDK.release.xcconfig */,
				988F60274207A623C5179416 /* Pods-common-sdk_common-PolyvCloudClassSDK.debug.xcconfig */,
				506C38BB4D862A9456F4C096 /* Pods-common-sdk_common-PolyvCloudClassSDK.release.xcconfig */,
				24668DAA6C51206A7C65EF6E /* Pods-business-cloudClass-PolyvCloudClassSDK.debug.xcconfig */,
				D2050A47E169D3866E69A43D /* Pods-business-cloudClass-PolyvCloudClassSDK.release.xcconfig */,
				F2F3F21C91998B8F2B4C0306 /* Pods-business-cloudClass-PLVLiveScenesSDK.debug.xcconfig */,
				84B4C2A83C9E73343C9AC159 /* Pods-business-cloudClass-PLVLiveScenesSDK.release.xcconfig */,
				71BCA6F80C42AD25617A21C4 /* Pods-business-livescenes-PLVLiveScenesSDK.debug.xcconfig */,
				FE0FCF5F60228C05697F446C /* Pods-business-livescenes-PLVLiveScenesSDK.release.xcconfig */,
				3A1280D9E678C44B22A923FA /* Pods-business-liveScenes-PLVLiveScenesSDK.debug.xcconfig */,
				97C1FEEFECFD45A38CF2A685 /* Pods-business-liveScenes-PLVLiveScenesSDK.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		6395DE13261F438C000D0A44 /* RTC */ = {
			isa = PBXGroup;
			children = (
				213973D529FA45000052F584 /* PLVRTCStatistics.h */,
				213973D429FA45000052F584 /* PLVRTCStatistics.m */,
				63C0CABF2636F5E100664CB5 /* PLVLinkMicGetTokenModel.h */,
				63C0CABD2636F5E000664CB5 /* PLVLinkMicGetTokenModel.m */,
				63C0CABE2636F5E100664CB5 /* PLVLinkMicGetTokenModel+PrivateInfo.h */,
				6395DE15261F438C000D0A44 /* RTCStreamer */,
				6395DE18261F438C000D0A44 /* RTCLinkMic */,
			);
			path = RTC;
			sourceTree = "<group>";
		};
		6395DE15261F438C000D0A44 /* RTCStreamer */ = {
			isa = PBXGroup;
			children = (
				63C0CAB62636F5D400664CB5 /* PLVRTCStreamerManager.h */,
				63C0CAB52636F5D400664CB5 /* PLVRTCStreamerManager.m */,
				63C284BB266B71B200DBF84D /* PLVRTCStreamerMixUser.h */,
				63C284BC266B71B200DBF84D /* PLVRTCStreamerMixUser.m */,
			);
			path = RTCStreamer;
			sourceTree = "<group>";
		};
		6395DE18261F438C000D0A44 /* RTCLinkMic */ = {
			isa = PBXGroup;
			children = (
				63C0CAB92636F5DB00664CB5 /* PLVLinkMicManager.h */,
				63C0CABA2636F5DB00664CB5 /* PLVLinkMicManager.m */,
			);
			path = RTCLinkMic;
			sourceTree = "<group>";
		};
		63CBC7362589B1D700E80897 /* BasePlayer */ = {
			isa = PBXGroup;
			children = (
				63CBC7372589B1D700E80897 /* PLVPlayer.h */,
				63CBC7392589B1D700E80897 /* PLVPlayer.m */,
				63CBC7382589B1D700E80897 /* PLVPlayer+SubClassExtension.h */,
			);
			path = BasePlayer;
			sourceTree = "<group>";
		};
		6719EDFE24DA4A9B0068A3E9 /* PublicConfig */ = {
			isa = PBXGroup;
			children = (
				6730B5F4212EA47E0086CFC0 /* PLVLiveVideoConfig.h */,
				6730B5F5212EA47E0086CFC0 /* PLVLiveVideoConfig.m */,
				6727C36828084C6A00965384 /* PLVLiveVideoConfig+PrivateInfo.h */,
			);
			path = PublicConfig;
			sourceTree = "<group>";
		};
		671ED4AE24D2A70F008D2976 /* Chatroom */ = {
			isa = PBXGroup;
			children = (
				00485BF1256CE03900616C65 /* PLVChatroomManager.h */,
				00485BF2256CE03900616C65 /* PLVChatroomManager.m */,
				006002AD284DE4A800D5FD51 /* PLVPlaybackMessageManager.h */,
				006002AE284DE4A800D5FD51 /* PLVPlaybackMessageManager.m */,
				00485BE0256CB89500616C65 /* Model */,
			);
			path = Chatroom;
			sourceTree = "<group>";
		};
		6790C27524226F2F004B3AC4 /* PPT */ = {
			isa = PBXGroup;
			children = (
				63F18F61251356FD0057A8B7 /* PLVPPTWebview.h */,
				63F18F62251356FE0057A8B7 /* PLVPPTWebview.m */,
			);
			path = PPT;
			sourceTree = "<group>";
		};
		6790C27624226F85004B3AC4 /* Interaction */ = {
			isa = PBXGroup;
			children = (
				63976730250A20C100D1F16B /* PLVInteractWebview.h */,
				63976731250A20C100D1F16B /* PLVInteractWebview.m */,
				63976738250A365900D1F16B /* PLVInteractBaseApp.h */,
				63976739250A365900D1F16B /* PLVInteractBaseApp.m */,
			);
			path = Interaction;
			sourceTree = "<group>";
		};
		DF162DE7DDA0D3C78DA52D60 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				653B18CC28DC4FE500175F49 /* PLVLogan.framework */,
				36E8E43426DCE01400DAEE02 /* PLVBusinessSDK.framework */,
				36E8E43026DCE00900DAEE02 /* PLVFoundationSDK.framework */,
				22D7915921A68E0400835ADD /* PolyvBusinessSDK.framework */,
				22D7915B21A68E0400835ADD /* PolyvFoundationSDK.framework */,
				2895D49A8ECCE607CB987E94 /* Pods_business_liveScenes_PLVLiveScenesSDK.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		FA1B8831283B711000BFDE7D /* Download */ = {
			isa = PBXGroup;
			children = (
				FA1B8832283B747400BFDE7D /* DownloadCore */,
				FA1B8833283B748000BFDE7D /* DownloadPath */,
				FA1B8834283B748C00BFDE7D /* DownloadModel */,
				FA1B8835283B74A000BFDE7D /* DownloadDatabase */,
			);
			path = Download;
			sourceTree = "<group>";
		};
		FA1B8832283B747400BFDE7D /* DownloadCore */ = {
			isa = PBXGroup;
			children = (
				FA1B8836283B74DA00BFDE7D /* PLVDownloadManager.h */,
				FA1B8837283B74DA00BFDE7D /* PLVDownloadManager.m */,
				FA1B8860283B8D4400BFDE7D /* PLVDownloadDataTasker.h */,
				FA1B8861283B8D4400BFDE7D /* PLVDownloadDataTasker.m */,
				FA1B885C283B8D1600BFDE7D /* PLVDownloadURLSession.h */,
				FA1B885D283B8D1600BFDE7D /* PLVDownloadURLSession.m */,
				FA1B8854283B8C6D00BFDE7D /* PLVDownloadCommonOperation.h */,
				FA1B8855283B8C6D00BFDE7D /* PLVDownloadCommonOperation.m */,
				FA1B8858283B8C7C00BFDE7D /* PLVDownloadPlaybackOperation.h */,
				FA1B8859283B8C7C00BFDE7D /* PLVDownloadPlaybackOperation.m */,
			);
			path = DownloadCore;
			sourceTree = "<group>";
		};
		FA1B8833283B748000BFDE7D /* DownloadPath */ = {
			isa = PBXGroup;
			children = (
				FA1B8840283B7F7C00BFDE7D /* PLVDownloadPathManager.h */,
				FA1B8841283B7F7C00BFDE7D /* PLVDownloadPathManager.m */,
			);
			path = DownloadPath;
			sourceTree = "<group>";
		};
		FA1B8834283B748C00BFDE7D /* DownloadModel */ = {
			isa = PBXGroup;
			children = (
				FA1B883A283B774600BFDE7D /* PLVDownloadTaskInfo.h */,
				FA1B883B283B774600BFDE7D /* PLVDownloadTaskInfo.mm */,
				FA1B883E283B7A5700BFDE7D /* PLVDownloadTaskInfo+PrivateExtension.h */,
				FA1B8846283B847900BFDE7D /* PLVDownloadPlaybackTaskInfo.h */,
				FA1B8847283B847900BFDE7D /* PLVDownloadPlaybackTaskInfo.mm */,
				FA1B884A283B866100BFDE7D /* PLVDownloadPlaybackTaskInfo+PrivateExtension.h */,
			);
			path = DownloadModel;
			sourceTree = "<group>";
		};
		FA1B8835283B74A000BFDE7D /* DownloadDatabase */ = {
			isa = PBXGroup;
			children = (
				FA1B884E283B86B500BFDE7D /* PLVDownloadDatabaseManager.h */,
				FA1B884F283B86B500BFDE7D /* PLVDownloadDatabaseManager.mm */,
				FA1B8852283B885400BFDE7D /* PLVDownloadDatabaseManager+Playback.h */,
			);
			path = DownloadDatabase;
			sourceTree = "<group>";
		};
		FA658DB5283C8D0900AFBC4E /* Model */ = {
			isa = PBXGroup;
			children = (
				FA658DB6283C8D1C00AFBC4E /* PLVPlaybackVideoInfoModel.h */,
				FA658DB7283C8D1C00AFBC4E /* PLVPlaybackVideoInfoModel.m */,
				043F13F728E199AC003E72F4 /* PLVPublicStreamGetInfoModel.h */,
				043F13F828E199AC003E72F4 /* PLVPublicStreamGetInfoModel.m */,
				043F13FB28E19F43003E72F4 /* PLVPublicStreamGetInfoModel+PrivateInfo.h */,
				65560BC02A274E0F0006EEE9 /* PLVQosLoadingTracerModel.h */,
				65560BC12A274E0F0006EEE9 /* PLVQosLoadingTracerModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		FA658DBB283C8F7100AFBC4E /* Playback */ = {
			isa = PBXGroup;
			children = (
				FA658DBC283C8F8800AFBC4E /* PLVPlaybackCacheManager.h */,
				FA658DBD283C8F8800AFBC4E /* PLVPlaybackCacheManager.m */,
			);
			path = Playback;
			sourceTree = "<group>";
		};
		FAB4571B27957B3100816F8B /* Beauty */ = {
			isa = PBXGroup;
			children = (
				FAB4571C27957B6200816F8B /* PLVBeautyManager.h */,
				FAB4571D27957B6200816F8B /* PLVBeautyManager.m */,
				FAE8DE9027D0A1ED007343FD /* PLVBeautyManager+Private.h */,
				8FB1BCF527D6022F0033A7BD /* PLVBeautyResourceManager.h */,
				8FB1BCF627D6022F0033A7BD /* PLVBeautyResourceManager.m */,
			);
			path = Beauty;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		22142F6F211309AD00062C6E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				36237D342772D8DB00C7831A /* PLVSocketWebViewBridge.h in Headers */,
				3607013D2BA14F7900E1E1B9 /* PLVLivePlayerPictureInPictureProtocol.h in Headers */,
				363D84E52BECDD9000BDCE36 /* PLVLiveSampleBufferDisplayView.h in Headers */,
				367469402769D2F700235819 /* PLVInteractWebViewBridge.h in Headers */,
				043F13FC28E19F43003E72F4 /* PLVPublicStreamGetInfoModel+PrivateInfo.h in Headers */,
				367AD2AF2B8DD972006244FB /* PLVQAWebViewBridge.h in Headers */,
				006002AB284DDF0400D5FD51 /* PLVPlaybackMessage.h in Headers */,
				007279EF25CBFA91001CE398 /* PLVWebViewBridge.h in Headers */,
				22142F77211309AD00062C6E /* PLVLiveScenesSDK.h in Headers */,
				65560BC22A274E0F0006EEE9 /* PLVQosLoadingTracerModel.h in Headers */,
				22142FD42113119600062C6E /* PLVLiveDefine.h in Headers */,
				6730B5F6212EA47E0086CFC0 /* PLVLiveVideoConfig.h in Headers */,
				FA1B883F283B7A5700BFDE7D /* PLVDownloadTaskInfo+PrivateExtension.h in Headers */,
				00466F8B23B0A20400F968DC /* PLVWLogReporterManager.h in Headers */,
				6509194A2B61F448009F50A0 /* PLVSocketManager+Private.h in Headers */,
				006002AF284DE4A800D5FD51 /* PLVPlaybackMessageManager.h in Headers */,
				6526517E2948803A007563AC /* PLVLoganUploadModel.h in Headers */,
				63F18F63251356FE0057A8B7 /* PLVPPTWebview.h in Headers */,
				4DE9DD8E28194812003EC5BF /* PLVChannelPlaybackInfoModel.h in Headers */,
				00E48A352589B2100068C884 /* PLVViewLogCustomParam.h in Headers */,
				362E3B5428F426D200384FC6 /* PLVStreamerCommodityWebViewBridge.h in Headers */,
				22142FE02113119600062C6E /* PLVLiveVideoChannelMenuInfo.h in Headers */,
				636144D62339CB1C002A44F3 /* PLVLiveAPIUtils.h in Headers */,
				FA658DBE283C8F8800AFBC4E /* PLVPlaybackCacheManager.h in Headers */,
				FA658DB8283C8D1C00AFBC4E /* PLVPlaybackVideoInfoModel.h in Headers */,
				00485BEF256CE01700616C65 /* PLVImageMessage.h in Headers */,
				364FBDC02A2D79110087DE53 /* PLVTuWenWebViewBridge.h in Headers */,
				228FFC0B21997AA5007E8413 /* PLVLiveVideoAPI.h in Headers */,
				FA1B884B283B866100BFDE7D /* PLVDownloadPlaybackTaskInfo+PrivateExtension.h in Headers */,
				63CBC73A2589B1D700E80897 /* PLVPlayer.h in Headers */,
				FA4FD38427B4B5BD004F4889 /* PLVLivePictureInPictureManager+Private.h in Headers */,
				FAE8DE9127D0A1ED007343FD /* PLVBeautyManager+Private.h in Headers */,
				006398D126C1122D0024D084 /* PLVLiveVClassAPI+Private.h in Headers */,
				00485BF3256CE03900616C65 /* PLVChatroomManager.h in Headers */,
				FA1B8850283B86B500BFDE7D /* PLVDownloadDatabaseManager.h in Headers */,
				63CBC73B2589B1D700E80897 /* PLVPlayer+SubClassExtension.h in Headers */,
				00C75BD9261C3E3100F141C5 /* PLVLivePrivateAPI.h in Headers */,
				655AED902950694800955B33 /* PLVLoganManager.h in Headers */,
				633E4256257B91090039D35D /* PLVLivePlayer.h in Headers */,
				FA51FF3927B35B1D004C3B4B /* PLVLivePictureInPictureManager.h in Headers */,
				63C284BD266B71B200DBF84D /* PLVRTCStreamerMixUser.h in Headers */,
				FA1B8853283B885400BFDE7D /* PLVDownloadDatabaseManager+Playback.h in Headers */,
				00485C0C256E52CC00616C65 /* PLVQuoteMessage.h in Headers */,
				63C0CAC12636F5E100664CB5 /* PLVLinkMicGetTokenModel+PrivateInfo.h in Headers */,
				6524811529519FC20039F997 /* PLVLoganManager+Private.h in Headers */,
				63C0CAC22636F5E100664CB5 /* PLVLinkMicGetTokenModel.h in Headers */,
				651B05EB2941E1DC00F78E8E /* PLVLoganUploadManager.h in Headers */,
				00C75BC5261C3C7400F141C5 /* PLVDocumentUploadClient.h in Headers */,
				FA1B8838283B74DA00BFDE7D /* PLVDownloadManager.h in Headers */,
				FA1B8862283B8D4400BFDE7D /* PLVDownloadDataTasker.h in Headers */,
				2917AB6B26C0DB8600054BCF /* PLVChannelClassManager.h in Headers */,
				00485BF7256CE10400616C65 /* PLVCustomMessage.h in Headers */,
				63C0CAB82636F5D400664CB5 /* PLVRTCStreamerManager.h in Headers */,
				FA1B8842283B7F7C00BFDE7D /* PLVDownloadPathManager.h in Headers */,
				002E89BA23B1F1F7006F08D4 /* PLVWELogEventDefine.h in Headers */,
				FAB4571E27957B6200816F8B /* PLVBeautyManager.h in Headers */,
				6397673A250A365900D1F16B /* PLVInteractBaseApp.h in Headers */,
				00C75BC7261C3C7400F141C5 /* PLVFileManager.h in Headers */,
				00485BEB256CE00400616C65 /* PLVSpeakMessage.h in Headers */,
				63976732250A20C100D1F16B /* PLVInteractWebview.h in Headers */,
				21C23E012586432100F2A5D3 /* PLVSocketEventDefine.h in Headers */,
				67116BA424E2A27400F4213F /* PLVPlaybackListModel.h in Headers */,
				00340F4D22E1BB9700336ACE /* PLVImageUpload.h in Headers */,
				BF7CFC7025BA64F70084C160 /* PLVCommodityModel.h in Headers */,
				8F789515267B806900A7C2A7 /* PLVRewardMessage.h in Headers */,
				36BACEFA26959E0C00C8CA31 /* PLVImageEmotionMessage.h in Headers */,
				36E8E1A826DCD62200DAEE02 /* PLVLiveConstants.h in Headers */,
				8FB1BCF727D6022F0033A7BD /* PLVBeautyResourceManager.h in Headers */,
				36A203432BF4B6E5009BBC3A /* PLVStreamerInteractWebViewBridge.h in Headers */,
				6390DBBE258AFD1E0031A29B /* PLVChannelInfoModel.h in Headers */,
				8F78D375269D7B360005AB76 /* PLVContainerWebViewBridge.h in Headers */,
				4D1E74C9275DC1BF008B7D3A /* PLVLivePlaybackSectionModel.h in Headers */,
				369C2A562C339B9A00389D5B /* PLVSpeakTopMessage.h in Headers */,
				36EB53382831EED000DEF138 /* PLVProductWebViewBridge.h in Headers */,
				FA1B885A283B8C7C00BFDE7D /* PLVDownloadPlaybackOperation.h in Headers */,
				0075EC862646331200116271 /* PLVConsoleLogger.h in Headers */,
				FA1B8848283B847900BFDE7D /* PLVDownloadPlaybackTaskInfo.h in Headers */,
				006002A3284DDDC800D5FD51 /* PLVPlaybackMsgIndex.h in Headers */,
				633E425A257B91220039D35D /* PLVLivePlaybackPlayer.h in Headers */,
				0060354C268D577E00147A78 /* PLVLiveVClassAPI.h in Headers */,
				4D46919528868CE000043A4E /* PLVFileMessage.h in Headers */,
				00C75BCB261C3C7400F141C5 /* PLVDocumentUploadModel.h in Headers */,
				658F9B162949B503003B031A /* PLVLoganUploadTokenModel.h in Headers */,
				00C75BC3261C3C7400F141C5 /* PLVDocumentUploader.h in Headers */,
				21DDF88D2966D01D00A00202 /* PLVRedpackMessage.h in Headers */,
				00C75BC8261C3C7400F141C5 /* PLVDocumentTokenModel.h in Headers */,
				FA1B885E283B8D1600BFDE7D /* PLVDownloadURLSession.h in Headers */,
				6390DBC1258B47690031A29B /* PLVChannelInfoModel+PrivateInfo.h in Headers */,
				043F13F928E199AC003E72F4 /* PLVPublicStreamGetInfoModel.h in Headers */,
				63C0CABB2636F5DB00664CB5 /* PLVLinkMicManager.h in Headers */,
				0067AEA42727E73000CFA7D0 /* PLVHiClassManager.h in Headers */,
				213973D729FA45000052F584 /* PLVRTCStatistics.h in Headers */,
				6727C36928084C6A00965384 /* PLVLiveVideoConfig+PrivateInfo.h in Headers */,
				FA1B883C283B774600BFDE7D /* PLVDownloadTaskInfo.h in Headers */,
				21C23DF9258602C000F2A5D3 /* PLVSocketManager.h in Headers */,
				043FD13228D078270005FA82 /* PLVPublicStreamPlayer.h in Headers */,
				0003CCA623F25E4700DB26B4 /* PLVWErrorManager.h in Headers */,
				FA1B8856283B8C6D00BFDE7D /* PLVDownloadCommonOperation.h in Headers */,
				006002A7284DDEAF00D5FD51 /* PLVPlaybackMsgUser.h in Headers */,
				4D3E417A27CDCAB300009706 /* PLVLiveHttpDnsManager.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		22142F71211309AD00062C6E /* PLVLiveScenesSDK */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 22142F7A211309AD00062C6E /* Build configuration list for PBXNativeTarget "PLVLiveScenesSDK" */;
			buildPhases = (
				5A3AF5C4381B2949EA9279B6 /* [CP] Check Pods Manifest.lock */,
				22142F6D211309AD00062C6E /* Sources */,
				22142F6E211309AD00062C6E /* Frameworks */,
				22142F6F211309AD00062C6E /* Headers */,
				22142F70211309AD00062C6E /* Resources */,
				D5EA5EC83228100751923F8E /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PLVLiveScenesSDK;
			productName = PolyvLiveSDK;
			productReference = 22142F72211309AD00062C6E /* PLVLiveScenesSDK.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		22142F69211309AD00062C6E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1000;
				ORGANIZATIONNAME = PLV;
				TargetAttributes = {
					22142F71211309AD00062C6E = {
						CreatedOnToolsVersion = 9.4.1;
						ProvisioningStyle = Automatic;
					};
					2299AA5321142D7200BE675D = {
						CreatedOnToolsVersion = 9.4.1;
						ProvisioningStyle = Automatic;
					};
					632F43F723CA333A005558F1 = {
						CreatedOnToolsVersion = 11.2.1;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 22142F6C211309AD00062C6E /* Build configuration list for PBXProject "PolyvCloudClassSDK" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 22142F68211309AD00062C6E;
			productRefGroup = 22142F73211309AD00062C6E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				22142F71211309AD00062C6E /* PLVLiveScenesSDK */,
				2299AA5321142D7200BE675D /* PolyvCloudClassSDKBuild */,
				632F43F723CA333A005558F1 /* AllFrameworksBuild */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		22142F70211309AD00062C6E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		2299AA5B21142DFC00BE675D /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 12;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/sh\n#要build的target名\nTARGET_NAME=${PROJECT_NAME}\nif [[ $1 ]]\nthen\nTARGET_NAME=$1\nfi\n\nUNIVERSAL_OUTPUT_FOLDER=\"${SRCROOT}/Products/\"\n\n#创建输出目录，并删除之前的framework文件\nmkdir -p \"${UNIVERSAL_OUTPUT_FOLDER}\"\nrm -rf \"${UNIVERSAL_OUTPUT_FOLDER}/${TARGET_NAME}.framework\"\n\n#分别编译模拟器和真机的Framework\nxcodebuild -target \"${TARGET_NAME}\" ONLY_ACTIVE_ARCH=NO -configuration ${CONFIGURATION} -sdk iphoneos BUILD_DIR=\"${BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\" clean build\nxcodebuild -target \"${TARGET_NAME}\" ONLY_ACTIVE_ARCH=NO -configuration ${CONFIGURATION} -sdk iphonesimulator BUILD_DIR=\"${BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\" clean build\n\n#拷贝framework到univer目录\ncp -R \"${BUILD_DIR}/${CONFIGURATION}-iphoneos/${TARGET_NAME}.framework\" \"${UNIVERSAL_OUTPUT_FOLDER}\"\n\n#合并framework，输出最终的framework到build目录\nlipo -create -output \"${UNIVERSAL_OUTPUT_FOLDER}/${TARGET_NAME}.framework/${TARGET_NAME}\" \"${BUILD_DIR}/${CONFIGURATION}-iphonesimulator/${TARGET_NAME}.framework/${TARGET_NAME}\" \"${BUILD_DIR}/${CONFIGURATION}-iphoneos/${TARGET_NAME}.framework/${TARGET_NAME}\"\n\n#删除编译之后生成的无关的配置文件\ndir_path=\"${UNIVERSAL_OUTPUT_FOLDER}/${TARGET_NAME}.framework/\"\nfor file in ls $dir_path\ndo\nif [[ ${file} =~ \".xcconfig\" ]]\nthen\nrm -f \"${dir_path}/${file}\"\nfi\ndone\n#判断build文件夹是否存在，存在则删除\nif [ -d \"${SRCROOT}/build\" ]\nthen\nrm -rf \"${SRCROOT}/build\"\nfi\n#rm -rf \"${BUILD_DIR}/${CONFIGURATION}-iphonesimulator\" \"${BUILD_DIR}/${CONFIGURATION}-iphoneos\"\n#打开合并后的文件夹\nopen \"${UNIVERSAL_OUTPUT_FOLDER}\"\n\n";
		};
		5A3AF5C4381B2949EA9279B6 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-business-liveScenes-PLVLiveScenesSDK-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		632F43FB23CA333F005558F1 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/bin/sh\nbuildFramework(){\n\nCUR_ORIGIN_Path=$(pwd)\n\nTARGET_NAME=$1\n\nRELEASE_FRAMEWORK_FOLDER=$2\n\nDEV_FRAMEWORK_PATH=$3\n\n# 打Release/Debug\nCurrent_Configuration=\"Release\"\n\n# 定义输出目录\nUNIVERSAL_OUTPUT_FOLDER=\"${SRCROOT}/ReleaseProducts\"\n\n# 创建输出目录（若不存在则新建）\nmkdir -p \"${UNIVERSAL_OUTPUT_FOLDER}\"\n\ncd $DEV_FRAMEWORK_PATH\n\n# 真机架构\necho \"\\033[32m【${TARGET_NAME}】- 开始打 ✨${Current_Configuration}真机架构✨ \\033[0m\"\nxcodebuild -target \"${TARGET_NAME}\" ONLY_ACTIVE_ARCH=NO -configuration ${Current_Configuration} -sdk iphoneos BUILD_DIR=\"${BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\"\n\n# 模拟器架构\necho \"\\033[32m【${TARGET_NAME}】- 开始打 ✨${Current_Configuration}模拟器架构✨ \\033[0m\"\nxcodebuild -target \"${TARGET_NAME}\" ONLY_ACTIVE_ARCH=NO -configuration ${Current_Configuration} -sdk iphonesimulator BUILD_DIR=\"${BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\" EXCLUDED_ARCHS=\"arm64\"\n\niPhoneos_Executable_File=\"${BUILD_ROOT}/${Current_Configuration}-iphoneos/${TARGET_NAME}.framework/${TARGET_NAME}\"\nif [ ! -e $iPhoneos_Executable_File ]; then\necho \"\\033[31m【${TARGET_NAME}】- ❌${Current_Configuration}真机架构不存在 \\033[0m\"\necho \" \"\nfi\n\niPhonesimulator_Executable_File=\"${BUILD_ROOT}/${Current_Configuration}-iphonesimulator/${TARGET_NAME}.framework/${TARGET_NAME}\"\nif [ ! -e $iPhonesimulator_Executable_File ]; then\necho \"\\033[31m【${TARGET_NAME}】- ❌${Current_Configuration}模拟器架构不存在 \\033[0m\"\necho \" \"\nexit;\nfi\n\nrm -rf \"${UNIVERSAL_OUTPUT_FOLDER}/${TARGET_NAME}.framework\"\n\niPhoneos_Framework=\"${BUILD_ROOT}/${Current_Configuration}-iphoneos/${TARGET_NAME}.framework\"\ncp -R \"${iPhoneos_Framework}\" \"${UNIVERSAL_OUTPUT_FOLDER}\"\n\nOutput_Executable_File=\"${UNIVERSAL_OUTPUT_FOLDER}/${TARGET_NAME}.framework/${TARGET_NAME}\"\necho \"\\033[33m【${TARGET_NAME}】- 拷贝后 ✨${Current_Configuration}真机架构Framework✨ 创建时间 + 路径 \\033[0m\"\nls -l $iPhoneos_Framework\necho \" \"\n\n# 合并四架构包\nlipo -create -output \"${Output_Executable_File}\" \"${iPhoneos_Executable_File}\" \"${iPhonesimulator_Executable_File}\" \n\nrm -rf \"${RELEASE_FRAMEWORK_FOLDER}\"\ncp -R \"${UNIVERSAL_OUTPUT_FOLDER}/${TARGET_NAME}.framework\" \"${RELEASE_FRAMEWORK_FOLDER}\"\n\ncd $CUR_ORIGIN_Path\n}\n\nremoveBuild(){\nfolderPath=$1\nif [ \"$folderPath\" = \"\" ]; then\n    echo \"⚠️ \\033[31m 文件夹路径参数为空，不执行删除操作 \\033[0m\"\n    exit 1\nfi\n\nbuildPath=\"${folderPath}/build\"\nif [ -d \"${buildPath}\" ]; then\n    rm -rf \"${buildPath}\"\n    echo \"⚠️ \\033[33m 缓存文件夹已删除 ${buildPath} \\033[0m\"\nfi\n}\n\necho \" \"\ncurPath=$(pwd)\ncd $curPath\nReleaseVersionShell_Path=$(/usr/libexec/PlistBuddy -c \"Print :ReleaseSetup_Path\" AutoReleaseShellTempPlist.plist)\necho \"\\033[33m ReleaseVersionShell_Path=${ReleaseVersionShell_Path} \\033[0m\"\n\nif [ ! -d \"${ReleaseVersionShell_Path}\" ]; then\n    echo \"❌\\033[31m 脚本配置文件夹不存在，脚本停止\\033[0m\"\n    exit 1\nfi\n\ncd $ReleaseVersionShell_Path\nDev_LiveScenesDemoPath=$(/usr/libexec/PlistBuddy -c \"Print :Dev_PolyvLiveScenesDemo_Path\" ReleaseSetup.plist)\n\nFoundationSDKPath=$(/usr/libexec/PlistBuddy -c \"Print :Release_PolyvFoundationSDK_Path\" ReleaseSetup.plist)\nDev_FoundationSDKPath=$(/usr/libexec/PlistBuddy -c \"Print :Dev_PolyvFoundationSDK_Path\" ReleaseSetup.plist)\nDev_FoundationSDK_TargetName=$(/usr/libexec/PlistBuddy -c \"Print :Dev_PolyvFoundationSDK_TargetName\" ReleaseSetup.plist)\n\nBusinessSDKPath=$(/usr/libexec/PlistBuddy -c \"Print :Release_PolyvBusinessSDK_Path\" ReleaseSetup.plist)\nDev_BusinessSDKPath=$(/usr/libexec/PlistBuddy -c \"Print :Dev_PolyvBusinessSDK_Path\" ReleaseSetup.plist)\nDev_BusinessSDK_TargetName=$(/usr/libexec/PlistBuddy -c \"Print :Dev_PolyvBusinessSDK_TargetName\" ReleaseSetup.plist)\n\nCloudClassSDKPath=$(/usr/libexec/PlistBuddy -c \"Print :Release_PolyvCloudClassSDK_Path\" ReleaseSetup.plist)\nDev_CloudClassSDKPath=$(/usr/libexec/PlistBuddy -c \"Print :Dev_PolyvCloudClassSDK_Path\" ReleaseSetup.plist)\nDev_CloudClassSDK_TargetName=$(/usr/libexec/PlistBuddy -c \"Print :Dev_PolyvCloudClassSDK_TargetName\" ReleaseSetup.plist)\n\ncd $Dev_LiveScenesDemoPath\nTARGET_1_NAME=\"${Dev_FoundationSDK_TargetName}\"\nif [ \"$TARGET_1_NAME\" = \"\" ]; then\n    echo \"❌\\033[31m FoundationSDK Target名为空，请配置ReleaseSetup.plist，脚本停止\\033[0m\"\n    exit 1\nfi\nFOLDER_RELEASE_1_NAME=\"${FoundationSDKPath}/Frameworks/${TARGET_1_NAME}.framework\"\n\nTARGET_2_NAME=\"${Dev_BusinessSDK_TargetName}\"\nif [ \"$TARGET_2_NAME\" = \"\" ]; then\n    echo \"❌\\033[31m BusinessSDK Target名为空，请配置ReleaseSetup.plist，脚本停止\\033[0m\"\n    exit 1\nfi\nFOLDER_RELEASE_2_NAME=\"${BusinessSDKPath}/Frameworks/${TARGET_2_NAME}.framework\"\n\nTARGET_3_NAME=\"${Dev_CloudClassSDK_TargetName}\"\nif [ \"$TARGET_3_NAME\" = \"\" ]; then\n    echo \"❌\\033[31m CloudClassSDK Target名为空，请配置ReleaseSetup.plist，脚本停止\\033[0m\"\n    exit 1\nfi\nFOLDER_RELEASE_3_NAME=\"${CloudClassSDKPath}/Frameworks/${TARGET_3_NAME}.framework\"\n\necho \"${FOLDER_RELEASE_1_NAME}\"\necho \"${FOLDER_RELEASE_2_NAME}\"\necho \"${FOLDER_RELEASE_3_NAME}\"\n\ncd \"${Dev_LiveScenesDemoPath}/Pods\"\nxcodebuild -target \"Pods-${TARGET_1_NAME}\" ONLY_ACTIVE_ARCH=NO -configuration Release -sdk iphoneos BUILD_DIR=\"${BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\"  VALID_ARCHS=\"arm64 armv7 x86_64 i386\"\nxcodebuild -target \"Pods-${TARGET_1_NAME}\" ONLY_ACTIVE_ARCH=NO -configuration Release -sdk iphonesimulator BUILD_DIR=\"${BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\"  VALID_ARCHS=\"x86_64 i386\" EXCLUDED_ARCHS=\"arm64\"\ncd $Dev_FoundationSDKPath\nbuildFramework ${TARGET_1_NAME} ${FOLDER_RELEASE_1_NAME} ${Dev_FoundationSDKPath}\ncd \"${Dev_LiveScenesDemoPath}/Pods\"\nxcodebuild -target \"Pods-business-liveScenes-${TARGET_3_NAME}\" ONLY_ACTIVE_ARCH=NO -configuration Release -sdk iphoneos BUILD_DIR=\"${BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\"  VALID_ARCHS=\"arm64 armv7 x86_64 i386\"\nxcodebuild -target \"Pods-business-liveScenes-${TARGET_3_NAME}\" ONLY_ACTIVE_ARCH=NO -configuration Release -sdk iphonesimulator BUILD_DIR=\"${BUILD_DIR}\" BUILD_ROOT=\"${BUILD_ROOT}\"  VALID_ARCHS=\"x86_64 i386\" EXCLUDED_ARCHS=\"arm64\"\ncd $Dev_LiveScenesDemoPath\nbuildFramework ${TARGET_2_NAME} ${FOLDER_RELEASE_2_NAME} ${Dev_BusinessSDKPath}\nbuildFramework ${TARGET_3_NAME} ${FOLDER_RELEASE_3_NAME} ${Dev_CloudClassSDKPath}\n\nUNIVERSAL_OUTPUT_FOLDER=\"${SRCROOT}/ReleaseProducts\"\nopen ${UNIVERSAL_OUTPUT_FOLDER}\n\nremoveBuild ${Dev_FoundationSDKPath}\nremoveBuild ${Dev_BusinessSDKPath}\nremoveBuild ${Dev_CloudClassSDKPath}\n\necho \"  \"\necho \"【\\033[32m${TARGET_1_NAME}.framework\\033[0m】检查创建时间：\"\nls -l \"${FOLDER_RELEASE_1_NAME}/${TARGET_1_NAME}\"\necho \"【\\033[32m${TARGET_1_NAME}.framework\\033[0m】检查架构：\"\nlipo -info \"${FOLDER_RELEASE_1_NAME}/${TARGET_1_NAME}\"\n\necho \"  \"\necho \"【\\033[32m${TARGET_2_NAME}.framework\\033[0m】检查创建时间：\"\nls -l \"${FOLDER_RELEASE_2_NAME}/${TARGET_2_NAME}\"\necho \"【\\033[32m${TARGET_2_NAME}.framework\\033[0m】检查架构：\"\nlipo -info \"${FOLDER_RELEASE_2_NAME}/${TARGET_2_NAME}\"\n\necho \"  \"\necho \"【\\033[32m${TARGET_3_NAME}.framework\\033[0m】检查创建时间：\"\nls -l \"${FOLDER_RELEASE_3_NAME}/${TARGET_3_NAME}\"\necho \"【\\033[32m${TARGET_3_NAME}.framework\\033[0m】检查架构：\"\nlipo -info \"${FOLDER_RELEASE_3_NAME}/${TARGET_3_NAME}\"\n\n\n\n";
		};
		D5EA5EC83228100751923F8E /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-business-liveScenes-PLVLiveScenesSDK/Pods-business-liveScenes-PLVLiveScenesSDK-resources.sh",
				"${PODS_ROOT}/TXLiteAVSDK_TRTC/TXLiteAVSDK_TRTC/TXLiteAVSDK_TRTC.xcframework/ios-arm64_armv7/TXLiteAVSDK_TRTC.framework/TXLiteAVSDK_TRTC.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TXLiteAVSDK_TRTC.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-business-liveScenes-PLVLiveScenesSDK/Pods-business-liveScenes-PLVLiveScenesSDK-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		22142F6D211309AD00062C6E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				FA658DB9283C8D1C00AFBC4E /* PLVPlaybackVideoInfoModel.m in Sources */,
				6526517F2948803A007563AC /* PLVLoganUploadModel.m in Sources */,
				4DE9DD8F28194812003EC5BF /* PLVChannelPlaybackInfoModel.m in Sources */,
				362E3B5528F426D200384FC6 /* PLVStreamerCommodityWebViewBridge.m in Sources */,
				BF7CFC7125BA64F70084C160 /* PLVCommodityModel.m in Sources */,
				00C75BC4261C3C7400F141C5 /* PLVFileManager.m in Sources */,
				0067AEA52727E73000CFA7D0 /* PLVHiClassManager.m in Sources */,
				63C0CABC2636F5DB00664CB5 /* PLVLinkMicManager.m in Sources */,
				63C284BE266B71B200DBF84D /* PLVRTCStreamerMixUser.m in Sources */,
				4D46919628868CE000043A4E /* PLVFileMessage.m in Sources */,
				364FBDC12A2D79110087DE53 /* PLVTuWenWebViewBridge.m in Sources */,
				4D1E74CA275DC1BF008B7D3A /* PLVLivePlaybackSectionModel.m in Sources */,
				8F789516267B806900A7C2A7 /* PLVRewardMessage.m in Sources */,
				36A203442BF4B6E5009BBC3A /* PLVStreamerInteractWebViewBridge.m in Sources */,
				369C2A572C339B9A00389D5B /* PLVSpeakTopMessage.m in Sources */,
				006002A4284DDDC800D5FD51 /* PLVPlaybackMsgIndex.m in Sources */,
				006002B0284DE4A800D5FD51 /* PLVPlaybackMessageManager.m in Sources */,
				007279EE25CBFA91001CE398 /* PLVWebViewBridge.m in Sources */,
				65560BC32A274E0F0006EEE9 /* PLVQosLoadingTracerModel.m in Sources */,
				363D84E42BECDD9000BDCE36 /* PLVLiveSampleBufferDisplayView.m in Sources */,
				00485BF4256CE03900616C65 /* PLVChatroomManager.m in Sources */,
				36237D352772D8DB00C7831A /* PLVSocketWebViewBridge.m in Sources */,
				FA1B8857283B8C6D00BFDE7D /* PLVDownloadCommonOperation.m in Sources */,
				FA1B8849283B847900BFDE7D /* PLVDownloadPlaybackTaskInfo.mm in Sources */,
				043FD13328D078270005FA82 /* PLVPublicStreamPlayer.m in Sources */,
				00485BEC256CE00400616C65 /* PLVSpeakMessage.m in Sources */,
				002E89BB23B1F1F7006F08D4 /* PLVWELogEventDefine.m in Sources */,
				00340F4E22E1BB9700336ACE /* PLVImageUpload.m in Sources */,
				FA1B8863283B8D4400BFDE7D /* PLVDownloadDataTasker.m in Sources */,
				0060354D268D577E00147A78 /* PLVLiveVClassAPI.m in Sources */,
				00C75BCA261C3C7400F141C5 /* PLVDocumentTokenModel.m in Sources */,
				FA658DBF283C8F8800AFBC4E /* PLVPlaybackCacheManager.m in Sources */,
				FA1B8851283B86B500BFDE7D /* PLVDownloadDatabaseManager.mm in Sources */,
				633E4257257B91090039D35D /* PLVLivePlayer.m in Sources */,
				213973D629FA45000052F584 /* PLVRTCStatistics.m in Sources */,
				36EB53392831EED000DEF138 /* PLVProductWebViewBridge.m in Sources */,
				00485C0B256E52CC00616C65 /* PLVQuoteMessage.m in Sources */,
				043F13FA28E199AC003E72F4 /* PLVPublicStreamGetInfoModel.m in Sources */,
				658F9B172949B503003B031A /* PLVLoganUploadTokenModel.m in Sources */,
				8F78D376269D7B360005AB76 /* PLVContainerWebViewBridge.m in Sources */,
				6730B5F7212EA47E0086CFC0 /* PLVLiveVideoConfig.m in Sources */,
				22142FD52113119600062C6E /* PLVLivePrivateAPI.m in Sources */,
				FA1B885F283B8D1600BFDE7D /* PLVDownloadURLSession.m in Sources */,
				0003CCA723F25E4700DB26B4 /* PLVWErrorManager.m in Sources */,
				21C23DFA258602C000F2A5D3 /* PLVSocketManager.m in Sources */,
				36BACEFB26959E0C00C8CA31 /* PLVImageEmotionMessage.m in Sources */,
				6390DBBF258AFD1E0031A29B /* PLVChannelInfoModel.m in Sources */,
				FAB4571F27957B6200816F8B /* PLVBeautyManager.m in Sources */,
				636144D72339CB1C002A44F3 /* PLVLiveAPIUtils.m in Sources */,
				00466F8C23B0A20400F968DC /* PLVWLogReporterManager.m in Sources */,
				4D3E417B27CDCAB300009706 /* PLVLiveHttpDnsManager.m in Sources */,
				655AED912950694800955B33 /* PLVLoganManager.m in Sources */,
				63C0CAC02636F5E100664CB5 /* PLVLinkMicGetTokenModel.m in Sources */,
				FA1B883D283B774600BFDE7D /* PLVDownloadTaskInfo.mm in Sources */,
				63C0CAB72636F5D400664CB5 /* PLVRTCStreamerManager.m in Sources */,
				228FFC0A21997AA5007E8413 /* PLVLiveVideoAPI.m in Sources */,
				36E8E1A926DCD62200DAEE02 /* PLVLiveConstants.m in Sources */,
				FA1B885B283B8C7C00BFDE7D /* PLVDownloadPlaybackOperation.m in Sources */,
				6397673B250A365900D1F16B /* PLVInteractBaseApp.m in Sources */,
				21C23E022586432100F2A5D3 /* PLVSocketEventDefine.m in Sources */,
				00485BF0256CE01700616C65 /* PLVImageMessage.m in Sources */,
				367AD2B02B8DD972006244FB /* PLVQAWebViewBridge.m in Sources */,
				651B05EC2941E1DC00F78E8E /* PLVLoganUploadManager.m in Sources */,
				63F18F64251356FE0057A8B7 /* PLVPPTWebview.m in Sources */,
				8FB1BCF827D6022F0033A7BD /* PLVBeautyResourceManager.m in Sources */,
				633E425B257B91220039D35D /* PLVLivePlaybackPlayer.m in Sources */,
				0075EC872646331200116271 /* PLVConsoleLogger.m in Sources */,
				00E48A362589B2100068C884 /* PLVViewLogCustomParam.m in Sources */,
				FA1B8839283B74DA00BFDE7D /* PLVDownloadManager.m in Sources */,
				2917AB6C26C0DB8600054BCF /* PLVChannelClassManager.m in Sources */,
				22142FDB2113119600062C6E /* PLVLiveVideoChannelMenuInfo.m in Sources */,
				00C75BC9261C3C7400F141C5 /* PLVDocumentUploadModel.m in Sources */,
				006002A8284DDEAF00D5FD51 /* PLVPlaybackMsgUser.m in Sources */,
				00485BF8256CE10400616C65 /* PLVCustomMessage.m in Sources */,
				63CBC73C2589B1D700E80897 /* PLVPlayer.m in Sources */,
				21DDF88E2966D01D00A00202 /* PLVRedpackMessage.m in Sources */,
				67116BA524E2A27400F4213F /* PLVPlaybackListModel.m in Sources */,
				00C75BC6261C3C7400F141C5 /* PLVDocumentUploader.m in Sources */,
				006002AC284DDF0400D5FD51 /* PLVPlaybackMessage.m in Sources */,
				3674693F2769D2F700235819 /* PLVInteractWebViewBridge.m in Sources */,
				FA51FF3A27B35B1D004C3B4B /* PLVLivePictureInPictureManager.m in Sources */,
				FA1B8843283B7F7C00BFDE7D /* PLVDownloadPathManager.m in Sources */,
				63976733250A20C100D1F16B /* PLVInteractWebview.m in Sources */,
				00C75BCC261C3C7400F141C5 /* PLVDocumentUploadClient.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		22142F78211309AD00062C6E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.4;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		22142F79211309AD00062C6E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		22142F7B211309AD00062C6E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3A1280D9E678C44B22A923FA /* Pods-business-liveScenes-PLVLiveScenesSDK.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = 8BMZBNN3Q5;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = "$(SRCROOT)/PolyvCloudClassSDK/Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.1.0;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-isystem",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Socket.IO-Client-Swift/SocketIO.framework/Headers\"",
					"-isystem",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Starscream/Starscream.framework/Headers\"",
					"-iframework",
					"\"${PODS_ROOT}/PolyvIJKPlayer\"",
					"-iframework",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Socket.IO-Client-Swift\"",
					"-iframework",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Starscream\"",
					"-iframework",
					"\"${PODS_ROOT}/AgoraRtcEngine_iOS\"",
					"-iframework",
					"\"${PODS_ROOT}/PolyvAliHttpDNS\"",
					"-fembed-bitcode",
				);
				PRODUCT_BUNDLE_IDENTIFIER = polyv.PolyvCloudClassSDK;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = "arm64 armv7 x86_64 i386";
			};
			name = Debug;
		};
		22142F7C211309AD00062C6E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 97C1FEEFECFD45A38CF2A685 /* Pods-business-liveScenes-PLVLiveScenesSDK.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Automatic;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = 8BMZBNN3Q5;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = "$(SRCROOT)/PolyvCloudClassSDK/Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.1.0;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_CFLAGS = (
					"$(inherited)",
					"-isystem",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Socket.IO-Client-Swift/SocketIO.framework/Headers\"",
					"-isystem",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Starscream/Starscream.framework/Headers\"",
					"-iframework",
					"\"${PODS_ROOT}/PolyvIJKPlayer\"",
					"-iframework",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Socket.IO-Client-Swift\"",
					"-iframework",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Starscream\"",
					"-iframework",
					"\"${PODS_ROOT}/AgoraRtcEngine_iOS\"",
					"-iframework",
					"\"${PODS_ROOT}/PolyvAliHttpDNS\"",
					"-fembed-bitcode",
				);
				PRODUCT_BUNDLE_IDENTIFIER = polyv.PolyvCloudClassSDK;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = "arm64 armv7 x86_64 i386";
			};
			name = Release;
		};
		2299AA5521142D7200BE675D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 11.2;
				MACH_O_TYPE = staticlib;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		2299AA5621142D7200BE675D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 11.2;
				MACH_O_TYPE = staticlib;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		632F43F823CA333A005558F1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTS_MACCATALYST = NO;
			};
			name = Debug;
		};
		632F43F923CA333A005558F1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTS_MACCATALYST = NO;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		22142F6C211309AD00062C6E /* Build configuration list for PBXProject "PolyvCloudClassSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22142F78211309AD00062C6E /* Debug */,
				22142F79211309AD00062C6E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		22142F7A211309AD00062C6E /* Build configuration list for PBXNativeTarget "PLVLiveScenesSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22142F7B211309AD00062C6E /* Debug */,
				22142F7C211309AD00062C6E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2299AA5421142D7200BE675D /* Build configuration list for PBXAggregateTarget "PolyvCloudClassSDKBuild" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2299AA5521142D7200BE675D /* Debug */,
				2299AA5621142D7200BE675D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		632F43FA23CA333A005558F1 /* Build configuration list for PBXAggregateTarget "AllFrameworksBuild" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				632F43F823CA333A005558F1 /* Debug */,
				632F43F923CA333A005558F1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 22142F69211309AD00062C6E /* Project object */;
}
