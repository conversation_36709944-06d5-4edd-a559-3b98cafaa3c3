//
//  PLVLiveSampleBufferDisplayView.h
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2023/3/22.
//  Copyright © 2023 PLV. All rights reserved.
//
//  ijk第三方渲染视图，负责使用SampleBuffer 渲染 ijk 软硬解数据

#import <UIKit/UIKit.h>

#if __has_include(<PLVIJKPlayer/PLVIJKPlayer.h>)
    #import <PLVIJKPlayer/PLVIJKFFMoviePlayerController.h>
    typedef UIImageView<PLVIJKSDLGLViewProtocol> PLVIJKSDLGLThirdView;
#elif __has_include(<IJKMediaFramework/IJKMediaFramework.h>)
    #import <IJKMediaFramework/IJKMediaFramework.h>
    typedef UIImageView<IJKSDLGLViewProtocol> PLVIJKSDLGLThirdView;
#else
    typedef UIImageView PLVIJKSDLGLThirdView;
    #ifndef PLV_NO_IJK_EXIST
    #define PLV_NO_IJK_EXIST
    #endif
#endif

NS_ASSUME_NONNULL_BEGIN

@interface PLVLiveSampleBufferDisplayView : PLVIJKSDLGLThirdView

- (id) initWithFrame:(CGRect)frame;

/// SampleBuffer 视图清理
- (void)clearSampleBufferDisplayView;

@end

NS_ASSUME_NONNULL_END
