//
//  PLVLivePictureInPictureManager+Private.h
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/2/10.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVLivePictureInPictureManager.h"
#import "PLVPlayer.h"

NS_ASSUME_NONNULL_BEGIN

@protocol PLVLivePictureInPictureManagerDelegate;

/// 仅面向内部的属性及方法
@interface PLVLivePictureInPictureManager ()

#pragma mark - [ 属性 ]

#pragma mark 可配置项

/// 直播/回放播放器，开启画中画的时候由单例持有，防止页面退出导致PLVPlayer被释放，进而导致viewlog统计失效。
/// 停止画中画的时候需要手动销毁，否则PLVPlayer无法释放。
/// 再次 [setupWithDisplaySuperview]，将会自动释放旧的PLVPlayer
@property (nonatomic, strong, nullable) PLVPlayer <PLVLivePlayerPictureInPictureProtocol> *livePlayer;

/// 画中画过程代理
@property (nonatomic, weak, nullable) id <PLVLivePictureInPictureManagerDelegate> delegate;

#pragma mark - [ 方法 ]

#pragma mark - PLVLivePictureInPictureMode_AVPlayer
/// 开始画中画
/// @param contentURL 画中画播放内容，AVPlayer不支持播放flv，这里需要播放m3u8、mp4内容
/// @param displaySuperview 画中画占位图所在父视图
- (void)startPictureInPictureWithContentURL:(NSURL *)contentURL displaySuperview:(UIView *)displaySuperview;

/// 让画中画播放器重新播放
/// @param contentURL 画中画播放内容
- (void)replayPictureInPicturePlayerWithURL:(NSURL *)contentURL;

/// 画中画播放器跳转到指定进度
/// @param toTime 当前播放时间点 (单位:秒)
- (void)seekPictureInPicturePlayerToTime:(NSTimeInterval)toTime;

#pragma mark - PLVLivePictureInPictureMode_IJKPlayer
/// 开启画中画，支持加密视频
- (void)startPictureInPictureWithPlayer:(PLVPlayer <PLVLivePlayerPictureInPictureProtocol> *)player;

/// 后台模式，初始化画中画配置
- (void)bindPictureInPictureForBackgroundMode:(PLVPlayer <PLVLivePlayerPictureInPictureProtocol> *)player;

/// 清理画中画功能绑定的播放器
- (void)unbindPictureInPicturePlayer:(PLVPlayer <PLVLivePlayerPictureInPictureProtocol> *)player;

/// 同步画中画播放控件的状态
/// @param playing YES 播放状态，NO暂停状态
- (void)syncPictureInPicturePlayControlStatus:(BOOL)playing;

/// 更新画中画控件的播放时间【回放】
/// @param playbackTime 播放时间
- (void)updatePictureInPictureControlPlaybackTime:(NSTimeInterval)playbackTime;

/// 清理画中画功能
- (void)cleanPictureInPicturePlayer;

@end

#pragma mark - [ 代理方法 ]
/// 画中画代理
@protocol PLVLivePictureInPictureManagerDelegate <NSObject>

@optional

/// 画中画即将开始
- (void)plvPictureInPictureWillStart;

/// 画中画已经开始
- (void)plvPictureInPictureDidStart;

/// 画中画开启失败
/// @param error 失败错误原因
- (void)plvPictureInPictureFailedToStartWithError:(NSError *)error;

/// 画中画即将停止
- (void)plvPictureInPictureWillStop;

/// 画中画已经停止
- (void)plvPictureInPictureDidStop;

/// 画中画播放器播放状态改变
/// @param playing 是否正在播放
- (void)plvPictureInPicturePlayerPlayingStateDidChange:(BOOL)playing;

/// 画中画播放器播放状态改变
/// @param playing 是否正在播放
/// @param systemInterrupts 是否系统中断
- (void)plvPictureInPicturePlayerPlayingStateDidChange:(BOOL)playing systemInterrupts:(BOOL)systemInterrupts;

@end

NS_ASSUME_NONNULL_END
