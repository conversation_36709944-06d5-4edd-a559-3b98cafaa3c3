//
//  PLVQosLoadingTracerModel.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2023/5/31.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVQosLoadingTracerModel.h"
#import <PLVFoundationSDK/PLVFdUtil.h>

@interface PLVQosLoadingTracerModel ()

@property (nonatomic, assign) NSTimeInterval startTime;

@property (nonatomic, assign) NSTimeInterval endTime;

@end

@implementation PLVQosLoadingTracerModel

- (instancetype)initWithType:(PLVQosLoadingTracerModelType)type {
    if (self = [super init]) {
        self.type = type;
        self.startTime = 0;
        self.endTime = 0;
    }
    return self;
}

- (void)addStartTime {
    self.startTime = [PLVFdUtil curTimeInterval];
}

- (void)addEndTime {
    self.endTime = [PLVFdUtil curTimeInterval];
}

- (BOOL)rangeUseable {
    return self.startTime > 0 && self.endTime > 0 && (self.endTime - self.startTime) > 0;
}

@end

