//
//  PLVPlaybackVideoInfoModel.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/24.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVPlaybackVideoInfoModel.h"
#import "PLVDownloadPathManager.h"
#import "PLVConsoleLogger.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

@implementation PLVPlaybackVideoInfoModel

+ (instancetype)playbackVideoInfoModelWithJsonDict:(NSDictionary *)jsonDict {
    if (![PLVFdUtil checkDictionaryUseable:jsonDict]) {
        return nil;
    }
    
    PLVPlaybackVideoInfoModel *model = [[[self class] alloc] init];
    if (model) {
        [model setPropertyWithDict:jsonDict];
    }
    return model;
}

- (void)setPropertyWithDict:(NSDictionary *)dict {
    if (![PLVFdUtil checkDictionaryUseable:dict]) {
        return;
    }
    self.liveType = PLV_SafeStringForDictKey(dict, @"liveType");
    
    self.title = PLV_SafeStringForDictKey(dict, @"title");
    self.duration = PLV_SafeStringForDictKey(dict, @"duration");
    self.firstImage = PLV_SafeStringForDictKey(dict, @"firstImage");
    if ([self.firstImage hasPrefix:@"//"]) {
        self.firstImage = [@"https:" stringByAppendingString:self.firstImage];
    }
    self.fileUrl = PLV_SafeStringForDictKey(dict, @"fileUrl");
    
    self.videoId = PLV_SafeStringForDictKey(dict, @"videoId");
    self.videoPoolId = PLV_SafeStringForDictKey(dict, @"videoPoolId");
    self.fileId = PLV_SafeStringForDictKey(dict, @"fileId");
    self.channelId = PLV_SafeStringForDictKey(dict, @"channelId");
    self.channelSessionId = PLV_SafeStringForDictKey(dict, @"channelSessionId");
    self.originSessionId = PLV_SafeStringForDictKey(dict, @"originSessionId");
    
    self.playbackCacheEnabled = PLV_SafeBoolForDictKey(dict, @"playbackCacheEnabled");
    self.pptJsonUrl = PLV_SafeStringForDictKey(dict, @"offlineJSUrl");
    
    self.listType = PLV_SafeStringForDictKey(dict, @"listType");
    NSDictionary *videoCache = PLV_SafeDictionaryForDictKey(dict, @"videoCache");
    if ([PLVFdUtil checkDictionaryUseable:videoCache]) {
        self.status = PLV_SafeBoolForDictKey(videoCache, @"status");
        self.videoUrl = PLV_SafeStringForDictKey(videoCache, @"videoUrl");
        self.videoSize = PLV_SafeIntegerForDictKey(videoCache, @"videoSize");
        self.zipUrl = PLV_SafeStringForDictKey(videoCache, @"zipUrl");
        if ([self.zipUrl hasPrefix:@"//"]) {
            self.zipUrl = [@"https:" stringByAppendingString:self.zipUrl];
        }
        self.zipSize = PLV_SafeIntegerForDictKey(videoCache, @"zipSize");
    }
    if (!self.status || ![self.class isMp4File:self.videoUrl]) {
        self.playbackCacheEnabled = NO;
    }
    
    self.subtitleList =  PLV_SafeArraryForDictKey(dict, @"subtitleList");
}

/// 通过record暂存视频信息的json生成model
+ (instancetype)playbackVideoInfoModelWithRecordJsonDict:(NSDictionary *)jsonDict {
    if (![PLVFdUtil checkDictionaryUseable:jsonDict]) {
        return nil;
    }
    
    PLVPlaybackVideoInfoModel *model = [[[self class] alloc] init];
    model.listType = @"record";
    model.fileId = PLV_SafeStringForDictKey(jsonDict, @"fileId");
    model.channelId = PLV_SafeStringForDictKey(jsonDict, @"channelId");
    model.title = PLV_SafeStringForDictKey(jsonDict, @"filename");
    model.duration = PLV_SafeStringForDictKey(jsonDict, @"duration");
    model.fileUrl = PLV_SafeStringForDictKey(jsonDict, @"mp4");
    model.channelSessionId = PLV_SafeStringForDictKey(jsonDict, @"channelSessionId");
    model.liveType = PLV_SafeStringForDictKey(jsonDict, @"liveType");
    model.originSessionId = PLV_SafeStringForDictKey(jsonDict, @"originSessionId");
    model.playbackCacheEnabled = PLV_SafeBoolForDictKey(jsonDict, @"playbackCacheEnabled");
    
    NSDictionary *videoCache = PLV_SafeDictionaryForDictKey(jsonDict, @"videoCache");
    if ([PLVFdUtil checkDictionaryUseable:videoCache]) {
        BOOL status = PLV_SafeBoolForDictKey(videoCache, @"status");
        if (status) {
            NSString *vurl = PLV_SafeStringForDictKey(videoCache, @"videoUrl");
            model.videoUrl = [self.class isMp4File:vurl] ? vurl : PLV_SafeStringForDictKey(jsonDict, @"mp4");
            if (![PLVFdUtil checkStringUseable:model.videoUrl]) {
                model.playbackCacheEnabled = NO;
            }
            
            model.zipSize = PLV_SafeIntegerForDictKey(videoCache, @"zipSize");
            model.liveType = PLV_SafeStringForDictKey(videoCache, @"liveType");
            model.videoSize = PLV_SafeIntegerForDictKey(videoCache, @"videoSize");
            model.zipUrl = PLV_SafeStringForDictKey(videoCache, @"zipUrl");
            if ([model.zipUrl hasPrefix:@"//"]) {
                model.zipUrl = [@"https:" stringByAppendingString:model.zipUrl];
            }
            model.pptJsonUrl = PLV_SafeStringForDictKey(videoCache, @"offlineJSUrl");
        }else {
            model.playbackCacheEnabled = NO;
        }
    }else {
        model.playbackCacheEnabled = NO;
    }
    
    model.subtitleList =  PLV_SafeArraryForDictKey(jsonDict, @"subtitleList");
    return model;
}

+ (BOOL)isMp4File:(NSString *)filePath {
    if (![PLVFdUtil checkStringUseable:filePath]) {
        return NO;
    }
    
    NSURL *url = [NSURL URLWithString:filePath];
    if (!url) {
        url = [NSURL fileURLWithPath:filePath];
    }
    NSString *path = [url path];
    NSString *lastComponent = [path lastPathComponent];
    NSString *extension = [lastComponent pathExtension];
    return [extension caseInsensitiveCompare:@"mp4"] == NSOrderedSame;
}

- (void)setSubtitleList:(NSArray *)subtitleList {
    _subtitleList = subtitleList;
    if ([PLVFdUtil checkArrayUseable:subtitleList]) {
        NSMutableArray *subtitleArray = [NSMutableArray array];
        for (NSDictionary *dict in subtitleList) {
            PLVPlaybackSubtitleModel *subtitleModel = [PLVPlaybackSubtitleModel modelWithDictionary:dict];
            if ([PLVPlaybackSubtitleModel isSubtitleAvailable:subtitleModel]) {
                [subtitleArray addObject:subtitleModel];
            }
        }
        if ([PLVFdUtil checkArrayUseable:subtitleArray]) {
            self.availableSubtitleList = [subtitleArray copy];
        }
    }
}

@end

@implementation PLVPlaybackLocalVideoInfoModel

- (void)setFileIdPath:(NSString *)fileIdPath {
    if (![PLVFdUtil checkStringUseable:fileIdPath]) {
        PLV_LOG_WARN(PLVConsoleLogModuleTypeDownload, @"配置fileIdPath非法");
        return;
    }
    
    // localVideoPath
    NSString * localVideoPath_bitRate = [fileIdPath stringByAppendingString:@"/video/1"];
    NSArray * videoPathArr = [[PLVDownloadPathManager shareManager]findFileAtFolderPath:localVideoPath_bitRate stringInFileName:@".mp4"];
    if (videoPathArr.count == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"通过fileIdPath无法找到视频文件");
        return;
    }
    
    _fileIdPath = fileIdPath;
    
    self.localVideoPath = videoPathArr.firstObject;
    
    // basePath
    self.basePath = [fileIdPath stringByAppendingString:@"/js"];
    
    // subTitleFolderPath
    self.subTitleFolderPath = [fileIdPath stringByAppendingString:@"/subtitle"];
    
    // localHtmlPath
    NSArray * htmlPathArr = [[PLVDownloadPathManager shareManager]findFileAtFolderPath:self.basePath stringInFileName:@".html"];
    if (htmlPathArr.count == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"通过fileIdPath无法找到html文件");
        return;
    }
    self.localHtmlPath = htmlPathArr.firstObject;
    
    // pptPath
    self.pptPath = [fileIdPath stringByAppendingString:@"/ppt"];

    // config.json
    NSArray * configFileArr = [[PLVDownloadPathManager shareManager]findFileAtFolderPath:fileIdPath stringInFileName:@"config.json"];
    if (configFileArr.count != 0) {
        NSString * configFilePath = configFileArr.firstObject;
        NSData * configFileData = [[NSData alloc] initWithContentsOfFile:configFilePath];
        NSDictionary * configFileDic = [NSJSONSerialization JSONObjectWithData:configFileData options:kNilOptions error:nil];
        
        NSString * videoIdStr = configFileDic[@"videoId"];
        NSString * videoPoolIdStr = configFileDic[@"videoPoolId"];
        NSString * liveTypeStr = configFileDic[@"liveType"];

        // videoId
        if ([PLVFdUtil checkStringUseable:videoIdStr]) {
            self.videoId = videoIdStr;
        }
        
        // videoPoolId
        if ([PLVFdUtil checkStringUseable:videoPoolIdStr]) {
            self.videoPoolId = videoPoolIdStr;
        }
        
        // liveType
        if ([PLVFdUtil checkStringUseable:liveTypeStr]) {
            self.liveType = liveTypeStr;
        }
    }
}


@end

@implementation PLVPlaybackSubtitleModel

+ (instancetype)modelWithDictionary:(NSDictionary *)dict {
    if (![PLVFdUtil checkDictionaryUseable:dict]) {
        return nil;
    }
    
    PLVPlaybackSubtitleModel *model = [[PLVPlaybackSubtitleModel alloc] init];
    model.subtitleId = PLV_SafeIntegerForDictKey(dict, @"id");
    model.name = PLV_SafeStringForDictKey(dict, @"name");
    model.srtUrl = PLV_SafeStringForDictKey(dict, @"srtUrl");
    model.language = PLV_SafeStringForDictKey(dict, @"language");
    model.source = PLV_SafeStringForDictKey(dict, @"source");
    model.isOriginal = [PLV_SafeStringForDictKey(dict, @"isOriginal") isEqualToString:@"Y"];
    model.status = PLV_SafeStringForDictKey(dict, @"status");
    
    return model;
}

+ (BOOL)isSubtitleAvailable:(PLVPlaybackSubtitleModel *)model {
    // 检查字幕是否可用（状态为publish）
    return [model.status isEqualToString:@"publish"] && [PLVFdUtil checkStringUseable:model.srtUrl];
}

+ (NSDictionary *)transformToDictionary:(PLVPlaybackSubtitleModel *)model {
    if (!model) {
        return nil;
    }
    return @{
        @"id": @(model.subtitleId),
        @"name": [PLVFdUtil checkStringUseable:model.name] ? model.name : @"",
        @"srtUrl": [PLVFdUtil checkStringUseable:model.srtUrl] ? model.srtUrl : @"",
        @"language": [PLVFdUtil checkStringUseable:model.language] ? model.language : @"",
        @"source": [PLVFdUtil checkStringUseable:model.source] ? model.source : @"",
        @"isOriginal": model.isOriginal ? @"Y" : @"N",
        @"status": [PLVFdUtil checkStringUseable:model.status] ? model.status : @"",};
}

@end
