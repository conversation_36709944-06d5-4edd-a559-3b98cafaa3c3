//
//  PLVQosLoadingTracerModel.h
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2023/5/31.
//  Copyright © 2023 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, PLVQosLoadingTracerModelType) {
    PLVQosLoadingTracerModelTypeChannelJson = 1,
    PLVQosLoadingTracerModelTypeChannelJsonStandby = 2,
    PLVQosLoadingTracerModelTypeLiveStreamStatus = 3,
    PLVQosLoadingTracerModelTypeRestrictInfo = 4,
    PLVQosLoadingTracerModelTypePlaybackVideoInfo = 5,
    PLVQosLoadingTracerModelTypeRecordVideoInfo = 6,
};

@interface PLVQosLoadingTracerModel : NSObject

@property (nonatomic, assign) PLVQosLoadingTracerModelType type;

@property (nonatomic, assign) BOOL rangeUseable;

// 起始时间戳
@property (nonatomic, assign, readonly) NSTimeInterval startTime;

// 结束时间戳
@property (nonatomic, assign, readonly) NSTimeInterval endTime;

- (instancetype)initWithType:(PLVQosLoadingTracerModelType)type;

- (void)addStartTime;

- (void)addEndTime;

@end

NS_ASSUME_NONNULL_END
