//
//  PLVLivePictureInPictureManager.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/2/9.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVLivePictureInPictureManager.h"
#import "PLVLivePlayer.h"
#import "PLVLivePlaybackPlayer.h"
#import "PLVConsoleLogger.h"
#import "PLVLivePictureInPictureManager+Private.h"
#import <AVKit/AVKit.h>

@interface PLVLivePictureInPictureManager ()<
AVPictureInPictureControllerDelegate,
AVPictureInPictureSampleBufferPlaybackDelegate
>

#pragma mark 数据
@property (nonatomic, strong) NSURL *contentUrl;        //!< 播放内容
@property (nonatomic, assign) BOOL isPlayingContentUrl;    //!< 是否正在播放内容
@property (nonatomic, assign) BOOL pictureInPictureStarting;    //!< 画中画正在开启
@property (nonatomic, assign) BOOL pictureInPictureActive;  //!< 画中画小窗是否开启
@property (nonatomic, assign) CGFloat originalVolume; // 播放器被静音前的音量
@property (nonatomic, assign) BOOL isPlaybackPaused; //!< IJKPlayer 模式播放器是否处于暂停状态
@property (nonatomic, assign) BOOL restorePictureInPicture; //!< 是否正在恢复画中画
@property (nonatomic, assign) BOOL isAutoStarted; //!< 小窗触发模式 是否是后台自动启动小窗


#pragma mark 功能对象
@property (nonatomic, strong) AVPictureInPictureController *pictureInPictureController API_AVAILABLE(ios(9.0));
/// PLVLivePictureInPictureMode_AVPlayer 模式
@property (nonatomic, strong) AVPlayerItem *pictureInPicturePlayerItem;
@property (nonatomic, strong) AVPlayer *pictureInPicturePlayer;
@property (nonatomic, strong) UIView *playerLayerSuperview;
@property (nonatomic, strong) AVPlayerLayer *pictureInPicturePlayerLayer;
/// PLVLivePictureInPictureMode_IJKPlayer 模式
@property (nonatomic, strong) AVSampleBufferDisplayLayer *sampleBufferDisplayLayer;

@end

@implementation PLVLivePictureInPictureManager

#pragma mark - [ Life Cycle ]

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (instancetype)init {
    if (self = [super init]) {
        _canAutoStartPictureInPicture = NO;
        self.pictureInPictureStarting = NO;
        self.isPlayingContentUrl = NO;
        self.pictureInPictureControlsVisible = YES;
        self.pictureInPictureMode = PLVLivePictureInPictureMode_AVPlayer;
        self.isAutoStarted = YES;
        
    }
    return self;
}

#pragma mark - [ Public Method ]

+ (instancetype)sharedInstance {
    static PLVLivePictureInPictureManager *_sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedInstance = [self new];
    });
    return _sharedInstance;
}

- (void)startPictureInPictureWithContentURL:(NSURL *)contentURL displaySuperview:(UIView *)displaySuperview {
    if (@available(iOS 9.0, *)) {
        if ([AVPictureInPictureController isPictureInPictureSupported]) {
            [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:nil];
            [[AVAudioSession sharedInstance] setActive:YES error:nil];
            
            [self cleanPictureInPicturePlayer];
            self.pictureInPictureStarting = YES;
            
            if ([self isLocalFileURL:contentURL]) {
                contentURL = [NSURL fileURLWithPath:contentURL.absoluteString];
            }
            // 初始化AVPlayerItem
            self.pictureInPicturePlayerItem = [AVPlayerItem playerItemWithURL:contentURL];
            [self.pictureInPicturePlayerItem addObserver:self forKeyPath:@"status" options:NSKeyValueObservingOptionNew context:nil];
            
            // 初始化播放器
            self.pictureInPicturePlayer = [AVPlayer playerWithPlayerItem:self.pictureInPicturePlayerItem];
            [self.pictureInPicturePlayer addObserver:self forKeyPath:@"rate" options:NSKeyValueObservingOptionNew context:nil];
            self.pictureInPicturePlayer.volume = 0.8;//设置volume为0.8，避免画中画音量过小，注意volume不会受系统音量改变而改变
            self.originalVolume = self.pictureInPicturePlayer.volume;

            if (@available(iOS 15.0, *)) {
                [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleAVPlayerRateDidChange:) name:AVPlayerRateDidChangeNotification object:self.pictureInPicturePlayer];
            }
            
            // 初始化播放图层
            self.playerLayerSuperview = displaySuperview;
            self.pictureInPicturePlayerLayer = [AVPlayerLayer playerLayerWithPlayer:self.pictureInPicturePlayer];
            self.pictureInPicturePlayerLayer.videoGravity = AVLayerVideoGravityResizeAspect;
            self.pictureInPicturePlayerLayer.frame = displaySuperview.bounds;
            [displaySuperview.layer insertSublayer:self.pictureInPicturePlayerLayer atIndex:0];
            [self.playerLayerSuperview addObserver:self forKeyPath:@"frame" options:NSKeyValueObservingOptionNew context:nil];
            
            // 初始化画中画控制器
            self.pictureInPictureController = [[AVPictureInPictureController alloc]initWithPlayerLayer:self.pictureInPicturePlayerLayer];
            [self setPictureInPictureControllerControlsVisible:self.pictureInPictureControlsVisible];
            if (@available(iOS 14.2, *)) {
                // home键之后自动开启画中画
                self.pictureInPictureController.canStartPictureInPictureAutomaticallyFromInline = _canAutoStartPictureInPicture;
            }
            self.pictureInPictureController.delegate = self;
            self.isPlayingContentUrl = NO;
        }else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePictureInPictureManager - The current device does not support 'PictureInPicture'");
        }
    } else {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePictureInPictureManager - 'PictureInPicture' is only available on iOS 9.0 or newer");
    }
}

/// 开启画中画，支持加密视频
- (void)startPictureInPictureWithPlayer:(PLVPlayer <PLVLivePlayerPictureInPictureProtocol> *)player {
    if (@available(iOS 15.0, *)){
        [self setupPictureInPictureWithPlayer:player];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self startPictureInPicture];
        });
    }
}

/// 后台模式，初始化画中画配置
- (void)bindPictureInPictureForBackgroundMode:(PLVPlayer <PLVLivePlayerPictureInPictureProtocol> *)player {
    if (self.livePlayer && self.livePlayer == player && self.pictureInPictureController){
        return;
    }
    
    if (@available(iOS 15.0, *)) {
        [self setupPictureInPictureWithPlayer:player];
    }
}

- (void)unbindPictureInPicturePlayer:(PLVPlayer<PLVLivePlayerPictureInPictureProtocol> *)player {
    if (self.livePlayer == player){
        self.livePlayer = nil;
    }
}

- (void)setupPictureInPictureWithPlayer:(PLVPlayer *)player API_AVAILABLE(ios(15.0)) {
    if (self.pictureInPictureMode != PLVLivePictureInPictureMode_IJKPlayer) {
        return;
    }
    
    self.livePlayer = nil;
    if ([AVPictureInPictureController isPictureInPictureSupported]) {
        if ([player isKindOfClass:[PLVLivePlayer class]]) {
            self.livePlayer = (PLVLivePlayer *)player;
        } else if ([player isKindOfClass:[PLVLivePlaybackPlayer class]]) {
            self.livePlayer = (PLVLivePlaybackPlayer *)player;
        }
        UIView *displaySuperview = player.displaySuperview;
        BOOL updateLayer = NO;
        for (UIView *subview in displaySuperview.subviews) {
            if ([subview.layer isKindOfClass:[AVSampleBufferDisplayLayer class]] &&
                ![self.sampleBufferDisplayLayer isEqual:subview.layer]) {
                updateLayer = YES;
                self.sampleBufferDisplayLayer = (AVSampleBufferDisplayLayer *)subview.layer;
            }
        }
        if (self.sampleBufferDisplayLayer) {
            if (@available(iOS 9.0, *)) {
                if (!_pictureInPictureController) {
                    self.pictureInPictureController = [self createPictureInPictureController];
                    [self setPictureInPictureControllerControlsVisible:self.pictureInPictureControlsVisible];
                } else if (updateLayer) {
                    if (@available(iOS 15.0, *)) {
                        AVPictureInPictureControllerContentSource *pipContentSource = [[AVPictureInPictureControllerContentSource alloc] initWithSampleBufferDisplayLayer:self.sampleBufferDisplayLayer playbackDelegate:self];
                        self.pictureInPictureController.contentSource = pipContentSource;
                    }
                }
            }
            [self setupTimebase];
        }
    } else {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePictureInPictureManager - The current device does not support 'PictureInPicture'");
    }
}

- (void)stopPictureInPicture {
    if (@available(iOS 9.0, *)) {
        if (!self.pictureInPictureController) {
            return;
        }
        [self.pictureInPictureController stopPictureInPicture];
    }else {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePictureInPictureManager - 'PictureInPicture' is only available on iOS 9.0 or newer");
    }
}

- (BOOL)checkPictureInPictureSupported {
    if (@available(iOS 9.0, *)) {
        return [AVPictureInPictureController isPictureInPictureSupported];
    }
    return NO;
}

- (void)replayPictureInPicturePlayerWithURL:(NSURL *)contentURL {
    if (!self.pictureInPicturePlayer) {
        return;
    }
    
    [self.pictureInPicturePlayerItem removeObserver:self forKeyPath:@"status" context:nil];
    if ([self isLocalFileURL:contentURL]) {
        contentURL = [NSURL fileURLWithPath:contentURL.absoluteString];
    }
    self.pictureInPicturePlayerItem = [AVPlayerItem playerItemWithURL:contentURL];
    [self.pictureInPicturePlayerItem addObserver:self forKeyPath:@"status" options:NSKeyValueObservingOptionNew context:nil];
    [self.pictureInPicturePlayer replaceCurrentItemWithPlayerItem:self.pictureInPicturePlayerItem];
}

- (void)seekPictureInPicturePlayerToTime:(NSTimeInterval)toTime {
    if (_pictureInPicturePlayer) {
        // 精准的跳转到某一刻
        [self.pictureInPicturePlayer seekToTime:CMTimeMake(toTime, 1) toleranceBefore:kCMTimeZero toleranceAfter:kCMTimeZero];
    }
}

- (void)updatePictureInPictureControlPlaybackTime:(NSTimeInterval)playbackTime {
    CMTime currentTime = CMTimebaseGetTime(self.sampleBufferDisplayLayer.controlTimebase);
    if (fabs(currentTime.value/currentTime.timescale - playbackTime) > 2) {
        CMTimebaseSetTime(self.sampleBufferDisplayLayer.controlTimebase, CMTimeMake(playbackTime, 1));
        if (_pictureInPictureController) {
            if (@available(iOS 15.0, *)) {
                [self.pictureInPictureController invalidatePlaybackState];
            }
        }
    }
}

- (void)mute {
    if (self.pictureInPicturePlayer) {
        self.pictureInPicturePlayer.volume = 0.0;
    }
}

- (void)cancelMute {
    if (self.pictureInPicturePlayer) {
        self.pictureInPicturePlayer.volume = self.originalVolume;
    }
}

#pragma mark - [ Private Method ]

- (AVPictureInPictureController *)createPictureInPictureController API_AVAILABLE(ios(9.0)){
    if (@available(iOS 15.0, *)) {
        AVPictureInPictureControllerContentSource *pipContentSource = [[AVPictureInPictureControllerContentSource alloc] initWithSampleBufferDisplayLayer:self.sampleBufferDisplayLayer playbackDelegate:self];
        AVPictureInPictureController *pipController = [[AVPictureInPictureController alloc] initWithContentSource:pipContentSource];
        // 后台模式自动播放
        pipController.canStartPictureInPictureAutomaticallyFromInline = _canAutoStartPictureInPicture;
        pipController.delegate = self;
        return pipController;
    }
    return nil;
}

- (void)syncPictureInPicturePlayControlStatus:(BOOL)playing {
    self.isPlaybackPaused = !playing;
    if (_pictureInPictureController) {
        if (@available(iOS 15.0, *)) {
            [self.pictureInPictureController invalidatePlaybackState];
        }
    }
}

- (void)setupTimebase {
    /*
     * 因为bug可能会引入私有API,如果遇到可先注释下面代码
     */
    CMTimebaseRef timebase;
    CMTimebaseCreateWithSourceClock(nil, CMClockGetHostTimeClock(), &timebase);
    CMTimebaseSetTime(timebase, kCMTimeZero);
    CMTimebaseSetRate(timebase, 1);
    self.sampleBufferDisplayLayer.controlTimebase = timebase;
    if (timebase) {
        CFRelease(timebase);
    }
}

- (void)seekLivePlaybackPlayerToTime:(NSTimeInterval)toTime {
    if (self.livePlayer == nil || toTime <= 0 ||
        ![self.livePlayer isKindOfClass:[PLVLivePlaybackPlayer class]]) {
        return;
    }
    
    SEL selector = NSSelectorFromString(@"seekLivePlaybackToTime:");
    if ([self.livePlayer respondsToSelector:selector]) {
        (((void (*)(id, SEL, double))[self.livePlayer methodForSelector:selector])(self.livePlayer, selector, toTime));
    }
}

/// 清理画中画功能
- (void)cleanPictureInPicturePlayer {
    self.playerLayerSuperview = nil;
    [self.pictureInPicturePlayerLayer removeFromSuperlayer];
    self.pictureInPicturePlayerLayer = nil;
    
    if (self.pictureInPicturePlayerItem) {
        [self.pictureInPicturePlayerItem removeObserver:self forKeyPath:@"status" context:nil];
        [self.pictureInPicturePlayerItem cancelPendingSeeks];
    }
    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                    name:nil
                                                  object:self.pictureInPicturePlayerItem];
    self.pictureInPicturePlayerItem = nil;
    
    [self.pictureInPicturePlayer removeObserver:self forKeyPath:@"rate" context:nil];
    [self.pictureInPicturePlayer pause];
    
    if (@available(iOS 15.0, *)) {
        [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerRateDidChangeNotification object:self.pictureInPicturePlayer];
    }
    
    self.pictureInPicturePlayer = nil;
    
    if (@available(iOS 9.0, *)) {
        self.pictureInPictureController.delegate = nil;
        self.pictureInPictureController = nil;
    }
    
    self.livePlayer = nil;
    self.sampleBufferDisplayLayer = nil;
    self.pictureInPictureStarting = NO;
    self.isPlayingContentUrl = NO;
}

/// 开启画中画
- (void)startPictureInPicture {
    if (@available(iOS 9.0, *)) {
        if (!self.pictureInPictureController) {
            return;
        }
        [self.pictureInPictureController startPictureInPicture];
    }else {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePictureInPictureManager - 'PictureInPicture' is only available on iOS 9.0 or newer");
    }
    // 手动模式开启画中画
    self.isAutoStarted = NO;
}

- (BOOL)isLocalFileURL:(NSURL *)url {
    NSString *scheme = [url scheme];
    return ![scheme isEqualToString:@"http"] && ![scheme isEqualToString:@"https"];
}

- (void)setPictureInPictureControllerControlsVisible:(BOOL)visible {
    if (@available(iOS 9.0, *)) {
        if (self.pictureInPictureController) {
            NSInteger controlsStyle = (self.pictureInPictureControlsVisible ? (visible ? 0 : 1) : 1);
            [self.pictureInPictureController setValue:[NSNumber numberWithInteger:controlsStyle] forKey:@"controlsStyle"];
        }
    }
}

#pragma mark Getter & Setter
- (BOOL)pictureInPicturePlayerPlaying {
    if (self.pictureInPictureMode == PLVLivePictureInPictureMode_AVPlayer) {
        if (!self.pictureInPicturePlayer) {
            return NO;
        }
        if (@available(iOS 10.0, *)) {
            return self.isPlayingContentUrl && self.pictureInPicturePlayer.timeControlStatus == AVPlayerTimeControlStatusPlaying;
        } else {
            return self.isPlayingContentUrl && self.pictureInPicturePlayer.rate != 0.0;
        }
    } else {
        if (self.livePlayer) {
            return self.livePlayer.playing;
        }
        return NO;
    }
}

- (BOOL)pictureInPictureActive {
    if (@available(iOS 9.0, *)) {
        if (!self.pictureInPictureController) {
            return NO;
        }
        return self.pictureInPictureController.pictureInPictureActive;
    } else {
        return NO;
    }
}

- (void)setPictureInPictureControlsVisible:(BOOL)pictureInPictureControlsVisible {
    _pictureInPictureControlsVisible = pictureInPictureControlsVisible;
    [self setPictureInPictureControllerControlsVisible:pictureInPictureControlsVisible];
}

- (void)setCanAutoStartPictureInPicture:(BOOL)canAutoStartPictureInPicture {
    _canAutoStartPictureInPicture = canAutoStartPictureInPicture;
    if (@available(iOS 14.2, *)) {
        self.pictureInPictureController.canStartPictureInPictureAutomaticallyFromInline = canAutoStartPictureInPicture;
    }
}

- (CMTime)duration {
    if (self.livePlayer) {
        return CMTimeMake(self.livePlayer.duration, 1);
    }
    return CMTimeMake(0, 1);
}

#pragma mark - [ KVO ]

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context {
    // 监听播放状态，需要ReadyToPlay才能开启画中画，否则不能开启且无回调无报错
    if (object == self.pictureInPicturePlayerItem &&
        [keyPath isEqualToString:@"status"]) {
        if (self.pictureInPicturePlayer.status == AVPlayerItemStatusReadyToPlay &&
            self.pictureInPictureStarting) {
            [self.pictureInPicturePlayer pause];
            dispatch_async(dispatch_get_main_queue(), ^{
                [self startPictureInPicture];
            });
        }
    }else if (object == self.pictureInPicturePlayer &&
              [keyPath isEqualToString:@"rate"]) {
        CGFloat rate = [[change objectForKey:NSKeyValueChangeNewKey]floatValue];
        BOOL playing = rate != 0.0;
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvPictureInPicturePlayerPlayingStateDidChange:)]) {
            [self.delegate plvPictureInPicturePlayerPlayingStateDidChange:playing];
        }
        if (playing) {
            [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:nil];
            [[AVAudioSession sharedInstance] setActive:YES error:nil];
        }
    }else if (object == self.playerLayerSuperview &&
              [keyPath isEqualToString:@"frame"]) {
        CGRect playerFrame = CGRectNull;
        if([object valueForKeyPath:keyPath] != [NSNull null]) {
            playerFrame = [[object valueForKeyPath:keyPath] CGRectValue];
            self.pictureInPicturePlayerLayer.frame = CGRectMake(0, 0, playerFrame.size.width, playerFrame.size.height);
        }
    }
}

- (void)handleAVPlayerRateDidChange:(NSNotification *)notification {
    if (@available(iOS 15.0, *)) {
        AVPlayer *player = [notification object];
        float rate = 0.0;
        if (player && [player isKindOfClass:AVPlayer.class]) {
            rate = [[player valueForKey:@"rate"] floatValue];
        }
        NSString *rateDidChangeReason = [[notification userInfo]objectForKey:AVPlayerRateDidChangeReasonKey];
        BOOL playing = rate != 0.0;
        if ([PLVFdUtil checkStringUseable:rateDidChangeReason] &&
            ([rateDidChangeReason isEqualToString:AVPlayerRateDidChangeReasonAudioSessionInterrupted] ||
             [rateDidChangeReason isEqualToString:AVPlayerRateDidChangeReasonSetRateCalled])) {
            BOOL systemInterrupts = [rateDidChangeReason isEqualToString:AVPlayerRateDidChangeReasonAudioSessionInterrupted];
            if (self.delegate && [self.delegate respondsToSelector:@selector(plvPictureInPicturePlayerPlayingStateDidChange:systemInterrupts:)]) {
                [self.delegate plvPictureInPicturePlayerPlayingStateDidChange:playing systemInterrupts:systemInterrupts];
            }
        }
    }
}

#pragma mark - [ Delegate ]
#pragma mark AVPictureInPictureControllerDelegate
-(void)pictureInPictureControllerWillStartPictureInPicture:(AVPictureInPictureController *)pictureInPictureController
API_AVAILABLE(ios(9.0)) {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"即将开启画中画");
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvPictureInPictureWillStart)]) {
        [self.delegate plvPictureInPictureWillStart];
    }
}
  
-(void)pictureInPictureControllerDidStartPictureInPicture:(AVPictureInPictureController *)pictureInPictureController
API_AVAILABLE(ios(9.0)) {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"已经开启画中画");
    self.pictureInPictureStarting = NO;
    self.isPlayingContentUrl = YES;
    
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvPictureInPictureDidStart)]) {
        [self.delegate plvPictureInPictureDidStart];
    }
    
    if (self.pictureInPictureMode == PLVLivePictureInPictureMode_AVPlayer) {
        [self.pictureInPicturePlayer play];
    } else if (@available(iOS 15.0, *)) {
        if (self.pictureInPictureMode == PLVLivePictureInPictureMode_IJKPlayer) {
            self.isPlaybackPaused = !self.livePlayer.playing;
        }
    }
}
  
-(void)pictureInPictureController:(AVPictureInPictureController *)pictureInPictureController failedToStartPictureInPictureWithError:(NSError *)error API_AVAILABLE(ios(9.0)) {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"开启画中画失败-%@", error);
    [self cleanPictureInPicturePlayer];
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvPictureInPictureFailedToStartWithError:)]) {
        [self.delegate plvPictureInPictureFailedToStartWithError:error];
    }
}
  
-(void)pictureInPictureControllerWillStopPictureInPicture:(AVPictureInPictureController *)pictureInPictureController API_AVAILABLE(ios(9.0)) {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"即将关闭画中画");
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvPictureInPictureWillStop)]) {
        [self.delegate plvPictureInPictureWillStop];
    }
}
  
-(void)pictureInPictureControllerDidStopPictureInPicture:(AVPictureInPictureController *)pictureInPictureController API_AVAILABLE(ios(9.0)) {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"已经关闭画中画");
    self.restorePictureInPicture = NO;
    self.isAutoStarted = YES; // 恢复默认模式

    if (self.pictureInPictureMode == PLVLivePictureInPictureMode_AVPlayer) {
        [self.playerLayerSuperview removeObserver:self forKeyPath:@"frame" context:nil];
        double currentTime = CMTimeGetSeconds(self.pictureInPicturePlayer.currentTime);
        [self seekLivePlaybackPlayerToTime:currentTime];
    }
    [self cleanPictureInPicturePlayer];
    
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvPictureInPictureDidStop)]) {
        [self.delegate plvPictureInPictureDidStop];
    }
}

/// 最后执行
-(void)pictureInPictureController:(AVPictureInPictureController *)pictureInPictureController restoreUserInterfaceForPictureInPictureStopWithCompletionHandler:(void (^)(BOOL))completionHandler API_AVAILABLE(ios(9.0)) {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"关闭画中画且恢复播放界面");
    // 点击画中画恢复按钮
    if (self.restoreDelegate &&
        [self.restoreDelegate respondsToSelector:@selector(plvPictureInPictureRestoreUserInterfaceForPictureInPictureStopWithCompletionHandler:)]) {
        [self.restoreDelegate plvPictureInPictureRestoreUserInterfaceForPictureInPictureStopWithCompletionHandler:completionHandler];
    }else {
        completionHandler(YES);
    }
}

#pragma mark - AVPictureInPictureSampleBufferPlaybackDelegate
///  PiP 窗口大小改变
- (void)pictureInPictureController:(AVPictureInPictureController *)pictureInPictureController
         didTransitionToRenderSize:(CMVideoDimensions)newRenderSize  API_AVAILABLE(ios(9.0)){
    
}

/// 点击 PiP 窗口中的播放/暂停
- (void)pictureInPictureController:(nonnull AVPictureInPictureController *)pictureInPictureController
                        setPlaying:(BOOL)playing  API_AVAILABLE(ios(9.0)){
    /// 当前处于恢复状态时不需要处理此播放事件
    if (!self.restorePictureInPicture) {
        if (playing) {
            if (self.livePlayer) {
                [self.livePlayer play];
            }
        } else {
            if (self.livePlayer) {
                [self.livePlayer pause];
            }
        }
        [self syncPictureInPicturePlayControlStatus:playing];
    }
}

/// 点击 PiP 窗口中的快进后图
- (void)pictureInPictureController:(nonnull AVPictureInPictureController *)pictureInPictureController
                    skipByInterval:(CMTime)skipInterval completionHandler:(nonnull void (^)(void))completionHandler  API_AVAILABLE(ios(9.0)){
    int64_t skipTime = skipInterval.value / skipInterval.timescale;
    int64_t skipPosition = self.livePlayer.currentPlaybackTime + skipTime;
    if (skipPosition < 0) {
        skipPosition = 0;
    } else if (skipPosition > self.livePlayer.duration) {
        skipPosition = self.livePlayer.duration;
    }
    [self seekLivePlaybackPlayerToTime:skipPosition];
    [self.livePlayer play];
    CMTimebaseSetTime(self.sampleBufferDisplayLayer.controlTimebase, CMTimeMake(skipPosition * skipInterval.timescale, skipInterval.timescale));
    if (@available(iOS 15.0, *)) {
        [self.pictureInPictureController invalidatePlaybackState];
    }
    completionHandler();
}

/// 当前视频是否处于暂停状态
/// 当点击播放/暂停按钮时，PiP 会调用该方法，决定 setPlaying: 的值，同时该方法返回值也决定了PiP窗口展示击播放/暂停 icon
- (BOOL)pictureInPictureControllerIsPlaybackPaused:(nonnull AVPictureInPictureController *)pictureInPictureController  API_AVAILABLE(ios(9.0)){
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"pictureInPictureControllerIsPlaybackPaused: %@", @(self.isPlaybackPaused));
    return self.isPlaybackPaused;
}

/// 视频的可播放时间范围
- (CMTimeRange)pictureInPictureControllerTimeRangeForPlayback:(nonnull AVPictureInPictureController *)pictureInPictureController  API_AVAILABLE(ios(9.0)){
    if ([self duration].value > 0) {
        // 只有回放才会设置时间范围
        return CMTimeRangeMake(kCMTimeZero, [self duration]);
    } else {
        return CMTimeRangeMake(kCMTimeNegativeInfinity, kCMTimePositiveInfinity);
    }
}

@end
