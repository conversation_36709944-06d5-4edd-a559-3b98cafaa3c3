//
//  PLVLivePlaybackPlayer.m
//  PLVCloudClassSDK
//
//  Created by Lincal on 2020/12/5.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVLivePlaybackPlayer.h"

#import "PLVLiveVideoConfig.h"
#import "PLVLivePrivateAPI.h"
#import "PLVLiveVideoAPI.h"
#import "PLVLiveAPIUtils.h"
#import "PLVChannelInfoModel.h"
#import "PLVChannelInfoModel+PrivateInfo.h"
#import "PLVWErrorManager.h"
#import "PLVWELogEventDefine.h"
#import "PLVWLogReporterManager.h"
#import "PLVPlayer+SubClassExtension.h"
#import "PLVLivePictureInPictureManager+Private.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVQosLoadingTracerModel.h"

#import "PLVConsoleLogger.h"

@interface PLVLivePlaybackPlayer ()<
PLVLivePictureInPictureManagerDelegate
>

#pragma mark 状态
@property (nonatomic, assign) BOOL isFinished; // 是否已播放完成
@property (nonatomic, assign) BOOL loadingLogSent; // 是否已发送loading日志
@property (nonatomic, assign) BOOL recordingBufferTime; // 是否正在记录buffer时间
@property (nonatomic, assign) BOOL playerErrorSend; // 是否已发送error日志
@property (nonatomic, assign) BOOL shouldContinuePlay; // 是否需要续播
@property (nonatomic, assign) NSTimeInterval interruptionTime; // 播放中断时的时间戳

#pragma mark 数据
@property (nonatomic, copy) NSString * accountUserId;
@property (nonatomic, copy) NSString * channelId;
@property (nonatomic, copy) NSString * vodId;
@property (nonatomic, copy) NSString *videoId;
@property (nonatomic, copy) NSString * fileId;

@property (nonatomic, assign) BOOL vodList;
@property (nonatomic, strong) PLVChannelInfoModel * channelInfo;
@property (nonatomic, copy) NSURL * contentURL;
@property (nonatomic, strong) PLVPlaybackVideoInfoModel * videoInfo;
@property (nonatomic, assign) BOOL isPlayCacheVideo; // 是否播放的缓存视频

@property (nonatomic, assign) CGFloat downloadProgress;
@property (nonatomic, assign) CGFloat playedProgress;
@property (nonatomic, assign) NSInteger lastErrorCode;
@property (nonatomic, assign) NSInteger maxRetryCount;
@property (nonatomic, assign) NSInteger retryCount;

@property (nonatomic, copy) NSString * lppi; /// 直播回放播放器ID (简化命名作保护，原名livePlaybackPlayerId，不允许公开)
@property (nonatomic, assign) NSTimeInterval wtti; /// 用户观看时长 (不包含连麦) (简化命名作保护，原名watchDurationTimeInterval，不允许公开)
@property (nonatomic, assign) NSTimeInterval stti; /// 用户停留时长 (简化命名作保护，原名stayDurationTimeInterval，不允许公开)
@property (nonatomic, assign) NSTimeInterval lwtti; /// 上个用户观看时长值 (简化命名作保护，原名lastWatchDurationTimeInterval，不允许公开)

@property (nonatomic, copy) NSString *playerId; /// 直播回放播放器ID （同 lppi 生成规则一致，从加载播放器成功，即生成一个playerid，过程中没有销毁播放器，则始终用同一个playerid，一个playerId可对应多个pid）

@property (nonatomic, strong) NSDate *loadStartDate; // 记录准备播放的时间，用于loading日志
@property (nonatomic, strong) NSDate *bufferStartDate; // 记录开始缓冲的时间，用于buffer日志
@property (nonatomic, assign) NSInteger time_loading; // 记录播放器加载业务流程消耗的时间，用于loading日志
@property (nonatomic, assign) NSInteger time_player; // 记录播放器起播的时间，用于loading日志
@property (nonatomic, strong) NSMutableArray <PLVQosLoadingTracerModel *> *tracerModels; //记录各个播放器起播业务流程信息

@property (nonatomic, assign) NSTimeInterval lastKeepPlayTime;// 跨端续播时长

#pragma mark 对象
@property (nonatomic, strong) NSTimer * progressTimer;
@property (nonatomic, strong) NSTimer * liveStatusTimer;

@end

@implementation PLVLivePlaybackPlayer
@synthesize pictureInPictureDelegate, canAutoStartPictureInPicture;

#pragma mark - [ Life Periode ]
- (void)dealloc{
    if (_progressTimer) {
        [_progressTimer invalidate];
        _progressTimer = nil;
    }
    if (_liveStatusTimer) {
        [_liveStatusTimer invalidate];
        _liveStatusTimer = nil;
    }
    [[PLVLivePictureInPictureManager sharedInstance] cleanPictureInPicturePlayer];
    PLV_LOG_INFO(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
}


#pragma mark - [ Public Methods ]
- (instancetype)initWithPLVAccountUserId:(NSString *)accountUserId channelId:(NSString *)channelId vodId:(NSString *)vodId vodList:(BOOL)vodList liveDelegate:(id<PLVLivePlaybackPlayerDelegate>)delegate {
    if (![PLVFdUtil checkStringUseable:channelId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePlaybackPlayer - init failed, parameters illegal, channelId:%@", channelId);
        return nil;
    }
    
    if (self = [super init]) {
        self.accountUserId = accountUserId;
        self.channelId = channelId;
        self.vodId = vodId;
        self.vodList = vodList;
        _livePlaybackDelegate = delegate;
        PLV_KEY_INFO(@"mediaIdList", channelId);
        
        [self setup];
        
        if (![PLVFdUtil checkStringUseable:self.vodId]) {
            [self requestPlaybackSetting];
        } else {
            [self requestPlaybackVideoChannelInfo];
        }
    }
    return self;
}

- (instancetype)initWithPolyvAccountUserId:(NSString *)accountUserId channelId:(NSString *)channelId vodId:(NSString *)vodId vodList:(BOOL)vodList{
    return [self initWithPLVAccountUserId:accountUserId channelId:channelId vodId:vodId vodList:vodList liveDelegate:nil];
}

- (instancetype)initWithPLVAccountUserId:(NSString *)accountUserId channelId:(NSString *)channelId recordFile:(PLVLiveRecordFileModel *)recordFile liveDelegate:(id<PLVLivePlaybackPlayerDelegate>)delegate {
    if (![PLVFdUtil checkStringUseable:channelId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePlaybackPlayer - init failed, parameters illegal, channelId:%@", channelId);
        return nil;
    }
    if (!recordFile || (![PLVFdUtil checkStringUseable:recordFile.mp4] && ![PLVFdUtil checkStringUseable:recordFile.m3u8]) || ![PLVFdUtil checkStringUseable:recordFile.channelSessionId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePlaybackPlayer - init failed, recordFile illegal");
        return nil;
    }
    if (self = [super init]) {
        self.accountUserId = accountUserId;
        self.channelId = channelId;
        _livePlaybackDelegate = delegate;
        PLV_KEY_INFO(@"mediaIdList", channelId);;
        [self setup];
        
        PLVPlaybackVideoInfoModel *vodModel = [[PLVPlaybackVideoInfoModel alloc]init];
        vodModel.videoPoolId = @"";
        vodModel.fileUrl = recordFile.m3u8? :recordFile.mp4;
        vodModel.duration = recordFile.duration;
        vodModel.channelId = self.channelId;
        vodModel.channelSessionId = recordFile.channelSessionId;
        vodModel.listType = @"record";
        vodModel.originSessionId = recordFile.originSessionId;
        vodModel.fileId = recordFile.fileId;
        self.videoInfo = vodModel;
        self.fileId = recordFile.fileId;
        [self requestRecordVideoChannelInfo];
    }
    return self;
}

- (void)seekLivePlaybackToTime:(NSTimeInterval)toTime{
    [super seekToTime:toTime];
    if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureActive &&
        [PLVLivePictureInPictureManager sharedInstance].pictureInPictureMode == PLVLivePictureInPictureMode_AVPlayer) {
        [self pause];
        [[PLVLivePictureInPictureManager sharedInstance] seekPictureInPicturePlayerToTime:toTime];
    }
}

- (void)switchLivePlaybackSpeedRate:(CGFloat)toSpeed{
    [super switchSpeedRate:toSpeed];
}

- (void)changeLivePlaybackVodId:(NSString *)vodId{
    if ([self.vodId isEqualToString:vodId]) {
        return;
    }
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"change vodId:%@", vodId);
    self.vodId = vodId;
    
    [self clearAllPlayer];
    [self requestPlaybackVideoChannelInfo];
}

- (void)changeLivePlaybackFileId:(NSString *)fileId {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"change fileId:%@", fileId);
    self.fileId = fileId;
    
    [self requestRecordVideoChannelInfo];
}

- (void)startPictureInPictureFromOriginView:(UIView *)originView {
    if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureMode == PLVLivePictureInPictureMode_AVPlayer) {
        NSURL *contentURL = [self createPictureInPictureContentURL];
        if (contentURL) {
            [[PLVLivePictureInPictureManager sharedInstance] startPictureInPictureWithContentURL:contentURL displaySuperview:originView];
        } else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePlaybackPlayer - startPictureInPicture failed, contentURL must not be nil");
        }
    } else if (@available(iOS 15.0, *)) {
        if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureMode == PLVLivePictureInPictureMode_IJKPlayer) {
            [[PLVLivePictureInPictureManager sharedInstance] startPictureInPictureWithPlayer:self];
        }
    }
}

- (void)stopPictureInPicture {
    [[PLVLivePictureInPictureManager sharedInstance] stopPictureInPicture];
}

- (void)setUpdateAutoStartPictureInPicture:(BOOL)updateAutoStartPictureInPicture{
    self.canAutoStartPictureInPicture = updateAutoStartPictureInPicture;
    
    if (self.canAutoStartPictureInPicture){
        // 画中画退到后台自动启动，入口
        [PLVLivePictureInPictureManager sharedInstance].canAutoStartPictureInPicture = self.canAutoStartPictureInPicture;
        [PLVLivePictureInPictureManager sharedInstance].delegate = self;
        [[PLVLivePictureInPictureManager sharedInstance] bindPictureInPictureForBackgroundMode:self];
    }
    else{
        // 清理画中画
        [[PLVLivePictureInPictureManager sharedInstance] cleanPictureInPicturePlayer];
    }
}

#pragma mark Getter
- (NSString *)playedTimeString{
    return [PLVFdUtil secondsToString:self.currentPlaybackTime];
}

- (NSString *)durationTimeString{
    return [PLVFdUtil secondsToString:self.duration];
}

- (NSString *)livePlaybackPlayerId{
    return _lppi;
}

- (NSInteger)watchDuration{
    return floor(_wtti);
}

- (NSInteger)stayDuration{
    return floor(_stti);
}

#pragma mark - [ Father Public Methods ]
- (void)play{
    if (self.vodId && !self.videoId && !self.videoInfo) {
        __weak typeof(self) weakSelf = self;
        [self requestPlaybackVideoInfo:^{
            [weakSelf loadMainPlayer];
        }];
    }
    if (self.isFinished) {
        [super seekToTime:0];
    } else if (self.shouldContinuePlay) {
        [super seekToTime:self.interruptionTime];
        self.shouldContinuePlay = NO;
        self.interruptionTime = 0;
    }
    [super play];
}

- (void)clearAllPlayer {
    [[PLVLivePictureInPictureManager sharedInstance] unbindPictureInPicturePlayer:self];
    [super clearAllPlayer];
}

#pragma mark - [ Private Methods ]
- (void)setup{
    self.videoToolBox = YES;
    self.shouldContinuePlay = NO;
    self.maxRetryCount = 1;
    [self resetPlayData];
    self.playerId = self.lppi;
    self.tracerModels = [NSMutableArray array];
    
    self.loadStartDate = [NSDate date];
    self.progressTimer = [NSTimer scheduledTimerWithTimeInterval:0.5 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(progressTimerEvent:) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:self.progressTimer forMode:NSRunLoopCommonModes];
    
    if (!self.vodList) {
        self.liveStatusTimer = [NSTimer scheduledTimerWithTimeInterval:10.0 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(liveStatusTimerEvent:) userInfo:nil repeats:YES];
        [[NSRunLoop currentRunLoop] addTimer:self.liveStatusTimer forMode:NSRunLoopCommonModes];
    }
    
    if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureActive) {
        [PLVLivePictureInPictureManager sharedInstance].restoreDelegate = nil;
        [[PLVLivePictureInPictureManager sharedInstance] stopPictureInPicture];
    }
}

- (void)requestPlaybackSetting {
    __weak typeof(self) weakSelf = self;
    BOOL shouldASKForPlaybackSetting = ![PLVFdUtil checkStringUseable:self.vodId];
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    [PLVLiveVideoAPI requestChannelPlaybackInfoWithChannelId:self.channelId appId:liveConfig.appId appSecret:liveConfig.appSecret vid:nil playbackType:self.vodList ? @"vod" : nil completion:^(PLVChannelPlaybackInfoModel * _Nullable channelPlaybackInfo) {
        if (channelPlaybackInfo) {
            if (!channelPlaybackInfo.enablePlayBack && shouldASKForPlaybackSetting) {
                NSError *error = [weakSelf reportErrorWithCode:PLVFPlayErrorCodeGetVideoInfo_DataError description:@"The playback switch is not enabled."];
                [weakSelf callbackForLoadMainPlayerFailureError:error];
                return;
            }
            
            if (shouldASKForPlaybackSetting && ![PLVFdUtil checkStringUseable:channelPlaybackInfo.playbackOrigin]) {
                NSError *error = [self reportErrorWithCode:PLVFPlayErrorCodeGetVideoInfo_DataError description:@"The playback origin is nil."];
                [weakSelf callbackForLoadMainPlayerFailureError:error];
                return;
            }
            
            if (shouldASKForPlaybackSetting && ![PLVFdUtil checkStringUseable:channelPlaybackInfo.type]) {
                NSError *error = [self reportErrorWithCode:PLVFPlayErrorCodeGetVideoInfo_DataError description:@"The playback type is nil."];
                [weakSelf callbackForLoadMainPlayerFailureError:error];
                return;
            }
            
            BOOL sectionEnabled = channelPlaybackInfo.sectionEnabled;
            // 回放方式-单个视频
            if ([channelPlaybackInfo.type isEqualToString:@"single"]) {
                if ([channelPlaybackInfo.playbackOrigin isEqualToString:@"record"]) {
                    if (!channelPlaybackInfo.hasRecordFile || !channelPlaybackInfo.recordFile) {
                        NSError *error = [self reportErrorWithCode:PLVFPlayErrorCodeGetVideoInfo_DataError description:@"The record fil is nil."];
                        [self callbackForLoadMainPlayerFailureError:error];
                        return;
                    }
                    PLVLiveRecordFileModel *recordFile = channelPlaybackInfo.recordFile;
                    PLVPlaybackVideoInfoModel *vodModel = [[PLVPlaybackVideoInfoModel alloc]init];
                    vodModel.videoPoolId = @"";
                    vodModel.fileUrl = recordFile.m3u8? :recordFile.mp4;
                    vodModel.duration = recordFile.duration;
                    vodModel.channelId = self.channelId;
                    vodModel.channelSessionId = recordFile.channelSessionId;
                    vodModel.listType = @"record";
                    vodModel.originSessionId = recordFile.originSessionId;
                    vodModel.fileId = recordFile.fileId;
                    weakSelf.videoInfo = vodModel;
                    weakSelf.fileId = recordFile.fileId;
                    [weakSelf requestRecordVideoChannelInfo];
                } else if ([channelPlaybackInfo.playbackOrigin isEqualToString:@"vod"] || [channelPlaybackInfo.playbackOrigin isEqualToString:@"playback"] || [channelPlaybackInfo.playbackOrigin isEqualToString:@"material"]) {
                    if (!channelPlaybackInfo.hasPlaybackVideo || !channelPlaybackInfo.targetPlaybackVideo) {
                        if (!channelPlaybackInfo.hasRecordFile || !channelPlaybackInfo.recordFile) {
                            NSError *error = [self reportErrorWithCode:PLVFPlayErrorCodeGetVideoInfo_DataError description:@"The playback video is nil."];
                            [weakSelf callbackForLoadMainPlayerFailureError:error];
                            return;
                        }
                    }
                    weakSelf.vodList = [channelPlaybackInfo.playbackOrigin isEqualToString:@"vod"];
                    weakSelf.vodId = channelPlaybackInfo.targetPlaybackVideo.videoPoolId;
                    [weakSelf requestPlaybackVideoChannelInfo];
                } else {
                    // 回调回放类型不支持
                    NSError *error = [self reportErrorWithCode:PLVFPlayErrorCodeGetVideoInfo_DataError description:@"The playback origin is not supported."];
                    [weakSelf callbackForLoadMainPlayerFailureError:error];
                    return;
                }
                // 回调章节是否开启
                [weakSelf callbackForSectionEnabledDidUpdated:sectionEnabled recordEnabled:[channelPlaybackInfo.playbackOrigin isEqualToString:@"record"]];
            } else {
                // 回放方式-列表回放
                if (!channelPlaybackInfo.hasPlaybackVideo || !channelPlaybackInfo.targetPlaybackVideo) {
                    // 回调回放视频为空，回放视频不可为空
                    NSError *error = [self reportErrorWithCode:PLVFPlayErrorCodeGetVideoInfo_DataError description:@"The playback video is nil."];
                    [weakSelf callbackForLoadMainPlayerFailureError:error];
                    return;
                }
                weakSelf.vodList = [channelPlaybackInfo.playbackOrigin isEqualToString:@"vod"];
                weakSelf.vodId = channelPlaybackInfo.targetPlaybackVideo.videoPoolId;
                [weakSelf requestPlaybackVideoChannelInfo];
                // 回调章节是否开启
                [weakSelf callbackForSectionEnabledDidUpdated:sectionEnabled recordEnabled:NO];
                // 回调往期列表功能
                [weakSelf callbackForPlaybackListEnabledDidUpdated:YES vodList:weakSelf.vodList];
            }
        } else {
            // 回调获取频道回放开关获取解析失败
            NSError *error = [self reportErrorWithCode:PLVFPlayErrorCodeGetVideoInfo_DataError description:@"The playback origin is not supported."];
            [weakSelf callbackForLoadMainPlayerFailureError:error];
            return;
        }
    } failure:^(NSError * _Nullable error) {
        [weakSelf callbackForLoadMainPlayerFailureError:error];
        return;
    }];
}

- (void)requestPlaybackVideoChannelInfo {
    __weak typeof(self) weakSelf = self;
    __block NSInteger blockCount = 0;
    void (^requestDataBlock) (NSInteger) = ^(NSInteger count) {
        if (!weakSelf.isPlayCacheVideo && count == 2) {
            [weakSelf loadMainPlayer];
        }
    };
    [self requestChannelInfo:^{
        if (!weakSelf.vodList) {
            [weakSelf requestLiveStreamStateForStreamHandle];
        }
        requestDataBlock(blockCount += 1);
    }];
    [self requestPlaybackVideoInfo:^{
        if (weakSelf.isPlayCacheVideo) {
            [weakSelf loadMainPlayer];
        }
        requestDataBlock(blockCount += 1);
    }];
}

- (void)requestRecordVideoChannelInfo {
    __weak typeof(self) weakSelf = self;
    __block NSInteger blockCount = 0;
    void (^requestDataBlock) (NSInteger) = ^(NSInteger count) {
        if (!weakSelf.isPlayCacheVideo && count == 2) {
            [weakSelf loadMainPlayer];
        }
    };
    [self requestChannelInfo:^{
        [weakSelf requestLiveStreamStateForStreamHandle];
        requestDataBlock(blockCount += 1);
    }];
    [self requestRecordVideoInfo:^{
        if (weakSelf.isPlayCacheVideo) {
            [weakSelf loadMainPlayer];
        }
        requestDataBlock(blockCount += 1);
    }];
}

- (void)resetPlayData{
    self.lppi = [PLVLiveVideoConfig createPlayerId];
    self.wtti = 0;
    self.stti = 0;
    self.lwtti = 0;
}

/** liveStatus请求接口参数补充
    用于观看日志补充页面进出数据 */
- (NSMutableDictionary *)createExtraParamDict {
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    NSInteger currentPlaybackTime = round(self.currentPlaybackTime);
    paramDict[@"pid"] = self.lppi ?: @"";
    paramDict[@"uid"] = self.accountUserId ?: @"";
    paramDict[@"ts"] = @([PLVFdUtil curTimeInterval]);
    // paramDict[@"href"] = href;
    paramDict[@"pn"] = PLVLiveVideoConfig. sharedInstance.playerName ?: @"";
    paramDict[@"pv"] = PLVLiveVideoConfig.sharedInstance.playerVersion ?: @"";
    paramDict[@"sid"] = self.videoInfo.originSessionId ?: @"";//回放中为originsessionId
    paramDict[@"csid"] = self.videoInfo.channelSessionId ?: @"";// 回放才有的
    paramDict[@"p1"] = self.customParam.vodSid ?: @"";
    paramDict[@"p2"] = self.customParam.vodParam2 ?: @"";
    paramDict[@"p3"] = [PLVDataUtil urlSafeBase64String:@"vod"];
    paramDict[@"p4"] = self.customParam.vodParam4 ?: @"";
    paramDict[@"p5"] = self.customParam.vodParam5 ?: @"";
    NSString * listType = self.videoInfo.listType;
    if ([PLVFdUtil checkStringUseable:listType] && [listType isEqualToString:@"record"]) {
        paramDict[@"pb"] = @(1);
    } else {
        paramDict[@"pb"] = self.vodList ? @(3) : @(2);
    }
    paramDict[@"fid"] = self.videoInfo.fileId;
    paramDict[@"playerid"] = self.playerId ?: @"";// 播放器playerId 一个playerId对应多个pid
    paramDict[@"pd"] = @(self.watchDuration);
    paramDict[@"cts"] = [NSString stringWithFormat:@"%ld", currentPlaybackTime];
    return paramDict;
}

- (NSError *)reportErrorWithCode:(NSInteger)code description:(NSString *)description {
    NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPlay code:code];
    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:description];
    return failError;
}

- (NSURL *)createPictureInPictureContentURL {
    NSString *fileId = self.videoInfo.fileId;
    if ([PLVFdUtil checkStringUseable:fileId]) {
        PLVPlaybackLocalVideoInfoModel *localPlayerModel = [self getRecordInfoModelInLocal];
        if (localPlayerModel) {
            NSString *fileURLString = localPlayerModel.localVideoPath;
            NSURL *fileURL = [NSURL URLWithString:fileURLString];
            return fileURL;
        }
    }
    
    /// 解析 播放地址
    NSString * fileURLString = [NSString stringWithFormat:@"%@",self.videoInfo.fileUrl]; // fileUrl 地址带“_s”为直播录制源文件（不计点播流量的地址）
    /// 设置 播放地址 参数
    BOOL hadParams = [fileURLString containsString:@"?"];
    if ([PLVFdUtil checkStringUseable:self.customParam.liveParam1]) {
        NSString * param1 = [PLVDataUtil urlSafeBase64String:self.customParam.liveParam1];
        fileURLString = [fileURLString stringByAppendingFormat:@"%@param1=%@", (hadParams ? @"&" : @"?"), param1];
    }
    
    hadParams = [fileURLString containsString:@"?"];
    if ([PLVFdUtil checkStringUseable:self.lppi]) {
        NSString * playerId = [PLVDataUtil urlSafeBase64String:self.lppi];
        fileURLString = [fileURLString stringByAppendingFormat:@"%@pid=%@", (hadParams ? @"&" : @"?"), playerId];
    }
    
    NSURL *contentURL = [NSURL URLWithString:fileURLString];
    return contentURL;
}

#pragma mark 请求
- (void)requestPlaybackVideoInfo:(void (^)(void))successBlock{
    if (![PLVFdUtil checkStringUseable:self.vodId]) {
        NSError *error = [self reportErrorWithCode:PLVFPlayErrorCodeGetVideoInfo_DataError description:@"vid is empty."];
        [self callbackForLoadMainPlayerFailureError:error];
        return;
    }
    NSString * listType = self.vodList ? @"vod" : nil;
    __weak typeof(self) weakSelf = self;
    PLVQosLoadingTracerModel *infoModel = [[PLVQosLoadingTracerModel alloc] initWithType:PLVQosLoadingTracerModelTypePlaybackVideoInfo];
    [infoModel addStartTime];
    __block PLVPlaybackVideoInfoModel * model;
    void (^requestPlaybackVideoInfoBlock) (void) = ^() {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"request videoInfo success");
        plv_dispatch_main_async_safe(^{
            [weakSelf addTracerModelEndTimeToModels:infoModel];
            NSString *videoPooId = model.videoPoolId ? : @"";
            if (!([weakSelf.vodId rangeOfString:videoPooId].location == NSNotFound)) {
                if (![weakSelf.videoInfo.videoPoolId isEqualToString:videoPooId]) {
                    [weakSelf resetPlayData];
                }
                weakSelf.videoInfo = model;
                weakSelf.videoId = model.videoId;
                weakSelf.fileId = model.fileId;
                if (successBlock) { successBlock(); }
                [weakSelf callbackForPlaybackVideoInfoDidUpdated:weakSelf.videoInfo];
            }
        })
    };
    model = [self getPlaybackVideoInfoModelInLocal];
    if (model) {
        self.isPlayCacheVideo = YES;
        requestPlaybackVideoInfoBlock();
    } else {
        self.isPlayCacheVideo = NO;
        [PLVLivePrivateAPI loadVideoInfoWithVid:self.vodId channelId:self.channelId listType:listType completion:^(NSDictionary * _Nonnull data) {
            [weakSelf addTracerModelEndTimeToModels:infoModel];
            model = [PLVPlaybackVideoInfoModel playbackVideoInfoModelWithJsonDict:data];
            model.vid = weakSelf.vodId;
            requestPlaybackVideoInfoBlock();
        } failure:^(NSError * _Nonnull error) {
            [weakSelf addTracerModelEndTimeToModels:infoModel];
            [weakSelf callbackForLoadMainPlayerFailureError:error];
        }];
    }
}

/// 获取暂存视频的信息
- (void)requestRecordVideoInfo:(void (^)(void))successBlock{
    if (![PLVFdUtil checkStringUseable:self.videoInfo.fileId]) {
        NSError *error = [self reportErrorWithCode:PLVFPlayErrorCodeGetVideoInfo_DataError description:@"fileId is empty."];
        [self callbackForLoadMainPlayerFailureError:error];
        return;
    }
    __weak typeof(self) weakSelf = self;
    PLVQosLoadingTracerModel *infoModel = [[PLVQosLoadingTracerModel alloc] initWithType:PLVQosLoadingTracerModelTypeRecordVideoInfo];
    __block PLVPlaybackVideoInfoModel * model;
    void (^requestRecordVideoInfoBlock) (void) = ^() {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"request record videoInfo success");
        plv_dispatch_main_async_safe(^{
            [weakSelf addTracerModelEndTimeToModels:infoModel];
            weakSelf.videoInfo = model;
            if (successBlock) { successBlock(); }
            [weakSelf callbackForPlaybackVideoInfoDidUpdated:weakSelf.videoInfo];
        })
    };
    model = [self getRecordInfoModelInLocal];
    if (model) {
        self.isPlayCacheVideo = YES;
        requestRecordVideoInfoBlock();
    } else {
        self.isPlayCacheVideo = NO;
        [PLVLivePrivateAPI loadRecordVideoInfoWithFileId:self.videoInfo.fileId channelId:self.channelId completion:^(NSDictionary * _Nonnull data) {
            [weakSelf addTracerModelEndTimeToModels:infoModel];
            model = [PLVPlaybackVideoInfoModel playbackVideoInfoModelWithRecordJsonDict:data];
            requestRecordVideoInfoBlock();
        } failure:^(NSError * _Nonnull error) {
            [weakSelf addTracerModelEndTimeToModels:infoModel];
            if ([PLVFdUtil checkStringUseable:weakSelf.videoInfo.fileUrl]) {
                if (successBlock) { successBlock(); }
                [weakSelf callbackForPlaybackVideoInfoDidUpdated:weakSelf.videoInfo];
            } else {
                [weakSelf callbackForLoadMainPlayerFailureError:error];
            }
        }];
    }
}

- (void)requestChannelInfo:(void (^)(void))successBlock{
    __weak typeof(self) weakSelf = self;
    PLVQosLoadingTracerModel *model = [[PLVQosLoadingTracerModel alloc] initWithType:PLVQosLoadingTracerModelTypeChannelJson];
    [model addStartTime];
    [PLVLivePrivateAPI loadChannelInfoRepeatedlyWithUserId:self.accountUserId channelId:self.channelId isStandby:NO completion:^(PLVChannelInfoModel * channelInfo) {
        [weakSelf addTracerModelEndTimeToModels:model];
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"request channelInfo success");
        /// 更新 频道信息
        weakSelf.channelInfo = channelInfo;
        [weakSelf callbackForChannelInfoDidUpdated];
        
        /// 请求 频道限制信息
        [weakSelf requestChannelRestrictInfoWithSuccessBlock:successBlock];
    } failure:^(NSError * error) {
        [weakSelf addTracerModelEndTimeToModels:model];
        PLVQosLoadingTracerModel *standbyModel = [[PLVQosLoadingTracerModel alloc] initWithType:PLVQosLoadingTracerModelTypeChannelJsonStandby];
        [standbyModel addStartTime];
        [PLVLivePrivateAPI loadChannelInfoRepeatedlyWithUserId:self.accountUserId channelId:self.channelId isStandby:YES completion:^(PLVChannelInfoModel * channelInfo) {
            [weakSelf addTracerModelEndTimeToModels:standbyModel];
            /// 更新 频道信息
            weakSelf.channelInfo = channelInfo;
            [weakSelf callbackForChannelInfoDidUpdated];
            
            /// 请求 频道限制信息
            [weakSelf requestChannelRestrictInfoWithSuccessBlock:successBlock];
        } failure:^(NSError * error) {
            [weakSelf addTracerModelEndTimeToModels:standbyModel];
            [weakSelf callbackForLoadMainPlayerFailureError:error];
        }];
    }];
}

- (void)requestChannelRestrictInfoWithSuccessBlock:(void (^)(void))successBlock{
    __weak typeof(self) weakSelf = self;
    PLVQosLoadingTracerModel *model = [[PLVQosLoadingTracerModel alloc] initWithType:PLVQosLoadingTracerModelTypeRestrictInfo];
    [model addStartTime];
    [weakSelf.channelInfo updateChannelRestrictInfo:^(PLVChannelRestrictState restrictState) {
        [weakSelf addTracerModelEndTimeToModels:model];
        if (restrictState == PLVChannelRestrictState_NoneRestrict) { /// 无限制
            if (successBlock) { successBlock(); }
            
        } else if (restrictState == PLVChannelRestrictState_PlayRestrict){ /// 限制播放
            [weakSelf reportAndCallbackForLoadMainPlayerErrorCode:PLVFPlayErrorCodeChannelRestrict_PlayRestrict];
            
        } else if (restrictState == PLVChannelRestrictState_Unknown || restrictState == PLVChannelRestrictState_GetFailed){ /// 未知限制 或 获取失败
            /// 尝试 重新请求
            BOOL willRunRetry = [weakSelf retryRequest:^{
                [weakSelf requestChannelRestrictInfoWithSuccessBlock:successBlock];
            }];
            
            if (!willRunRetry) {
                /// 放弃重试
                [weakSelf reportAndCallbackForLoadMainPlayerErrorCode:PLVFPlayErrorCodeChannelRestrict_RequestFailed];
            }
        }
    }];
}

- (void)requestLiveStreamStateForStreamHandle {
    if (!(self.channelInfo &&
          self.channelInfo.restrictState == PLVChannelRestrictState_NoneRestrict)) {
        return;
    }
    if (self.vodList) {
        return;
    }
    __weak typeof(self) weakSelf = self;
    [PLVLivePrivateAPI liveStreamStatus:self.channelInfo.streamID channelId:self.channelId params:[self createExtraParamDict] completion:^(PLVChannelLiveStreamState streamState, NSString * _Nonnull sessionId) {} failure:^(NSError * error) {
        [weakSelf callbackForLoadMainPlayerFailureError:error];
    }];
}

- (BOOL)retryRequest:(void (^)(void))requestBlock{
    BOOL willRunRetry = NO;
    if (self.retryCount <= self.maxRetryCount && requestBlock) {
        /// 进行重试
        self.retryCount++;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), requestBlock);
        willRunRetry = YES;
    }
    if (!willRunRetry) { self.retryCount = 0; }
    return willRunRetry;
}

#pragma mark 播放
- (void)loadMainPlayer {
    if (!self.isPlayCacheVideo && !(self.channelInfo &&
          self.channelInfo.restrictState == PLVChannelRestrictState_NoneRestrict)) {
        return;
    }
    
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s", __FUNCTION__);
    /// 清理 全部 播放器
    [self clearAllPlayer];
    
    __weak typeof(self) weakSelf = self;
    if (self.channelInfo.playbackKeepPlayEnabled && !self.isPlayCacheVideo) {
        NSString * listType = self.videoInfo.listType;
        BOOL isVod = [PLVFdUtil checkStringUseable:listType] && [listType isEqualToString:@"vod"];
        NSString *vid = isVod ? self.videoInfo.videoPoolId : self.videoInfo.videoId;
        NSArray *components = [vid componentsSeparatedByString:@"_"];
        if (components.count > 0) {
            vid = components[0];
        }
        NSString *viewerId = self.vodList ? self.customParam.vodSid : self.customParam.liveParam1;
        [PLVLivePrivateAPI requestPlaybackPositionWithVid:vid viewerId:viewerId completion:^(NSTimeInterval lastPosition) {
            weakSelf.lastKeepPlayTime = lastPosition;
            [weakSelf loadMainPlayerInternal];
        }];
    } else {
        self.lastKeepPlayTime = -1;
        [self loadMainPlayerInternal];
    }
}

- (void)loadMainPlayerInternal {
    /// 设置 播放配置
    PLVOptions * options = [PLVOptions optionsByDefault];
    [options setPlayerOptionIntValue:1 forKey:@"loop"];
    if (self.videoToolBox) {
        [options setPlayerOptionIntValue:1 forKey:@"videotoolbox"];
    } else {
        [options setPlayerOptionIntValue:0 forKey:@"videotoolbox"];
    }
    [options setPlayerOptionIntValue:1 forKey:@"enable-accurate-seek"]; // seek到准确位置播放，但可能会引起其他问题
    [options setPlayerOptionIntValue:1 forKey:@"framedrop"];
    [options setFormatOptionIntValue:1 forKey:@"dns_cache_clear"];
    [options setPlayerOptionIntValue:20 * 1024 * 1024 forKey:@"max-buffer-size"];

    NSURL *fileURL = [self getPlayerContentUrlSplitJointOption:options];
    
    /// 开始播放
    self.contentURL = fileURL;
    [super loadMainContentToPlayWithContentURL:fileURL withOptions:options];
    
    // 如果加载视频播放器时画中画正在使用，那么让画中画更新播放地址
    if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureActive &&
        [PLVLivePictureInPictureManager sharedInstance].pictureInPictureMode == PLVLivePictureInPictureMode_AVPlayer) {
        NSURL *contentURL = [self createPictureInPictureContentURL];
        if (contentURL) {
            [[PLVLivePictureInPictureManager sharedInstance] replayPictureInPicturePlayerWithURL:contentURL];
        }else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePlaybackPlayer - startPictureInPicture failed, contentURL must not be nil");
        }
    }
}


/// 获取播放器最终播放的url，并且拼接option
- (NSURL *)getPlayerContentUrlSplitJointOption:(PLVOptions *)options {
    
    NSString *fileId = self.videoInfo.fileId;
    if ([PLVFdUtil checkStringUseable:fileId]) {
            
            PLVPlaybackLocalVideoInfoModel *localPlayerModel = [self getRecordInfoModelInLocal];
        if (localPlayerModel) {
            NSString *fileURLString = localPlayerModel.localVideoPath;
            NSURL *fileURL = [NSURL URLWithString:fileURLString];
            return fileURL;
        }
    }
    
    /// 解析 播放地址
    NSString * fileURLString = [NSString stringWithFormat:@"%@",self.videoInfo.fileUrl]; // fileUrl 地址带“_s”为直播录制源文件（不计点播流量的地址）
    BOOL isHls = [fileURLString hasSuffix:@"m3u8"];
    if (isHls) {
        [options setPlayerOptionIntValue:1 forKey:@"force-seek-zeroth-to-first"];
        [options setFormatOptionIntValue:5 forKey:@"max_open_fail"];
    }
    /// 设置 播放地址 参数
    BOOL hadParams = [fileURLString containsString:@"?"];
    if ([PLVFdUtil checkStringUseable:self.customParam.liveParam1]) {
        NSString * param1 = [PLVDataUtil urlSafeBase64String:self.customParam.liveParam1];
        fileURLString = [fileURLString stringByAppendingFormat:@"%@param1=%@", (hadParams ? @"&" : @"?"), param1];
    }
    
    hadParams = [fileURLString containsString:@"?"];
    if ([PLVFdUtil checkStringUseable:self.lppi]) {
        NSString * playerId = [PLVDataUtil urlSafeBase64String:self.lppi];
        fileURLString = [fileURLString stringByAppendingFormat:@"%@pid=%@", (hadParams ? @"&" : @"?"), playerId];
    }
    
    /// DNS
    NSURL * fileURL = [NSURL URLWithString:fileURLString];
    NSString * ipHostURLString = [PLVLiveAPIUtils getHttpDNSWithURLString:fileURLString];
    if ([PLVFdUtil checkStringUseable:ipHostURLString]) {
        if (isHls) {
            NSError *error;
            NSString *m3u8Content = [NSString stringWithContentsOfURL:fileURL encoding:NSUTF8StringEncoding error:&error];
            if (!error) {
                NSString *tsUrlPattern = @"http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?ts";
                NSString *firstTsUrl;
                NSString *m3u8Ip = [NSURL URLWithString:ipHostURLString].host;
                [options setFormatOptionValue:fileURL.host forKey:@"ip_host_key"];
                [options setFormatOptionValue:m3u8Ip forKey:@"ip_addr_key"];
                NSRange firstTsUrlRange = [m3u8Content rangeOfString:tsUrlPattern options:NSRegularExpressionSearch];
                if (firstTsUrlRange.location != NSNotFound) {
                    firstTsUrl = [m3u8Content substringWithRange:firstTsUrlRange];
                    firstTsUrl = [firstTsUrl stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
                    NSURL *firstTSURL = [NSURL URLWithString:firstTsUrl];
                    NSString *firstTSIp = [PLVLiveAPIUtils getIPAddrWithHost:firstTSURL.host];
                    if (firstTSIp.length) {
                        [options setFormatOptionValue:firstTSURL.host forKey:@"ip_host"];
                        [options setFormatOptionValue:firstTSIp forKey:@"ip_addr"];
                    }
                }
            }
        } else {
            [options setFormatOptionValue:[NSString stringWithFormat:@"Host: %@", fileURL.host] forKey:@"headers"];
            fileURL = [NSURL URLWithString:ipHostURLString];
        }
    }
    return fileURL;
}

#pragma mark 统计 Qos

- (void)reportFirstLoading {
    if (self.loadingLogSent) {
        [self reportBuffer];
        return;
    }
    
    // 记录第一次loading时间
    if (!self.time_loading) {
        double diffTime = [[NSDate date] timeIntervalSinceDate:self.loadStartDate];
        self.time_loading = (int)floor(diffTime * 1000);
    }
    if (!self.time_player) {
        return;
    }
    self.loadingLogSent = YES;
    
    [[PLVWLogReporterManager sharedManager] reportVodLoadingQosWithChannel:self.channelInfo time:(int)self.time_loading time_player:(int)self.time_player time_business:[self mainPlayerLoadingBusinessTime] playerId:self.lppi vid:self.vodId];
}

- (void)reportBuffer {
    if (!self.recordingBufferTime) {
        return;
    }
    self.recordingBufferTime = NO;
    
    double diffTime = [[NSDate date] timeIntervalSinceDate:self.bufferStartDate];
    [[PLVWLogReporterManager sharedManager] reportVodBufferQosWithChannel:self.channelInfo time:(int)floor(diffTime * 1000) playerId:self.lppi vid:self.vodId];
}

- (void)reportPlaybackFailureAnalysis{
    if (self.playerErrorSend) { // 保持之前直播播放器 PLVLivePlayer 的逻辑，错误日志只上报一次
        return;
    }
    self.playerErrorSend = YES;
    
    __weak typeof(self) weakSelf = self;
    
    NSURLRequest * request = [NSURLRequest requestWithURL:self.contentURL
                                              cachePolicy:NSURLRequestReloadIgnoringCacheData
                                          timeoutInterval:20.0];
    [[[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        NSInteger responseCode = [(NSHTTPURLResponse *)response statusCode];
        NSDictionary * userInfo = [NSDictionary dictionaryWithObject:@"null" forKey:NSLocalizedDescriptionKey];
        NSError * playErr = [NSError errorWithDomain:@"net.polyv.live" code:-100 userInfo:userInfo];
        
        NSString * errorCodeString = @"";
        if (error) {
            errorCodeString = @"load_livevideo_failure";
            playErr = error;
        } else if (responseCode != 200) {
            if (responseCode == 403) {
                errorCodeString = @"stream_403_error";
            } else {
                errorCodeString = @"stream_NOT200_error";
            }
        } else {
            errorCodeString = @"other_error";
        }
        NSString * errormsg = [NSString stringWithFormat:@"code:%ld, reason:%@", (long)playErr.code, playErr.localizedDescription];
        [[PLVWLogReporterManager sharedManager] reportVodErrorQosWithChannel:weakSelf.channelInfo
                                                                         uri:weakSelf.contentURL.absoluteString
                                                                      status:[NSString stringWithFormat:@"%ld", (long)responseCode]
                                                                   errorcode:errorCodeString
                                                                    errormsg:errormsg
                                                                    playerId:weakSelf.lppi
                                                                         vid:weakSelf.vodId];
    }] resume];
}

// 添加结束时间并记录到业务流程数组
- (void)addTracerModelEndTimeToModels:(PLVQosLoadingTracerModel *)model {
    [model addEndTime];
    if (self.loadingLogSent || !model.rangeUseable) {
        return;
    }
    
    BOOL isExist = NO;
    
    if (![PLVFdUtil checkArrayUseable:self.tracerModels]) {
        self.tracerModels = [NSMutableArray array];
    } else {
        for (PLVQosLoadingTracerModel *tracerModel in self.tracerModels) {
            if (tracerModel.type == model.type) {
                [self.tracerModels replaceObjectAtIndex:[self.tracerModels indexOfObject:tracerModel] withObject:model];
                isExist = YES;
                break;
            }
        }
    }
    if (!isExist) {
        [self.tracerModels addObject:model];
    }
}

// 计算业务流程总耗时
- (int)mainPlayerLoadingBusinessTime {
    if (![PLVFdUtil checkArrayUseable:self.tracerModels]) {
        return 0;
    }
    
    NSMutableArray *ranges = [NSMutableArray array];
    for (PLVQosLoadingTracerModel *model in self.tracerModels) {
        if (model.rangeUseable) {
            [ranges addObject:@[@(model.startTime), @(model.endTime)]];
        }
    }
    if (![PLVFdUtil checkArrayUseable:ranges]) {
        return 0;
    }
    
    [ranges sortUsingComparator:^NSComparisonResult(NSArray *rangeArr1, NSArray *rangeArr2) {
        NSTimeInterval start1 = [rangeArr1[0] doubleValue];
        NSTimeInterval start2 = [rangeArr2[0] doubleValue];
        if (start1 == start2) {
            return NSOrderedSame;
        } else if (start1 > start2) {
            return NSOrderedDescending;
        } else {
            return NSOrderedAscending;
        }
    }];
    
    NSMutableArray *mergedRanges = [NSMutableArray array];
    for (NSArray *rangeArr in ranges) {
        NSTimeInterval start = [rangeArr[0] doubleValue];
        NSTimeInterval end = [rangeArr[1] doubleValue];
        if (mergedRanges.count == 0 || [mergedRanges.lastObject[1] doubleValue] < start) {
            [mergedRanges addObject:@[@(start), @(end)]];
        } else {
            NSArray *lastRangeArr = mergedRanges.lastObject;
            NSTimeInterval lastEnd = [lastRangeArr[1] doubleValue];
            NSTimeInterval newEnd = MAX(lastEnd, end);
            [mergedRanges replaceObjectAtIndex:(mergedRanges.count - 1) withObject:@[@(start), @(newEnd)]];
        }
    }
    NSTimeInterval totalDuration = 0;
    for (NSArray *rangeArr in mergedRanges) {
        NSTimeInterval start = [rangeArr[0] doubleValue];
        NSTimeInterval end = [rangeArr[1] doubleValue];
        NSTimeInterval duration = end - start;
        totalDuration += duration;
    }
    
    return (int)totalDuration;
}

#pragma mark 统计 ViewLog
- (void)reportVodTimeCount{
    __weak typeof(self)weakSelf = self;
    [[PLVWLogReporterManager sharedManager] reportVodViewLogWithParam:self.customParam
                                                             playerId:self.lppi
                                                  logModelConfigBlock:^PLVFViewLogModel * _Nonnull{
        NSString * vid = weakSelf.videoInfo.videoPoolId;
        if (![vid containsString:@"_"]) {
            vid = [vid stringByAppendingFormat:@"_%@",[vid substringToIndex:1]];
        }
        
        NSInteger videoDuration = 0;
        id duration = weakSelf.videoInfo.duration;
        if ([duration isKindOfClass:NSNumber.class] || [duration isKindOfClass:NSString.class]) {
            videoDuration = [duration integerValue];
        }
        
        NSInteger currentPlaybackTime = round(weakSelf.currentPlaybackTime);
        
        PLVFViewLogModel * model = [[PLVFViewLogModel alloc] init];
        model.vid = vid;
        model.cts = [NSString stringWithFormat:@"%ld", currentPlaybackTime];
        model.kp = weakSelf.channelInfo.playbackKeepPlayEnabled ? @(1) : @(0);
        model.pd = @(weakSelf.watchDuration);
        model.sd = @(weakSelf.stayDuration);
        model.duration = @(videoDuration);
        model.flow = @(0);
        model.href = weakSelf.videoInfo.fileUrl;
        model.pb = @(3);
        model.ptype = @(0);
        return model;
    }];
}

- (void)reportLivePlaybackTimeCount{
    __weak typeof(self)weakSelf = self;
    [[PLVWLogReporterManager sharedManager] reportLiveViewLogWithParam:self.customParam
                                                              playerId:self.lppi
                                                   logModelConfigBlock:^PLVFViewLogModel * _Nonnull{
        NSInteger currentPlaybackTime = round(weakSelf.currentPlaybackTime);
        PLVFViewLogModel * model = [[PLVFViewLogModel alloc] init];
        model.vid = [PLVFdUtil checkStringUseable:weakSelf.videoInfo.videoId] ? weakSelf.videoInfo.videoId : @"";
        model.cts = [NSString stringWithFormat:@"%ld", currentPlaybackTime];
        model.kp = weakSelf.channelInfo.playbackKeepPlayEnabled ? @(1) : @(0);
        model.cid = weakSelf.channelId;
        model.pd = @(weakSelf.watchDuration);
        model.sd = @(weakSelf.stayDuration);
        model.flow = @(0);
        model.session_id = weakSelf.videoInfo.channelSessionId;
        model.param3 = @"vod";
        model.pb = ([PLVFdUtil checkStringUseable:weakSelf.videoInfo.listType] && [weakSelf.videoInfo.listType isEqualToString:@"record"]) ? @(1):@(2);
        model.ptype = @(0);
        return model;
    }];
}

#pragma mark 请求本地视频数据

- (PLVPlaybackLocalVideoInfoModel *)getPlaybackVideoInfoModelInLocal {
    NSString * listType = self.vodList ? @"vod" : nil;
    if (self.livePlaybackDelegate && [self.livePlaybackDelegate respondsToSelector:@selector(plvLivePlaybackPlayerGetPlaybackCache:videoId:channelId:listType:isRecord:)]) {
        if ([PLVFdUtil checkStringUseable:self.vodId]) {
            NSArray *components = [self.vodId componentsSeparatedByString:@"_"];
            NSString *videoId = components.count > 0 ? components[0] : self.vodId;
            return [self.livePlaybackDelegate plvLivePlaybackPlayerGetPlaybackCache:self videoId:videoId channelId:self.channelId listType:listType isRecord:NO];
        }
        return [self.livePlaybackDelegate plvLivePlaybackPlayerGetPlaybackCache:self videoId:self.vodId channelId:self.channelId listType:listType isRecord:NO];
    } else {
        return nil;
    }
}


- (PLVPlaybackLocalVideoInfoModel *)getRecordInfoModelInLocal {
    if ([self.livePlaybackDelegate respondsToSelector:@selector(plvLivePlaybackPlayerGetPlaybackCache:videoId:channelId:listType:isRecord:)]) {
        return [self.livePlaybackDelegate plvLivePlaybackPlayerGetPlaybackCache:self videoId:self.videoInfo.fileId  channelId:self.channelId listType:nil isRecord:YES];
    } else {
        return nil;
    }
}


#pragma mark 回调
- (void)reportAndCallbackForLoadMainPlayerErrorCode:(PLVFPlayErrorCode)ErrorCode{
    NSError * failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPlay code:ErrorCode];
    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:nil];
    [self callbackForLoadMainPlayerFailureError:failError];
}

- (void)callbackForLoadMainPlayerFailureError:(NSError * _Nullable)error{
    if (self.lastErrorCode == error.code) { return; }
    self.lastErrorCode = error.code;
    plv_dispatch_main_async_safe(^{
        if ([self.livePlaybackDelegate respondsToSelector:@selector(plvLivePlaybackPlayer:loadMainPlayerFailureWithError:)]) {
            [self.livePlaybackDelegate plvLivePlaybackPlayer:self loadMainPlayerFailureWithError:error];
        }
    })
}

- (void)callbackForChannelInfoDidUpdated{
    plv_dispatch_main_async_safe(^{
        if ([self.livePlaybackDelegate respondsToSelector:@selector(plvLivePlaybackPlayer:channelInfoDidUpdated:)]) {
            [self.livePlaybackDelegate plvLivePlaybackPlayer:self channelInfoDidUpdated:self.channelInfo];
        }
    })
}

- (void)callbackForUpdateProgress{
    plv_dispatch_main_async_safe(^{
        if ([self.livePlaybackDelegate respondsToSelector:@selector(plvLivePlaybackPlayer:downloadProgress:playedProgress:playedTimeString:durationTimeString:)]) {
            [self.livePlaybackDelegate plvLivePlaybackPlayer:self downloadProgress:self.downloadProgress playedProgress:self.playedProgress playedTimeString:self.playedTimeString durationTimeString:self.durationTimeString];
        }
    })
}

- (void)callbackForUpdateZeroProgress{
    plv_dispatch_main_async_safe(^{
        if ([self.livePlaybackDelegate respondsToSelector:@selector(plvLivePlaybackPlayer:downloadProgress:playedProgress:playedTimeString:durationTimeString:)]) {
            [self.livePlaybackDelegate plvLivePlaybackPlayer:self downloadProgress:self.downloadProgress playedProgress:0.0 playedTimeString:self.playedTimeString durationTimeString:self.durationTimeString];
        }
    })
}

- (void)callbackForPlaybackVideoInfoDidUpdated:(PLVPlaybackVideoInfoModel *)model {
    plv_dispatch_main_async_safe(^{
        if ([self.livePlaybackDelegate respondsToSelector:@selector(plvLivePlaybackPlayer:playbackVideoInfoDidUpdated:)]) {
            [self.livePlaybackDelegate plvLivePlaybackPlayer:self playbackVideoInfoDidUpdated:model];
        }
    })
}

- (void)callbackForSectionEnabledDidUpdated:(BOOL)sectionEnabled recordEnabled:(BOOL)recordEnabled {
    plv_dispatch_main_async_safe(^{
        if ([self.livePlaybackDelegate respondsToSelector:@selector(plvLivePlaybackPlayer:sectionEnabled:recordEnabled:)]) {
            [self.livePlaybackDelegate plvLivePlaybackPlayer:self sectionEnabled:sectionEnabled recordEnabled:recordEnabled];
        }
    })
}

- (void)callbackForPlaybackListEnabledDidUpdated:(BOOL)playbackListEnabled vodList:(BOOL)vodList {
    plv_dispatch_main_async_safe(^{
        if ([self.livePlaybackDelegate respondsToSelector:@selector(plvLivePlaybackPlayer:playbackListEnabled:vodList:)]) {
            [self.livePlaybackDelegate plvLivePlaybackPlayer:self playbackListEnabled:playbackListEnabled vodList:vodList];
        }
    })
}

#pragma mark - [ Father Private Methods ]
- (void)callSubClassMoviePlayerLoadStateDidChange:(PLVPlayerMainSubType)mainSubType {
    self.isFinished = NO;
    if (mainSubType == PLVPlayerMainSubType_Sub) {
        return;
    }
    if ((self.mainPlayerLoadState & IJKMPMovieLoadStatePlaythroughOK) != 0) {
        [self reportFirstLoading];
    } else if ((self.mainPlayerLoadState & IJKMPMovieLoadStateStalled) != 0) {
        if (self.loadingLogSent && !self.recordingBufferTime) {
            self.bufferStartDate = [NSDate date];
            self.recordingBufferTime = YES;
            PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"recording buffer");
        }
    }
}

- (void)callSubClassMoviePlayerPlaybackStateDidChange:(PLVPlayerMainSubType)mainSubType {
    if (mainSubType == PLVPlayerMainSubType_Sub &&
        [PLVLivePictureInPictureManager sharedInstance].pictureInPictureMode == PLVLivePictureInPictureMode_AVPlayer) {
        return;
    }

    if (self.playing && self.canAutoStartPictureInPicture){
        // 画中画退到后台自动启动，入口
        [PLVLivePictureInPictureManager sharedInstance].canAutoStartPictureInPicture = self.canAutoStartPictureInPicture;
        [PLVLivePictureInPictureManager sharedInstance].delegate = self;
        [[PLVLivePictureInPictureManager sharedInstance] bindPictureInPictureForBackgroundMode:self];
    }
    
    // 存在多个播放器的场景，需要同步播放状态
    if ([PLVLivePictureInPictureManager sharedInstance].livePlayer == self){
        if (self.mainPlayerPlaybackState == IJKMPMoviePlaybackStatePlaying ||
            self.mainPlayerPlaybackState == IJKMPMoviePlaybackStateSeekingForward ||
            self.mainPlayerPlaybackState == IJKMPMoviePlaybackStateSeekingBackward){
            [[PLVLivePictureInPictureManager sharedInstance] syncPictureInPicturePlayControlStatus:YES];
        } else {
            [[PLVLivePictureInPictureManager sharedInstance] syncPictureInPicturePlayControlStatus:NO];
        }
    }
    
    if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureActive) {
        [self plvPictureInPicturePlayerPlayingStateDidChange:self.playing];
    }    
}

- (void)callSubClassPlaybackDidFinish:(PLVPlayerMainSubType)mainSubType withReason:(IJKMPMovieFinishReason)finishReson{
    if (self.currentPlaybackTime >= (int)self.duration) {
        self.isFinished = YES;
    } else if (self.duration - self.currentPlaybackTime >= 1
               || finishReson == IJKMPMovieFinishReasonPlaybackError) {
        self.shouldContinuePlay = YES;
        self.interruptionTime = self.currentPlaybackTime;
    }
    if (mainSubType == PLVPlayerMainSubType_Sub) {
        return;
    }
    if (finishReson == IJKMPMovieFinishReasonPlaybackError) {
        [self reportPlaybackFailureAnalysis];
    }
}

- (void)callSubClassMoviePlayerWillDestroy:(PLVPlayerMainSubType)mainSubType{
    if (mainSubType == PLVPlayerMainSubType_Main) {
        self.contentURL = nil;
    }
}

- (void)callSubClassMoviePlayerFirstVideoFrameRendered:(PLVPlayerMainSubType)mainSubType time:(int)time {
    if (mainSubType == PLVPlayerMainSubType_Main && !self.time_player && !self.loadingLogSent) {
        self.time_player = time;
        [self reportFirstLoading];
    }
}

#pragma mark - [ Event ]
#pragma mark Timer
- (void)progressTimerEvent:(NSTimer *)timer{
    _stti += 0.5;
    BOOL playerPlaying = self.playing || [PLVLivePictureInPictureManager sharedInstance].pictureInPicturePlayerPlaying;
    if (self.duration > 0.0 && playerPlaying) {
        
        /// 回调进度
        CGFloat downloadProgress = self.playableDuration / self.duration;
        if (downloadProgress > 0.97) { downloadProgress = 1.0; }
        self.downloadProgress = downloadProgress;
        self.playedProgress = self.currentPlaybackTime / self.duration;
        [self callbackForUpdateProgress];
        if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureActive) {
            [[PLVLivePictureInPictureManager sharedInstance] updatePictureInPictureControlPlaybackTime:self.currentPlaybackTime];
        }
        
        _wtti += 0.5;
        
        /// 日志统计
        if (!self.isPlayCacheVideo) {
            if (self.lwtti != self.wtti && fmod(self.wtti, 10.0) == 0.0 && self.wtti > 0.0) {
                NSString * listType = self.videoInfo.listType;
                if ([PLVFdUtil checkStringUseable:listType] && [listType isEqualToString:@"vod"]) {
                    [self reportVodTimeCount];
                } else {
                    [self reportLivePlaybackTimeCount];
                }
            }
            self.lwtti = self.wtti;
        }
        
        // 播放结束后禁止seek到0，会缺少播放完成通知，影响直播回放最后的数据
        // 播放器完成回调较慢，先使用时长判断播放完成
        if (self.currentPlaybackTime >= (int)self.duration) {
            self.isFinished = YES;
        }
    }
}

- (void)liveStatusTimerEvent:(NSTimer *)timer{
    [self requestLiveStreamStateForStreamHandle];
}

#pragma mark - [ Delegate ]
#pragma mark PLVLivePictureInPictureManagerDelegate
- (void)plvPictureInPictureWillStart {
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayerPictureInPictureWillStart:)]) {
        [self.pictureInPictureDelegate plvLivePlayerPictureInPictureWillStart:self];
    }
}

/// 画中画已经开始
- (void)plvPictureInPictureDidStart {
    if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureMode == PLVLivePictureInPictureMode_AVPlayer) {
        [PLVLivePictureInPictureManager sharedInstance].livePlayer = self;
        [[PLVLivePictureInPictureManager sharedInstance] seekPictureInPicturePlayerToTime:self.currentPlaybackTime];
    }
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayerPictureInPictureDidStart:)]) {
        [self.pictureInPictureDelegate plvLivePlayerPictureInPictureDidStart:self];
    }
}

/// 画中画开启失败
/// @param error 失败错误原因
- (void)plvPictureInPictureFailedToStartWithError:(NSError *)error {
    NSError * failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPlay code:PLVFPlayErrorCodePictureInPicture_OpenError];
    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:error.localizedDescription];
    
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayer:pictureInPictureFailedToStartWithError:)]) {
        [self.pictureInPictureDelegate plvLivePlayer:self pictureInPictureFailedToStartWithError:error];
    }
}

/// 画中画即将停止
- (void)plvPictureInPictureWillStop {
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayerPictureInPictureWillStop:)]) {
        [self.pictureInPictureDelegate plvLivePlayerPictureInPictureWillStop:self];
    }
}

/// 画中画已经停止
- (void)plvPictureInPictureDidStop {
    [PLVLivePictureInPictureManager sharedInstance].livePlayer = nil;
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayerPictureInPictureDidStop:)]) {
        [self.pictureInPictureDelegate plvLivePlayerPictureInPictureDidStop:self];
    }
}

- (void)plvPictureInPicturePlayerPlayingStateDidChange:(BOOL)playing {
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayer:pictureInPicturePlayerPlayingStateDidChange:)]) {
        [self.pictureInPictureDelegate plvLivePlayer:self pictureInPicturePlayerPlayingStateDidChange:playing];
    }
}

- (void)plvPictureInPicturePlayerPlayingStateDidChange:(BOOL)playing systemInterrupts:(BOOL)systemInterrupts {
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayer:pictureInPicturePlayerPlayingStateDidChange:systemInterrupts:)]) {
        [self.pictureInPictureDelegate plvLivePlayer:self pictureInPicturePlayerPlayingStateDidChange:playing systemInterrupts:systemInterrupts];
    }
}

@end
