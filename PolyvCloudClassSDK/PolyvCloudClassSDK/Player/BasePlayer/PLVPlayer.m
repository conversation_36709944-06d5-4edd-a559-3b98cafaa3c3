//
//  PLVPlayer.m
//  PLVCloudClassSDK
//
//  Created by Lincal on 2020/12/4.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVPlayer.h"

#import "PLVLivePictureInPictureManager.h"
#import "PLVLiveSampleBufferDisplayView.h"
#import "PLVPlayer+SubClassExtension.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

#import "PLVConsoleLogger.h"

// [内部说明]
// 1. 支持 PolyvIJK 或 普通IJK 的编译 (依赖库中存在是哪个IJK库，则自动面向哪个IJK；优先选择 PolyvIJK)
//    同时支持未集成 IJK 的环境的编译，若未集成 IJK，则 PLVPlayer 成为一个空壳类
//
// 2. 此类为裸播放器，是一个播放器基类，非业务播放器基类。
//    因此不应该在此类中实现业务逻辑（比如偶尔有 “直播、直播回放” 共有的逻辑，会希望写到一个基类中）
//    不建议这么做的原因是：
//    (1) 裸播放器，提供了允许下移至更底层SDK的能力基础；
//    (2) 实现了可提供给大客户纯播放器的能力，便于客户替代竞品；
//    (3) 具备了未来项目，可共用一套播放器的能力；
#if __has_include(<PLVIJKPlayer/PLVIJKPlayer.h>)
    typedef PLVIJKFFMoviePlayerController PLVIJKPlayer;
#elif __has_include(<IJKMediaFramework/IJKMediaFramework.h>)
    typedef IJKFFMoviePlayerController PLVIJKPlayer;
#endif

@interface PLVPlayer ()

#ifndef PLV_NO_IJK_EXIST

#pragma mark 数据
@property (nonatomic, assign) CGFloat originalVolume; // 主播放器被静音前的音量
@property (nonatomic, assign) CGFloat subPlayerOriginalVolume; // 副播放器被静音前的音量
@property (nonatomic, assign) CGFloat currentSpeed;

#pragma mark 状态
@property (nonatomic, assign) BOOL mainPlayerPlaying; // 主播放器是否正在播放中
@property (nonatomic, assign) BOOL willPaused;
@property (nonatomic, assign) IJKMPMovieScalingMode scalingMode;

#pragma mark 功能对象
@property (nonatomic, strong) PLVIJKPlayer * mainPlayer;
@property (nonatomic, strong) PLVIJKPlayer * subPlayer;
@property (nonatomic, strong) UIView * displaySuperview;
@property (nonatomic, strong) NSTimer * timer;
@property (nonatomic, strong) NSDate *mainPlayerStartDate; // 记录准备播放的时间，用于loading日志
@property (nonatomic, strong) PLVLiveSampleBufferDisplayView *sampleBufferView;

#endif

@end

@implementation PLVPlayer

#ifndef PLV_NO_IJK_EXIST

#pragma mark - [ Life Period ]
- (void)dealloc{
    [self destroyTimer];
    [self clearAllPlayer];
    PLV_LOG_INFO(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
}

- (instancetype)init {
    if (self = [super init]) {
        self.scalingMode = IJKMPMovieScalingModeAspectFit;
    }
    return self;
}


#pragma mark - [ Public Methods ]
#pragma mark 通用
- (void)loadMainContentToPlayWithContentURL:(NSURL *)contentURL withOptions:(PLVOptions *)options {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    if (dispatch_queue_get_label(DISPATCH_CURRENT_QUEUE_LABEL) == dispatch_queue_get_label(dispatch_get_main_queue())) {
        [self clearMainPlayer];
        
        PLVOptions * optionsFinal = [self callbackForPlayerWillLoad:PLVPlayerMainSubType_Main withOptions:options];
        self.mainPlayerStartDate = [NSDate date];
        self.mainPlayer = [self loadPlayerWithContentURL:contentURL withOptions:optionsFinal mainSubType:PLVPlayerMainSubType_Main];
        self.mainPlayer.pauseInBackground = self.pauseMainPlayerInBackground;
        [self mainPlayerAddObserveEvent];
        self.originalVolume = self.mainPlayer.playbackVolume;
        
        [self addTimer];
        [self addDisplaySuperviewForMainPlayer];
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self loadMainContentToPlayWithContentURL:contentURL withOptions:options];
        });
    }
}

- (void)loadSubContentToPlayWithContentURL:(NSURL *)contentURL withOptions:(PLVOptions *)options {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    if (dispatch_queue_get_label(DISPATCH_CURRENT_QUEUE_LABEL) == dispatch_queue_get_label(dispatch_get_main_queue())) {
        [self clearSubPlayer];
        
        PLVOptions * optionsFinal = [self callbackForPlayerWillLoad:PLVPlayerMainSubType_Sub withOptions:options];
        self.subPlayer = [self loadPlayerWithContentURL:contentURL withOptions:optionsFinal mainSubType:PLVPlayerMainSubType_Sub];
        self.subPlayer.pauseInBackground = self.pauseSubPlayerInBackground;
        [self subPlayerAddObserveEvent];
        self.subPlayerOriginalVolume = self.subPlayer.playbackVolume;
        
        [self addDisplaySuperviewForSubPlayer];
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self loadSubContentToPlayWithContentURL:contentURL withOptions:options];
        });
    }
}

- (PLVIJKPlayer *)loadPlayerWithContentURL:(NSURL *)contentURL withOptions:(PLVOptions *)options mainSubType:(PLVPlayerMainSubType)mainSubType {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    PLVIJKPlayer *ijkPlayer;
    if (@available(iOS 15.0, *)) {
        if (mainSubType == PLVPlayerMainSubType_Main &&
            [PLVLivePictureInPictureManager sharedInstance].pictureInPictureMode == PLVLivePictureInPictureMode_IJKPlayer) {
            [self.sampleBufferView clearSampleBufferDisplayView];
            ijkPlayer = [[PLVIJKPlayer alloc] initWithMoreContent:contentURL withOptions:options withGLView:self.sampleBufferView];
        }
    }
    if (!ijkPlayer) {
        ijkPlayer = [[PLVIJKPlayer alloc] initWithContentURL:contentURL withOptions:options];
    }
    ijkPlayer.shouldAutoplay = YES;
    ijkPlayer.allowsMediaAirPlay = NO;
    ijkPlayer.scalingMode = self.scalingMode;
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"PLVPlayer:loadPlayerWithContentURL ScalingMode :%ld",(long)self.scalingMode);
    [ijkPlayer prepareToPlay];

    return ijkPlayer;
}

- (void)setupDisplaySuperview:(UIView *)displaySuperview{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    if (displaySuperview && [displaySuperview isKindOfClass:UIView.class]) {
        self.displaySuperview = displaySuperview;

        [self addDisplaySuperviewForMainPlayer];
        [self addDisplaySuperviewForSubPlayer];
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVPlayer - %s failed, displayeSuperview illegal:%@",__FUNCTION__,displaySuperview);
    }
}

- (void)setupScalingMode:(IJKMPMovieScalingMode)scalingMode {
    self.scalingMode = scalingMode;
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"PLVPlayer:setup ScalingMode :%ld",(long)scalingMode);
}

- (void)clearMainPlayer {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    if (_mainPlayer) {
        [self callSubClassMoviePlayerWillDestroy:PLVPlayerMainSubType_Main];
        [self destroyTimer];
        [_mainPlayer.view removeFromSuperview];
        [_mainPlayer shutdown];
        [self callbackForPlayerDidDestroyed:PLVPlayerMainSubType_Main];
    }
    _mainPlayer = nil;
}

- (void)clearSubPlayer {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    if (_subPlayer) {
        [self callSubClassMoviePlayerWillDestroy:PLVPlayerMainSubType_Sub];
        [_subPlayer.view removeFromSuperview];
        [_subPlayer shutdown];
        [self callbackForPlayerDidDestroyed:PLVPlayerMainSubType_Sub];
    }
    _subPlayer = nil;
}

- (void)clearAllPlayer {
    [[NSNotificationCenter defaultCenter] removeObserver:self]; // TODO 销毁播放时需不需要也单独调用一次？
    [self clearSubPlayer];
    [self clearMainPlayer];
}

- (void)pause{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    self.willPaused = YES;
    if (_mainPlayer) { [_mainPlayer pause]; }
}

- (void)subPlayerPause {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    self.willPaused = YES;
    if (_subPlayer) { [_subPlayer pause]; }
}

- (void)mute {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    self.mainPlayer.playbackVolume = 0.0;
}

- (void)cancelMute {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    self.mainPlayer.playbackVolume = self.originalVolume;
}

- (void)subPlayerMute {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    self.subPlayer.playbackVolume = 0.0;
}

- (void)subPlayerCancleMute {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    self.subPlayer.playbackVolume = self.subPlayerOriginalVolume;
}

#pragma mark 快直播相关
/// 获取快直播下的包重传次数
- (int64_t)getWebrtcNackCount {
    return [self.mainPlayer getWebrtcNackCount];
}

/// 获取快直播下的视频包接收个数
- (int64_t)getWebrtcVideoPacketReceive {
    return [self.mainPlayer getWebrtcVideoPacketReceive];
}

#pragma mark 非直播相关
- (void)play{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    self.willPaused = NO;
    [self.mainPlayer play];
}

- (void)subPlayerPlay {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    self.willPaused = NO;
    if (_subPlayer) { [_subPlayer play]; }
}

- (void)seekToTime:(NSTimeInterval)toTime{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    self.willPaused = NO;
    self.mainPlayer.currentPlaybackTime = toTime;
}

- (void)switchSpeedRate:(CGFloat)toSpeed {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    if (toSpeed <= 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVPlayer - switchLivePlaybackSpeedRate failed, toSpeed illegal:%f", toSpeed);
        return;
    }
    self.willPaused = NO;
    _currentSpeed = toSpeed;
    self.mainPlayer.playbackRate = toSpeed;
}

#pragma mark Getter
- (long)curFrameAgoraUserTC {
    if (!_mainPlayer) {
        return 0;
    }
    return [_mainPlayer getCurFrameAgoraUserTC];
}

- (int64_t)videoCacheDuration {
    if (!_mainPlayer) {
        return 0;
    }
    return [_mainPlayer videoCacheDuration];
}

- (int64_t)originalTcpSpeed {
    if (!_mainPlayer) {
        return 0;
    }
    return [_mainPlayer originalTcpSpeed];
}

- (NSString *)tcpSpeed {
    if (_mainPlayer) {
        return [_mainPlayer tcpSpeed];
    } else if (_subPlayer){
        return [_subPlayer tcpSpeed];
    } else{
        //NSLog(@"PLVPlayer - get tcpSpeed failed, there is no player");
        return @"";
    }
}

- (BOOL)mainPlayerExist{
    return _mainPlayer ? YES : NO;
}

- (BOOL)subPlayerExist{
    return _subPlayer ? YES : NO;
}

- (BOOL)playing{
    if (_mainPlayer) {
        return (_mainPlayer.playbackState == IJKMPMoviePlaybackStatePlaying ? YES : NO);
    } else if (_subPlayer){
        return (_subPlayer.playbackState == IJKMPMoviePlaybackStatePlaying ? YES : NO);
    } else{
        return NO;
    }
}

- (CGSize)naturalSize{
    if (_mainPlayer) {
        return _mainPlayer.naturalSize;
    } else if (_subPlayer){
        return _subPlayer.naturalSize;
    } else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVPlayer - get naturalSize failed, there is no player");
        return CGSizeZero;
    }
}

- (IJKMPMoviePlaybackState)mainPlayerPlaybackState{
    return self.mainPlayer.playbackState;
}

- (IJKMPMovieLoadState)mainPlayerLoadState{
    return self.mainPlayer.loadState;
}

- (NSTimeInterval)currentPlaybackTime{
    return self.mainPlayer.currentPlaybackTime;
}

- (NSTimeInterval)duration{
    return self.mainPlayer.duration;
}

- (NSTimeInterval)playableDuration{
    return self.mainPlayer.playableDuration;
}

- (CGFloat)currentSpeed{
    return self.mainPlayer.playbackRate;
}

- (PLVLiveSampleBufferDisplayView *)sampleBufferView {
    if (!_sampleBufferView) {
        _sampleBufferView = [[PLVLiveSampleBufferDisplayView alloc] init];
    }
    return _sampleBufferView;
}

#pragma mark - [ Private Methods ]
- (void)addTimer{
    if (_timer) { [self destroyTimer]; }
    
    _timer = [NSTimer scheduledTimerWithTimeInterval:0.5 target:[PLVFWeakProxy proxyWithTarget:self ] selector:@selector(timerEvent:) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:_timer forMode:NSRunLoopCommonModes];
}

- (void)destroyTimer{
    if (_timer) {
        [_timer invalidate];
        _timer = nil;
    }
}

- (void)mainPlayerAddObserveEvent{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"mainPlayerAddObserveEvent");
    NSNotificationCenter * defaultCenter = [NSNotificationCenter defaultCenter];
    // 加载相关
    [defaultCenter addObserver:self selector:@selector(moviePlayerLoadStateDidChange:) name:IJKMPMoviePlayerLoadStateDidChangeNotification object:self.mainPlayer];
    [defaultCenter addObserver:self selector:@selector(moviePlaybackIsPreparedToPlay:) name:IJKMPMediaPlaybackIsPreparedToPlayDidChangeNotification object:self.mainPlayer];

    // 播放相关
    [defaultCenter addObserver:self selector:@selector(moviePlayerPlaybackStateDidChange:) name:IJKMPMoviePlayerPlaybackStateDidChangeNotification object:self.mainPlayer];
    [defaultCenter addObserver:self selector:@selector(moviePlayerPlaybackDidFinish:) name:IJKMPMoviePlayerPlaybackDidFinishNotification object:self.mainPlayer];
    
    [defaultCenter addObserver:self selector:@selector(moviePlayerFirstVideoFrameRendered:) name:IJKMPMoviePlayerFirstVideoFrameRenderedNotification object:self.mainPlayer];
    
    // Seek相关
    [defaultCenter addObserver:self selector:@selector(moviePlayerDidSeekComplete:) name:IJKMPMoviePlayerDidSeekCompleteNotification object:self.mainPlayer];
    [defaultCenter addObserver:self selector:@selector(moviePlayerAccurateSeekComplete:) name:IJKMPMoviePlayerAccurateSeekCompleteNotification object:self.mainPlayer];
    
    // SEI相关
    [defaultCenter addObserver:self selector:@selector(moviePlayerSeiDidChange:) name:IJKMPMoviePlayerSeiDidChangeNotification object:self.mainPlayer];
}

- (void)subPlayerAddObserveEvent{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"subPlayerAddObserveEvent");
    NSNotificationCenter * defaultCenter = [NSNotificationCenter defaultCenter];
    // 加载相关
    [defaultCenter addObserver:self selector:@selector(subMoviePlaybackIsPreparedToPlay:) name:IJKMPMediaPlaybackIsPreparedToPlayDidChangeNotification object:self.subPlayer];

    // 播放相关
    [defaultCenter addObserver:self selector:@selector(subMoviePlayerPlaybackDidFinish:) name:IJKMPMoviePlayerPlaybackDidFinishNotification object:self.subPlayer];
    
    // App前台后台相关
    [defaultCenter addObserver:self selector:@selector(applicationDidBecomeActive:) name:UIApplicationDidBecomeActiveNotification object:nil];
}

- (void)addDisplaySuperviewForMainPlayer{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    if (_mainPlayer && _displaySuperview) {
        _mainPlayer.view.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        _mainPlayer.view.frame = _displaySuperview.bounds;
        [_displaySuperview insertSubview:_mainPlayer.view atIndex:0];
    }
}

- (void)addDisplaySuperviewForSubPlayer{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    if (_subPlayer && _displaySuperview) {
        _subPlayer.view.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        _subPlayer.view.frame = _displaySuperview.bounds;
        [_displaySuperview addSubview:_subPlayer.view];
    }
}

#pragma mark 回调
- (PLVOptions *)callbackForPlayerWillLoad:(PLVPlayerMainSubType)mainSubType withOptions:(PLVOptions *)options{
    PLVOptions * optionsFinal = options;
    if ([self.delegate respondsToSelector:@selector(plvPlayer:playerWillLoad:withOptions:)]) {
        optionsFinal = [self.delegate plvPlayer:self playerWillLoad:mainSubType withOptions:options];
    }
    return optionsFinal;
}

- (void)callbackForLoadStateDidChange:(PLVPlayerMainSubType)mainSubType{
    plv_dispatch_main_async_safe(^{
        if ([self.delegate respondsToSelector:@selector(plvPlayer:playerLoadStateDidChange:)]) {
            [self.delegate plvPlayer:self playerLoadStateDidChange:mainSubType];
        }
    })
}

- (void)callbackForPreparedToPlay:(PLVPlayerMainSubType)mainSubType{
    plv_dispatch_main_async_safe(^{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"preparedToPlay : %ld", mainSubType);
        if ([self.delegate respondsToSelector:@selector(plvPlayer:playerIsPreparedToPlay:)]) {
            [self.delegate plvPlayer:self playerIsPreparedToPlay:mainSubType];
        }
    })
}

- (void)callbackForPlaybackStateDidChange:(PLVPlayerMainSubType)mainSubType{
    plv_dispatch_main_async_safe(^{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"playbackStateDidChange: %ld", mainSubType);
        if ([self.delegate respondsToSelector:@selector(plvPlayer:playerPlaybackStateDidChange:)]) {
            [self.delegate plvPlayer:self playerPlaybackStateDidChange:mainSubType];
        }
    })
}

- (void)callbackForPlaybackStateDidChange:(PLVPlayerMainSubType)mainSubType playing:(BOOL)playing{
    plv_dispatch_main_async_safe(^{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"playbackStateDidChange: %ld playin: %s", mainSubType, playing ? "YES" : "NO");
        if ([self.delegate respondsToSelector:@selector(plvPlayer:playerPlayingStateDidChange:playing:)]) {
            [self.delegate plvPlayer:self playerPlayingStateDidChange:mainSubType playing:playing];
        }
    })
}

- (void)callbackForPlaybackDidFinish:(PLVPlayerMainSubType)mainSubType finishReson:(IJKMPMovieFinishReason)reason{
    plv_dispatch_main_async_safe(^{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"playbackStateDidChange: %ld reason:%ld", mainSubType, reason);
        if ([self.delegate respondsToSelector:@selector(plvPlayer:playerPlaybackDidFinish:finishReson:)]) {
            [self.delegate plvPlayer:self playerPlaybackDidFinish:mainSubType finishReson:reason];
        }
    })
}

- (void)callbackForPlayerDidDestroyed:(PLVPlayerMainSubType)mainSubType{
    plv_dispatch_main_async_safe(^{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"playbackStateDidChange: %ld", mainSubType);
        if ([self.delegate respondsToSelector:@selector(plvPlayer:playerDidDestroyed:)]) {
            [self.delegate plvPlayer:self playerDidDestroyed:mainSubType];
        }
    })
}

- (void)callbackForSeiDidChanged:(long)timeStamp;{
    plv_dispatch_main_async_safe(^{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"seiDidChanged: %ld", timeStamp);
        if ([self.delegate respondsToSelector:@selector(plvPlayer:playerSeiDidChanged:)]) {
            [self.delegate plvPlayer:self playerSeiDidChanged:timeStamp];
        }
    })
}

#pragma mark - [ Event ]
#pragma mark Notification For MainPlayer
/// 加载状态改变
- (void)moviePlayerLoadStateDidChange:(NSNotification *)notification {
    [self callSubClassMoviePlayerLoadStateDidChange:PLVPlayerMainSubType_Main];
    
    [self callbackForLoadStateDidChange:PLVPlayerMainSubType_Main];
}

/// 已准备好播放
- (void)moviePlaybackIsPreparedToPlay:(NSNotification *)notification {
    self.showingContent = YES; // TODO 很难通过管理BOOL来实现，应该通过getter内置判断

    if (self.willPaused) { [self.mainPlayer pause]; }

    [self callbackForPreparedToPlay:PLVPlayerMainSubType_Main];
}

/// 播放状态改变
- (void)moviePlayerPlaybackStateDidChange:(NSNotification *)notification {
    BOOL currentPlaying = (_mainPlayer.playbackState == IJKMPMoviePlaybackStatePlaying ? YES : NO);
    BOOL playingStateDidChanged = (currentPlaying != self.mainPlayerPlaying);
    self.mainPlayerPlaying = currentPlaying;

    [self callbackForPlaybackStateDidChange:PLVPlayerMainSubType_Main];
    [self callSubClassMoviePlayerPlaybackStateDidChange:PLVPlayerMainSubType_Main];

    if (playingStateDidChanged) {
        [self callbackForPlaybackStateDidChange:PLVPlayerMainSubType_Main playing:currentPlaying];
    }
}

/// 播放结束
- (void)moviePlayerPlaybackDidFinish:(NSNotification *)notification {
    IJKMPMovieFinishReason reason = ((NSNumber *)notification.userInfo[IJKMPMoviePlayerPlaybackDidFinishReasonUserInfoKey]).integerValue;
    if (reason == IJKMPMovieFinishReasonPlaybackEnded) {
        // 播放结束
    } else if (reason == IJKMPMovieFinishReasonPlaybackError){
        // 播放出现错误
    } else if (reason == IJKMPMovieFinishReasonUserExited){
        // 用户退出播放
    }
    
    [self callSubClassPlaybackDidFinish:PLVPlayerMainSubType_Main withReason:reason];
    
    [self callbackForPlaybackDidFinish:PLVPlayerMainSubType_Main finishReson:reason];
}

- (void)moviePlayerFirstVideoFrameRendered:(NSNotification *)notification {
    double diffTime = [[NSDate date] timeIntervalSinceDate:self.mainPlayerStartDate];
    [self callSubClassMoviePlayerFirstVideoFrameRendered:PLVPlayerMainSubType_Main time:(int)floor(diffTime * 1000)];
}

/// Seek完成
- (void)moviePlayerDidSeekComplete:(NSNotification *)notification {

}

/// 精准Seek完成
- (void)moviePlayerAccurateSeekComplete:(NSNotification *)notification {

}

/// SEI信息发生改变
- (void)moviePlayerSeiDidChange:(NSNotification *)notification {
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvPlayer:playerSeiDidChanged:)]) {
        NSUInteger seiType = [notification.userInfo[IJKMPMoviePlayerSeiDidChangeTypeUserInfoKey] unsignedIntegerValue];
        NSString * seiData = notification.userInfo[IJKMPMoviePlayerSeiDidChangeDataUserInfoKey];
        //NSLog(@"%@ seiType:%lu, seiData:%@", NSStringFromSelector(_cmd), seiType,seiData);
            
        if ((seiType == 100 || seiType == 243) && [PLVFdUtil checkStringUseable:seiData]) { // agora sei data
            NSData * jsonData = [seiData dataUsingEncoding:NSUTF8StringEncoding];
            NSError * error = nil;
            NSDictionary * jsonDict = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:&error];
            if (!error && [jsonDict isKindOfClass:NSDictionary.class]) {
                NSString * app_data = PLV_SafeStringForDictKey(jsonDict, @"app_data");
                if ([PLVFdUtil checkStringUseable:app_data]) {
                    [self callbackForSeiDidChanged:app_data.integerValue];
                }
            } else {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"%@ seiType:%lu, seiData:%@", NSStringFromSelector(_cmd), seiType,seiData);
            }
        }
    }
}

#pragma mark Notification For SubPlayer
- (void)subMoviePlaybackIsPreparedToPlay:(NSNotification *)notification {
    self.showingContent = YES;
    
    UIApplicationState state = [UIApplication sharedApplication].applicationState;
    if (state == UIApplicationStateBackground && self.pauseSubPlayerInBackground) {
        [self subPlayerPause];
        self.willPaused = NO;
    }
    
    [self callbackForPreparedToPlay:PLVPlayerMainSubType_Sub];
}

- (void)subMoviePlayerPlaybackDidFinish:(NSNotification *)notification {
    [self clearSubPlayer];
    
    IJKMPMovieFinishReason reason = ((NSNumber *)notification.userInfo[IJKMPMoviePlayerPlaybackDidFinishReasonUserInfoKey]).integerValue;
    if (reason == IJKMPMovieFinishReasonPlaybackEnded) {
        // 播放结束
    } else if (reason == IJKMPMovieFinishReasonPlaybackError){
        // 播放出现错误
    } else if (reason == IJKMPMovieFinishReasonUserExited){
        // 用户退出播放
    }
    
    [self callbackForPlaybackDidFinish:PLVPlayerMainSubType_Sub finishReson:reason];
}

- (void)applicationDidBecomeActive:(NSNotification *)notification {
    if (_subPlayer && !self.willPaused && (_subPlayer.playbackState == IJKMPMoviePlaybackStatePaused)) {
        /// 若副播放器原本是非暂停状态，则进入前台，自动恢复播放
        [_subPlayer play];
    }
}

#pragma mark Timer
- (void)timerEvent:(NSTimer *)timer{
    if (_mainPlayer && self.playing && self.duration > 0) {
        if (self.currentPlaybackTime - self.duration > 1) {  /// 兼容部分视频，播放完成但不触发 ‘DidFinish’ 回调
            [self pause];
            NSDictionary * userInfo = @{IJKMPMoviePlayerPlaybackDidFinishReasonUserInfoKey : @(IJKMPMovieFinishReasonPlaybackEnded),
                                        @"note": @"PLVVodPlayerController"};
            NSNotification * notification = [NSNotification notificationWithName:IJKMPMoviePlayerPlaybackDidFinishNotification object:self.mainPlayer userInfo:userInfo];
            [[NSNotificationCenter defaultCenter] postNotification:notification];
        }
    }
}


#pragma mark - [ SubClass Methods ]
- (void)callSubClassMoviePlayerLoadStateDidChange:(PLVPlayerMainSubType)mainSubType{
    // 请子类实现该方法 NSLog(@"PLVPlayer - %s failed, subclass not implement this method",__FUNCTION__);
}

- (void)callSubClassMoviePlayerPlaybackStateDidChange:(PLVPlayerMainSubType)mainSubType{
    // 请子类实现该方法 NSLog(@"PLVPlayer - %s failed, subclass not implement this method",__FUNCTION__);
}

- (void)callSubClassPlaybackDidFinish:(PLVPlayerMainSubType)mainSubType withReason:(IJKMPMovieFinishReason)finishReson{
    // 请子类实现该方法 NSLog(@"PLVPlayer - %s failed, subclass not implement this method",__FUNCTION__);
}

- (void)callSubClassMoviePlayerWillDestroy:(PLVPlayerMainSubType)mainSubType{
    // 请子类实现该方法 NSLog(@"PLVPlayer - %s failed, subclass not implement this method",__FUNCTION__);
}

- (void)callSubClassMoviePlayerFirstVideoFrameRendered:(PLVPlayerMainSubType)mainSubType time:(int)time {
    // 请子类实现该方法 NSLog(@"PLVPlayer - %s failed, subclass not implement this method",__FUNCTION__);
}

#endif

@end
