//
//  PLVPublicStreamPlayer.m
//  PLVLiveScenesSDK
//
//  Created by juno on 2022/9/13.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVPublicStreamPlayer.h"

#import <PLVBusinessSDK/PLVBPublicStreamVOLCPlayer.h>
#import <PLVFoundationSDK/PLVFoundationSDK.h>

#import "PLVConsoleLogger.h"
#import "PLVLivePrivateAPI.h"
#import <AVKit/AVKit.h>

@interface PLVPublicStreamPlayer ()<PLVBPublicStreamPlayerDelegate>

#pragma mark 功能对象
@property (nonatomic, strong) PLVBPublicStreamVOLCPlayer *player;
@property (nonatomic, strong) UIView *displaySuperview;
@property (nonatomic, strong) PLVReachability *reachability; // 监听网络变化

#pragma mark 数据
@property (nonatomic, assign) BOOL streamPlaying;
@property (nonatomic, strong) PLVPublicStreamGetInfoModel *getPublicStreamInfoModel;

@end

@implementation PLVPublicStreamPlayer

#pragma mark - [ Life Period ]
- (void)dealloc{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"streamPlayer dealloc");
}

- (instancetype)init{
    if (self = [super init]) {
        self.streamPlaying = NO;
        self.player = [[PLVBPublicStreamVOLCPlayer alloc]init];
        self.player.delegate = self;
    }
    return self;
}

#pragma mark - [ Public Methods ]

- (void)setupPlayerWith:(PLVPublicStreamGetInfoModel *)model {
    self.getPublicStreamInfoModel = model;
    __weak typeof(self) weakSelf = self;
    [PLVLivePrivateAPI getVOLCPublicStreamInfoWithModel:model success:^(NSDictionary * _Nonnull dict) {
        NSString *data = dict[@"data"];
        if (![PLVFdUtil checkStringUseable:data]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"%s get publicStreamInfo failed with 【param illegal】(responseDict:%@)", __FUNCTION__, dict);
        }else{
            [weakSelf.player updatePublicStreamInfoWithStr:data];
            [weakSelf setupReachability];   // 设置网络监听
            [weakSelf.player createRTCEngine];
            [weakSelf.player setupDisplaySuperview:self.displaySuperview];
            [weakSelf play];
        }
    } failure:^(NSError * _Nonnull error) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"%s get publicStreamInfo failed with 【%@】", __FUNCTION__, error);
    }];
}

- (void)setupDisplaySuperview:(UIView *)displaySuperview {
    if (displaySuperview && [displaySuperview isKindOfClass:UIView.class]) {
        _displaySuperview = displaySuperview;
        [self.player setupDisplaySuperview:displaySuperview];
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVPublicStreamPlayer - %s failed, displayeSuperview illegal:%@",__FUNCTION__, displaySuperview);
    }
}

/// 重新加载播放器
- (void)reloadStreamPlayer {
    [self clearPlayer];
    
    self.player = [[PLVBPublicStreamVOLCPlayer alloc]init];
    [self setupPlayerWith:self.getPublicStreamInfoModel];
    [self.player setupDisplaySuperview:self.displaySuperview];
    
    int result = [self.player play];
    if (result) {
        self.streamPlaying = YES;
    }
    [self callbackPlayingStateDidChange:result == 0 ? YES : NO];
}

- (void)play {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"publicStreamPlayer %s", __FUNCTION__);
    int result = [self.player play];
    if (result) {
        self.streamPlaying = YES;
    }
    [self callbackPlayingStateDidChange:result == 0 ? YES : NO];
}

- (void)stop {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"publicStreamPlayer %s", __FUNCTION__);
    int result = [self.player stop];
    if (result == 0) {
        self.streamPlaying = NO;
        [self callbackPlayingStateDidChange:NO];
    }
}

- (void)pause {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"publicStreamPlayer %s", __FUNCTION__);
    int result = [self.player setupDisplaySuperview:nil];
    if (result == 0) {
        [self callbackPlayingStateDidChange:NO];
    }
}

- (void)resume {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"publicStreamPlayer %s", __FUNCTION__);
    int result = [self.player setupDisplaySuperview:self.displaySuperview];
    if (result == 0) {
        [self callbackPlayingStateDidChange:YES];
    }
}

- (void)clearPlayer {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"publicStreamPlayer %s", __FUNCTION__);
    if (_player) {
        [_player setupDisplaySuperview:nil];
        [self stop];
        [_player destroyRTCEngine];
    }
    _player = nil;
}

#pragma mark - [ Private Methods ]

- (void)setupReachability {
    self.reachability = [PLVReachability reachabilityForInternetConnection];
    [self.reachability startNotifier];
}

#pragma mark 回调
- (void)callbackPlayingStateDidChange:(BOOL)playing {
    if (playing) {
        [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback error:nil];
        [[AVAudioSession sharedInstance] setActive:YES error:nil];
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvPublicStreamPlayer:streamPlayerPlayingStateDidChange:)]) {
        [self.delegate plvPublicStreamPlayer:self streamPlayerPlayingStateDidChange:playing];
    }
}

- (void)callbackPlayerNetworkQuality:(PLVPublicStreamPlayerNetworkQuality)networkQuality {
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvPublicStreamPlayer:publicStreamNetworkQuality:)]) {
            [self.delegate plvPublicStreamPlayer:self publicStreamNetworkQuality:networkQuality];
        }
    })
}

#pragma mark - [ Delegate ]
#pragma mark PLVBPublicStreamPlayerDelegate
- (void)plvbPublicStreamPlayer:(PLVBPublicStreamVOLCPlayer *)streamPlayer videoLossRate:(float)videoLossRate audioLossRate:(float)audioLossRate {
    if (!self.player) {
        return;
    }
    
    if (self.reachability.currentReachabilityStatus == PLVNotReachable) {
        [self callbackPlayerNetworkQuality:PLVPublicStreamPlayerNetworkQuality_NoConnection];
        return;
    }
    
    PLVPublicStreamPlayerNetworkQuality quality;
    if (videoLossRate < 0.1) {
        quality = PLVPublicStreamPlayerNetworkQuality_Good;
    } else if (videoLossRate < 0.3) {
        quality = PLVPublicStreamPlayerNetworkQuality_Middle;
    } else {
        quality = PLVPublicStreamPlayerNetworkQuality_Poor;
    }
    
    [self callbackPlayerNetworkQuality:quality];
}

@end
