//
//  PLVLivePlayer.m
//  PLVCloudClassSDK
//
//  Created by Lincal on 2020/12/5.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVLivePlayer.h"

#import "PLVLivePrivateAPI.h"
#import "PLVLiveVideoConfig.h"
#import "PLVLiveAPIUtils.h"
#import "PLVChannelInfoModel+PrivateInfo.h"
#import "PLVPlayer+SubClassExtension.h"
#import "PLVWErrorManager.h"
#import "PLVWELogEventDefine.h"
#import "PLVWLogReporterManager.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVLivePictureInPictureManager+Private.h"
#import "PLVQosLoadingTracerModel.h"

#import "PLVConsoleLogger.h"

@interface PLVLivePlayer ()<
PLVLivePictureInPictureManagerDelegate
>

#pragma mark 状态
@property (nonatomic, assign) PLVChannelLiveStreamState currentStreamState;
@property (nonatomic, assign) PLVLivePlayerLoadState loadState;
@property (nonatomic, assign) BOOL playingWarmUpVideo;
@property (nonatomic, assign, readonly) BOOL externalInLinkMic; // 外部是否 ‘正在连麦’
@property (nonatomic, assign, readonly) BOOL externalPausedWatchNoDelay; // 外部是否 ‘已暂停无延迟观看’

@property (nonatomic, assign) BOOL loadingLogSent; // 是否已发送loading日志
@property (nonatomic, assign) BOOL recordingBufferTime; // 是否正在记录buffer时间
@property (nonatomic, assign) BOOL recordingStallingTime; // 是否正在记录stalling时间
@property (nonatomic, assign) BOOL playerErrorSend; // 是否已发送error日志
@property (nonatomic, assign) BOOL noDelayLiveWatching; // 播放器当前是否正在播放无延迟直播
@property (nonatomic, assign) BOOL quickLiveWatching; // 播放器当前是否正在播放快直播
@property (nonatomic, assign) BOOL publicStreamWatching; // 播放器当前是否正在播放公共流
@property (nonatomic, assign) BOOL noDelayWatchMode; // 播放器当前是否为无延迟观看模式（无延迟直播、快直播、公共流）

#pragma mark 数据
@property (nonatomic, copy) NSString * accountUserId;
@property (nonatomic, copy) NSString * channelId;
@property (nonatomic, strong) PLVChannelInfoModel * channelInfo;
@property (nonatomic, copy) NSString * currentSessionId; // 该值仅取 channel-sessionid/query 接口请求所得

@property (nonatomic, copy) NSURL * contentURL;
@property (nonatomic, copy) NSString * targetCodeRate;
@property (nonatomic, assign) NSInteger targetLineIndex;

@property (nonatomic, assign) NSInteger maxRetryCount;
@property (nonatomic, assign) NSInteger retryCount;
@property (nonatomic, assign) NSInteger lastErrorCode;

@property (nonatomic, assign) int64_t lastWebrtcNackCount;  // 快直播下的包重传次数
@property (nonatomic, assign) int64_t lastWebrtcVideoPacketReceive; // 快直播下的视频包接收个数

/**
 关于各数据说明-2021年10月12日-Lincal：
 
 < 1.6.2版本前(不包括1.6.2) >
 当前 PLVLivePlayer对象 创建后：
 1. pid 保持不变，直到销毁（根据此特性，可作为 PLVLivePlayer对象 生命周期的判断）；即使 reloadLivePlayer刷新直播、即使 SessionId 发生变化 均不会改变pid；
 2. pd 持续累加，即使 SessionId 发生变化；
 3. sd 持续累加，即使 SessionId 发生变化，且，直播前进入频道的停留时间，也会累计入sd中；
 
 < 1.6.2版本后(包括1.6.2) >
 本次改动依据：一是对齐web端，二是参照内部文档《关于PID的相关说明》，三是与产品同学沟通结果。对应单号LIVE-44869
 1. pid 并非保持不变，在 SessionId 发生变化时，刷新pid（参考内部文档 《关于PID的相关说明》http://wiki.igeeker.org/pages/viewpage.action?pageId=28457679 中类比“changeVid”场景）
 2. pd 在 SessionId 发生变化时，重置为0；
 3. sd 在 SessionId 发生变化时，重置为0；
 改动后的影响：pid不再表示 PLVLivePlayer对象的生命周期，kibana排查时，不应以pid作为PLVLivePlayer对象的创建销毁标准；
 */
@property (nonatomic, copy) NSString * lpi; /// 直播播放器ID (简化命名作保护，原名livePlayerId，不允许公开，请勿截图暴露)
@property (nonatomic, assign) NSInteger wt; /// 用户观看时长 (不包含连麦；包含无延迟) (简化命名作保护，原名watchDuration，不允许公开，请勿截图暴露)
@property (nonatomic, assign) NSInteger twt; /// 用户总观看时长 (包含连麦；包含无延迟) (简化命名作保护，原名totalWatchDuration，不允许公开，请勿截图暴露)
@property (nonatomic, assign) NSInteger st; /// 用户停留时长 (简化命名作保护，原名stayDuration，不允许公开，请勿截图暴露)

@property (nonatomic, copy) NSString *playerId; /// 直播播放器ID （同 lpi 生成规则一致，从加载播放器成功，即生成一个playerid，过程中没有销毁播放器 ，则始终用同一个playerid，一个playerId可对应多个pid）

@property (nonatomic, strong) NSDate *loadStartDate; // 记录准备播放的时间，用于loading日志
@property (nonatomic, strong) NSDate *bufferStartDate; // 记录开始缓冲的时间，用于buffer日志
@property (nonatomic, assign) NSInteger time_loading; // 记录播放器加载业务流程消耗的时间，用于loading日志
@property (nonatomic, assign) NSInteger time_player; // 记录播放器起播的时间，用于loading日志
@property (nonatomic, strong) NSMutableArray <PLVQosLoadingTracerModel *> *tracerModels; //记录各个播放器起播业务流程信息

#pragma mark 功能对象
@property (nonatomic, strong) NSTimer * liveStatusTimer;
@property (nonatomic, strong) NSTimer * timeCountTimer;
@property (nonatomic, strong) NSTimer * quickLiveTimer;
@property (nonatomic, strong) NSTimer *stallingTimer;
@property (nonatomic, strong) PLVReachability *reachability; // 监听网络变化
@property (nonatomic, weak) dispatch_block_t showMessageBlock;

@end

@implementation PLVLivePlayer
@synthesize pictureInPictureDelegate, canAutoStartPictureInPicture;

#pragma mark - [ Life Period ]
- (void)dealloc{
    if (_liveStatusTimer) {
        [_liveStatusTimer invalidate];
        _liveStatusTimer = nil;
    }
    if (_timeCountTimer) {
        [_timeCountTimer invalidate];
        _timeCountTimer = nil;
    }
    [self stopQuickLiveTimer];
    [self stopStallingTimer];
    [[PLVLivePictureInPictureManager sharedInstance] cleanPictureInPicturePlayer];
    PLV_LOG_INFO(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
}


#pragma mark - [ Public Methods ]
- (instancetype)initWithPLVAccountUserId:(NSString *)accountUserId channelId:(NSString *)channelId{
    if (![PLVFdUtil checkStringUseable:accountUserId] ||
        ![PLVFdUtil checkStringUseable:channelId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePlayer - init failed, parameters illegal, accountUserId:%@, channelId:%@",accountUserId,channelId);
        return nil;
    }
    
    if (self = [super init]) {
        self.accountUserId = accountUserId;
        self.channelId = channelId;
        PLV_KEY_INFO(@"mediaIdList", channelId);
        
        [self setupData];
        [self addTimers];
        
        __weak typeof(self) weakSelf = self;
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            [self requestChannelInfoWithSuccessBlock:^{
                [weakSelf requestLiveStreamStateForStreamHandle];
            }];
        });
                
        // 只能存在一个画中画窗口，所以当已经有画中画窗口存在的时候，进入另一个播放器将会关闭之前的画中画窗口
        if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureActive) {
            [PLVLivePictureInPictureManager sharedInstance].restoreDelegate = nil;
            [[PLVLivePictureInPictureManager sharedInstance] stopPictureInPicture];
        }
    }
    return self;
}

- (instancetype)initWithPolyvAccountUserId:(NSString *)accountUserId channelId:(NSString *)channelId{
    return [self initWithPLVAccountUserId:accountUserId channelId:channelId];
}

- (void)reloadLivePlayer{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s", __FUNCTION__);
    [super clearMainPlayer];
    [super play];
    [self switchToLineIndex:self.channelInfo.currentLineIndex codeRate:self.channelInfo.currentDefinition];
}

/// SDK内部使用的，全局的 “当前频道是否无延迟” 值
BOOL SDKInnerGlobalChannelWatchNoDelay = NO;

- (void)switchToLineIndex:(NSInteger)targetLineIndex codeRate:(NSString *)targetCodeRate {
    SDKInnerGlobalChannelWatchNoDelay = self.channelWatchNoDelay || self.channelWatchPublicStream; /// 同步“刷新直播播放器”的时机进行更新值

    if (self.externalInLinkMic || self.noDelayLiveWatching || self.publicStreamWatching) { return; }

    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"switchToLineIndex:%ld codeRate:", targetLineIndex, targetCodeRate);
    if (targetLineIndex >= 0) { self.targetLineIndex = targetLineIndex; }
    self.targetCodeRate = targetCodeRate;
    
    __weak typeof(self) weakSelf = self;
    [self requestChannelInfoWithSuccessBlock:^{
        [weakSelf loadMainPlayer];
    }];
}

- (void)switchToAudioMode:(BOOL)audioMode{
    if (self.externalInLinkMic || self.noDelayWatchMode) { return; }
    
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"switchToAudioMode:%@", audioMode ? @"YES" : @"NO");
    _audioMode = audioMode;
    
    __weak typeof(self) weakSelf = self;
    [self requestChannelInfoWithSuccessBlock:^{
        [weakSelf loadMainPlayer];
    }];
}

- (void)switchToNoDelayWatchMode:(BOOL)noDelayWatchMode{
    if ((self.externalInLinkMic) || (!self.channelWatchNoDelay && !self.channelWatchQuickLive && !self.channelWatchPublicStream)) {
        return;
    }
    
    if (_noDelayWatchMode == noDelayWatchMode) {
        return;
    }
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"switchToNoDelayWatchMode:%@", noDelayWatchMode ? @"YES" : @"NO");
    _noDelayWatchMode = noDelayWatchMode;
    [self resetPlayData];
    
    if (self.channelWatchQuickLive) {
        [super play];
    }
    __weak typeof(self) weakSelf = self;
    [self requestChannelInfoWithSuccessBlock:^{
        [weakSelf loadMainPlayer];
    }];
    NSString *eventString = noDelayWatchMode ? @"switchToNoDelayWatchMode:YES" : @"switchToNoDelayWatchMode:NO";
    [[PLVWLogReporterManager sharedManager] reportWithEvent:eventString modul:PLVWELogModulPlay information:nil];
}

- (void)startPictureInPictureFromOriginView:(UIView *)originView {
    if (self.audioMode){
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePlayer - startPictureInPicture failed, audio mode is not supported");
        return;
    }
    
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s", __FUNCTION__);
    if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureMode == PLVLivePictureInPictureMode_AVPlayer) {
        NSURL *contentURL = [self createPictureInPictureContentURL];
        if (contentURL) {
            [PLVLivePictureInPictureManager sharedInstance].delegate = self;
            [[PLVLivePictureInPictureManager sharedInstance] startPictureInPictureWithContentURL:contentURL displaySuperview:originView];
        }else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePlayer - startPictureInPicture failed, contentURL must not be nil");
        }
    } else if (@available(iOS 15.0, *)) {
        if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureMode == PLVLivePictureInPictureMode_IJKPlayer) {
            [PLVLivePictureInPictureManager sharedInstance].delegate = self;
            [[PLVLivePictureInPictureManager sharedInstance] startPictureInPictureWithPlayer:self];
        }
    }
}

- (void)stopPictureInPicture {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s", __FUNCTION__);
    [[PLVLivePictureInPictureManager sharedInstance] stopPictureInPicture];
}

- (void)setUpdateAutoStartPictureInPicture:(BOOL)updateAutoStartPictureInPicture{
    self.canAutoStartPictureInPicture = updateAutoStartPictureInPicture;
    
    if (self.canAutoStartPictureInPicture){
        // 画中画退到后台自动启动，入口
        [PLVLivePictureInPictureManager sharedInstance].canAutoStartPictureInPicture = self.canAutoStartPictureInPicture;
        [PLVLivePictureInPictureManager sharedInstance].delegate = self;
        [[PLVLivePictureInPictureManager sharedInstance] bindPictureInPictureForBackgroundMode:self];
    }
    else{
        // 清理画中画
        [[PLVLivePictureInPictureManager sharedInstance] cleanPictureInPicturePlayer];
    }
}

#pragma mark Getter
- (NSString *)livePlayerId{
    return _lpi;
}

- (NSInteger)watchDuration{
    return _wt;
}

- (NSInteger)totalWatchDuration{
    return _twt;
}

- (NSInteger)stayDuration{
    return _st;
}

- (BOOL)noDelayLiveWatching {
    return _channelWatchNoDelay && _noDelayWatchMode;
}

- (BOOL)quickLiveWatching {
    return _channelWatchQuickLive && _noDelayWatchMode;
}

- (BOOL)publicStreamWatching {
    return _channelWatchPublicStream && _noDelayWatchMode;
}

#pragma mark Setter
- (void)setChannelWatchNoDelay:(BOOL)channelWatchNoDelay {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"set channelWatchNoDelay:%@", channelWatchNoDelay ? @"YES" : @"NO");
    _channelWatchNoDelay = channelWatchNoDelay;
    _noDelayWatchMode = _channelWatchNoDelay || _channelWatchQuickLive || _channelWatchPublicStream;
}

- (void)setChannelWatchQuickLive:(BOOL)channelWatchQuickLive {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"set channelWatchQuickLive:%@", channelWatchQuickLive ? @"YES" : @"NO");
    _channelWatchQuickLive = channelWatchQuickLive;
    _noDelayWatchMode = _channelWatchNoDelay || _channelWatchQuickLive || _channelWatchPublicStream;
}

- (void)setChannelWatchPublicStream:(BOOL)channelWatchPublicStream {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"set channelWatchPublicStream:%@", channelWatchPublicStream ? @"YES" : @"NO");
    _channelWatchPublicStream = channelWatchPublicStream;
    _noDelayWatchMode = _channelWatchNoDelay || _channelWatchQuickLive || _channelWatchPublicStream;
}

#pragma mark - [ Father Public Methods ]
- (void)clearAllPlayer {
    [[PLVLivePictureInPictureManager sharedInstance] unbindPictureInPicturePlayer:self];
    [super clearAllPlayer];
}

#pragma mark - [ Private Methods ]
- (void)setupData{
    self.videoToolBox = YES;
    self.maxRetryCount = 1;
    self.allowShowWarmUpContent = YES;
    self.currentStreamState = PLVChannelLiveStreamState_Unknown;
    
    self.loadStartDate = [NSDate date];
    [self resetPlayData];
    self.playerId = self.lpi;
    self.tracerModels = [NSMutableArray array];
}

- (void)resetPlayData{
    self.lpi = [PLVLiveVideoConfig createPlayerId];
    self.wt = 0;
    self.twt = 0;
    self.st = 0;
}

- (void)addTimers {
    self.liveStatusTimer = [NSTimer scheduledTimerWithTimeInterval:10.0 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(liveStatusTimerEvent:) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:self.liveStatusTimer forMode:NSRunLoopCommonModes];

    self.timeCountTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(timeCountTimerEvent:) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:self.timeCountTimer forMode:NSRunLoopCommonModes];
}

- (void)startQuickLiveTimer {
    self.quickLiveTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(quickLiveTimerEvent:) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:self.quickLiveTimer forMode:NSRunLoopCommonModes];
}

- (void)stopQuickLiveTimer {
    if (_quickLiveTimer) {
        [_quickLiveTimer invalidate];
        _quickLiveTimer = nil;
    }
}

- (void)setupReachability {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(livePlayerReachabilityChange:) name:kPLVReachabilityChangedNotification object:nil];
    self.reachability = [PLVReachability reachabilityForInternetConnection];
    [self.reachability startNotifier];
}

- (void)updateSessionId:(NSString *)sessionId{
    [self.channelInfo updateChannelSessionId:sessionId];
    if (![self.currentSessionId isEqualToString:sessionId]) {
        [self resetPlayData];
        [self callbackForChannelInfoDidUpdated];
    }
    self.currentSessionId = sessionId;
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"sessionId update:%@",sessionId);
}

/** liveStatus请求接口参数补充
    用于观看日志补充页面进出数据 */
- (NSMutableDictionary *)createExtraParamDict {
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    paramDict[@"pid"] = self.lpi ?: @"";
    paramDict[@"uid"] = self.accountUserId ?: @"";
    paramDict[@"ts"] = @([PLVFdUtil curTimeInterval]);
    paramDict[@"pn"] = PLVLiveVideoConfig.sharedInstance.playerName ?: @"";
    paramDict[@"pv"] = PLVLiveVideoConfig.sharedInstance.playerVersion ?: @"";
    paramDict[@"sid"] = self.channelInfo.sessionId ?: @"";
    paramDict[@"p1"] = self.customParam.liveParam1 ?: @"";
    paramDict[@"p2"] = self.customParam.liveParam2 ?: @"";
    paramDict[@"p3"] = [PLVDataUtil urlSafeBase64String:@"live"];
    paramDict[@"p4"] = self.customParam.liveParam4 ?: @"";
    paramDict[@"p5"] = self.customParam.liveParam5 ?: @"";
    paramDict[@"ptype"] = (self.noDelayLiveWatching || self.quickLiveWatching || self.publicStreamWatching) ? @(2) : @(0);
    paramDict[@"playerid"] = self.playerId ?: @"";// 播放器playerId 一个playerId对应多个pid
    paramDict[@"pd"] = @(self.watchDuration);
    return paramDict;
}

- (NSURL *)createPictureInPictureContentURL {
    /// 解析 播放地址
    NSString * contentURLString = self.channelInfo.currentDefinitionM3u8Url;
    if (![PLVFdUtil checkStringUseable:contentURLString]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePlayer - startPictureInPicture failed,the currentDefinitionM3u8Url should not be nil.");
        return nil;
    }
    
    /// 设置 播放地址 参数
    BOOL hadParams = [contentURLString containsString:@"?"];
    if ([PLVFdUtil checkStringUseable:self.customParam.liveParam1]) {
        NSString * param1 = [PLVDataUtil urlSafeBase64String:self.customParam.liveParam1];
        contentURLString = [contentURLString stringByAppendingFormat:@"%@param1=%@", (hadParams ? @"&" : @"?"), param1];
    }
    
    hadParams = [contentURLString containsString:@"?"];
    if ([PLVFdUtil checkStringUseable:self.lpi]) {
        NSString * playerId = [PLVDataUtil urlSafeBase64String:self.lpi];
        contentURLString = [contentURLString stringByAppendingFormat:@"%@pid=%@", (hadParams ? @"&" : @"?"), playerId];
    }
    
    NSURL *contentURL = [NSURL URLWithString:contentURLString];
    return contentURL;
}

#pragma mark 请求
- (void)requestChannelInfoWithSuccessBlock:(void (^)(void))successBlock{
    self.loadState = PLVLivePlayerLoadState_ChannelInfoRequesting;
    __weak typeof(self) weakSelf = self;
    PLVQosLoadingTracerModel *model = [[PLVQosLoadingTracerModel alloc] initWithType:PLVQosLoadingTracerModelTypeChannelJson];
    [model addStartTime];
    [PLVLivePrivateAPI loadChannelInfoRepeatedlyWithUserId:self.accountUserId channelId:self.channelId isStandby:NO completion:^(PLVChannelInfoModel * channelInfo) {
        [weakSelf addTracerModelEndTimeToModels:model];
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"request channelInfo success");
        /// 更新 频道信息
        weakSelf.channelInfo = channelInfo;
        [weakSelf.channelInfo updateChannelSessionId:weakSelf.currentSessionId];
        [weakSelf callbackForChannelInfoDidUpdated];
        
        /// 请求 频道限制信息
        [weakSelf requestChannelRestrictInfoWithSuccessBlock:successBlock];
    } failure:^(NSError * error) {
        [weakSelf addTracerModelEndTimeToModels:model];
        PLVQosLoadingTracerModel *standbyModel = [[PLVQosLoadingTracerModel alloc] initWithType:PLVQosLoadingTracerModelTypeChannelJsonStandby];
        [standbyModel addStartTime];
        [PLVLivePrivateAPI loadChannelInfoRepeatedlyWithUserId:self.accountUserId channelId:self.channelId isStandby:YES completion:^(PLVChannelInfoModel * channelInfo) {
            [weakSelf addTracerModelEndTimeToModels:standbyModel];
            PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"request channelInfo repeated success");
            /// 更新 频道信息
            weakSelf.channelInfo = channelInfo;
            [weakSelf.channelInfo updateChannelSessionId:weakSelf.currentSessionId];
            [weakSelf callbackForChannelInfoDidUpdated];
            
            /// 请求 频道限制信息
            [weakSelf requestChannelRestrictInfoWithSuccessBlock:successBlock];
        } failure:^(NSError * error) {
            [weakSelf addTracerModelEndTimeToModels:standbyModel];
            weakSelf.loadState = PLVLivePlayerLoadState_NotLoading;
            [weakSelf callbackForLoadMainPlayerFailureError:error];
        }];
    }];
    
}

- (void)requestChannelRestrictInfoWithSuccessBlock:(void (^)(void))successBlock{
    self.loadState = PLVLivePlayerLoadState_ChannelRestrictInfoRequesting;
    __weak typeof(self) weakSelf = self;
    PLVQosLoadingTracerModel *model = [[PLVQosLoadingTracerModel alloc] initWithType:PLVQosLoadingTracerModelTypeRestrictInfo];
    [model addStartTime];
    [weakSelf.channelInfo updateChannelRestrictInfo:^(PLVChannelRestrictState restrictState) {
        [weakSelf addTracerModelEndTimeToModels:model];
        if (restrictState == PLVChannelRestrictState_NoneRestrict) { /// 无限制
            [weakSelf updateChannelCodeRateOptions];
            PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"updateChannelRestrictInfo success");
            if (successBlock) { successBlock(); }
            
        } else if (restrictState == PLVChannelRestrictState_PlayRestrict){ /// 限制播放
            [weakSelf reportAndCallbackForLoadMainPlayerErrorCode:PLVFPlayErrorCodeChannelRestrict_PlayRestrict];
            
        } else if (restrictState == PLVChannelRestrictState_Unknown || restrictState == PLVChannelRestrictState_GetFailed){ /// 未知限制 或 获取失败
            /// 尝试 重新请求
            BOOL willRunRetry = [weakSelf retryRequest:^{
                [weakSelf requestChannelRestrictInfoWithSuccessBlock:successBlock];
            }];
            
            if (!willRunRetry) {
                /// 放弃重试
                weakSelf.loadState = PLVLivePlayerLoadState_NotLoading;
                [weakSelf reportAndCallbackForLoadMainPlayerErrorCode:PLVFPlayErrorCodeChannelRestrict_RequestFailed];
            }
        }
    }];
}

- (void)requestLiveStreamStateForStreamHandle {
    if (self.channelInfo && self.channelInfo.restrictState == PLVChannelRestrictState_NoneRestrict) {
        self.loadState = PLVLivePlayerLoadState_ChannelStreamStateRequesting;
        __weak typeof(self) weakSelf = self;
        PLVQosLoadingTracerModel *model = [[PLVQosLoadingTracerModel alloc] initWithType:PLVQosLoadingTracerModelTypeLiveStreamStatus];
        [model addStartTime];
        [PLVLivePrivateAPI liveStreamStatus:self.channelInfo.streamID channelId:self.channelId params:[self createExtraParamDict] completion:^(PLVChannelLiveStreamState newestStreamState,NSString *sessionId) {
            [weakSelf addTracerModelEndTimeToModels:model];
            /// 修改 加载状态
            weakSelf.loadState = PLVLivePlayerLoadState_NotLoading;
            
            /// 针对流状态作处理、更新流状态
            [weakSelf updateLiveStreamState:newestStreamState];
            
            /// 更新sessionId
            [weakSelf updateSessionId:sessionId];
        } failure:^(NSError * error) {
            [weakSelf addTracerModelEndTimeToModels:model];
            /// 针对流状态作处理、更新流状态
            [weakSelf updateLiveStreamState:PLVChannelLiveStreamState_Unknown];
            
            /// 回调外部
            [weakSelf callbackForLoadMainPlayerFailureError:error];
        }];
    }
}

- (BOOL)retryRequest:(void (^)(void))requestBlock{
    BOOL willRunRetry = NO;
    if (self.retryCount <= self.maxRetryCount && requestBlock) {
        /// 进行重试
        self.retryCount++;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), requestBlock);
        willRunRetry = YES;
    }
    if (!willRunRetry) { self.retryCount = 0; }
    return willRunRetry;
}

#pragma mark 播放
- (void)updateChannelCodeRateOptions{
    [self.channelInfo switchToTargetLineIndex:self.targetLineIndex targetDefinition:self.targetCodeRate];
    [self callbackForPlayOptionsUpdate];
}

- (void)updateLiveStreamState:(PLVChannelLiveStreamState)newestStreamState{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"liveStreamState update:%ld",newestStreamState);
    if (newestStreamState == PLVChannelLiveStreamState_End) { /// 无直播
        /// 主播放器 停止
        [super clearMainPlayer];
        [self cancelShowPlayerLoadTimeoutOnDelay];
        
        /// 展示 暖场 内容
        if (self.allowShowWarmUpContent) { [self showWarmUpContent]; }
    } else if (newestStreamState == PLVChannelLiveStreamState_Live) { /// 直播中
        /// 开始播放
        if (!self.externalInLinkMic && !self.noDelayLiveWatching && !self.publicStreamWatching) {
            if (self.currentStreamState != PLVChannelLiveStreamState_Live) { /// 需要判断 lineIndex的变化
                [self loadMainPlayer];
            }
        }
        
        // 如果恢复直播流的时候有画中画正在使用，那么让画中画播放器重新拉流
        if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureActive &&
            [PLVLivePictureInPictureManager sharedInstance].pictureInPictureMode == PLVLivePictureInPictureMode_AVPlayer &&
            self.currentStreamState != PLVChannelLiveStreamState_Live) {
            NSURL *contentURL = [self createPictureInPictureContentURL];
            if (contentURL) {
                [[PLVLivePictureInPictureManager sharedInstance] replayPictureInPicturePlayerWithURL:contentURL];
            }else {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePlayer - startPictureInPicture failed, contentURL must not be nil");
            }
        }
        
    } else if (newestStreamState == PLVChannelLiveStreamState_Stop){ /// 直播暂停
        /// 主播放器 停止
        [super clearMainPlayer];
    }
    
    SDKInnerGlobalChannelWatchNoDelay = self.channelWatchNoDelay || self.channelWatchPublicStream; /// 同步“流状态更新”的时机进行更新值
    
    // 回调外部
    BOOL streamStateDidChanged = (self.currentStreamState != newestStreamState);
    self.currentStreamState = newestStreamState;
    if (streamStateDidChanged) { [self callbackForStreamStateUpdate:streamStateDidChanged]; }
}

- (void)loadMainPlayer {
    if (self.noDelayLiveWatching || self.publicStreamWatching) {
        return;
    }
    
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s",__FUNCTION__);
    
    if (self.channelInfo && self.channelInfo.restrictState == PLVChannelRestrictState_NoneRestrict) {
        /// 清理 全部 播放器、暖场内容
        [self clearAllPlayer];
        /// 清理快直播定时器
        [self stopQuickLiveTimer];
        self.playingWarmUpVideo = NO;
        [self callbackForShowWarmUpImage:NO imageURLString:nil];
        
        /// 设置 播放配置
        PLVOptions * options = [PLVOptions optionsByDefault];
        [options setFormatOptionIntValue:1024 forKey:@"probsize"];
        [options setFormatOptionIntValue:1 forKey:@"analyzeduration"];
        [options setFormatOptionIntValue:1 forKey:@"dns_cache_clear"];
        if ([self.channelInfo.streamType isEqualToString:@"disk"] || !self.videoToolBox) {
            // 硬盘推流使用软解码，因为暂停时新的视频流导致iOS硬解码失败
            [options setPlayerOptionIntValue:0 forKey:@"videotoolbox"];
        } else {
            [options setPlayerOptionIntValue:1 forKey:@"videotoolbox"];
        }
        [options setCodecOptionIntValue:IJK_AVDISCARD_DEFAULT forKey:@"skip_loop_filter"];
        [options setCodecOptionIntValue:IJK_AVDISCARD_DEFAULT forKey:@"skip_frame"];
        if (self.chaseFrame) {
            [options setPlayerOptionIntValue:3000 forKey:@"max_cached_duration"];// 追帧参数
            [options setPlayerOptionIntValue:1 forKey:@"infbuf"];
        }
        [options setPlayerOptionIntValue:5 forKey:@"framedrop"];
        
        NSString *currentDefinitionUrl = self.channelInfo.currentDefinitionUrl;
        /// 快直播
        if (self.quickLiveWatching && [PLVFdUtil checkStringUseable:self.channelInfo.quickLiveUrl]) {
            currentDefinitionUrl = self.channelInfo.quickLiveUrl;
            [options setPlayerOptionIntValue:1 forKey:@"infbuf"];
            [options setPlayerOptionIntValue:0 forKey:@"packet-buffering"];
            [options setPlayerOptionIntValue:0 forKey:@"max_cached_duration"];

            // 播放器缓冲区控制
            [options setPlayerOptionIntValue:0 forKey:@"playcontrol-optimize"];
            [options setPlayerOptionIntValue:200 forKey:@"high-level-ms"]; // playcontrol-optimize 为0 参数失效
            [options setPlayerOptionIntValue:100 forKey:@"low-level-ms"]; // playcontrol-optimize 为0 参数失效
            
            // 2.0 版本后开始生效 playcontrol-optimize 需要关闭
            [options setFormatOptionIntValue:1 forKey:@"webrtc_enable_play_control"]; // 开启内部播控
            [options setFormatOptionIntValue:1 forKey:@"webrtc_enable_smoothing_output"]; // 平滑输出
            
//            [options setFormatOptionValue:@"1000" forKey:@"webrtc_min_delay"]; // 2.0 版本后 参数废弃
//            [options setFormatOptionIntValue:0 forKey:@"webrtc_max_jitter_delay"]; // 2.0 版本后不再设置，leb 默认3000
            
            // 链路信令服务器
            [options setFormatOptionValue:@"webrtc-dk.tliveplay.com" forKey:@"webrtc_server_address"];

            [self startQuickLiveTimer];
        }

        /// 解析 播放地址
        NSString * contentURLString = self.audioMode ? self.channelInfo.currentAudioURLString : currentDefinitionUrl;
        /// 设置网络监听
        [self setupReachability];
        /// 设置 播放地址 参数
        BOOL hadParams = [contentURLString containsString:@"?"];
        if ([PLVFdUtil checkStringUseable:self.customParam.liveParam1]) {
            NSString * param1 = [PLVDataUtil urlSafeBase64String:self.customParam.liveParam1];
            contentURLString = [contentURLString stringByAppendingFormat:@"%@param1=%@", (hadParams ? @"&" : @"?"), param1];
        }
        
        hadParams = [contentURLString containsString:@"?"];
        if ([PLVFdUtil checkStringUseable:self.lpi]) {
            NSString * playerId = [PLVDataUtil urlSafeBase64String:self.lpi];
            contentURLString = [contentURLString stringByAppendingFormat:@"%@pid=%@", (hadParams ? @"&" : @"?"), playerId];
        }
        
        /// DNS
        NSURL * contentURL = [NSURL URLWithString:contentURLString];
        if (!self.quickLiveWatching && !self.channelInfo.realDiskEnabled) { // 真伪直播不支持HttpDNS
            NSString * ipHostURLString = [PLVLiveAPIUtils getHttpDNSWithURLString:contentURLString];
            if ([PLVFdUtil checkStringUseable:ipHostURLString]) {
                [options setFormatOptionValue:[NSString stringWithFormat:@"Host: %@", contentURL.host] forKey:@"headers"];
                contentURL = [NSURL URLWithString:ipHostURLString];
            }
        }
        
        /// 开始播放
        self.contentURL = contentURL;
        [super loadMainContentToPlayWithContentURL:contentURL withOptions:options];
        
        if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureActive) {
            [self pause];
        }
        
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePlayer - loadMainPlayer illegal, restrictState = %ld",self.channelInfo.restrictState);
    }
}

- (void)showPlayerLoadTimeoutOnDelay{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"show player load");
    if (self.showMessageBlock) {
        dispatch_block_cancel(self.showMessageBlock);
        self.showMessageBlock = nil;
    }
    
    __weak typeof(self) weakSelf = self;
    dispatch_block_t showMessageBlock = dispatch_block_create(DISPATCH_BLOCK_INHERIT_QOS_CLASS, ^{
        [weakSelf reportAndCallbackForLoadMainPlayerErrorCode:PLVFPlayErrorCodeNetwork_NotGoodNetwork];
    });
    self.showMessageBlock = showMessageBlock;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10 * NSEC_PER_SEC)), dispatch_get_main_queue(), showMessageBlock);
}

- (void)cancelShowPlayerLoadTimeoutOnDelay{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"cancel show player load");
    if (self.showMessageBlock) {
        dispatch_block_cancel(self.showMessageBlock);
        self.showMessageBlock = nil;
    }
}

#pragma mark 暖场
- (void)showWarmUpContent{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s", __FUNCTION__);
    if (!self.externalInLinkMic && [PLVFdUtil checkStringUseable:self.channelInfo.warmUpContentUrlString]) {
        NSURL * warmUpContentURL = [NSURL URLWithString:self.channelInfo.warmUpContentUrlString];
        if (!warmUpContentURL) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"PLVLivePlayer - load ad content failed, warmUpContentURL:%@",warmUpContentURL);
        }
        
        if (self.channelInfo.warmUpType == PLVChannelWarmUpType_Video) { // ’视频类型‘的暖场
            if (!self.subPlayerExist || !self.playingWarmUpVideo) {
                // 未转换成ip地址的host
                NSString * warmUpVideoHost = warmUpContentURL.host;
                NSString * ipHostURLStr = [PLVLiveAPIUtils getHttpDNSWithURLString:self.channelInfo.warmUpContentUrlString];
                if ([PLVFdUtil checkStringUseable:ipHostURLStr]) {
                    warmUpContentURL = [NSURL URLWithString:ipHostURLStr];
                }
                [self playWarmUpVideo:warmUpContentURL warmupVideoHost:warmUpVideoHost];
            }
        } else if (self.channelInfo.warmUpType == PLVChannelWarmUpType_Image){ // ’图片类型‘的暖场
            self.playingWarmUpVideo = YES;
            [self callbackForShowWarmUpImage:YES imageURLString:self.channelInfo.warmUpContentUrlString];
        }
    }
}

- (void)playWarmUpVideo:(NSURL *)warmupVideoURL warmupVideoHost:(NSString *)warmUpVideoHost{
    self.playingWarmUpVideo = YES;

    PLVOptions * options = [PLVOptions optionsByDefault];
    [options setPlayerOptionIntValue:0 forKey:@"loop"];
    [options setPlayerOptionIntValue:1 forKey:@"videotoolbox"];
    [options setPlayerOptionIntValue:1 forKey:@"framedrop"];
    [options setFormatOptionIntValue:1 forKey:@"dns_cache_clear"];
    [options setPlayerOptionIntValue:10 * 1024 * 1024 forKey:@"max-buffer-size"];
    if ([PLVLiveVideoConfig sharedInstance].enableHttpDNS && ![PLVLiveVideoConfig sharedInstance].enableIPV6) {
        [options setFormatOptionValue:[NSString stringWithFormat:@"Host: %@", warmUpVideoHost] forKey:@"headers"];
    }
    [super loadSubContentToPlayWithContentURL:warmupVideoURL withOptions:options];
}

#pragma mark 统计 Qos
- (void)reportFirstLoading {
    if (self.loadingLogSent) {
        [self reportBuffer];
        return;
    }
    
    // 记录第一次loading时间
    if (!self.time_loading) {
        double diffTime = [[NSDate date] timeIntervalSinceDate:self.loadStartDate];
        self.time_loading = (int)floor(diffTime * 1000);
    }
    if (!self.time_player) {
        return;
    }
    self.loadingLogSent = YES;
    
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s", __FUNCTION__);
    [[PLVWLogReporterManager sharedManager] reportLiveLoadingQosWithChannel:self.channelInfo time:(int)self.time_loading time_player:(int)self.time_player time_business:[self mainPlayerLoadingBusinessTime] playerId:self.lpi];
}

- (void)reportBuffer {
    if (!self.recordingBufferTime) {
        return;
    }
    self.recordingBufferTime = NO;
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"%s", __FUNCTION__);
    double diffTime = [[NSDate date] timeIntervalSinceDate:self.bufferStartDate];
    [[PLVWLogReporterManager sharedManager] reportLiveBufferQosWithChannel:self.channelInfo time:(int)floor(diffTime * 1000) playerId:self.lpi];
}

- (void)reportStalling {
    [self stopStallingTimer];
    if (!self.recordingStallingTime) {
        return;
    }
    self.recordingStallingTime = NO;
    if (self.channelInfo.stallingSendFrequency > 0) {
        [[PLVWLogReporterManager sharedManager] reportLiveStallingQosWithChannel:self.channelInfo time:(int)(self.channelInfo.stallingSendFrequency * 1000) playerId:self.lpi];
    }
}

- (void)startReportStalling {
    if (self.recordingStallingTime) {
        return;
    }
    [self stopStallingTimer];
    
    NSTimeInterval time = (NSTimeInterval)self.channelInfo.stallingSendFrequency;
    if (time > 0) {
        NSTimer *timer = [NSTimer timerWithTimeInterval:time target:self selector:@selector(reportStalling) userInfo:nil repeats:NO];
        [[NSRunLoop currentRunLoop] addTimer:timer forMode:NSRunLoopCommonModes];
        self.stallingTimer = timer;
        self.recordingStallingTime = YES;
    }
}

- (void)stopReportStalling {
    [self stopStallingTimer];
    self.recordingStallingTime = NO;
}

- (void)stopStallingTimer {
    if (_stallingTimer) {
        [_stallingTimer invalidate];
        _stallingTimer = nil;
    }
}
- (void)reportPlaybackFailureAnalysis {
    if (self.playerErrorSend) { // 保持原来的逻辑，错误日志只上报一次
        return;
    }
    self.playerErrorSend = YES;
    
    __weak typeof(self) weakSelf = self;
    // TODO 考虑 Founation 封装便捷方法
    NSURLRequest * request = [NSURLRequest requestWithURL:self.contentURL cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:20.0];
    [[[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        NSInteger responseCode = [(NSHTTPURLResponse *)response statusCode];
        NSDictionary * userInfo = [NSDictionary dictionaryWithObject:@"null" forKey:NSLocalizedDescriptionKey];
        NSError * playErr = [NSError errorWithDomain:@"net.polyv.live" code:-100 userInfo:userInfo];
        
        NSString * errorCodeString = @"";
        if (error) {
            errorCodeString = @"load_livevideo_failure";
            playErr = error;
        } else if (responseCode != 200) {
            if (responseCode == 403) {
                errorCodeString = @"stream_403_error";
            } else {
                errorCodeString = @"stream_NOT200_error";
            }
        } else {
            errorCodeString = @"other_error";
        }
        NSString * errormsg = [NSString stringWithFormat:@"code:%ld, reason:%@", (long)playErr.code, playErr.localizedDescription];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypePlayer, @"reportPlaybackFailureAnalysis %@", errormsg);
        [[PLVWLogReporterManager sharedManager] reportLiveErrorQosWithChannel:weakSelf.channelInfo
                                                                          uri:weakSelf.contentURL.absoluteString
                                                                       status:[NSString stringWithFormat:@"%ld", (long)responseCode]
                                                                    errorcode:errorCodeString
                                                                     errormsg:errormsg
                                                                     playerId:weakSelf.lpi];
    }] resume];
}

// 添加结束时间并记录到业务流程数组
- (void)addTracerModelEndTimeToModels:(PLVQosLoadingTracerModel *)model {
    [model addEndTime];
    if (self.loadingLogSent || !model.rangeUseable) {
        return;
    }
    
    BOOL isExist = NO;
    
    if (![PLVFdUtil checkArrayUseable:self.tracerModels]) {
        self.tracerModels = [NSMutableArray array];
    } else {
        for (PLVQosLoadingTracerModel *tracerModel in self.tracerModels) {
            if (tracerModel.type == model.type) {
                [self.tracerModels replaceObjectAtIndex:[self.tracerModels indexOfObject:tracerModel] withObject:model];
                isExist = YES;
                break;
            }
        }
    }
    if (!isExist) {
        [self.tracerModels addObject:model];
    }
}

// 计算业务流程总耗时
- (int)mainPlayerLoadingBusinessTime {
    if (![PLVFdUtil checkArrayUseable:self.tracerModels]) {
        return 0;
    }
    
    NSMutableArray *ranges = [NSMutableArray array];
    for (PLVQosLoadingTracerModel *model in self.tracerModels) {
        if (model.rangeUseable) {
            [ranges addObject:@[@(model.startTime), @(model.endTime)]];
        }
    }
    if (![PLVFdUtil checkArrayUseable:ranges]) {
        return 0;
    }
    
    [ranges sortUsingComparator:^NSComparisonResult(NSArray *rangeArr1, NSArray *rangeArr2) {
        NSTimeInterval start1 = [rangeArr1[0] doubleValue];
        NSTimeInterval start2 = [rangeArr2[0] doubleValue];
        if (start1 == start2) {
            return NSOrderedSame;
        } else if (start1 > start2) {
            return NSOrderedDescending;
        } else {
            return NSOrderedAscending;
        }
    }];
    
    NSMutableArray *mergedRanges = [NSMutableArray array];
    for (NSArray *rangeArr in ranges) {
        NSTimeInterval start = [rangeArr[0] doubleValue];
        NSTimeInterval end = [rangeArr[1] doubleValue];
        if (mergedRanges.count == 0 || [mergedRanges.lastObject[1] doubleValue] < start) {
            [mergedRanges addObject:@[@(start), @(end)]];
        } else {
            NSArray *lastRangeArr = mergedRanges.lastObject;
            NSTimeInterval lastEnd = [lastRangeArr[1] doubleValue];
            NSTimeInterval newEnd = MAX(lastEnd, end);
            [mergedRanges replaceObjectAtIndex:(mergedRanges.count - 1) withObject:@[@(start), @(newEnd)]];
        }
    }
    NSTimeInterval totalDuration = 0;
    for (NSArray *rangeArr in mergedRanges) {
        NSTimeInterval start = [rangeArr[0] doubleValue];
        NSTimeInterval end = [rangeArr[1] doubleValue];
        NSTimeInterval duration = end - start;
        totalDuration += duration;
    }
    
    return (int)totalDuration;
}

#pragma mark 统计 ViewLog
- (void)reportTimeCount{
    self.st++;
    
    BOOL picturePlayerPlaying = [PLVLivePictureInPictureManager sharedInstance].pictureInPicturePlayerPlaying;
    BOOL mainPlayerPlaying = (self.mainPlayerPlaybackState & IJKMPMoviePlaybackStatePlaying) || picturePlayerPlaying;
    BOOL watchingNoDelay = self.channelWatchNoDelay || self.channelWatchPublicStream;
    BOOL watchingNoDelayNotInLinkMic = watchingNoDelay && self.currentStreamState == PLVChannelLiveStreamState_Live && !self.externalInLinkMic;
    
    if (mainPlayerPlaying || (watchingNoDelayNotInLinkMic && !self.externalPausedWatchNoDelay)) { self.wt++; }
    
    if (mainPlayerPlaying || watchingNoDelayNotInLinkMic || self.externalInLinkMic) {
        self.twt++;
        if (self.channelInfo && self.twt > 0 && self.wt > 0 && (self.st % self.channelInfo.reportFreq.integerValue == 0)) {
            __weak typeof(self) weakSelf = self;
            [[PLVWLogReporterManager sharedManager] reportLiveViewLogWithParam:self.customParam
                                                                      playerId:self.lpi
                                                           logModelConfigBlock:^PLVFViewLogModel * _Nonnull{
                PLVFViewLogModel * model = [[PLVFViewLogModel alloc] init];
                model.cid = [NSString stringWithFormat:@"%@", weakSelf.channelInfo.channelId];
                model.uid = weakSelf.channelInfo.accountUserId;
                model.pd = @(weakSelf.wt);
                model.sd = @(weakSelf.st);
                model.flow = @(0);
                model.param3 = @"live";
                model.session_id = weakSelf.channelInfo.sessionId;
                model.ptype = (self.noDelayLiveWatching || self.quickLiveWatching || self.publicStreamWatching) ? @(2) : @(0);
                return model;
            }];
        }
    }
}

#pragma mark 快直播网络质量检测
- (void)quickLiveNetworkQualityPolling {
    if (!self.mainPlayerExist || !self.quickLiveWatching) {
        return;
    }
    
    int64_t webrtcNackCount = [self getWebrtcNackCount];
    int64_t webrtcVideoPacketReceive = [self getWebrtcVideoPacketReceive];
    
    int64_t nackCoutDiff = webrtcNackCount - self.lastWebrtcNackCount;
    int64_t videoPacketReceiveDiff = webrtcVideoPacketReceive - self.lastWebrtcVideoPacketReceive;
    
    self.lastWebrtcNackCount = webrtcNackCount;
    self.lastWebrtcVideoPacketReceive = webrtcVideoPacketReceive;
    
    if (self.reachability.currentReachabilityStatus == PLVNotReachable) {
        [self.liveDelegate plvLivePlayer:self quickLiveNetworkQuality:PLVLivePlayerQuickLiveNetworkQuality_NoConnection];
        return;
    }
    
    if (nackCoutDiff < 0 || videoPacketReceiveDiff <= 0){
        return;
    }
    
    double ratioNack = (double)nackCoutDiff / videoPacketReceiveDiff;
    PLVLivePlayerQuickLiveNetworkQuality quality;
    if (ratioNack < 0.1) {
        quality = PLVLivePlayerQuickLiveNetworkQuality_Good;
    } else if (ratioNack < 0.3) {
        quality = PLVLivePlayerQuickLiveNetworkQuality_Middle;
    } else {
        quality = PLVLivePlayerQuickLiveNetworkQuality_Poor;
    }
    
    plv_dispatch_main_async_safe(^{
        if (self.liveDelegate && [self.liveDelegate respondsToSelector:@selector(plvLivePlayer:quickLiveNetworkQuality:)]) {
            [self.liveDelegate plvLivePlayer:self quickLiveNetworkQuality:quality];
        }
    })
}

#pragma mark 回调
- (void)reportAndCallbackForLoadMainPlayerErrorCode:(PLVFPlayErrorCode)ErrorCode{
    NSError * failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPlay code:ErrorCode];
    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:nil];
    [self callbackForLoadMainPlayerFailureError:failError];
}

- (void)callbackForLoadMainPlayerFailureError:(NSError * _Nullable)error{
    if (self.lastErrorCode == error.code && self.loadState != PLVLivePlayerLoadState_NotLoading) {
        return;
    }
    self.lastErrorCode = error.code;
    plv_dispatch_main_async_safe(^{
        if ([self.liveDelegate respondsToSelector:@selector(plvLivePlayer:loadMainPlayerFailureWithError:)]) {
            [self.liveDelegate plvLivePlayer:self loadMainPlayerFailureWithError:error];
        }
    })
}

- (void)callbackForChannelInfoDidUpdated{
    plv_dispatch_main_async_safe(^{
        if ([self.liveDelegate respondsToSelector:@selector(plvLivePlayer:channelInfoDidUpdated:)]) {
            [self.liveDelegate plvLivePlayer:self channelInfoDidUpdated:self.channelInfo];
        }
    })
}

- (void)callbackForShowWarmUpImage:(BOOL)show imageURLString:(NSString *)warmUpImageURLString{
    plv_dispatch_main_async_safe(^{
        if ([self.liveDelegate respondsToSelector:@selector(plvLivePlayer:showWarmUpImage:warmUpImageURLString:)]) {
            [self.liveDelegate plvLivePlayer:self showWarmUpImage:show warmUpImageURLString:warmUpImageURLString];
        }
    })
}

- (void)callbackForStreamStateUpdate:(BOOL)streamStateDidChanged{
    plv_dispatch_main_async_safe(^{
        if ([self.liveDelegate respondsToSelector:@selector(plvLivePlayer:streamStateUpdate:streamStateDidChanged:)]) {
            [self.liveDelegate plvLivePlayer:self streamStateUpdate:self.currentStreamState streamStateDidChanged:streamStateDidChanged];
        }
    })
}

- (BOOL)callbackForGetInLinkMic{
    BOOL inLinkMic = NO;
    if ([self.liveDelegate respondsToSelector:@selector(plvLivePlayerGetInLinkMic:)]) {
        inLinkMic = [self.liveDelegate plvLivePlayerGetInLinkMic:self];
    }
    return inLinkMic;
}

- (BOOL)callbackForGetPausedWatchNoDelay {
    BOOL pausedWatchNoDelay = NO;
    if ((self.channelWatchNoDelay || self.channelWatchPublicStream) &&
        [self.liveDelegate respondsToSelector:@selector(plvLivePlayerGetPausedWatchNoDelay:)]) {
        pausedWatchNoDelay = [self.liveDelegate plvLivePlayerGetPausedWatchNoDelay:self];
    }
    return pausedWatchNoDelay;
}

- (void)callbackForPlayOptionsUpdate{
    plv_dispatch_main_async_safe(^{
        if ([self.liveDelegate respondsToSelector:@selector(plvLivePlayer:codeRateOptions:currentCodeRate:lineNum:currentLineIndex:)]) {
            [self.liveDelegate plvLivePlayer:self codeRateOptions:self.channelInfo.definitionNamesOptions currentCodeRate:self.channelInfo.currentDefinition lineNum:self.channelInfo.lineNum currentLineIndex:self.channelInfo.currentLineIndex];
        }
    })
}

#pragma mark Getter
- (BOOL)externalInLinkMic{
    return [self callbackForGetInLinkMic];
}

- (BOOL)externalPausedWatchNoDelay {
    return [self callbackForGetPausedWatchNoDelay];
}

#pragma mark - [ Father Private Methods ]
- (void)callSubClassMoviePlayerLoadStateDidChange:(PLVPlayerMainSubType)mainSubType {
    if (self.mainPlayerLoadState & IJKMPMovieLoadStateStalled) {
        [self showPlayerLoadTimeoutOnDelay];
        
        if (self.loadingLogSent && !self.recordingBufferTime) {
            self.bufferStartDate = [NSDate date];
            self.recordingBufferTime = YES;
            [self startReportStalling];
            PLV_LOG_DEBUG(PLVConsoleLogModuleTypePlayer, @"recording buffer");
        }
    } else {
        [self cancelShowPlayerLoadTimeoutOnDelay];
        
        if (self.mainPlayerLoadState & IJKMPMovieLoadStatePlaythroughOK) {
            [self reportFirstLoading];
            [self stopReportStalling];
        }
    }
}

- (void)callSubClassMoviePlayerPlaybackStateDidChange:(PLVPlayerMainSubType)mainSubType {
    if (mainSubType == PLVPlayerMainSubType_Sub &&
        [PLVLivePictureInPictureManager sharedInstance].pictureInPictureMode == PLVLivePictureInPictureMode_AVPlayer) {
        return;
    }
    
    if (self.audioMode){
        // 音频模式解绑，音频模式不支持后台启动小窗
        [[PLVLivePictureInPictureManager sharedInstance] cleanPictureInPicturePlayer];
    } else if (self.playing && self.canAutoStartPictureInPicture && !self.audioMode){
        // 画中画退到后台自动启动，入口
        [PLVLivePictureInPictureManager sharedInstance].canAutoStartPictureInPicture = self.canAutoStartPictureInPicture;
        [PLVLivePictureInPictureManager sharedInstance].delegate = self;
        [[PLVLivePictureInPictureManager sharedInstance] bindPictureInPictureForBackgroundMode:self];
    }
    
    // 存在多个播放器的场景，需要同步播放状态
    if (!self.audioMode &&
        [PLVLivePictureInPictureManager sharedInstance].livePlayer == self){
        if (self.mainPlayerPlaybackState == IJKMPMoviePlaybackStatePlaying ||
            self.mainPlayerPlaybackState == IJKMPMoviePlaybackStateSeekingForward ||
            self.mainPlayerPlaybackState == IJKMPMoviePlaybackStateSeekingBackward){
            [[PLVLivePictureInPictureManager sharedInstance] syncPictureInPicturePlayControlStatus:YES];
        } else {
            [[PLVLivePictureInPictureManager sharedInstance] syncPictureInPicturePlayControlStatus:NO];
        }
    }
    
    if ([PLVLivePictureInPictureManager sharedInstance].pictureInPictureActive) {
        [self plvPictureInPicturePlayerPlayingStateDidChange:self.playing];
    }
}

- (void)callSubClassPlaybackDidFinish:(PLVPlayerMainSubType)mainSubType withReason:(IJKMPMovieFinishReason)finishReson {
    if (mainSubType == PLVPlayerMainSubType_Main) {
        [self clearMainPlayer];
        [self cancelShowPlayerLoadTimeoutOnDelay];
        
        if (finishReson == IJKMPMovieFinishReasonPlaybackError) {
            if (self.channelInfo.lines.count > 0) {
                NSInteger nextTargetLineIndex = self.channelInfo.currentLineIndex + 1;
                self.targetLineIndex = (nextTargetLineIndex % self.channelInfo.lines.count); // TODO 是否需要添加次数限制
            }
            [self reportPlaybackFailureAnalysis];
        }
    }else if (mainSubType == PLVPlayerMainSubType_Sub){
        self.playingWarmUpVideo = NO;
    }
}

- (void)callSubClassMoviePlayerWillDestroy:(PLVPlayerMainSubType)mainSubType{
    if (mainSubType == PLVPlayerMainSubType_Main) {
        self.contentURL = nil;
    }else if (mainSubType == PLVPlayerMainSubType_Sub){
        self.playingWarmUpVideo = NO;
    }
}

- (void)callSubClassMoviePlayerFirstVideoFrameRendered:(PLVPlayerMainSubType)mainSubType time:(int)time {
    if (mainSubType == PLVPlayerMainSubType_Main && !self.time_player && !self.loadingLogSent) {
        self.time_player = time;
        [self reportFirstLoading];
    }
}


#pragma mark - [ Event ]
#pragma mark TimerEvent
- (void)liveStatusTimerEvent:(NSTimer *)timer{
    [self requestLiveStreamStateForStreamHandle];
}

- (void)timeCountTimerEvent:(NSTimer *)timer{
    [self reportTimeCount];
}

- (void)quickLiveTimerEvent:(NSTimer *)timer{
    [self quickLiveNetworkQualityPolling];
}

#pragma mark Notification
- (void)livePlayerReachabilityChange:(NSNotification *)notification {
    [self reloadLivePlayer];

}

#pragma mark - [ Delegate ]
#pragma mark PLVLivePictureInPictureManagerDelegate
- (void)plvPictureInPictureWillStart {
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayerPictureInPictureWillStart:)]) {
        [self.pictureInPictureDelegate plvLivePlayerPictureInPictureWillStart:self];
    }
}

/// 画中画已经开始
- (void)plvPictureInPictureDidStart {
    [PLVLivePictureInPictureManager sharedInstance].livePlayer = self;
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayerPictureInPictureDidStart:)]) {
        [self.pictureInPictureDelegate plvLivePlayerPictureInPictureDidStart:self];
    }
}

/// 画中画开启失败
/// @param error 失败错误原因
- (void)plvPictureInPictureFailedToStartWithError:(NSError *)error {
    
    NSError * failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPlay code:PLVFPlayErrorCodePictureInPicture_OpenError];
    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:error.localizedDescription];
    
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayer:pictureInPictureFailedToStartWithError:)]) {
        [self.pictureInPictureDelegate plvLivePlayer:self pictureInPictureFailedToStartWithError:error];
    }
}

/// 画中画即将停止
- (void)plvPictureInPictureWillStop {
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayerPictureInPictureWillStop:)]) {
        [self.pictureInPictureDelegate plvLivePlayerPictureInPictureWillStop:self];
    }
}

/// 画中画已经停止
- (void)plvPictureInPictureDidStop {
    [PLVLivePictureInPictureManager sharedInstance].livePlayer = nil;
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayerPictureInPictureDidStop:)]) {
        [self.pictureInPictureDelegate plvLivePlayerPictureInPictureDidStop:self];
    }
}

- (void)plvPictureInPicturePlayerPlayingStateDidChange:(BOOL)playing {
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayer:pictureInPicturePlayerPlayingStateDidChange:)]) {
        [self.pictureInPictureDelegate plvLivePlayer:self pictureInPicturePlayerPlayingStateDidChange:playing];
    }
}

- (void)plvPictureInPicturePlayerPlayingStateDidChange:(BOOL)playing systemInterrupts:(BOOL)systemInterrupts {
    if (self.pictureInPictureDelegate &&
        [self.pictureInPictureDelegate respondsToSelector:@selector(plvLivePlayer:pictureInPicturePlayerPlayingStateDidChange:systemInterrupts:)]) {
        [self.pictureInPictureDelegate plvLivePlayer:self pictureInPicturePlayerPlayingStateDidChange:playing systemInterrupts:systemInterrupts];
    }
}

@end
