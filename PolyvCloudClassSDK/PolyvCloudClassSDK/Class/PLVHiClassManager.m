//
//  PLVHiClassManager.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/10/26.
//  Copyright © 2021 PLV. All rights reserved.
//

#import "PLVHiClassManager.h"

#import "PLVLiveVideoAPI.h"
#import "PLVLiveVClassAPI.h"
#import "PLVSocketManager.h"
#import "PLVConsoleLogger.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

/// 拖堂最长时间为4个小时，超过4小时会强行下课
static const NSTimeInterval kMaxDelayDuration = 4 * 60 * 60;

@interface PLVHiClassManager ()<
PLVSocketManagerProtocol
>

#pragma mark 数据

/// 课节相关
@property (nonatomic, copy) NSString *courseCode;
@property (nonatomic, copy) NSString *lessonId;
@property (nonatomic, assign) PLVHiClassStatus status;
@property (nonatomic, assign) NSInteger duration;
@property (nonatomic, assign) NSInteger classTime;
@property (nonatomic, assign) NSInteger lessonStartTime;
@property (nonatomic, assign) NSInteger lessonEndTime;

/// 分组相关
@property (nonatomic, assign) PLVHiClassGroupState groupState;
@property (nonatomic, copy) NSString *groupId;
@property (nonatomic, copy) NSString *groupName;
@property (nonatomic, copy) NSString *groupLeaderId;
@property (nonatomic, copy) NSString *groupLeaderName;
@property (nonatomic, assign) BOOL teacherInGroup;

#pragma mark 外部数据封装

@property (nonatomic, copy) NSString *channelId; // 频道号
@property (nonatomic, copy) NSString *userId; // 登录用户ID
@property (nonatomic, copy) NSString *userName; // 登录用户昵称
@property (nonatomic, copy) NSString *userType; // 登录用户类型字符串
@property (nonatomic, assign) BOOL isTeacher; // 登录用户类型字符串是否是"teacher",是为YES，否则为NO

#pragma mark 功能对象

@property (nonatomic, strong) NSTimer *durationTimer; // 上课时长计时器
@property (nonatomic, strong) NSTimer *inLiveTimer; // 上课状态监听计时器
@property (nonatomic, strong) NSTimer *reloginTimer; // 非上课时，讲师身份每隔10秒请求验证讲师是否在别处登录

@end

@implementation PLVHiClassManager {
    /// PLVSocketManager回调的执行队列
    dispatch_queue_t socketDelegateQueue;
}

#pragma mark - [ Life Cycle ]

- (instancetype)init {
    self = [super init];
    if (self) {
    }
    return self;
}

#pragma mark - [ Public Method ]

+ (instancetype)sharedManager {
    static dispatch_once_t onceToken;
    static PLVHiClassManager *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[self alloc] init];
    });
    return manager;
}

- (void)setupWithLessonDetail:(NSDictionary *)detailDict courseCode:(NSString * _Nullable)courseCode {
    if (![PLVFdUtil checkDictionaryUseable:detailDict]) {
        return;
    }
    
    self.courseCode = courseCode;
    self.lessonId = PLV_SafeStringForDictKey(detailDict, @"lessonId");
    self.status = PLV_SafeIntegerForDictKey(detailDict, @"status");
    self.classTime = PLV_SafeIntegerForDictKey(detailDict, @"classTime");
    self.lessonStartTime = PLV_SafeIntegerForDictKey(detailDict, @"lessonStartTime");
    self.lessonEndTime = PLV_SafeIntegerForDictKey(detailDict, @"lessonEndTime");
}

- (void)clear {
    [[PLVSocketManager sharedManager] removeDelegate:self];
    
    [self stopInLiveTimer];
    [self stopReloginTimer];
    [self stopDurationTimer];
    
    self.courseCode = nil;
    self.lessonId = nil;
    self.status = PLVHiClassStatusNotInClass;
    self.classTime = 0;
    self.lessonStartTime = 0;
    self.lessonEndTime = 0;
    
    self.teacherInGroup = NO;
    self.groupState = PLVHiClassGroupStateNotInGroup;
    self.groupId = nil;
    self.groupName = nil;
    self.groupLeaderId = nil;
    self.groupLeaderName = nil;
    
    [self leaveGroup];
}

- (void)enterClassroom {
    // 添加 socket 事件监听
    socketDelegateQueue = dispatch_get_global_queue(0, DISPATCH_QUEUE_PRIORITY_DEFAULT);
    [[PLVSocketManager sharedManager] addDelegate:self delegateQueue:socketDelegateQueue];
    
    // 无论是什么上课状态，都启动 inLiveTimer 计时器
    [self startInLiveTimer];
    
    if (self.status == PLVHiClassStatusInClass) { // 如果是上课中，直接启动 durationTimer 计时器
        [self startDurationTimer];
    } else { // 非上课中，启动 reloginTimer 计时器
        [self startReloginTimer];
    }
}

#pragma mark 讲师专用方法

- (void)startClass {
    [self changeLessonStatus:YES];
}

- (void)finishClass {
    [self changeLessonStatus:NO];
}

#pragma mark 学生专用方法

- (void)requestWatcherNextLessonInfoWithCompletion:(void(^)(NSDictionary *lessonDict, NSInteger duration))completion {
    if (self.isTeacher) {
        if (completion) {
            completion(nil, 0);
        }
        return;
    }
    
    __weak typeof(self) weakSelf = self;
    void(^lessonFinishInfoCompletionBlock)(NSInteger duration) = ^(NSInteger duration) {
        [PLVLiveVClassAPI watcherLessonListWithCourseCode:self.courseCode lessonId:self.lessonId success:^(NSArray * _Nonnull responseArray) {
            NSDictionary *lessonDict = nil;
            for (NSDictionary *dict in responseArray) {
                if ([PLVFdUtil checkDictionaryUseable:dict]) {
                    NSString *nextLessonId = PLV_SafeStringForDictKey(dict, @"lessonId");
                    NSInteger status = PLV_SafeIntegerForDictKey(dict, @"status");
                    if (![weakSelf.lessonId isEqualToString:nextLessonId] && status == 0) {
                        lessonDict = dict;
                        break;
                    }
                }
            }
            
            if (completion) {
                completion(lessonDict, duration);
            }
        } failure:^(NSError * _Nonnull error) {
            if (completion) {
                completion(nil, 0);
            }
        }];
    };
    
    [PLVLiveVClassAPI lessonFinishInfoWithLessonId:self.lessonId isTeach:NO success:^(NSDictionary * _Nonnull responseDict) {
        NSInteger endTime = PLV_SafeIntegerForDictKey(responseDict, @"endTime");
        NSInteger startTime = PLV_SafeIntegerForDictKey(responseDict, @"startTime");
        NSInteger duration = MAX(0, (endTime - startTime) / 1000);
        lessonFinishInfoCompletionBlock(duration);
    } failure:^(NSError * _Nonnull error) {
        lessonFinishInfoCompletionBlock(0);
    }];
    
}

- (BOOL)requestHelp {
    return [self emitGroupRequestHelpEvent];
}

- (BOOL)cancelRequestHelp {
    return [self emitCancelHelpEvent];
}

#pragma mark Getter & Setter

- (BOOL)currentUserIsGroupLeader {
    return self.groupState == PLVHiClassGroupStateInGroup && self.groupLeaderId && [self.groupLeaderId isEqualToString:self.userId];
}

#pragma mark - [ Private Method ]

- (void)updateDuration {
    if (self.classTime == 0) {
        self.duration = 0;
    } else {
        self.duration = MAX(0, ([PLVFdUtil curTimeInterval] - self.classTime) / 1000.0);
    }
}

- (void)startDurationTimer {
    if (self.durationTimer) {
        return;
    }
    
    [self updateDuration];
    self.durationTimer = [NSTimer timerWithTimeInterval:1.0
                                                 target:[PLVFWeakProxy proxyWithTarget:self]
                                               selector:@selector(durationTimerEvent)
                                               userInfo:nil
                                                repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:self.durationTimer forMode:NSRunLoopCommonModes];
}

- (void)stopDurationTimer {
    if (_durationTimer) {
        [_durationTimer invalidate];
        _durationTimer = nil;
    }
}

- (void)startInLiveTimer {
    if (self.inLiveTimer) {
        return;
    }
    
    self.inLiveTimer = [NSTimer timerWithTimeInterval:10.0
                                               target:[PLVFWeakProxy proxyWithTarget:self]
                                             selector:@selector(inLiveTimerEvent)
                                             userInfo:nil
                                              repeats:YES];
    [[NSRunLoop mainRunLoop] addTimer:self.inLiveTimer forMode:NSRunLoopCommonModes];
}

- (void)stopInLiveTimer {
    if (_inLiveTimer) {
        [_inLiveTimer invalidate];
        _inLiveTimer = nil;
    }
}

- (void)startReloginTimer {
    if (!self.isTeacher ||
        self.status == PLVHiClassStatusInClass) {
        [self stopReloginTimer];
        return;
    }
    
    self.reloginTimer = [NSTimer timerWithTimeInterval:10.0
                                                target:[PLVFWeakProxy proxyWithTarget:self]
                                              selector:@selector(reloginTimerEvent)
                                              userInfo:nil
                                               repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:self.reloginTimer forMode:NSRunLoopCommonModes];
}

- (void)stopReloginTimer {
    if (_reloginTimer) {
        [_reloginTimer invalidate];
        _reloginTimer = nil;
    }
}

/// 上课状态变为【上课中】
- (void)haveStaredClass {
    self.status = PLVHiClassStatusInClass;
    [self startDurationTimer];
    [self stopReloginTimer];
    [self notifyClassStartSuccess];
}

/// 上课状态变为【已下课】
- (void)haveFinishedClass {
    self.classTime = 0;
    self.status = PLVHiClassStatusFinishClass;
    [self stopDurationTimer];
    [self startReloginTimer];
    [self notifyClassFinishSuccess];
}

- (void)joinGroup:(NSString *)groupId {
    self.groupState = PLVHiClassGroupStateJoiningGroup;
    self.groupId = groupId;
    self.groupName = nil;
    self.groupLeaderId = nil;
    self.groupLeaderName = nil;
    self.teacherInGroup = NO;
    
    [self notifyPrepareJoinGroup];
    
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(emitJoinDiscussEvent) object:nil];
    
    // 3秒后对开始分组的消息进行答复
    __weak typeof(self) weakSelf = self;
    plv_dispatch_main_async_safe(^{
        [weakSelf performSelector:@selector(emitJoinDiscussEvent) withObject:nil afterDelay:3.0];
    })
}

- (void)changeGroup:(NSString *)groupId {
    self.groupState = PLVHiClassGroupStateJoiningGroup;
    self.groupId = groupId;
    self.groupName = nil;
    self.groupLeaderId = nil;
    self.groupLeaderName = nil;
    self.teacherInGroup = NO;
    
    [self notifyChangeGroup];
    
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(emitJoinDiscussEvent) object:nil];
    
    [self emitJoinDiscussEvent];
}

- (void)leaveGroup {
    self.groupState = PLVHiClassGroupStateNotInGroup;
    self.groupId = nil;
    self.groupName = nil;
    self.groupLeaderId = nil;
    self.groupLeaderName = nil;
    self.teacherInGroup = NO;
    
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(emitJoinDiscussEvent) object:nil];
    
    [self emitLeaveDiscussEvent];
}

- (void)updateGroupName:(NSString *)groupName {
    self.groupName = [PLVFdUtil checkStringUseable:groupName] ? groupName : @"";
}

- (void)updateGroupLeaderId:(NSString *)groupLeaderId groupLeaderName:(NSString *)groupLeaderName {
    NSString *originLeaderId = self.groupLeaderId;
    self.groupLeaderId = [PLVFdUtil checkStringUseable:groupLeaderId] ? groupLeaderId : @"";
    self.groupLeaderName = [PLVFdUtil checkStringUseable:groupLeaderName] ? groupLeaderName : @"";
    [self notifyGroupLeaderUpdateWithOriginalLeaderId:originLeaderId currentLeaderId:self.groupLeaderId];
}

#pragma mark Getter & Setter

- (NSString *)channelId {
    return [PLVSocketManager sharedManager].roomId;
}

- (NSString *)userId {
    return [PLVSocketManager sharedManager].viewerId;
}

- (NSString *)userName {
    return [PLVSocketManager sharedManager].viewerName;
}

- (NSString *)userType {
    return [PLVSocketManager sharedManager].userTypeString;
}

- (BOOL)isTeacher {
    BOOL isTeacher = NO;
    if ([PLVFdUtil checkStringUseable:self.userType]) {
        isTeacher = [self.userType isEqualToString:@"teacher"];
    }
    return isTeacher;
}

#pragma mark Http Request

- (void)changeLessonStatus:(BOOL)start {
    if (!self.isTeacher) {
        return;
    }
    
    if (![PLVSocketManager sharedManager].login) { // socket未登录
        if (start) {
            [self notifyClassStartFailureWithErrorMessage:PLVFDLocalizableString(@"socket尚未登录")];
        } else {
            [self notifyClassFinishFailureWithErrorMessage:PLVFDLocalizableString(@"socket尚未登录")];
        }
        return;
    }
    
    NSInteger status = start ? 1 : 2;
    __weak typeof(self) weakSelf = self;
    [PLVLiveVClassAPI teacherChangeLessonStatusWithLessonId:self.lessonId status:status success:^(id responseObject) {
        //上下课接口调用成功后需要发送Socket消息
        if (start) {
            [weakSelf emitOnSliceStartMessage];
            [weakSelf haveStaredClass];
        } else {
            [weakSelf emitFinishClassMessage];
            [weakSelf haveFinishedClass];
        }
    } failure:^(NSError *error) {
        if (start) {
            [weakSelf notifyClassStartFailureWithErrorMessage:error.localizedDescription];
        } else {
            [weakSelf notifyClassFinishFailureWithErrorMessage:error.localizedDescription];
        }
    }];
}

- (void)requestGroupList {
    __weak typeof(self) weakSelf = self;
    [PLVLiveVClassAPI watcherGetLiveAPIChannelTokenWithLessonId:self.lessonId courseCode:self.courseCode success:^(NSDictionary * _Nonnull responseDict) {
        NSString *token = PLV_SafeStringForDictKey(responseDict, @"token");
        NSString *appId = PLV_SafeStringForDictKey(responseDict, @"appId");
        [PLVLiveVideoAPI requestGroupListWithChannelId:weakSelf.channelId appId:appId channelToken:token success:^(NSArray<NSDictionary *> * _Nonnull groupArray) {
            for (NSDictionary *dict in groupArray) {
                NSString *groupId = PLV_SafeStringForDictKey(dict, @"groupId");
                NSString *groupName = PLV_SafeStringForDictKey(dict, @"groupName");
                if (groupId &&
                    weakSelf.groupId &&
                    [groupId isEqualToString:weakSelf.groupId]) {
                    [weakSelf updateGroupName:groupName];
                    break;
                }
            }
            [weakSelf requestGroupOnlineUserList];
        } failure:^(NSError * _Nonnull error) {
            [weakSelf requestGroupOnlineUserList];
        }];
    } failure:^(NSError * _Nonnull error) {
        [weakSelf requestGroupOnlineUserList];
    }];
    
}

- (void)requestGroupOnlineUserList {
    __weak typeof(self) weakSelf = self;
    [PLVLiveVideoAPI requestChatRoomListUsersWithRoomId:self.groupId
                                                   page:0
                                                 length:500
                                              sessionId:self.lessonId
                                               streamer:YES
                                                success:^(NSDictionary *data) {
        NSArray *userArray = data[@"userlist"];
        if ([PLVFdUtil checkArrayUseable:userArray]) {
            BOOL findTeacher = NO;
            BOOL findGroupLeader = NO;
            for (NSDictionary *userDict in userArray) {
                if ([PLVFdUtil checkDictionaryUseable:userDict]) {
                    NSString *userType = PLV_SafeStringForDictKey(userDict, @"userType");
                    if (userType && [userType isEqualToString:@"teacher"]) {
                        weakSelf.teacherInGroup = YES;
                        findTeacher = YES;
                    }
                    NSDictionary *classStatus = PLV_SafeDictionaryForDictKey(userDict, @"classStatus");
                    if ([PLVFdUtil checkDictionaryUseable:classStatus] ) {
                        NSInteger isGroupLeader = [classStatus[@"groupLeader"] integerValue];
                        if (isGroupLeader == 1) {
                            NSString *groupLeaderId = PLV_SafeStringForDictKey(userDict, @"userId");
                            NSString *groupLeaderName = PLV_SafeStringForDictKey(userDict, @"nick");
                            [weakSelf updateGroupLeaderId:groupLeaderId groupLeaderName:groupLeaderName];
                            findGroupLeader = YES;
                        }
                    }
                    if (findTeacher && findGroupLeader) {
                        break;
                    }
                }
            }
        }
    } failure:nil];
}

- (void)requestLessonDetail {
    __weak typeof(self) weakSelf = self;
    void(^successBlock)(NSDictionary * _Nonnull responseDict) = ^(NSDictionary * _Nonnull responseDict) {
        weakSelf.classTime = PLV_SafeIntegerForDictKey(responseDict, @"classTime");
        [weakSelf updateDuration];
    };
    
    if (self.isTeacher) {
        [PLVLiveVClassAPI teacherLessonDetailWithLessonId:self.lessonId success:successBlock failure:nil];
    } else {
        [PLVLiveVClassAPI watcherLessonDetailWithCourseCode:self.courseCode lessonId:self.lessonId success:successBlock failure:nil];
    }
}

#pragma mark Socket

- (void)emitInLiveEvent {
    if (![PLVFdUtil checkStringUseable:self.lessonId]) { // 课节ID不能为空
        return;
    }
    NSDictionary *paramDict = @{ @"sessionId" : self.lessonId };
    __weak typeof(self) weakSelf = self;
    [[PLVSocketManager sharedManager] emitEvent:@"inLive" content:paramDict timeout:5.0 callback:^(NSArray *ackArray) {
        NSString *inLive = nil;
        if ([PLVFdUtil checkArrayUseable:ackArray]) {
            NSInteger code = PLV_SafeIntegerForDictKey(ackArray.firstObject, @"code");
            if (code == 200) {
                NSDictionary *data = PLV_SafeDictionaryForDictKey(ackArray.firstObject, @"data");
                inLive = PLV_SafeStringForDictKey(data, @"inLive");
            }
        }
        if (inLive) {
            if (weakSelf.status != PLVHiClassStatusInClass &&
                [inLive isEqualToString:@"live"]) {
                [weakSelf requestLessonDetail];
                [weakSelf haveStaredClass];
            } else if (weakSelf.status == PLVHiClassStatusInClass &&
                       [inLive isEqualToString:@"end"]) {
                [weakSelf haveFinishedClass];
            }
        }
    }];
}

- (void)emitOnSliceStartMessage {
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    paramDict[@"EVENT"] = @"onSliceStart";
    paramDict[@"courseType"] = @"smallClass";
    paramDict[@"docType"] = @(1);
    paramDict[@"emitMode"] = @(0);
    paramDict[@"timeStamp"] = @(0);
    
    paramDict[@"classInterval"] = @(self.lessonEndTime - [PLVFdUtil curTimeInterval]);
    paramDict[@"userId"] = self.userId ?: @"";
    paramDict[@"roomId"] = self.channelId ?: @"";
    paramDict[@"sessionId"] = self.lessonId ?: @"";
    [[PLVSocketManager sharedManager] emitMessage:paramDict timeout:5.0 callback:^(NSArray * _Nonnull ackArray) {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypeVerbose, @"PLVMultiRoleLinkMicPresenter - Socket 'onSliceStart' ackArray: %@", ackArray);
    }];
}

- (void)emitFinishClassMessage {
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    paramDict[@"EVENT"] = @"finishClass";
    paramDict[@"clearPermission"] = @(1);
    [[PLVSocketManager sharedManager] emitMessage:paramDict timeout:5.0 callback:^(NSArray * _Nonnull ackArray) {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypeVerbose, @"PLVMultiRoleLinkMicPresenter - Socket 'finishClass' ackArray: %@", ackArray);
    }];
}

- (void)emitJoinDiscussEvent {
    NSDictionary *paramDict = @{ @"EVENT" : @"joinDiscuss" };
    __weak typeof(self) weakSelf = self;
    [[PLVSocketManager sharedManager] emitEvent:@"seminar" content:paramDict timeout:5.0 callback:^(NSArray * _Nonnull ackArray) {
        BOOL success = NO;
        id jsonObject = nil;
        if ([PLVFdUtil checkArrayUseable:ackArray]) {
            if ([ackArray.firstObject isKindOfClass:[NSString class]]) {
                NSData *jsonData = [ackArray.firstObject dataUsingEncoding:NSUTF8StringEncoding];
                if (jsonData && jsonData.length > 0) {
                    jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
                }
            } else if ([ackArray.firstObject isKindOfClass:[NSDictionary class]]) {
                jsonObject = ackArray.firstObject;
            }
            
            NSString *status = PLV_SafeStringForDictKey(jsonObject, @"status");
            if ([status isEqualToString:@"success"]) {
                if (![PLVFdUtil checkStringUseable:weakSelf.groupId]) { // 防止之前没从joinDiscuss的消息体中获取到groupId
                    NSDictionary *dataDict = PLV_SafeDictionaryForDictKey(jsonObject, @"data");
                    NSString *groupId = PLV_SafeStringForDictKey(dataDict, @"id");
                    weakSelf.groupId = groupId;
                }
                [weakSelf emitJoinSuccessEvent];
                [weakSelf requestGroupList];
                success = YES;
            }
        }
        
        if (success) {
            NSDictionary *data = PLV_SafeDictionaryForDictKey(jsonObject, @"data");
            weakSelf.groupState = PLVHiClassGroupStateInGroup;
            [weakSelf notifyJoinGroupSuccessWithData:data];
        } else {
            weakSelf.groupState = PLVHiClassGroupStateNotInGroup;
            [weakSelf notifyJoinGroupFailure];
        }
    }];
}

- (void)emitJoinSuccessEvent {
    NSDictionary *paramDict = @{ @"roomId" : (self.groupId ?: @"") };
    [[PLVSocketManager sharedManager] emitEvent:@"joinSuccess" content:paramDict];
}

- (void)emitLeaveDiscussEvent {
    NSDictionary *paramDict = @{ @"EVENT" : @"leaveDiscuss" };
    __weak typeof(self) weakSelf = self;
    [[PLVSocketManager sharedManager] emitEvent:@"seminar" content:paramDict timeout:5.0 callback:^(NSArray * _Nonnull ackArray) {
        BOOL success = NO;
        id jsonObject = nil;
        if ([PLVFdUtil checkArrayUseable:ackArray]) {
            if ([ackArray.firstObject isKindOfClass:[NSString class]]) {
                NSData *jsonData = [ackArray.firstObject dataUsingEncoding:NSUTF8StringEncoding];
                if (jsonData && jsonData.length > 0) {
                    jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
                }
            } else if ([ackArray.firstObject isKindOfClass:[NSDictionary class]]) {
                jsonObject = ackArray.firstObject;
            }
            
            NSString *status = PLV_SafeStringForDictKey(jsonObject, @"status");
            if ([status isEqualToString:@"success"]) {
                success = YES;
            }
        }
        
        NSDictionary *data = success ? PLV_SafeDictionaryForDictKey(jsonObject, @"data") : nil;
        [weakSelf notifyLeaveGroupWithData:data];
    }];
}

- (BOOL)emitGroupRequestHelpEvent {
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    paramDict[@"EVENT"] = @"groupRequestHelp";
    paramDict[@"groupId"] = self.groupId ?: @"";
    paramDict[@"userId"] = self.userId ?: @"";
    BOOL success = [[PLVSocketManager sharedManager] emitEvent:@"seminar" content:paramDict];
    return success;
}

- (BOOL)emitCancelHelpEvent {
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    paramDict[@"EVENT"] = @"cancelHelp";
    paramDict[@"groupId"] = self.groupId ?: @"";
    BOOL success = [[PLVSocketManager sharedManager] emitEvent:@"seminar" content:paramDict];
    return success;
}

#pragma mark Delegate 方法触发

- (void)notifyClassStartSuccess {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerClassStartSuccess:)]) {
            [self.delegate hiClassManagerClassStartSuccess:self];
        }
    })
}

- (void)notifyClassFinishSuccess {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerClassFinishSuccess:)]) {
            [self.delegate hiClassManagerClassFinishSuccess:self];
        }
    })
}

- (void)notifyClassStartFailureWithErrorMessage:(NSString *)errorMessage {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerClassStartFailure:errorMessage:)]) {
            [self.delegate hiClassManagerClassStartFailure:self errorMessage:errorMessage];
        }
    })
}

- (void)notifyClassFinishFailureWithErrorMessage:(NSString *)errorMessage {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerClassFinishFailure:errorMessage:)]) {
            [self.delegate hiClassManagerClassFinishFailure:self errorMessage:errorMessage];
        }
    })
}

- (void)notifyClassDurationChanged {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerClassDurationChanged:duration:)]) {
            [self.delegate hiClassManagerClassDurationChanged:self duration:self.duration];
        }
    })
}

- (void)notifyClassWillForceFinishInTenMins {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerClassWillForceFinishInTenMins:)]) {
            [self.delegate hiClassManagerClassWillForceFinishInTenMins:self];
        }
    })
}

- (void)notifyClassDidForceFinish {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerClassDidForceFinish:)]) {
            [self.delegate hiClassManagerClassDidForceFinish:self];
        }
    })
}

- (void)notifyTeacherReloginWithErrorMsg:(NSString *)errorMsg {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerTeacherRelogin:errorMessage:)]) {
            [self.delegate hiClassManagerTeacherRelogin:self errorMessage:errorMsg];
        }
    })
}

- (void)notifyPrepareJoinGroup {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerDidPrepareJoinGroup:)]) {
            [self.delegate hiClassManagerDidPrepareJoinGroup:self];
        }
    })
}

- (void)notifyJoinGroupSuccessWithData:(NSDictionary *)data {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerDidJoinGroupSuccess:ackData:)]) {
            [self.delegate hiClassManagerDidJoinGroupSuccess:self ackData:data];
        }
    })
}

- (void)notifyJoinGroupFailure {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerDidJoinGroupFailure:)]) {
            [self.delegate hiClassManagerDidJoinGroupFailure:self];
        }
    })
}

- (void)notifyGroupLeaderUpdateWithOriginalLeaderId:(NSString *)originalLeaderId currentLeaderId:(NSString *)currentLeaderId {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerDidGroupLeaderUpdate:originalLeaderId:currentLeaderId:)]) {
            [self.delegate hiClassManagerDidGroupLeaderUpdate:self originalLeaderId:originalLeaderId currentLeaderId:currentLeaderId];
        }
    })
}

- (void)notifyChangeGroup {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerDidChangeGroup:)]) {
            [self.delegate hiClassManagerDidChangeGroup:self];
        }
    })
}

- (void)notifyLeaveGroupWithData:(NSDictionary *)data {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerDidLeaveGroup:ackData:)]) {
            [self.delegate hiClassManagerDidLeaveGroup:self ackData:data];
        }
    })
}

- (void)notifyTeacherJoinGroup {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerDidTeacherJoinGroup:)]) {
            [self.delegate hiClassManagerDidTeacherJoinGroup:self];
        }
    })
}

- (void)notifyTeacherLeaveGroup {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerDidTeacherLeaveGroup:)]) {
            [self.delegate hiClassManagerDidTeacherLeaveGroup:self];
        }
    })
}

- (void)notifyCancelRequestHelp {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManagerDidCancelRequestHelp:)]) {
            [self.delegate hiClassManagerDidCancelRequestHelp:self];
        }
    })
}

- (void)notifyReceiveHostBroadcast:(NSString *)content {
    plv_dispatch_main_async_safe(^{
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(hiClassManager:didReceiveHostBroadcast:)]) {
            [self.delegate hiClassManager:self didReceiveHostBroadcast:content];
        }
    })
}

#pragma mark - [ Event ]

#pragma mark Timer

- (void)durationTimerEvent {
    if (self.status != PLVHiClassStatusInClass) {
        [self stopDurationTimer];
        return;
    }
    
    self.duration ++;
    [self notifyClassDurationChanged];
    
    if (self.isTeacher) { // 老师端拖堂强制下课逻辑
        // 拖堂时间
        NSInteger delayTime = ([PLVFdUtil curTimeInterval] - self.lessonEndTime) / 1000.0;
        BOOL hasDelayFinish = delayTime > 0;
        if (hasDelayFinish &&
            self.status == PLVHiClassStatusInClass) { // 已拖堂
            if (delayTime == (kMaxDelayDuration - 10 * 60)) { // 还有10分钟就拖堂4小时，触发回调通知demo层
                [self notifyClassWillForceFinishInTenMins];
            } else if (delayTime >= kMaxDelayDuration) { // 超过4小时强行下课
                [self finishClass];
                [self notifyClassDidForceFinish];
            }
        }
    }
}

- (void)inLiveTimerEvent {
    [self emitInLiveEvent];
}

- (void)reloginTimerEvent {
    if (!self.isTeacher ||
        self.status == PLVHiClassStatusInClass) {
        [self stopReloginTimer];
        return;
    }
    
    __weak typeof(self) weakSelf = self;
    [PLVLiveVClassAPI teacherReloginVerifyWithLessonId:self.lessonId
                                               relogin:^(NSString * _Nonnull errorDesc) {
        [weakSelf notifyTeacherReloginWithErrorMsg:errorDesc];
    } success:nil failure:nil];
}

#pragma mark - [ Delegate ]

#pragma mark PLVSocketManagerProtocol

- (void)socketMananger_didReceiveMessage:(NSString *)subEvent
                                    json:(NSString *)jsonString
                              jsonObject:(id)object {
    NSDictionary *jsonDict = (NSDictionary *)object;
    if (![jsonDict isKindOfClass:[NSDictionary class]]) {
        return;
    }
    
    //讲师或者学生收到上下课的监听
    if ([subEvent isEqualToString:@"onSliceStart"]) {
        [self requestLessonDetail];
        [self haveStaredClass];
    } else if ([subEvent isEqualToString:@"finishClass"]) {
        [self haveFinishedClass];
   }
}

- (void)socketMananger_didReceiveEvent:(NSString *)event
                              subEvent:(NSString *)subEvent
                                  json:(NSString *)jsonString
                            jsonObject:(id)object {
    NSDictionary *jsonDict = (NSDictionary *)object;
    if (![jsonDict isKindOfClass:[NSDictionary class]]) {
        return;
    }
    
    if (self.isTeacher) {
        return;
    }
    
    if ([event isEqualToString:@"seminar"]) {
        if ([subEvent isEqualToString:@"joinDiscuss"]) {
            NSString *groupId = PLV_SafeStringForDictKey(jsonDict, @"groupId");
            if (self.groupState == PLVHiClassGroupStateNotInGroup || !self.groupId) { // 若当前未处于分组讨论中，触发准备加入分组回调
                [self joinGroup:groupId];
            } else { // 否则判断是否切换分组
                if (groupId && ![self.groupId isEqualToString:groupId]) {
                    [self changeGroup:groupId];
                } else { // 已加入分组用户重新登录时会再次收到joinDiscuss消息
                    [self emitJoinSuccessEvent];
                }
            }
        } else if ([subEvent isEqualToString:@"leaveDiscuss"]) { // 退出分组讨论
            [self leaveGroup];
        } else if ([subEvent isEqualToString:@"setLeader"]) { // 设置组长
            if (self.groupState == PLVHiClassGroupStateInGroup) {
                NSString *groupLeaderId = PLV_SafeStringForDictKey(jsonDict, @"userId");
                NSString *groupLeaderName = PLV_SafeStringForDictKey(jsonDict, @"nick");
                [self updateGroupLeaderId:groupLeaderId groupLeaderName:groupLeaderName];
            }
        } else if ([subEvent isEqualToString:@"hostJoin"]) { // 讲师进入分组
            self.teacherInGroup = YES;
            [self notifyTeacherJoinGroup];
        } else if ([subEvent isEqualToString:@"hostLeave"]) { // 讲师离开分组
            self.teacherInGroup = NO;
            [self notifyTeacherLeaveGroup];
        } else if ([subEvent isEqualToString:@"cancelHelp"]) { // 取消【请求讲师帮助】
            [self notifyCancelRequestHelp];
        } else if ([subEvent isEqualToString:@"hostSendToAllGroup"]) { // 收到讲师广播通知
            NSString *content = PLV_SafeStringForDictKey(jsonDict, @"content");
            if (content) {
                [self notifyReceiveHostBroadcast:content];
            }
        }
    }
}

- (void)socketMananger_didConnectStatusChange:(PLVSocketConnectStatus)connectStatus {
    if (connectStatus == PLVSocketConnectStatusReconnect) {
        if (self.groupState == PLVHiClassGroupStateInGroup) { //聊天室断开后，需要自行离开分组；重连聊天室后会收到'joinDiscuss'事件表示可重新加入分组。
            [self leaveGroup];
        }
    }
}

@end
