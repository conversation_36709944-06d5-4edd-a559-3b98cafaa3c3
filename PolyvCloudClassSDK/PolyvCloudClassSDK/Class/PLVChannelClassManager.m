//
//  PLVChannelClassManager.m
//  PLVLiveScenesSDK
//
//  Created by Lincal on 2021/8/9.
//  Copyright © 2021 PLV. All rights reserved.
//

#import "PLVChannelClassManager.h"

#import "PLVSocketManager.h"
#import "PLVLivePrivateAPI.h"
#import "PLVConsoleLogger.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

/// [内部说明]
/// 1. PLVChannelClassManager 未来将负责
///    上下课Socket消息发送、‘课程是否Started’状态管理、课程对应SessionId管理
///
/// 2. 频道信息、频道菜单信息，均暂时不考虑归于此类进行管理
@interface PLVChannelClassManager ()<
PLVSocketManagerProtocol
>

#pragma mark 状态
@property (nonatomic, assign) PLVChannelLiveStreamState currentStreamState;

#pragma mark 数据
@property (nonatomic, copy) NSString * sessionId;
@property (nonatomic, copy) NSString * stream;
@property (nonatomic, copy) NSString * channelId;
@property (nonatomic, assign) NSTimeInterval pushtimeTimestamp; /// 获取到的 开始推流时间戳
@property (nonatomic, assign) NSTimeInterval pushDuration; /// 获取到的 已推流时长

#pragma mark 功能对象
@property (nonatomic, strong) NSTimer * pushTimer;
@property (nonatomic, strong) NSTimer * streamStateTimer;
@property (nonatomic, copy) void(^requestLiveStreamStateCompletion)(PLVChannelLiveStreamState streamState,BOOL streamStateDidChanged);
@property (nonatomic, copy) void(^requestLiveStreamStateFailure)(NSError * _Nonnull error);

@end

@implementation PLVChannelClassManager{
    /// PLVSocketManager回调的执行队列
    dispatch_queue_t socketDelegateQueue;
}

#pragma mark - [ Life Cycle ]
- (void)dealloc{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeVerbose, @"POLYVTEST - PLVChannelClassManager dealloc");
    [self stopLiveStreamStateTimer];
    [self stopPushTimer];
}

- (instancetype)initWithChannelId:(NSString *)channelId{
    if (self = [super init]) {
        self.channelId = channelId;
        [self setupData];
        
        /// 添加 socket 事件监听
        socketDelegateQueue = dispatch_get_global_queue(0, DISPATCH_QUEUE_PRIORITY_DEFAULT);
        [[PLVSocketManager sharedManager] addDelegate:self delegateQueue:socketDelegateQueue];
    }
    return self;
}


#pragma mark - [ Public Methods ]
- (void)startObserveStreamStateWithStream:(NSString *)stream completion:(void (^)(PLVChannelLiveStreamState, BOOL))completion failure:(void (^)(NSError * _Nonnull))failure{
    [self startLiveStreamStateTimer];
    self.stream = stream;
    self.requestLiveStreamStateCompletion = completion;
    self.requestLiveStreamStateFailure = failure;
}

- (void)stopObserveStreamState{
    self.currentStreamState = PLVChannelLiveStreamState_Unknown;
    [self stopLiveStreamStateTimer];
    self.requestLiveStreamStateCompletion = nil;
    self.requestLiveStreamStateFailure = nil;
}

#pragma mark Getter
- (NSTimeInterval)currentPushDuration{
    if (self.pushtimeTimestamp <= 0) { return 0; }
    NSTimeInterval currentPushDuration = ([[NSDate date] timeIntervalSince1970] * 1000 - self.pushtimeTimestamp) / 1000.0;
    return currentPushDuration;
}


#pragma mark - [ Private Methods ]
- (void)setupData{
    self.currentStreamState = PLVChannelLiveStreamState_Unknown;
}

#pragma mark Net Request
- (void)requestLiveStreamState{
    __weak typeof(self) weakSelf = self;
    [PLVLivePrivateAPI liveStreamStatus:self.stream channelId:self.channelId params:nil completion:^(PLVChannelLiveStreamState newestStreamState,NSString *sessionId) {
        BOOL streamStateDidChanged = (weakSelf.currentStreamState != newestStreamState);
        weakSelf.currentStreamState = newestStreamState;
        if (weakSelf.requestLiveStreamStateCompletion) { weakSelf.requestLiveStreamStateCompletion(newestStreamState,streamStateDidChanged); }
        
        if (streamStateDidChanged) {
            if (newestStreamState == PLVChannelLiveStreamState_Live) {
                [weakSelf startPushTimer];
            }else{
                [weakSelf stopPushTimer];
            }
        }
    } failure:^(NSError * error) {
        if (weakSelf.requestLiveStreamStateFailure) { weakSelf.requestLiveStreamStateFailure(error); }
    }];
}

#pragma mark Timer Manage
- (void)startLiveStreamStateTimer{
    if (_streamStateTimer) { [self stopLiveStreamStateTimer]; }
    _streamStateTimer = [NSTimer scheduledTimerWithTimeInterval:10.0 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(streamStateTimerEvent:) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:_streamStateTimer forMode:NSRunLoopCommonModes];
}

- (void)stopLiveStreamStateTimer{
    [_streamStateTimer invalidate];
    _streamStateTimer = nil;
}

- (void)startPushTimer{
    if (_pushTimer) { [self stopPushTimer]; }
    _pushTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(pushTimerEvent:) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:_pushTimer forMode:NSRunLoopCommonModes];
}

- (void)stopPushTimer{
    [_pushTimer invalidate];
    _pushTimer = nil;
}

#pragma mark Callback
- (void)callbackForCurrentPushDuration{
    plv_dispatch_main_async_safe(^{
        if ([self.delegate respondsToSelector:@selector(plvChannelClassManager:currentPushDuration:)]) {
            [self.delegate plvChannelClassManager:self currentPushDuration:self.currentPushDuration];
        }
    })
}


#pragma mark - [ Event ]
#pragma mark TimerEvent
- (void)streamStateTimerEvent:(NSTimer *)timer{
    [self requestLiveStreamState];
}

- (void)pushTimerEvent:(NSTimer *)timer{
    [self callbackForCurrentPushDuration];
}


#pragma mark - [ Delegate ]
#pragma mark PLVSocketManagerProtocol
/// socket 接收到 "message" 事件
- (void)socketMananger_didReceiveMessage:(NSString *)subEvent
                                    json:(NSString *)jsonString
                              jsonObject:(id)object {
    NSDictionary *jsonDict = (NSDictionary *)object;
    if (![jsonDict isKindOfClass:[NSDictionary class]]) {
        return;
    }
    if ([subEvent isEqualToString:@"onSliceID"]) {
        NSNumber * duration = (NSNumber *)jsonDict[@"data"][@"duration"];
        if (duration) { self.pushDuration = duration.doubleValue; }
        NSNumber * pushtime = (NSNumber *)jsonDict[@"data"][@"pushtime"];
        if (pushtime) { self.pushtimeTimestamp = pushtime.doubleValue; }
        NSString * sessionId = jsonDict[@"data"][@"sessionId"];
        if ([PLVFdUtil checkStringUseable:sessionId]) { self.sessionId = sessionId; }
    } else if([subEvent isEqualToString:@"onSliceStart"]) {
        NSNumber * pushtime = (NSNumber *)jsonDict[@"pushtime"];
        if (pushtime) { self.pushtimeTimestamp = pushtime.doubleValue; }
        NSString * sessionId = jsonDict[@"sessionId"];
        if ([PLVFdUtil checkStringUseable:sessionId]) { self.sessionId = sessionId; }
    } else if([subEvent containsString:@"finishClass"]) {
        self.currentStreamState = PLVChannelLiveStreamState_End;
    }
}

@end
