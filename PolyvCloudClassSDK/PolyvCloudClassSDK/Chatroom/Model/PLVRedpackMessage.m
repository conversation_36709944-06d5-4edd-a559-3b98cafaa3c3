//
//  PLVRedpackMessage.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/1/5.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVRedpackMessage.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

static NSTimeInterval kPLVRedpackMessageVaildInterval = 24 * 60 * 60;

@implementation PLVRedpackMessage

- (void)setContent:(NSString *)content {
    if (!content || ![content isKindOfClass:[NSString class]]) {
        _content = nil;
    } else {
        content = [content stringByReplacingOccurrencesOfString:@"&lt;" withString:@"<"];
        content = [content stringByReplacingOccurrencesOfString:@"&gt;" withString:@">"];
        content = [content stringByReplacingOccurrencesOfString:@"&amp;" withString:@"&"];
        _content = content;
    }
}

- (NSString *)typeString {
    NSString *string = @"";
    switch (self.type) {
        case PLVRedpackMessageTypeUnknown:
            string = @"";
            break;
        case PLVRedpackMessageTypeAliPassword:
            string = @"alipay_password_official_normal";
            break;
    }
    return string;
}

- (PLVRedpackState)state {
    if (self.time != 0 &&
        [PLVFdUtil curTimeInterval] - self.time >= kPLVRedpackMessageVaildInterval * 1000) { // 发出超过24小时的红包默认为失效
        return PLVRedpackStateExpired;
    } else {
        return _state;
    }
}

@end
