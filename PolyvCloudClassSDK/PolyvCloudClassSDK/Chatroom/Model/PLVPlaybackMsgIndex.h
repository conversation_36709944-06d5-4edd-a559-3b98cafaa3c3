//
//  PLVPlaybackMsgIndex.h
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/6/6.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface PLVPlaybackMsgIndex : NSObject

/// 用于标记该段数据是否被缓存，默认为NO
@property (nonatomic, assign) BOOL cache;
/// 段id
@property (nonatomic, assign) NSUInteger indexId;
/// 该段数据的起始视频时间戳，单位毫秒
@property (nonatomic, assign) NSTimeInterval startTime;
/// 该段数据的结束视频时间戳，单位毫秒
@property (nonatomic, assign) NSTimeInterval endTime;
/// 该段数据的消息条数，最大2000
@property (nonatomic, assign) NSUInteger messageCount;

/// 解析后台接口返回数据
- (instancetype)initWithDictionary:(NSDictionary *)dictionary;

@end

NS_ASSUME_NONNULL_END
