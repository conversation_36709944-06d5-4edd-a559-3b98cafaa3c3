//
//  PLVFileMessage.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2022/7/19.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVFileMessage.h"

@implementation PLVFileMessage

- (void)setName:(NSString *)name {
    if (!name || ![name isKindOfClass:[NSString class]]) {
        _name = nil;
    } else {
        name = [name stringByReplacingOccurrencesOfString:@"&lt;" withString:@"<"];
        name = [name stringByReplacingOccurrencesOfString:@"&gt;" withString:@">"];
        name = [name stringByReplacingOccurrencesOfString:@"&amp;" withString:@"&"];
        _name = name;
    }
}

@end
