//
//  PLVQuoteMessage.m
//  PLVLiveScenesDemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/11/25.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVQuoteMessage.h"

@implementation PLVQuoteMessage

- (void)setContent:(NSString *)content {
    if (!content || ![content isKindOfClass:[NSString class]]) {
        _content = nil;
    } else {
        content = [content stringByReplacingOccurrencesOfString:@"&lt;" withString:@"<"];
        content = [content stringByReplacingOccurrencesOfString:@"&gt;" withString:@">"];
        content = [content stringByReplacingOccurrencesOfString:@"&amp;" withString:@"&"];
        _content = [content copy];
    }
}

- (void)setQuoteContent:(NSString *)quoteContent {
    if (!quoteContent || ![quoteContent isKindOfClass:[NSString class]]) {
        _quoteContent = nil;
    } else {
        quoteContent = [quoteContent stringByReplacingOccurrencesOfString:@"&lt;" withString:@"<"];
        quoteContent = [quoteContent stringByReplacingOccurrencesOfString:@"&gt;" withString:@">"];
        quoteContent = [quoteContent stringByReplacingOccurrencesOfString:@"&amp;" withString:@"&"];
        _quoteContent = [quoteContent copy];
    }
}

@end
