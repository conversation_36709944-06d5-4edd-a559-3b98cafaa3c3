//
//  PLVImageMessage.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/11/24.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVImageMessage.h"
#import <PLVFoundationSDK/PLVFdUtil.h>

@implementation PLVImageMessage

- (instancetype)init {
    self = [super init];
    if (self) {
        self.imageId = [NSString stringWithFormat:@"chat_img_iOS_%@", [PLVFdUtil curTimeStamp]];
        self.imageName = [NSString stringWithFormat:@"%@.jpeg", self.imageId];
    }
    return self;
}

- (void)setImage:(UIImage *)image {
    _image = image;
    self.imageSize = image.size;
}

@end
