//
//  PLVPlaybackMsgIndex.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/6/6.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVPlaybackMsgIndex.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

@implementation PLVPlaybackMsgIndex

#pragma mark - [ Public Method ]

- (instancetype)initWithDictionary:(NSDictionary *)dictionary {
    self = [super init];
    if (self) {
        if ([PLVFdUtil checkDictionaryUseable:dictionary]) {
            self.startTime = PLV_SafeIntegerForDictKey(dictionary, @"startTime");
            self.endTime = PLV_SafeIntegerForDictKey(dictionary, @"endTime");
            self.messageCount = PLV_SafeIntegerForDictKey(dictionary, @"count");
            
            NSInteger indexId = PLV_SafeIntegerForDictKey(dictionary, @"id");
            self.indexId = MAX(0, indexId);
        }
    }
    return self;
}

#pragma mark - [ Private Method ]

- (BOOL)isEqualToPlaybackMsgIndex:(PLVPlaybackMsgIndex *)playbackMsgIndex {
    if (!playbackMsgIndex) {
        return NO;
    }

    return playbackMsgIndex.indexId == self.indexId;
}

#pragma mark - [ Override ]

- (BOOL)isEqual:(id)object {
    if (self == object) {
        return YES;
    }

    if (![object isKindOfClass:[PLVPlaybackMsgIndex class]]) {
        return NO;
    }

    return [self isEqualToPlaybackMsgIndex:(PLVPlaybackMsgIndex *)object];
}

- (NSUInteger)hash {
    return self.indexId;
}

@end
