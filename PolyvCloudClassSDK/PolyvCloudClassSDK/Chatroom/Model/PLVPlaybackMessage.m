//
//  PLVPlaybackMessage.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/6/6.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVPlaybackMessage.h"
#import "PLVSpeakMessage.h"
#import "PLVQuoteMessage.h"
#import "PLVImageMessage.h"
#import "PLVImageEmotionMessage.h"
#import "PLVSpeakTopMessage.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

@implementation PLVPlaybackMessage

#pragma mark - [ Public Method ]

- (instancetype)initWithDictionary:(NSDictionary *)dictionary {
    self = [super init];
    if (self) {
        if ([PLVFdUtil checkDictionaryUseable:dictionary]) {
            self.playbackTime = PLV_SafeIntegerForDictKey(dictionary, @"relativeTime");
            
            id userObject = dictionary[@"user"];
            NSDictionary *userDict = [PLVDataUtil dictionaryWithJsonString:userObject];
            self.user = [[PLVPlaybackMsgUser alloc] initWithDictionary:userDict];
            
            NSString *msgId = PLV_SafeStringForDictKey(dictionary, @"id");
            NSTimeInterval time = PLV_SafeIntegerForDictKey(dictionary, @"time");
            id content = dictionary[@"content"];
            NSDictionary *contentDict = [PLVDataUtil dictionaryWithJsonString:content];
            id quoteObject = dictionary[@"quote"];
            NSDictionary *quoteDict = [PLVDataUtil dictionaryWithJsonString:quoteObject];
            NSString *msgType = PLV_SafeStringForDictKey(dictionary, @"msgType");
            
            if ([PLVFdUtil checkStringUseable:msgType] && [msgType isEqualToString:@"speakTop"]) {
                PLVSpeakTopMessage *speakTopMessage = [[PLVSpeakTopMessage alloc] initWithDictionary:dictionary];
                self.message = speakTopMessage;
            } else if (content && [content isKindOfClass:[NSString class]] && !contentDict) {
                if (quoteDict && [quoteDict isKindOfClass:[NSDictionary class]] && [quoteDict count] > 0) { // 回复消息
                    PLVQuoteMessage *quoteMessage = [[PLVQuoteMessage alloc] init];
                    quoteMessage.msgId = msgId;
                    quoteMessage.content = content;
                    quoteMessage.time = time;
                    quoteMessage.playbackTime = self.playbackTime;
                    quoteMessage.quoteUserId = PLV_SafeStringForDictKey(quoteDict, @"userId");
                    quoteMessage.quoteUserName = PLV_SafeStringForDictKey(quoteDict, @"nick");
                    
                    NSString *quoteContent = PLV_SafeStringForDictKey(quoteDict, @"content");
                    if (quoteContent) {
                        quoteMessage.quoteContent = quoteContent;
                    }
                    NSDictionary *imageDict = PLV_SafeDictionaryForDictKey(quoteDict, @"image");
                    if (imageDict) {
                        NSString *imageUrl = PLV_SafeStringForDictKey(imageDict, @"url");
                        CGFloat width = PLV_SafeFloatForDictKey(imageDict, @"width");
                        CGFloat height = PLV_SafeFloatForDictKey(imageDict, @"height");
                        quoteMessage.quoteImageUrl = imageUrl;
                        quoteMessage.quoteImageSize = CGSizeMake(width, height);
                    }
                    self.message = quoteMessage;
                } else { // 文字消息
                    PLVSpeakMessage *speakMessage = [[PLVSpeakMessage alloc] init];
                    speakMessage.msgId = msgId;
                    speakMessage.content = content;
                    speakMessage.time = time;
                    speakMessage.playbackTime = self.playbackTime;
                    self.message = speakMessage;
                }
            } else if (contentDict && [contentDict isKindOfClass:[NSDictionary class]]) {
                NSString *imageId = PLV_SafeStringForDictKey(contentDict, @"id");
                NSString *imageUrl = PLV_SafeStringForDictKey(contentDict, @"uploadImgUrl");
                CGSize imageSize = CGSizeZero;
                NSDictionary *sizeDict = PLV_SafeDictionaryForDictKey(contentDict, @"size");
                if (sizeDict) {
                    CGFloat width = PLV_SafeFloatForDictKey(sizeDict, @"width");
                    CGFloat height = PLV_SafeFloatForDictKey(sizeDict, @"height");
                    imageSize = CGSizeMake(width, height);
                }
                
                NSString *type = PLV_SafeStringForDictKey(contentDict, @"type");
                if ([type isEqualToString:@"emotion"]) { // 图片表情消息
                    PLVImageEmotionMessage *emotionMessage = [[PLVImageEmotionMessage alloc] init];
                    emotionMessage.msgId = msgId;
                    emotionMessage.time = time;
                    emotionMessage.playbackTime = self.playbackTime;
                    emotionMessage.imageId = imageId;
                    emotionMessage.imageUrl = imageUrl;
                    emotionMessage.imageSize = imageSize;
                    emotionMessage.sendState = PLVImageEmotionMessageSendStateSuccess;
                    self.message = emotionMessage;
                } else if ([type isEqualToString:@"chatImg"]) { // 图片消息
                    PLVImageMessage *imageMessage = [[PLVImageMessage alloc] init];
                    imageMessage.msgId = msgId;
                    imageMessage.time = time;
                    imageMessage.playbackTime = self.playbackTime;
                    imageMessage.imageId = imageId;
                    imageMessage.imageUrl = imageUrl;
                    imageMessage.imageSize = imageSize;
                    imageMessage.uploadState = PLVImageUploadStateSuccess;
                    imageMessage.sendState = PLVImageMessageSendStateSuccess;
                    imageMessage.uploadProgress = 1;
                    self.message = imageMessage;
                }
            }
        }
    }
    return self;
}

+ (BOOL)playbackMessageValid:(PLVPlaybackMessage *)playbackMessage {
    if (!playbackMessage ||
        !playbackMessage.message) {
        return NO;
    }
    
    id message = playbackMessage.message;
    if ([message isKindOfClass:[PLVSpeakMessage class]] ||
        [message isKindOfClass:[PLVQuoteMessage class]] ||
        [message isKindOfClass:[PLVImageMessage class]] ||
        [message isKindOfClass:[PLVImageEmotionMessage class]] ||
        [message isKindOfClass:[PLVSpeakTopMessage class]]) {
        return YES;
    }
    
    return NO;
}

#pragma mark - [ Private Method ]

- (BOOL)isEqualToPlaybackMessage:(PLVPlaybackMessage *)playbackMessage {
    if (!playbackMessage) {
        return NO;
    }

    if (!playbackMessage.message || !self.message) {
        return NO;
    }
    
    if ([playbackMessage.message isKindOfClass:[PLVSpeakMessage class]] &&
        [self.message isKindOfClass:[PLVSpeakMessage class]]) {
        
        PLVSpeakMessage *aMessage = (PLVSpeakMessage *)self.message;
        PLVSpeakMessage *bMessage = (PLVSpeakMessage *)playbackMessage.message;
        return aMessage.msgId && bMessage.msgId && [aMessage.msgId isEqualToString:bMessage.msgId];
        
    } else if ([playbackMessage.message isKindOfClass:[PLVQuoteMessage class]] &&
        [self.message isKindOfClass:[PLVQuoteMessage class]]) {
        
        PLVQuoteMessage *aMessage = (PLVQuoteMessage *)self.message;
        PLVQuoteMessage *bMessage = (PLVQuoteMessage *)playbackMessage.message;
        return aMessage.msgId && bMessage.msgId && [aMessage.msgId isEqualToString:bMessage.msgId];
    
    } else if ([playbackMessage.message isKindOfClass:[PLVImageMessage class]] &&
        [self.message isKindOfClass:[PLVImageMessage class]]) {
    
        PLVImageMessage *aMessage = (PLVImageMessage *)self.message;
        PLVImageMessage *bMessage = (PLVImageMessage *)playbackMessage.message;
        return aMessage.msgId && bMessage.msgId && [aMessage.msgId isEqualToString:bMessage.msgId];
    
    } else if ([playbackMessage.message isKindOfClass:[PLVImageEmotionMessage class]] &&
        [self.message isKindOfClass:[PLVImageEmotionMessage class]]) {
    
        PLVImageEmotionMessage *aMessage = (PLVImageEmotionMessage *)self.message;
        PLVImageEmotionMessage *bMessage = (PLVImageEmotionMessage *)playbackMessage.message;
        return aMessage.msgId && bMessage.msgId && [aMessage.msgId isEqualToString:bMessage.msgId];
    
    } else {
        return NO;
    }
}

#pragma mark - [ Override ]

- (BOOL)isEqual:(id)object {
    if (self == object) {
        return YES;
    }

    if (![object isKindOfClass:[PLVPlaybackMessage class]]) {
        return NO;
    }

    return [self isEqualToPlaybackMessage:(PLVPlaybackMessage *)object];
}

- (NSUInteger)hash {
    NSString *msgId = nil;
    if ([self.message isKindOfClass:[PLVSpeakMessage class]]) {
        
        PLVSpeakMessage *message = (PLVSpeakMessage *)self.message;
        msgId = message.msgId;
        
    } else if ([self.message isKindOfClass:[PLVQuoteMessage class]]) {
        
        PLVQuoteMessage *message = (PLVQuoteMessage *)self.message;
        msgId = message.msgId;
    
    } else if ([self.message isKindOfClass:[PLVImageMessage class]]) {
    
        PLVImageMessage *message = (PLVImageMessage *)self.message;
        msgId = message.msgId;
    
    } else if ([self.message isKindOfClass:[PLVImageEmotionMessage class]]) {
    
        PLVImageEmotionMessage *message = (PLVImageEmotionMessage *)self.message;
        msgId = message.msgId;
    
    }
    
    NSUInteger hash = [msgId hash];
    return hash;
}

@end
