//
//  PLVSpeakMessage.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/11/24.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVSpeakMessage.h"

@implementation PLVSpeakMessage

- (void)setContent:(NSString *)content {
    if (!content || ![content isKindOfClass:[NSString class]]) {
        _content = nil;
    } else {
        content = [content stringByReplacingOccurrencesOfString:@"&lt;" withString:@"<"];
        content = [content stringByReplacingOccurrencesOfString:@"&gt;" withString:@">"];
        content = [content stringByReplacingOccurrencesOfString:@"&amp;" withString:@"&"];
        _content = content;
    }
}

@end
