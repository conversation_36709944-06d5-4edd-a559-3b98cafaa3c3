//
//  PLVPlaybackMsgUser.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/6/6.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVPlaybackMsgUser.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

@implementation PLVPlaybackMsgUser

- (instancetype)initWithDictionary:(NSDictionary *)dictionary {
    self = [super init];
    if (self) {
        if ([PLVFdUtil checkDictionaryUseable:dictionary]) {
            self.userId = PLV_SafeStringForDictKey(dictionary, @"userId");
            self.userName = PLV_SafeStringForDictKey(dictionary, @"nick");
            self.avatarUrl = PLV_SafeStringForDictKey(dictionary, @"pic");
            self.role = PLV_SafeStringForDictKey(dictionary, @"userType");
            self.actor = PLV_SafeStringForDictKey(dictionary, @"actor");
        }
    }
    return self;
}

@end
