//
//  PLVSpeakTopMessage.m
//  PLVLiveScenesSDK
//
//  Created by Saky<PERSON> on 2024/7/2.
//  Copyright © 2024 PLV. All rights reserved.
//

#import "PLVSpeakTopMessage.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

@implementation PLVSpeakTopMessage

#pragma mark - [ Public Method ]

- (instancetype)initWithDictionary:(NSDictionary *)dictionary {
    self = [super init];
    if (self) {
        if ([PLVFdUtil checkDictionaryUseable:dictionary]) {
            self.msgId = PLV_SafeStringForDictKey(dictionary, @"id");
            self.time = PLV_SafeIntegerForDictKey(dictionary, @"time");
            self.content = PLV_SafeStringForDictKey(dictionary, @"content");
            self.relativeTime = PLV_SafeIntegerForDictKey(dictionary, @"relativeTime");
            self.nick = PLV_SafeStringForDictKey(dictionary, @"nick");
            self.pic = PLV_SafeStringForDictKey(dictionary, @"pic");
            self.topActor = PLV_SafeStringForDictKey(dictionary, @"topActor");
            self.action = PLV_SafeStringForDictKey(dictionary, @"action");
            self.others = PLV_SafeArraryForDictKey(dictionary, @"others");
        }
    }
    return self;
}

- (void)setContent:(NSString *)content {
    if (!content || ![content isKindOfClass:[NSString class]]) {
        _content = nil;
    } else {
        content = [content stringByReplacingOccurrencesOfString:@"&lt;" withString:@"<"];
        content = [content stringByReplacingOccurrencesOfString:@"&gt;" withString:@">"];
        content = [content stringByReplacingOccurrencesOfString:@"&amp;" withString:@"&"];
        _content = [content copy];
    }
}

- (NSDictionary *)toDictionary {
    NSMutableDictionary *dictionary = [NSMutableDictionary dictionary];
    if (self.msgId) {
        dictionary[@"id"] = self.msgId;
    }
    
    if (self.time != 0) {
        dictionary[@"time"] = @(self.time);
    }
    
    if (self.content) {
        dictionary[@"content"] = self.content;
    }
    if (self.relativeTime != 0) {
        dictionary[@"relativeTime"] = @(self.relativeTime);
    }
    
    if (self.nick) {
        dictionary[@"nick"] = self.nick;
    }
    
    if (self.pic) {
        dictionary[@"pic"] = self.pic;
    }
    
    if (self.topActor) {
        dictionary[@"topActor"] = self.topActor;
    }
    
    if (self.action) {
        dictionary[@"action"] = self.action;
    }
    
    if (self.others) {
        dictionary[@"others"] = self.others;
    }
    
    return [dictionary copy];
}

@end
