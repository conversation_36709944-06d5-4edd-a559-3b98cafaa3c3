//
//  PLVPlaybackMessageManager.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/6/6.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVPlaybackMessageManager.h"
#import "PLVPlaybackMsgIndex.h"
#import "PLVPlaybackMessage.h"
#import "PLVSpeakTopMessage.h"
#import "PLVLivePrivateAPI.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

// 更新预加载消息数组的时间间隔，单位'秒'
static NSInteger kIntervalTime = 1.0;
// 预加载未来多少秒内的消息
static NSInteger kPreloadTime = 10.0;
// 缓存文件存储路径：temp/{kCacheDirectory}/{sessionId}_{index}
static NSString *kCacheDirectory = @"plvpb";

@interface PLVPlaybackMessageManager ()
/// 频道号
@property (nonatomic, copy) NSString *channelId;
/// 回放场次id
@property (nonatomic, copy) NSString *sessionId;
/// 回放视频id
@property (nonatomic, copy) NSString *videoId;

/// 分段信息数据模型数组
@property (nonatomic, strong) NSMutableArray <PLVPlaybackMsgIndex *>*msgIndexArray;
/// 当前已加载分段数据页数，默认0
@property (nonatomic, assign) NSInteger currentPage;
/// 分段信息数据总页数，默认0
@property (nonatomic, assign) NSInteger totalPage;
/// 分段信息http接口调用中
@property (nonatomic, assign) BOOL loadingMsgIndex;

/// 按playbackTime字段升序排列的预加载消息数组
@property (nonatomic, strong) NSMutableArray <PLVPlaybackMessage *>*preLoadMsgArray;
/// 预加载数组更新聊天消息http接口调用中
@property (nonatomic, assign) BOOL preloadingMessage;

/// 按playbackTime字段升序排列的speakTop消息数组
@property (nonatomic, strong) NSMutableArray <PLVPlaybackMessage *>*speakTopMsgArray;

/// 回调定时器
@property (nonatomic, strong) NSTimer *timer;
/// 当前视频播放时间戳，单位毫秒
@property (nonatomic, assign) NSTimeInterval currentPlaybackTime;

@end

@implementation PLVPlaybackMessageManager {
    // 操作数组的信号量，防止多线程读写数组
    dispatch_semaphore_t _msgIndexArrayLock;
    dispatch_semaphore_t _preLoadMsgArrayLock;
    dispatch_semaphore_t _speakTopMsgArrayLock;
}

#pragma mark - [ Life Cycle ]

- (instancetype)initWithChannelId:(NSString *)channelId sessionId:(NSString *)sessionId videoId:(NSString *)videoId {
    self = [super init];
    if (self) {
        self.channelId = (channelId && [channelId isKindOfClass:[NSString class]]) ? channelId : @"";
        self.sessionId = (sessionId && [sessionId isKindOfClass:[NSString class]]) ? sessionId : @"";
        self.videoId = (videoId && [videoId isKindOfClass:[NSString class]]) ? videoId : @"";
        
        // 创建沙盒文件夹
        [PLVPlaybackMessageManager createCacheDirectory];
        
        // 请求聊天消息分段信息
        [self requestMessageIndexWithPage:1];
        
        // 创建定时器
        [self createTimer];
        
        self.currentPlaybackTime = -1;
        self.maxPreloadCount = 200;
        self.preLoadMsgArray = [[NSMutableArray alloc] initWithCapacity:self.maxPreloadCount];
        self.speakTopMsgArray = [[NSMutableArray alloc] initWithCapacity:self.maxPreloadCount];
        _msgIndexArrayLock = dispatch_semaphore_create(1);
        _preLoadMsgArrayLock = dispatch_semaphore_create(1);
        _speakTopMsgArrayLock = dispatch_semaphore_create(1);
    }
    return self;
}

#pragma mark - [ Override ]

+ (void)initialize { // 防止杀进程导致结束回放未清除缓存
    [PLVPlaybackMessageManager deleteCacheDirectory];
}

- (void)dealloc {
    [self destroyTimer];
    // 结束回放清除缓存
    [PLVPlaybackMessageManager deleteCacheDirectory];
}

#pragma mark - [ Public Method ]

- (NSArray <PLVPlaybackMessage *>*)playbackMessagInPreloadMessagesFrom:(NSTimeInterval)startTime to:(NSTimeInterval)endTime {
    if ([self.preLoadMsgArray count] == 0) {
        return @[];
    }
    
    // 数据过滤
    NSPredicate *predicate1 = [NSPredicate predicateWithFormat:@"playbackTime>=%f", startTime];
    NSPredicate *predicate2 = [NSPredicate predicateWithFormat:@"playbackTime<%f", endTime];
    NSArray *filteredArray = [[self preLoadMessageArray] filteredArrayUsingPredicate:predicate1];
    filteredArray = [filteredArray filteredArrayUsingPredicate:predicate2];
    
    return filteredArray;
}

- (NSArray <PLVPlaybackMessage *>*)playbackSpeakTopMessageBefore:(NSTimeInterval)time {
    NSArray *array = nil;
    
    dispatch_semaphore_wait(_speakTopMsgArrayLock, DISPATCH_TIME_FOREVER);
    if ([self.speakTopMsgArray count] > 0) {
        array = [self.speakTopMsgArray copy];
    } else {
        array = @[];
    }
    dispatch_semaphore_signal(_speakTopMsgArrayLock);
    
    if ([array count] == 0) {
        return @[];
    }
    
    // 数据过滤
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"playbackTime<%f", time];
    array = [array filteredArrayUsingPredicate:predicate];
    
    return array;
}

- (void)loadMorePlaybackMessagBefore:(NSTimeInterval)playbackTime {
    if (self.totalPage == 0) {
        [self requestMessageIndexWithPage:1];
        
        // 不管获取数据成功与否，都必须触发该回调，demo层需要根据该回调结束加载动画
        [self notifyLoadMorePlaybackMessageFinish:nil];
    } else {
        // 该时间段前相关分段
        NSArray *indexArray = [self indexModelsFromStartTime:0 endTime:playbackTime];
        [self loadLatestMessageFromIndexArray:indexArray before:playbackTime];
    }
}

#pragma mark - [ Private Method ]

#pragma mark 分段信息

/// 把接口数据转换为数据模型存入数组msgIndexArray中
- (void)setupMsgIndexArray:(NSArray *)array {
    if (![PLVFdUtil checkArrayUseable:array]) {
        return;
    }
    
    NSMutableArray *muArray = [[NSMutableArray alloc] initWithCapacity:array.count];
    for (NSDictionary *dict in array) {
        PLVPlaybackMsgIndex *indexModel = [[PLVPlaybackMsgIndex alloc] initWithDictionary:dict];
        [muArray addObject:indexModel];
    }
    
    if ([muArray count] > 0) {
        // 数据去重
        NSMutableSet *muSet = [[NSMutableSet alloc] init];
        [muSet addObjectsFromArray:[muArray copy]];
        [muSet addObjectsFromArray:[self.msgIndexArray copy]];
        
        // 数据排序
        NSSortDescriptor *sortDescriptor = [NSSortDescriptor sortDescriptorWithKey:@"indexId" ascending:YES];
        NSArray *sortedArray = [[muSet copy] sortedArrayUsingDescriptors:@[sortDescriptor]];
        
        dispatch_semaphore_wait(_msgIndexArrayLock, DISPATCH_TIME_FOREVER);
        [self.msgIndexArray removeAllObjects];
        if ([sortedArray count] > 0) {
            [self.msgIndexArray addObjectsFromArray:sortedArray];
        }
        dispatch_semaphore_signal(_msgIndexArrayLock);
    }
}

/// 根据分段id寻找对应的分段数据模型
- (PLVPlaybackMsgIndex *)indexModelWithIndex:(NSInteger)indexId {
    if (!self.msgIndexArray) {
        return nil;
    }
    
    NSArray *tempArray = [self.msgIndexArray copy];
    PLVPlaybackMsgIndex *indexModel = [tempArray objectAtIndex:indexId-1];
    if (indexModel && indexModel.indexId == indexId) {
        return indexModel;
    } else {
        for (PLVPlaybackMsgIndex *indexModel in tempArray) {
            if (indexModel.indexId == indexId) {
                return indexModel;
            }
        }
    }
    return nil;
}

/// 指定时间段(startTime,endTime)对应分段数据模型
- (NSArray <PLVPlaybackMsgIndex *>*)indexModelsFromStartTime:(NSTimeInterval)startTime endTime:(NSTimeInterval)endTime {
    if (startTime > endTime || endTime < 0 || [self.msgIndexArray count] == 0) {
        return @[];
    }
    
    NSPredicate *predicate1 = [NSPredicate predicateWithFormat:@"startTime<=%f", endTime];
    NSPredicate *predicate2 = [NSPredicate predicateWithFormat:@"endTime>=%f", startTime];
    NSArray *filteredArray = [[self.msgIndexArray copy] filteredArrayUsingPredicate:predicate1];
    filteredArray = [filteredArray filteredArrayUsingPredicate:predicate2];
    return filteredArray;
}

- (void)requestMessageIndexWithPage:(NSInteger)page {
    if (self.loadingMsgIndex) {
        return;
    }
    
    self.loadingMsgIndex = YES;
    
    __weak typeof(self) weakSelf = self;
    [PLVLivePrivateAPI requestPlaybakMessageHistoryIndexWithRoomId:self.channelId sessionId:self.sessionId videoId:self.videoId page:page success:^(NSDictionary * _Nonnull responseDict) {
        weakSelf.loadingMsgIndex = NO;
        
        NSInteger totalPage = PLV_SafeIntegerForDictKey(responseDict, @"totalPage");
        if (totalPage <= 0) {
            if (weakSelf.totalPage == 0) {
                if (weakSelf.delegate &&
                    [weakSelf.delegate respondsToSelector:@selector(loadMessageInfoSuccess:playbackMessageManager:)]) {
                    [weakSelf.delegate loadMessageInfoSuccess:NO playbackMessageManager:weakSelf];
                }
            }
            return;
        }
        
        NSInteger count = PLV_SafeIntegerForDictKey(responseDict, @"total");
        if (!weakSelf.msgIndexArray) {
            weakSelf.msgIndexArray = [[NSMutableArray alloc] initWithCapacity:count];
            
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(loadMessageInfoSuccess:playbackMessageManager:)]) {
                [weakSelf.delegate loadMessageInfoSuccess:YES playbackMessageManager:weakSelf];
            }
        }
        
        NSArray *partDetail = PLV_SafeArraryForDictKey(responseDict, @"partDetail");
        [weakSelf setupMsgIndexArray:partDetail];
        
        weakSelf.totalPage = totalPage;
        weakSelf.currentPage = page;
        
    } failure:^(NSError * _Nonnull error) {
        weakSelf.loadingMsgIndex = NO;
        
        if (weakSelf.totalPage == 0) {
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(loadMessageInfoSuccess:playbackMessageManager:)]) {
                [weakSelf.delegate loadMessageInfoSuccess:NO playbackMessageManager:weakSelf];
            }
        }
    }];
}

#pragma mark 回放消息

// 解析回放消息原始数据->数据模型
- (NSArray <PLVPlaybackMessage *> *)playbackMessageArrayWithObject:(NSArray *)objectArray {
    NSMutableArray *muArray = [[NSMutableArray alloc] initWithCapacity:[objectArray count]];
    NSMutableArray *speakTopMuArray = [[NSMutableArray alloc] initWithCapacity:[objectArray count]];
    for (NSDictionary *dict in objectArray) {
        PLVPlaybackMessage *message = [[PLVPlaybackMessage alloc] initWithDictionary:dict];
        if ([PLVPlaybackMessage playbackMessageValid:message]) {
            [muArray addObject:message];
            if ([message.message isKindOfClass:[PLVSpeakTopMessage class]]) {
                [speakTopMuArray addObject:message];
            }
        }
    }
    dispatch_semaphore_wait(_speakTopMsgArrayLock, DISPATCH_TIME_FOREVER);
    if ([speakTopMuArray count] > 0) {
        [self.speakTopMsgArray addObjectsFromArray:speakTopMuArray];
        NSArray *speakTopArray = [self rearrangeMessageArray:self.speakTopMsgArray];
        self.speakTopMsgArray = [NSMutableArray arrayWithArray:speakTopArray];
    }
    dispatch_semaphore_signal(_speakTopMsgArrayLock);
    return [muArray copy];
}

// 根据msgId对数组数据进行去重，再根据playbackTime字段进行重新排序
- (NSArray *)rearrangeMessageArray:(NSArray <PLVPlaybackMessage *> *)array {
    if (![PLVFdUtil checkArrayUseable:array]) {
        return @[];
    }
    
    // 数据去重
    NSMutableSet *muSet = [[NSMutableSet alloc] init];
    [muSet addObjectsFromArray:array];
    // 数据排序
    NSSortDescriptor *sortDescriptor = [NSSortDescriptor sortDescriptorWithKey:@"playbackTime" ascending:YES];
    NSArray *sortedArray = [[muSet copy] sortedArrayUsingDescriptors:@[sortDescriptor]];
    return sortedArray;
}

/// 根据playbackTime字段对数组元素进行过滤
- (NSArray *)filterPlaybackMessageArray:(NSArray *)array From:(NSTimeInterval)startTime to:(NSTimeInterval)endTime {
    NSPredicate *predicate1 = [NSPredicate predicateWithFormat:@"playbackTime>=%f", startTime];
    NSPredicate *predicate2 = [NSPredicate predicateWithFormat:@"playbackTime<%f", endTime];
    NSArray *filteredArray = [array filteredArrayUsingPredicate:predicate1];
    filteredArray = [filteredArray filteredArrayUsingPredicate:predicate2];
    return filteredArray;
}

/// 根据playbackTime字段对数组元素进行过滤
- (NSArray *)filterPlaybackMessageArray:(NSArray *)array before:(NSTimeInterval)playbackTime {
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"playbackTime<%f", playbackTime];
    NSArray *filteredArray = [array filteredArrayUsingPredicate:predicate];
    return filteredArray;
}

#pragma mark 预加载数组

/// 预加载数组拷贝
- (NSArray *)preLoadMessageArray {
    NSArray *array = nil;
    
    dispatch_semaphore_wait(_preLoadMsgArrayLock, DISPATCH_TIME_FOREVER);
    if ([self.preLoadMsgArray count] > 0) {
        array = [self.preLoadMsgArray copy];
    } else {
        array = @[];
    }
    dispatch_semaphore_signal(_preLoadMsgArrayLock);
    
    return array;
}

- (void)preloadMessagesFrom:(NSTimeInterval)startTime to:(NSTimeInterval)endTime {
    NSArray *indexArray = [self indexModelsFromStartTime:startTime endTime:endTime]; // 获取相应时间段内对应分段
    if ([indexArray count] == 0) {
        return;
    }
      
    __block NSMutableArray <NSNumber *>*uncacheIndexIdArray = [[NSMutableArray alloc] initWithCapacity:[indexArray count]];
    __block NSMutableArray *muMessageArray = [[NSMutableArray alloc] init];
    for (int i = 0; i < [indexArray count]; i++) {
        PLVPlaybackMsgIndex *indexModel = indexArray[i];
        if (indexModel.cache) { // 数据已缓存到沙盒中
            // 读取沙盒数据
            NSString *fileName = [self fileNameWithIndexId:indexModel.indexId];
            NSArray *array = [self playbackMessageArrayInFile:fileName];
            // 解析数据，转换为数据模型
            NSArray <PLVPlaybackMessage *> *messageArray = [self playbackMessageArrayWithObject:array];
            [muMessageArray addObjectsFromArray:messageArray];
        } else {
            [uncacheIndexIdArray addObject:@(indexModel.indexId)];
        }
    }
    
    if ([muMessageArray count] > 0) {
        [self updatePreLoadMessageArray:[muMessageArray copy]];
    }
    
    if ([uncacheIndexIdArray count] > 0) { // 需要调用http接口获取数据
        NSArray <NSNumber *>*indexArray = [uncacheIndexIdArray copy];
        for (NSNumber *indexId in indexArray) {
            [self requestMessageAtIndex:[indexId integerValue]];
        }
    }
}

- (void)requestMessageAtIndex:(NSInteger)indexId {
    if (index <= 0) {
        return;
    }
    
    if (self.preloadingMessage) {
        return;
    }
    
    self.preloadingMessage = YES;
    
    __weak typeof(self) weakSelf = self;
    [PLVLivePrivateAPI requestPlaybakMessageHistoryWithRoomId:self.channelId sessionId:self.sessionId indexId:indexId success:^(NSArray * _Nonnull responseArray) {
        weakSelf.preloadingMessage = NO;
        if (responseArray &&
            [responseArray isKindOfClass:[NSArray class]] &&
            [responseArray count] > 0) {
            
            // 存数据到沙盒
            NSString *fileName = [weakSelf fileNameWithIndexId:indexId];
            BOOL success = [PLVPlaybackMessageManager cache:responseArray fileName:fileName];
            if (success) {
                PLVPlaybackMsgIndex *indexModel = [weakSelf indexModelWithIndex:indexId];
                indexModel.cache = YES;
            }
            
            // 解析数据得到数据模型数组
            NSArray <PLVPlaybackMessage *> *messageArray = [weakSelf playbackMessageArrayWithObject:responseArray];
            if ([messageArray count] > 0) { // 更新到预加载数组
                [weakSelf updatePreLoadMessageArray:messageArray];
            }
        }
    } failure:^(NSError * _Nonnull error) {
        weakSelf.preloadingMessage = NO;
    }];
}

/// 预加载数组更新
- (void)updatePreLoadMessageArray:(NSArray <PLVPlaybackMessage *> *)array {
    //1.把参数的数组添加到预加载数组中
    NSMutableArray *muArray = [[NSMutableArray alloc] init];
    [muArray addObjectsFromArray:array];
    [muArray addObjectsFromArray:[self preLoadMessageArray]];
    
    //2.对汇总数组进行去重、排序
    NSArray *arrangedArray = [self rearrangeMessageArray:[muArray copy]];
    
    //3.对预加载数组进行过滤， 截取当前播放时间到往后10秒
    NSArray *filteredArray = [self filterPlaybackMessageArray:arrangedArray From:self.currentPlaybackTime to:self.currentPlaybackTime + kPreloadTime * 1000];
    
    //4.对预加载数组进行长度限制，确保不超过200，若超过，截取前面200条
    if ([filteredArray count] > self.maxPreloadCount) {
        filteredArray = [filteredArray subarrayWithRange:NSMakeRange(0, self.maxPreloadCount)];
    }
    
    //5.把预加载数组的数据全部用处理后的数组替换
    dispatch_semaphore_wait(_preLoadMsgArrayLock, DISPATCH_TIME_FOREVER);
    [self.preLoadMsgArray removeAllObjects];
    if ([filteredArray count] > 0) {
        [self.preLoadMsgArray addObjectsFromArray:filteredArray];
    }
    dispatch_semaphore_signal(_preLoadMsgArrayLock);
}

#pragma mark 加载更多过往消息

- (void)loadLatestMessageFromIndexArray:(NSArray *)indexArray before:(NSTimeInterval)playbackTime {
    if ([indexArray count] == 0) {
        // 不管获取数据成功与否，都必须触发该回调，demo层需要根据该回调结束加载动画
        [self notifyLoadMorePlaybackMessageFinish:nil];
        return;
    }
    
    PLVPlaybackMsgIndex *indexModel = [indexArray lastObject];
    if (indexModel.cache) { // 数据已缓存到沙盒中
        // 读取沙盒数据
        NSString *fileName = [self fileNameWithIndexId:indexModel.indexId];
        NSArray *array = [self playbackMessageArrayInFile:fileName];
        
        // 解析数据得到数据模型数组
        NSArray <PLVPlaybackMessage *> *messageArray = [self playbackMessageArrayWithObject:array];
        messageArray = [self filterPlaybackMessageArray:messageArray before:playbackTime];
        if ([messageArray count] > 0) {
            [self notifyLoadMorePlaybackMessageFinish:messageArray];
        } else {
            NSMutableArray *currentIndexArray = [[NSMutableArray alloc] initWithArray:indexArray];
            [currentIndexArray removeLastObject];
            [self loadLatestMessageFromIndexArray:[currentIndexArray copy] before:playbackTime];
        }
    } else { //调用http接口获取数据并成功后触发回调
        __weak typeof(self) weakSelf = self;
        [self requestMessageAtIndex:indexModel.indexId completion:^(NSArray<PLVPlaybackMessage *> *messageArray) {
            if (!messageArray) {
                [weakSelf notifyLoadMorePlaybackMessageFinish:nil];
            } else {
                messageArray = [weakSelf filterPlaybackMessageArray:messageArray before:playbackTime];
                if ([messageArray count] > 0) {
                    [weakSelf notifyLoadMorePlaybackMessageFinish:messageArray];
                } else {
                    NSMutableArray *currentIndexArray = [[NSMutableArray alloc] initWithArray:indexArray];
                    [currentIndexArray removeLastObject];
                    [weakSelf loadLatestMessageFromIndexArray:[currentIndexArray copy] before:playbackTime];
                }
            }
        }];
    }
}

- (void)requestMessageAtIndex:(NSInteger)indexId completion:(void (^)(NSArray <PLVPlaybackMessage *>*messageArray))completion {
    __weak typeof(self) weakSelf = self;
    [PLVLivePrivateAPI requestPlaybakMessageHistoryWithRoomId:self.channelId sessionId:self.sessionId indexId:indexId success:^(NSArray * _Nonnull responseArray) {
        if (responseArray &&
            [responseArray isKindOfClass:[NSArray class]] &&
            [responseArray count] > 0) {
            
            // 存数据到沙盒
            NSString *fileName = [weakSelf fileNameWithIndexId:indexId];
            BOOL success = [PLVPlaybackMessageManager cache:responseArray fileName:fileName];
            if (success) {
                PLVPlaybackMsgIndex *indexModel = [weakSelf indexModelWithIndex:indexId];
                indexModel.cache = YES;
            }
            
            if (completion) {
                // 解析数据得到数据模型数组
                NSArray <PLVPlaybackMessage *> *messageArray = [weakSelf playbackMessageArrayWithObject:responseArray];
                completion(messageArray);
            }
        } else {
            if (completion) {
                completion(nil);
            }
        }
    } failure:^(NSError * _Nonnull error) {
        if (completion) {
            completion(nil);
        }
    }];
}

- (void)notifyLoadMorePlaybackMessageFinish:(NSArray *)array {
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(loadMoreHistoryMessagesSuccess:playbackMessageManager:)]) {
        [self.delegate loadMoreHistoryMessagesSuccess:(array ?: @[]) playbackMessageManager:self];
    }
}

#pragma mark Timer

- (void)createTimer {
    if (_timer) {
        [self destroyTimer];
    }
    _timer = [NSTimer scheduledTimerWithTimeInterval:kIntervalTime
                                              target:[PLVFWeakProxy proxyWithTarget:self]
                                            selector:@selector(timerAction:)
                                            userInfo:nil
                                             repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:self.timer forMode:NSRunLoopCommonModes];
}

- (void)destroyTimer {
    [_timer invalidate];
    _timer = nil;
}

#pragma mark Cache FileManager

/// 创建缓存文件夹
+ (void)createCacheDirectory {
    NSString *cacheDirectory = [NSString stringWithFormat:@"%@%@", NSTemporaryDirectory(), kCacheDirectory];
    BOOL isDirectory = NO;
    BOOL exist = [[NSFileManager defaultManager] fileExistsAtPath:cacheDirectory isDirectory:&isDirectory];
    if (!exist || !isDirectory) {
        [[NSFileManager defaultManager] createDirectoryAtPath:cacheDirectory withIntermediateDirectories:YES attributes:nil error:nil];
    }
}

/// 清除缓存文件夹
+ (void)deleteCacheDirectory {
    NSString *cacheDirectory = [NSString stringWithFormat:@"%@%@", NSTemporaryDirectory(), kCacheDirectory];
    if ([[NSFileManager defaultManager] fileExistsAtPath:cacheDirectory]) {
        [[NSFileManager defaultManager] removeItemAtPath:cacheDirectory error:nil];
    }
}

/// 写入缓存
/// 存储数组list的数据到文件fileName中
+ (BOOL)cache:(NSArray *)list fileName:(NSString *)fileName {
    [PLVPlaybackMessageManager createCacheDirectory];
    
    NSString *cacheFile = [NSString stringWithFormat:@"%@%@/%@", NSTemporaryDirectory(), kCacheDirectory, fileName];
    if ([[NSFileManager defaultManager] fileExistsAtPath:cacheFile]) {
        [[NSFileManager defaultManager] removeItemAtPath:cacheFile error:nil];
    }
    
    NSString *jsonData = [PLVDataUtil jsonStringWithJSONObject:list];
    NSData *data = [jsonData dataUsingEncoding:NSUTF8StringEncoding];
    BOOL success = [data writeToFile:cacheFile atomically:YES];
    return success;
}

/// 读取缓存
- (NSArray *)playbackMessageArrayInFile:(NSString *)fileName {
    NSString *cacheFile = [NSString stringWithFormat:@"%@%@/%@", NSTemporaryDirectory(), kCacheDirectory, fileName];
    if (![[NSFileManager defaultManager] fileExistsAtPath:cacheFile]) {
        return @[];
    }
    NSData *data = [[NSFileManager defaultManager] contentsAtPath:cacheFile];
    id jsonObject = [NSJSONSerialization JSONObjectWithData:data
                                                    options:NSJSONReadingMutableContainers
                                                      error:nil];
    if (![PLVFdUtil checkArrayUseable:jsonObject]) {
        return @[];
    }
    NSArray *messageArray = (NSArray *)jsonObject;
    return messageArray;
}

/// 通过分段ID获取文件名
- (NSString *)fileNameWithIndexId:(NSInteger)indexId {
    return [NSString stringWithFormat:@"%@_%zd", self.sessionId, indexId];
}

#pragma mark - [ Event ]

#pragma mark Timer

- (void)timerAction:(NSTimer *)timer {
    if (self.currentPage < self.totalPage) {
        [self requestMessageIndexWithPage:self.currentPage + 1];
    }
    
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(currentPlaybackTimeForPlaybackMessageManager:)]) {
        NSTimeInterval time = [self.delegate currentPlaybackTimeForPlaybackMessageManager:self];
        time = MAX(0, time); // time表示回放视频当前播放时间戳，单位秒
        
        if (self.currentPlaybackTime < 0 || self.currentPlaybackTime != time) { // 尚未开始播放，或播放进度发生变化时
            self.currentPlaybackTime = time; // 更新播放进度
            
            // 更新预加载消息数组
            NSTimeInterval startTime = time;
            NSTimeInterval endTime = time + kPreloadTime * 1000;
            [self preloadMessagesFrom:startTime to:endTime];
        }
    }
}

@end
