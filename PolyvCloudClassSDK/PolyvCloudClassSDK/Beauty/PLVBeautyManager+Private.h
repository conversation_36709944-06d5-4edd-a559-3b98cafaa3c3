//
//  PLVBeautyManager+Private.h
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/3/3.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PLVBeautyManager.h"
#import "PLVBeautyResourceManager.h"

NS_ASSUME_NONNULL_BEGIN

@protocol PLVBeautyManagerDelegate;

/// 美颜管理器 (私有不对外公开)
///
/// @note 负责美颜功能的业务管理
@interface PLVBeautyManager ()

#pragma mark - [ 属性 ]
#pragma mark 可配置项
/// delegate
@property (nonatomic, weak) id <PLVBeautyManagerDelegate> delegate;
/// opengl上下文
@property (nonatomic, strong) EAGLContext *eaglContext;
/// 美颜引擎
@property (nonatomic, strong) PLVBBeautyManager *beautyEngine;

#pragma mark - [ 方法 ]
#pragma mark 基础调用
/// 创建 美颜管理器
///
/// @note 由外部根据频道信息获取到美颜类型值，并在调用此方法时传入；
///       若传入的beautyType值无效，将创建失败，并返回 nil。
///
/// @param beautyType 美颜类型值
+ (instancetype)beautyManagerWithBeautyType:(NSString *)beautyType;

/// 使用资源设置美颜SDK
- (int)setupBeautyWithResource:(PLVBeautyResourceManager *)resource;

/// 美颜处理，只支持 OpenGL 2D 纹理的输入、输出
/// @param texture 输入纹理
/// @param outputTexture 输出纹理
/// @param width 宽
/// @param height 高
/// @param rotate 算法检测角度
/// @param timeStamp 时间戳
/// @return 0:处理成功
- (int)processTexture:(GLuint)texture outputTexture:(GLuint)outputTexture width:(int)width height:(int)height rotate:(int)rotate timeStamp:(double)timeStamp;

/// 字节美颜SDK 处理 输入输出 CVPixelBufferRef 数据类型
/// @param inBuf  输入的图像数据
/// @param outBuf  输出的图像数据
///  @param rotate 算法检测角度
/// @param timeStamp 时间戳
/// @return 0:处理成功
- (int)processPixelBuffer:(CVPixelBufferRef)inBuf outBuf:(CVPixelBufferRef)outBuf rotate:(int)rotate timeStamp:(double)timeStamp;

@end

#pragma mark - [ 代理方法 ]

/// 美颜管理器代理
@protocol PLVBeautyManagerDelegate <NSObject>

- (void)plvBeautyManager:(PLVBeautyManager * _Nonnull)manager didOccurError:(NSError *)error;

@end

NS_ASSUME_NONNULL_END
