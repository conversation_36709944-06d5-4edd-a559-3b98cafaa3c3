//
//  PLVBeautyResourceManager.h
//  PLVLiveScenesSDK
//
//  Created by lijingtong on 2022/3/7.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <PLVBusinessSDK/PLVBBeautyManager.h>

NS_ASSUME_NONNULL_BEGIN
@class PLVBeautyResourceManager;
@protocol PLVBeautyResourceManagerDelegate <NSObject>

@optional


/// 从服务器更新美颜资源信息的回调
/// @param beautyResourceManager 美颜资源管理器
- (void)plvBeautyResourceManagerUpdateResourceInfoSuccecc:(PLVBeautyResourceManager *)beautyResourceManager;

/// 资源获取成功 时回调
- (void)plvBeautyResourceManagerdidGetResourceSuccecc:(PLVBeautyResourceManager *)beautyResourceManager;

/// 资源获取失败 时回调
- (void)plvBeautyResourceManager:(PLVBeautyResourceManager *)beautyResourceManager didGetResourceFail:(NSError *)error;

@end

/// 美颜资源管理类
/// note 负责资源的获取，并对其进行管理，遵守美颜sdk的资源协议，给美颜sdk提供资源
@interface PLVBeautyResourceManager : NSObject<PLVBBeautyResourceProvider>

/// 代理
@property (nonatomic, weak)id<PLVBeautyResourceManagerDelegate> delegate;

/// 单例
+ (instancetype)sharedManager;

/// 从服务器更新美颜资源的信息，更新结果通过代理回调
- (void)updateResourceInfoFromServer;

/// 资源是否准备就绪
- (BOOL)resourceIsReady;

/// 异步请求资源
- (void)asyncGetResource;

#pragma mark 资源路径

/// 模型文件路径
- (NSString *)modelDirPath;

/// 滤镜路径
/// @param filterName 滤镜名称
- (NSString *)filterPath:(NSString *)filterName;

/// 贴纸路径
/// @param stickerName 贴纸名称
- (NSString *)stickerPath:(NSString *)stickerName;

/// 特效素材路径
/// @param nodeName 特效名称
- (NSString *)composerNodePath:(NSString *)nodeName;

/// 授权文件路径
- (NSString *)licensePath;

@end

NS_ASSUME_NONNULL_END
