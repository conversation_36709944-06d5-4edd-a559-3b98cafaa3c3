//
//  PLVBeautyManager.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/1/17.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBeautyManager.h"
#import "PLVBeautyManager+Private.h"
#import "PLVConsoleLogger.h"
#import "PLVWErrorManager.h"
#import "PLVWLogReporterManager.h"

@interface PLVBeautyManager ()<PLVBBeautyManagerDelegate>

//@property (nonatomic, strong) PLVBBeautyManager *beautyEngine;

@end

@implementation PLVBeautyManager

#pragma mark - [ Life Cycle ]
- (void)dealloc{
    self.delegate = nil;
    
    /// RTC处理
    [self destroyBeautyTask];
    
    PLV_LOG_INFO(PLVConsoleLogModuleTypeBeauty, @"%s", __FUNCTION__);
}

#pragma mark - [ Public Methods ]

NSString *PLVSDKGlobalBeautySDKVersion = nil;

#pragma mark 基础调用
/// 创建 美颜管理器
+ (instancetype)beautyManagerWithBeautyType:(NSString *)beautyType {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeBeauty, @"beautyManager beautyManagerWithBeautyType"); /// 一般情况下不允许打印 beautyType
    
    if (![PLVFdUtil checkStringUseable:beautyType]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeBeauty, @"%s failed with 【params invalid】(beautyType:%@)",__FUNCTION__,beautyType);
        return nil;
    }
    
    PLVBeautyManager *manager;
    
    /// 创建 核心美颜管理器
    PLVBBeautyManager *beautyManager = [NSClassFromString(@"PLVBBeautyManager") beautyManagerWithBeautyType:beautyType];
    if (beautyManager) {
        /// 创建 美颜管理器
        manager = [[PLVBeautyManager alloc]init];
        manager.beautyEngine = beautyManager;

        beautyManager.delegate = manager;
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeBeauty, @"%s beautyManager init failed with (beautyType:%@)", __FUNCTION__, beautyType);
    }
    return manager;
}

/// 初始化美颜引擎
- (int)setupBeautyWithResource:(PLVBeautyResourceManager *)resource {
    if (!self.beautyEngine.beautyIsReady) {
        int result = [self.beautyEngine createBeautyTaskWithResourceProvider:resource];
        return result;
    }else {
        self.beautyEngine.resourceProvider = resource;
        return 0;
    }
}

#pragma mark 美颜设置
- (BOOL)isBeautyOptionSupport:(PLVBBeautyOption)option {
    return [self.beautyEngine isBeautyOptionSupport:option];
}

- (void)updateBeautyOption:(PLVBBeautyOption)option withIntensity:(CGFloat)intensity {
    [self.beautyEngine updateBeautyOption:option withIntensity:intensity];
}

- (void)removeBeautyOption:(PLVBBeautyOption)option {
    [self.beautyEngine removeBeautyOption:option];
}

- (void)clearBeautyOption {
    [self.beautyEngine clearBeautyOption];
}

- (NSArray<PLVBFilterOption *> *)getSupportFilterOptions {
    return [self.beautyEngine getSupportFilterOptions];
}

- (void)setFilterOption:(PLVBFilterOption *)option withIntensity:(CGFloat)intensity {
    [self.beautyEngine setFilterOption:option withIntensity:intensity];
}

- (int)processTexture:(GLuint)texture outputTexture:(GLuint)outputTexture width:(int)width height:(int)height rotate:(int)rotate timeStamp:(double)timeStamp {
    int ret = [self.beautyEngine processTexture:texture outputTexture:outputTexture width:width height:height rotate:rotate timeStamp:timeStamp];
    return ret;
}

/// 字节美颜sdk处理
- (int)processPixelBuffer:(CVPixelBufferRef)inBuf outBuf:(CVPixelBufferRef)outBuf rotate:(int)rotate timeStamp:(double)timeStamp{
    int ret = [self.beautyEngine processPixelBuffer:inBuf outBuf:outBuf rotate:rotate timeStamp:timeStamp];
    
    return ret;
}

#pragma mark - [ Private Methods ]
- (EAGLContext *)eaglContext {
    return self.beautyEngine.eaglContext;
}

#pragma mark Beauty
- (void)destroyBeautyTask{
    if (self.beautyEngine.beautyIsReady) {
        [self.beautyEngine destroyTask];
    }
}

#pragma mark - [ Delegate ]
#pragma mark PLVBBeautyManagerDelegate
-(void)plvbBeautyManager:(PLVBBeautyManager *)manager didOccurError:(NSError *)error {
    NSError *finalError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulBeauty code:PLVFBeautyErrorCodeBeautySDK_ProcessError];
    finalError = PLVErrorWithUnderlyingError(finalError, error);
    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:finalError.code information:nil];
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvBeautyManager:didOccurError:)]) {
        [self.delegate plvBeautyManager:self didOccurError:finalError];
    }
}

@end
