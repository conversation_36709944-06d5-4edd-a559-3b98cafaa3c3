//
//  PLVBeautyResourceManager.m
//  PLVLiveScenesSDK
//
//  Created by lijingtong on 2022/3/7.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVBeautyResourceManager.h"

// 依赖库
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import <SSZipArchive/SSZipArchive.h>

#import "PLVLivePrivateAPI.h"
#import "PLVWErrorManager.h"
#import "PLVWLogReporterManager.h"

static NSString *LICENSE_PATH = @"LicenseBag";
static NSString *COMPOSER_PATH = @"ComposeMakeup";
static NSString *FILTER_PATH = @"FilterResource";
static NSString *STICKER_PATH = @"StickerResource";
static NSString *MODEL_PATH = @"ModelResource";

static NSString *LICENSE_NAME = @"license.licbag";

static NSString *BUNDLE = @"bundle";
static NSString *RESOURCEFILE = @"BytedBeautyEffectResource";

static NSInteger MAX_RETRYNUM = 3; // 最大下载重试次数

@interface PLVBeautyResourceManager()

/// 通过接口获取的美颜信息
@property (nonatomic, copy) NSString *beautyLicenseKey;
@property (nonatomic, copy) NSString *beautyLicenseSecret;
@property (nonatomic, copy) NSString *resourceFileMD5;  // 资源md5值
@property (nonatomic, copy) NSString *resourceFileUrl;  // 资源下载地址


@property (nonatomic, assign) NSInteger retryNum; // 下载重试次数
@property (nonatomic, assign) BOOL isDownLoading; // 是否正在下载

@end

@implementation PLVBeautyResourceManager

#pragma mark - [ Public Method ]

+ (instancetype)sharedManager {
    static PLVBeautyResourceManager *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[PLVBeautyResourceManager alloc] init];
    });
    return manager;
}

- (void)updateResourceInfoFromServer {
    NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
    NSString *appBundleId = [infoDictionary objectForKey:@"CFBundleIdentifier"];
    __weak typeof(self) weakSelf = self;
    [PLVLivePrivateAPI getBeautySettingWithPackageName:appBundleId success:^(NSDictionary * _Nonnull responseObject) {
        if ([weakSelf setupBeautyInfo:responseObject]) {
            [weakSelf notifyUpdateResourceSuccecc];
        }else {
            NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulBeauty code:PLVFBeautyErrorCodeBeautySetting_DataError];
            [weakSelf notifyGetResourceFail:failError];
        }
    } failure:^(NSError * _Nonnull error) {
        [weakSelf notifyGetResourceFail:error];
    }];
}

- (BOOL)resourceIsReady {
    NSArray *fileArray = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:[self unZipArchiveResourcePath] error:nil];
    NSString *cacheMD5 = [self getCacheResourceMD5];
    if ([PLVFdUtil checkArrayUseable:fileArray] &&
        [cacheMD5 isEqualToString:self.resourceFileMD5]) {
        return YES;
    }
    return NO;
}

- (void)asyncGetResource {
    if ([self resourceIsReady]) {
        [self notifyGetResourceSuccess];
        return;
    }
    
    if (self.isDownLoading ||
        self.retryNum > MAX_RETRYNUM) {
        return;
    }
    
    if (![PLVFdUtil checkStringUseable:self.resourceFileUrl]) {
        [self reportAndCallbackForBeautyErrorCode:PLVFBeautyErrorCodeBeautyResource_ParameterError];
        return;
    }

    self.isDownLoading = YES;
    self.retryNum += 1;
    
    // 删除本地资源
    [self cleanResource];
    
    NSURLRequest *request = [NSURLRequest requestWithURL:[NSURL URLWithString:self.resourceFileUrl]];
    NSString *beautyResourceDirPath = [self beautyResourceDirPath];
    NSString *toFilePath = [beautyResourceDirPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.zip", RESOURCEFILE]];
    NSString *unZipFilePath = [self unZipArchiveResourcePath];
    
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil requestFileWithRequest:request toFilePath:toFilePath completion:^(NSString * _Nonnull filePath) {
        if ([PLVFdUtil checkStringUseable:filePath]) {
            [weakSelf unZipArchiveWithFilePath:filePath toDestination:unZipFilePath];
        }else {
            [weakSelf reportAndCallbackForBeautyErrorCode:PLVFBeautyErrorCodeBeautyResource_DataError];
        }
    } fail:^(NSError * _Nonnull error) {
        if (self.retryNum < MAX_RETRYNUM) {
            [weakSelf asyncGetResource];
        } else {
            [weakSelf reportAndCallbackForBeautyErrorCode:PLVFBeautyErrorCodeBeautyResource_CodeError];
        }
    }];
}

#pragma mark - [ Delegate ]
#pragma mark - PLVBBeautyResourceProvider

/// 模型文件路径
- (NSString *)modelDirPath {
    return [self resourceFilePath:MODEL_PATH ofType:BUNDLE];
}

/// 滤镜路径
/// @param filterName 滤镜名称
- (NSString *)filterPath:(NSString *)filterName {
    NSString *filterPath = [[self resourceFilePath:FILTER_PATH ofType:BUNDLE] stringByAppendingFormat:@"/Filter/"];
    return [filterPath stringByAppendingString:filterName];
}

/// 贴纸路径
/// @param stickerName 贴纸名称
- (NSString *)stickerPath:(NSString *)stickerName {
    NSString *stickerPath = [[self resourceFilePath:STICKER_PATH ofType:BUNDLE] stringByAppendingFormat:@"/stickers/"];
    return [stickerPath stringByAppendingString:stickerName];
}

/// 特效素材路径
/// @param nodeName 特效名称
- (NSString *)composerNodePath:(NSString *)nodeName {
    NSString *composerNodePath = [[self resourceFilePath:COMPOSER_PATH ofType:BUNDLE] stringByAppendingFormat:@"/ComposeMakeup/"];
    return [composerNodePath stringByAppendingString:nodeName];
}

/// 在线授权文件的保存路径，需要拼接授权文件的名字
- (NSString *)licensePath {
    NSString *licenseName = [NSString stringWithFormat:@"/%@", LICENSE_NAME];
    return [[self unZipArchiveResourcePath] stringByAppendingString:licenseName];
}

/// 请求在线授权文件需要的key
- (NSString *)licenseKey {
    return self.beautyLicenseKey;
}

/// 请求在线授权文件需要的Secret
-(NSString *)licenseSecret {
    return self.beautyLicenseSecret;
}

#pragma mark - [ Private Method ]

/// 设置美颜信息
- (BOOL)setupBeautyInfo:(NSDictionary *)beautyInfo {
    // 解密美颜key 和 secret
    NSString *licenseKey = beautyInfo[@"key"];
    NSString *licenseSecret = beautyInfo[@"secret"];
    NSString *nonce = beautyInfo[@"nonce"];
    NSString *packageName = beautyInfo[@"packageName"];
    if ([PLVFdUtil checkStringUseable:licenseKey] &&
        [PLVFdUtil checkStringUseable:licenseSecret] &&
        [PLVFdUtil checkStringUseable:nonce] &&
        [PLVFdUtil checkStringUseable:packageName]) {
        NSString *tempString = [NSString stringWithFormat:@"%@%@", packageName, nonce];
        tempString = [PLVDataUtil md5HexDigest:tempString];
        NSString *key = [tempString substringToIndex:16];
        NSString *iv = [tempString substringFromIndex:tempString.length - 16];
        
        NSData *licenseKeyData = [[NSData alloc] initWithBase64EncodedString:licenseKey options:NSDataBase64DecodingIgnoreUnknownCharacters];
        NSData *decryptedLicenseKeyData = [PLVDataUtil AES128DecryptData:licenseKeyData withKey:key iv:iv];
        self.beautyLicenseKey = [[NSString alloc]initWithData:decryptedLicenseKeyData encoding:NSUTF8StringEncoding];
        
        NSData *licenseSecretData = [[NSData alloc] initWithBase64EncodedString:licenseSecret options:NSDataBase64DecodingIgnoreUnknownCharacters];
        NSData *decryptedLicenseSecretData = [PLVDataUtil AES128DecryptData:licenseSecretData withKey:key iv:iv];
        self.beautyLicenseSecret = [[NSString alloc] initWithData:decryptedLicenseSecretData encoding:NSUTF8StringEncoding];
    }
    
    self.resourceFileMD5 = beautyInfo[@"materialMd5"];
    self.resourceFileUrl = beautyInfo[@"materialUrl"];
    
    if ([PLVFdUtil checkStringUseable:self.beautyLicenseKey] &&
        [PLVFdUtil checkStringUseable:self.beautyLicenseSecret] &&
        [PLVFdUtil checkStringUseable:self.resourceFileMD5] &&
        [PLVFdUtil checkStringUseable:self.resourceFileUrl]) {
        return YES;
    }
    return NO;
}

/// 读取md5缓存数据
- (NSString *)getCacheResourceMD5 {
    NSString *cacheMd5 = [[NSUserDefaults standardUserDefaults] objectForKey:@"cacheResourceMD5Key"];
    return cacheMd5 ? cacheMd5 : @"noCache";
}

///  写入md5缓存数据
- (void)saveCacheResourceMD5:(NSString *)md5String {
    [[NSUserDefaults standardUserDefaults] setObject:md5String forKey:@"cacheResourceMD5Key"];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

#pragma mark Getter

- (NSString *)beautyResourceDirPath {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *cachesPath = paths[0];
    NSString *dirPath = [NSString stringWithFormat:@"%@/beautyResource", cachesPath];
    BOOL isDirectory = NO;
    BOOL exist = [[NSFileManager defaultManager] fileExistsAtPath:dirPath isDirectory:&isDirectory];
    if (exist && isDirectory) {
        return dirPath;;
    }
    BOOL success = [[NSFileManager defaultManager] createDirectoryAtPath:dirPath withIntermediateDirectories:YES attributes:nil error:nil];
    return success ? dirPath : nil;
}

- (NSString *)unZipArchiveResourcePath {
    return [[self beautyResourceDirPath] stringByAppendingPathComponent:RESOURCEFILE];
}

// 解压
- (void)unZipArchiveWithFilePath:(NSString *)filePath toDestination:(NSString *)toDestination{
    BOOL success = [SSZipArchive unzipFileAtPath:filePath toDestination:toDestination];
    if (success) {
        [self saveCacheResourceMD5:self.resourceFileMD5];
        [self notifyGetResourceSuccess];
    } else {
        [self reportAndCallbackForBeautyErrorCode:PLVFBeautyErrorCodeResourceHandle_UnzipError];
    }
}

- (NSString *)resourceFilePath:(NSString *)fileName ofType:(nonnull NSString *)type {
    if ([PLVFdUtil checkStringUseable:fileName] &&
        [PLVFdUtil checkStringUseable:type]) {
        return [[self unZipArchiveResourcePath] stringByAppendingPathComponent:[NSString stringWithFormat:@"resource/%@.%@", fileName, type]];
    }
    return nil;
}

#pragma mark 删除本地资源

- (void)cleanResource {
    [[NSFileManager defaultManager] removeItemAtPath:[self beautyResourceDirPath] error:nil];
}

#pragma mark notify

- (void)reportAndCallbackForBeautyErrorCode:(PLVFBeautyErrorCode)ErrorCode{
    NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulBeauty code:ErrorCode];
    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:nil];
    [self notifyGetResourceFail:failError];
}

- (void)notifyGetResourceSuccess {
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvBeautyResourceManagerdidGetResourceSuccecc:)]) {
        [self.delegate plvBeautyResourceManagerdidGetResourceSuccecc:self];
    }
}

- (void)notifyGetResourceFail:(NSError *)error {
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvBeautyResourceManager:didGetResourceFail:)]) {
        [self.delegate plvBeautyResourceManager:self didGetResourceFail:error];
    }
}

- (void)notifyUpdateResourceSuccecc {
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvBeautyResourceManagerUpdateResourceInfoSuccecc:)]) {
            [self.delegate plvBeautyResourceManagerUpdateResourceInfoSuccecc:self];
        }
    })
}

@end
