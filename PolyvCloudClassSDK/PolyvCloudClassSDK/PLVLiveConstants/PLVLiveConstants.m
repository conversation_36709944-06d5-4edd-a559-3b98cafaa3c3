//
//  PLVLiveConstants.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/25.
//  Copyright © 2021 PLV. All rights reserved.
//

#import "PLVLiveConstants.h"

#pragma mark - 默认图片地址

/// 聊天室 - 默认老师头像地址
NSString *const PLVLiveConstantsChatroomTeacherAvatarURL = @"https://livestatic.polyv.net/assets/images/teacher.png";

/// 房间 - 手机设备图片地址
NSString *const PLVLiveConstantsRoomEffectDeviceIconURL = @"https://www.polyv.net/images/effect/effect-device.png";

#pragma mark - URL地址

/// 图文加载的URL
NSString *const PLVLiveConstantsLiveFrontPictureTextURL = @"https://websdk.videocc.net/interactions-receive-sdk-ui-webview/latest/tuwen.html";

/// 互动WebView URL
NSString *const PLVLiveConstantsInteractTriviaCardURL = @"https://live.polyv.net/front/trivia-card/mobile?version=6&useWKWebviewForIOS=true";

/// 新版 互动WebView URL  (协议改造)
NSString *const PLVLiveConstantsInteractNewWebViewURL = @"https://websdk.videocc.net/interactions-receive-sdk-ui-webview/latest/index.html";

/// 推流端 互动WebView URL
NSString *const PLVLiveConstantsStreamerInteractWebViewURL = @"https://websdk.videocc.net/interactions-launch-sdk-ui-webview/latest/checkin.html";

/// Document PPT文档加载HTML
NSString *const PLVLiveConstantsDocumentPPTForMobileHTML = @"https://player.polyv.net/resp/ppt-h5/latest/page/pptForMobile.html";

/// 问答模块URL
NSString *const PLVLiveConstantsWatchQAURL = @"https://websdk.videocc.net/interactions-receive-sdk-ui-webview/latest/qa.html";

/// 容器 PPT文档加载HTML
NSString *const PLVLiveConstantsContainerPPTForMobileHTML = @"https://player.polyv.net/resp/white-board-sdk/latest/web-view/small-class-mobile.html";

/// 边看边买 商品列表 HTML
NSString *const PLVLiveConstantsProductListHTML = @"https://websdk.videocc.net/interactions-receive-sdk-ui-webview/latest/product.html";

/// 直播间分享海报的基础 HTML
NSString *const PLVLiveConstantsPosterShareHTML = @"https://live.polyv.cn/invite-poster/poster.html";

/// 手机开播 直播间商品库管理 HTML
NSString *const PLVLiveConstantsStreamerProductHTML = @"https://websdk.videocc.net/interactions-launch-sdk-ui-webview/latest/product.html";
