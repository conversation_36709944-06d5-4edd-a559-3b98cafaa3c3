//
//  PLVLiveVideoChannelMenuInfo.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON>(<EMAIL>) on 2018/7/19.
//  Copyright © 2018年 plv.net. All rights reserved.
//

#import "PLVLiveVideoChannelMenuInfo.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

@interface PLVLiveVideoChannelMenu ()

@property (nonatomic, copy) NSString *menuId;
@property (nonatomic, copy) NSString *menuType;
@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *content;
@property (nonatomic, copy) NSNumber *ordered;
@property (nonatomic, assign) BOOL displayEnabled;

@end

@implementation PLVLiveVideoChannelMenu

+ (NSDictionary *)jsonValidator {
    return @{
             @"menuId":[NSString class],
             @"menuType":[NSString class],
             @"name":[NSString class],
             @"content":[NSString class],
             @"ordered":[NSNumber class]
             };
}

@end



@interface PLVLiveVideoChannelMenuInfo ()

@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *coverImage;
@property (nonatomic, copy) NSString *publisher;
@property (nonatomic, copy) NSNumber *likes;
@property (nonatomic, copy) NSNumber *pageView;
@property (nonatomic, copy) NSString *startTime;
@property (nonatomic, copy) NSString *status;
@property (nonatomic, copy) NSString *watchStatus;
@property (nonatomic, copy) NSString *splashImg;
@property (nonatomic, copy) NSString *watchUrl;
@property (nonatomic, strong) NSArray *channelMenus;


@property (nonatomic, strong) NSString *scene;
@property (nonatomic, assign) BOOL hasPlayback;
@property (nonatomic, strong) NSString *playBackEnabled;
@property (nonatomic, strong) NSString *viewerSignalEnabled;
@property (nonatomic, strong) NSString *awardTrophyEnabled;
@property (nonatomic, strong) NSString *rtcType;
@property (nonatomic, assign) BOOL pureRtcAvailState;
@property (nonatomic, assign) BOOL pureRtcEnabled;
@property (nonatomic, assign) BOOL rtcAudioSubEnabled;
@property (nonatomic, assign) BOOL restrictChatEnabled;
@property (nonatomic, copy) NSNumber *maxViewer;
@property (nonatomic, assign) BOOL chatRobotEnabled;
@property (nonatomic, assign) BOOL mobileAnchorProductEnabled;
@property (nonatomic, assign) BOOL inviteAudioEnabled;
@property (nonatomic, copy) NSNumber *linkMicLimit;
@property (nonatomic, assign) BOOL quoteReplyEnabled;
@property (nonatomic, copy) NSString *channelType;
@property (nonatomic, copy) NSString *transmitType;
@property (nonatomic, copy) NSNumber *masterChannelId;
@property (nonatomic, strong) NSDictionary *promotionInfo;
@property (nonatomic, assign) BOOL watchEventTrackEnabled;
@property (nonatomic, copy) NSString *watchLangType;
@property (nonatomic, copy) NSString *mobileAlonePushMixMode;
@property (nonatomic, copy) NSString *teacherActor;
@property (nonatomic, copy) NSString *teacherNickname;
@property (nonatomic, strong) NSDictionary *teacher;
@property (nonatomic, assign) BOOL viewerPptTurningEnabled;
@property (nonatomic, assign) BOOL playbackMultiplierEnabled;
@property (nonatomic, assign) BOOL playbackProgressBarEnabled;
@property (nonatomic, copy) NSString *playbackProgressBarOperationType;
@property (nonatomic, assign) BOOL showPlayButtonEnabled;
@property (nonatomic, assign) BOOL newMicEnabled;
@property (nonatomic, copy) NSString *defaultOpenMicLinkEnabled;
@property (nonatomic, copy) NSString *lotteryGiftButtonTextCH;
@property (nonatomic, copy) NSString *lotteryGiftButtonTextEN;
@property (nonatomic, assign) BOOL productHotEffectEnabled;
@property (nonatomic, strong) NSDictionary *productHotEffectTips;
@property (nonatomic, strong) NSDictionary *globalRtcRecordSetting;
@property (nonatomic, assign) BOOL fenestrulePlayEnabled;
@property (nonatomic, assign) BOOL productPayOrderEnabled;
@property (nonatomic, assign) BOOL pinMsgEnabled;
@property (nonatomic, assign) BOOL portraitOnlineListEnabled;
@property (nonatomic, assign) BOOL chatViewerGroupEnabled;
@property (nonatomic, assign) BOOL materialLibraryEnabled;
@property (nonatomic, strong) NSDictionary *multiMeetingSetting;
@property (nonatomic, assign) BOOL multiMeetingEnabled;
@property (nonatomic, copy) NSString *mainChannelId;
@property (nonatomic, assign) BOOL isSubChannel;
@property (nonatomic, copy) NSString *multiMeetingOrderType;
@property (nonatomic, copy) NSString *multiMeetingListLayout;
@property (nonatomic, strong) NSDictionary *watchThemeModel;
@property (nonatomic, assign) BOOL showMixLayoutButtonEnabled;
@property (nonatomic, assign) BOOL showHVScreenButtonEnabled;
@property (nonatomic, copy) NSString *defaultScreenOrientation;

@end

@implementation PLVLiveVideoChannelMenuInfo
    
- (void)setValue:(id)value forKey:(NSString *)key {
    [super setValue:value forKey:key]; // 必须调用父类方法
    
    if ([key isEqualToString:@"channelMenus"]) { // 特殊字符处理
        NSMutableArray * subArr = [NSMutableArray array];
        for (NSDictionary * dic in value) {
            PLVLiveVideoChannelMenu * menu = [[PLVLiveVideoChannelMenu alloc] initWithDictionary:dic];
            if (menu) {
                [subArr addObject:menu];
                if ([menu.menuType isEqualToString:@"members"] && menu.displayEnabled) {
                    self.portraitOnlineListEnabled = YES;
                }
            }
        }
        self.channelMenus = subArr;
    } else if ([key isEqualToString:@"promotionInfo"]) {
        if (value && [value isKindOfClass:[NSDictionary class]]) {
            self.promotionInfo = (NSDictionary *)value;
            self.watchEventTrackEnabled = PLV_SafeBoolForDictKey(self.promotionInfo, @"watchEventTrackEnabled");
        }
    } else if ([key isEqualToString:@"teacher"]) {
        if (value && [value isKindOfClass:[NSDictionary class]]) {
            self.teacher = (NSDictionary *)value;
            self.teacherActor = PLV_SafeStringForDictKey(self.teacher, @"actor");
            self.teacherNickname = PLV_SafeStringForDictKey(self.teacher, @"nickname");
        }
    } else if ([key isEqualToString:@"globalRtcRecordSetting"]) {
        if (value && [value isKindOfClass:[NSDictionary class]]) {
            self.globalRtcRecordSetting = (NSDictionary *)value;
            self.fenestrulePlayEnabled = PLV_SafeBoolForDictKey(value, @"fenestrulePlayEnabled");
        }
    } else if ([key isEqualToString:@"multiMeetingSetting"]) {
        if (value && [value isKindOfClass:[NSDictionary class]]) {
            self.multiMeetingSetting = (NSDictionary *)value;
            self.mainChannelId = PLV_SafeStringForDictKey(self.multiMeetingSetting, @"mainChannelId");
            self.isSubChannel = PLV_SafeBoolForDictKey(self.multiMeetingSetting, @"isSubChannel");
            self.multiMeetingEnabled = PLV_SafeBoolForDictKey(self.multiMeetingSetting, @"multiMeetingEnabled");
            self.multiMeetingListLayout = PLV_SafeStringForDictKey(self.multiMeetingSetting, @"multiMeetingListLayout");
            self.multiMeetingOrderType = PLV_SafeStringForDictKey(self.multiMeetingSetting, @"multiMeetingOrderType");
        }
    } else if ([key isEqualToString:@"watchThemeModel"]) {
        if (value && [value isKindOfClass:[NSDictionary class]]) {
            self.watchThemeModel = (NSDictionary *)value;
        }
    }
}

+ (NSDictionary *)jsonConvertor {
    return @{@"playbackEnabled":@"playBackEnabled", @"masterStream": @"mainRoomStream"};
}
    
+ (NSDictionary *)jsonValidator {
    return @{
             @"name":[NSString class],
             @"coverImage":[NSString class],
             @"publisher":[NSString class],
             @"likes":[NSNumber class],
             @"pageView":[NSNumber class],
             @"startTime":[NSString class],
             @"status":[NSString class],
             @"watchStatus":[NSString class],
             @"channelMenus":@[],
             @"scene":[NSString class],
             @"hasPlayback":[NSNumber class],
             @"playbackEnabled":[NSString class],
             @"splashImg":[NSString class]
//             @"viewerSignalEnabled":[NSString class],
//             @"awardTrophyEnabled":[NSString class]
             };
}

- (BOOL)watchNoDelay{
    return self.pureRtcAvailState && [self.rtcType isEqualToString:@"urtc"] && self.pureRtcEnabled;
}

- (BOOL)watchQuickLive {
    return self.pureRtcAvailState && [self.rtcType isEqualToString:@"trtc"] && self.quickLiveEnabled;
}

- (BOOL)rtcAudioSubEnabled{
    if (!self.watchNoDelay) {
        return NO;
    }else{
        return _rtcAudioSubEnabled;
    }
}

- (BOOL)watchPublicStream {
    return self.pureRtcAvailState && [self.rtcType isEqualToString:@"volc"] && self.publicStreamEnabled;
}

- (BOOL)publicStreamEnabled {
    return YES;
}

- (NSString *)splashImg {
    NSString *fullURLString = _splashImg;
    if ([fullURLString hasPrefix:@"//"]) {
        fullURLString = [@"https:" stringByAppendingString:fullURLString];
    }
    return fullURLString;
}

- (BOOL)restrictChatEnabled {
    if (self.chatRobotEnabled) {
        return NO;
    } else {
        return _restrictChatEnabled;
    }
}

- (BOOL)transmitMode {
    if (self.transmitType &&
        [self.transmitType isKindOfClass:[NSString class]] &&
        [self.transmitType isEqualToString:@"double"]) {
        return YES;
    } else {
        return NO;
    }
}

- (BOOL)mainRoom {
    if (self.channelType &&
        [self.channelType isKindOfClass:[NSString class]] &&
        [self.channelType isEqualToString:@"transmit"]) {
        return YES;
    } else {
        return NO;
    }
}

- (NSString *)mainRoomChannelId {
    if (self.masterChannelId && self.masterChannelId > 0) {
        return [NSString stringWithFormat:@"%@", self.masterChannelId];
    } else {
        return @"";
    }
}

@end
