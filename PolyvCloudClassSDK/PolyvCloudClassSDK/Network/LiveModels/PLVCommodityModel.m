//
//  PLVCommodityModel.m
//  PLVLiveScenesSDK
//
//  Created by ftao on 2020/6/29.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVCommodityModel.h"
#import <PLVFoundationSDK/PLVFdUtil.h>

@implementation PLVCommodityModel

+ (instancetype)commodityModelWithDict:(NSDictionary *)dict {
    if (![dict isKindOfClass:NSDictionary.class]) {
        return nil;
    }
    
    PLVCommodityModel *model = [[PLVCommodityModel alloc] init];
    if (model) {
        model.productId = PLV_SafeIntegerForDictKey(dict, @"productId");
        model.showId = PLV_SafeIntegerForDictKey(dict, @"showId");
        model.rank = PLV_SafeIntegerForDictKey(dict, @"rank");
        model.name = PLV_SafeStringForDictKey(dict, @"name");
        model.cover = PLV_SafeStringForDictKey(dict, @"cover");
        model.status = PLV_SafeIntegerForDictKey(dict, @"status");
        model.linkType = PLV_SafeIntegerForDictKey(dict, @"linkType");
        model.link = PLV_SafeStringForDictKey(dict, @"link");
        model.mobileAppLink = PLV_SafeStringForDictKey(dict, @"mobileAppLink");
        model.price = [PLVCommodityModel numberToString:dict[@"price"]];
        model.realPrice = [PLVCommodityModel numberToString:dict[@"realPrice"]];
        model.priceType = PLV_SafeStringForDictKey(dict, @"priceType");
        model.customPrice = PLV_SafeStringForDictKey(dict, @"customPrice");
        model.features = PLV_SafeStringForDictKey(dict, @"features");
        model.featureArray = [PLVCommodityModel arrayFromJSONString:model.features];
        model.yield = PLV_SafeStringForDictKey(dict, @"yield");
        model.productType = PLV_SafeStringForDictKey(dict, @"productType");
        model.productDesc = PLV_SafeStringForDictKey(dict, @"productDesc");
        model.btnShow = PLV_SafeStringForDictKey(dict, @"btnShow");
        model.productPushRule = PLV_SafeStringForDictKey(dict, @"productPushRule");
        model.buyType = PLV_SafeStringForDictKey(dict, @"buyType");
        model.deliveryType = PLV_SafeStringForDictKey(dict, @"deliveryType");
        model.logId = PLV_SafeStringForDictKey(dict, @"logId");
        model.params = [PLVCommodityModel dictionaryFromJSONString:PLV_SafeStringForDictKey(dict, @"params")];
        model.formattedLink = [PLVCommodityModel formattedLinkForCommodity:model];
        model.openPriceEnable = [PLV_SafeStringForDictKey(dict, @"openPriceEnable") isEqualToString:@"Y"];
    }
    return model;
}

+ (NSString *)formattedLinkForCommodity:(PLVCommodityModel *)model {
    NSString *linkURLString = nil;
    if (model.linkType == 10) { // 通用链接
        linkURLString = model.link;
    } else if (model.linkType == 11) { // 多平台链接
        linkURLString = model.mobileAppLink;
    }
    if ([PLVFdUtil checkStringUseable:linkURLString]) {
        NSURL *linkURL = [NSURL URLWithString:linkURLString];
        if (linkURL && !linkURL.scheme) {
            linkURLString = [@"https://" stringByAppendingString:linkURL.absoluteString];
        }
    }
    
    return linkURLString;
}

/// 解决NSNumber转NSString造成浮点数精度丢失的问题
+ (NSString *)numberToString:(NSNumber *)number {
    double numberDouble = [number doubleValue];
    NSString *numberString = [NSString stringWithFormat:@"%f", numberDouble];
    NSString *tagString = [NSString stringWithFormat:@"%@", [NSDecimalNumber decimalNumberWithString:numberString]];
    return tagString;
}

+ (NSArray *)arrayFromJSONString:(NSString *)jsonString {
    if (![PLVFdUtil checkStringUseable:jsonString]) {
        return nil;
    }
    
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSArray *features = [NSJSONSerialization JSONObjectWithData:jsonData
                                                    options:NSJSONReadingMutableContainers
                                                    error:&err];
    if (err || ![PLVFdUtil checkArrayUseable:features]) {
        return nil;
    }
    
    return features;
}

+ (NSDictionary *)dictionaryFromJSONString:(NSString *)jsonString {
    if (![PLVFdUtil checkStringUseable:jsonString]) {
        return nil;
    }
    
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSDictionary *parames = [NSJSONSerialization JSONObjectWithData:jsonData
                                                    options:NSJSONReadingMutableContainers
                                                    error:&err];
    if (err || ![PLVFdUtil checkDictionaryUseable:parames]) {
        return nil;
    }
    
    return parames;
}

@end
