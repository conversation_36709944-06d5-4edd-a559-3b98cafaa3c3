//
//  PLVPlaybackListModel.m
//  PLVLiveScenesSDK
//
//  Created by ftao on 2020/8/11.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVPlaybackListModel.h"

@implementation PLVPlaybackVideoModel

- (void)setValue:(id)value forKey:(NSString *)key {
    if (value && ![key isEqualToString:@"seed"]) {
        // 将vlue不为nil（null）的值转为字符串类型（seek属性除外）
        value = [NSString stringWithFormat:@"%@",value];
    }
    [super setValue:value forKey:key];
}

@end

@implementation PLVPlaybackListModel

- (void)setValue:(id)value forKey:(NSString *)key {
    if ([key isEqualToString:@"contents"] && [value isKindOfClass:NSArray.class]) {
        NSMutableArray *mArr = [NSMutableArray array];
        for (NSDictionary *dic in value) {
            PLVPlaybackVideoModel *video = [[PLVPlaybackVideoModel alloc] initWithDictionary:dic];
            if (video) {
                [mArr addObject:video];
            }
        }
        value = mArr;
    }
    
    [super setValue:value forKey:key];
}

@end
