//
//  PLVChannelPlaybackInfoModel.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2022/4/27.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVChannelPlaybackInfoModel.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

@interface PLVLiveRecordFileModel ()

@end

@implementation PLVLiveRecordFileModel

- (void)setValue:(id)value forKey:(NSString *)key {
    if (value) {
        value = [NSString stringWithFormat:@"%@",value];
        if ([key isEqualToString:@"duration"]) {
            value = [NSString stringWithFormat:@"%f",[PLVFdUtil secondsToTimeInterval:value]];
        }
    }
    
    [super setValue:value forKey:key];
}

@end

@interface PLVTargetPlaybackVideoModel ()

@end

@implementation PLVTargetPlaybackVideoModel

- (void)setValue:(id)value forKey:(NSString *)key {
    if (value) {
        value = [NSString stringWithFormat:@"%@",value];
        if ([key isEqualToString:@"duration"]) {
            value = [NSString stringWithFormat:@"%f",[PLVFdUtil secondsToTimeInterval:value]];
        }
    } else {
        if (!value && [key isEqualToString:@"seed"]) {
            // seek属性将vlue为nil（null）的值转为0
            value = @(0);
        }
    }
    
    [super setValue:value forKey:key];
}

@end

@interface PLVChannelPlaybackInfoModel ()

@property (nonatomic, assign) BOOL hasPlaybackVideo;
@property (nonatomic, assign) BOOL hasRecordFile;
@property (nonatomic, assign) BOOL enablePlayBack;
@property (nonatomic, copy) NSString *playbackOrigin;
@property (nonatomic, assign) BOOL vodUserStatus;
@property (nonatomic, copy) NSString *sectionEnabled;
@property (nonatomic, strong) PLVLiveRecordFileModel *recordFile;
@property (nonatomic, strong) PLVTargetPlaybackVideoModel *targetPlaybackVideo;
@property (nonatomic, copy) NSString *vodUrl;
@property (nonatomic, copy) NSString *vodSessionId;
@property (nonatomic, copy) NSString *targetSessionId;
@property (nonatomic, copy) NSString *type;
@property (nonatomic, copy) NSString *playbackType;

@end

@implementation PLVChannelPlaybackInfoModel

- (void)setValue:(id)value forKey:(NSString *)key {
    [super setValue:value forKey:key];
    if ( value && [key isEqualToString:@"recordFile"] ) {
        PLVLiveRecordFileModel *recordFile = [[PLVLiveRecordFileModel alloc] initWithDictionary:value];
        self.recordFile = recordFile;
    } else if ( value && [key isEqualToString:@"targetPlaybackVideo"] ) {
        PLVTargetPlaybackVideoModel *targetPlaybackVideo = [[PLVTargetPlaybackVideoModel alloc] initWithDictionary:value];
        self.targetPlaybackVideo = targetPlaybackVideo;
    }
}

@end
