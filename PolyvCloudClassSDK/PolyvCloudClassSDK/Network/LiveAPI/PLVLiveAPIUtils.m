//
//  PLVLiveAPIUtils.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2019/9/24.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVLiveAPIUtils.h"

#import "PLVLiveVideoConfig.h"
#import "PLVLiveHttpDnsManager.h"
#import "PLVLiveVideoConfig+PrivateInfo.h"

@implementation PLVLiveAPIUtils

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:nil uppercase:YES SHA256:NO signatureNonce:NO encrypt:NO requestEncrypt:NO plainBlock:nil httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:YES SHA256:NO signatureNonce:NO encrypt:NO requestEncrypt:NO plainBlock:nil httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                             plainBlock:(nullable NSString* (^)(NSString *timestamp))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:YES SHA256:NO signatureNonce:NO encrypt:NO requestEncrypt:NO plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        if (plainBlock) {
            return plainBlock(timestamp);
        } else {
            return nil;
        }
    } httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                         requestEncrypt:(BOOL)requestEncrypt
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:YES SHA256:NO signatureNonce:NO encrypt:NO requestEncrypt:requestEncrypt plainBlock:nil httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                              uppercase:(BOOL)uppercase
                             plainBlock:(nullable NSString* (^)(NSString *timestamp))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:uppercase SHA256:NO signatureNonce:NO encrypt:NO requestEncrypt:NO plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        if (plainBlock) {
            return plainBlock(timestamp);
        } else {
            return nil;
        }
    } httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                                 SHA256:(BOOL)SHA256
                             plainBlock:(nullable NSString* (^)(NSString *timestamp))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:YES SHA256:SHA256 signatureNonce:NO encrypt:NO requestEncrypt:NO plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        if (plainBlock) {
            return plainBlock(timestamp);
        } else {
            return nil;
        }
    } httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                              uppercase:(BOOL)uppercase
                                 SHA256:(BOOL)SHA256
                             plainBlock:(nullable NSString* (^)(NSString *timestamp))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:uppercase SHA256:SHA256 signatureNonce:NO encrypt:NO requestEncrypt:NO plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        if (plainBlock) {
            return plainBlock(timestamp);
        } else {
            return nil;
        }
    } httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                params:(nullable NSDictionary *)params
                                SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:YES SHA256:SHA256 signatureNonce:signatureNonce encrypt:NO requestEncrypt:NO plainBlock:nil httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                params:(nullable NSDictionary *)params
                             uppercase:(BOOL)uppercase
                                SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:uppercase SHA256:SHA256 signatureNonce:signatureNonce encrypt:NO requestEncrypt:NO plainBlock:nil httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                                 SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:YES SHA256:SHA256 signatureNonce:signatureNonce encrypt:NO requestEncrypt:NO plainBlock:plainBlock httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                                 SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:YES SHA256:SHA256 signatureNonce:signatureNonce encrypt:encrypt requestEncrypt:NO plainBlock:plainBlock httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                                 SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                              forceMode:(BOOL)forceMode
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:YES SHA256:SHA256 signatureNonce:signatureNonce encrypt:encrypt requestEncrypt:NO forceMode:forceMode plainBlock:plainBlock httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                                 SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                         requestEncrypt:(BOOL)requestEncrypt
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:YES SHA256:SHA256 signatureNonce:signatureNonce encrypt:encrypt requestEncrypt:requestEncrypt plainBlock:plainBlock httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                                 SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                         requestEncrypt:(BOOL)requestEncrypt
                              forceMode:(BOOL)forceMode
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:YES SHA256:SHA256 signatureNonce:signatureNonce encrypt:encrypt requestEncrypt:requestEncrypt forceMode:forceMode plainBlock:plainBlock httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                              uppercase:(BOOL)uppercase
                                 SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:uppercase SHA256:SHA256 signatureNonce:signatureNonce encrypt:encrypt requestEncrypt:NO plainBlock:plainBlock httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                              uppercase:(BOOL)uppercase
                                 SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                         requestEncrypt:(BOOL)requestEncrypt
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest *request = [self requestWithURL:url params:params uppercase:uppercase SHA256:SHA256 signatureNonce:signatureNonce encrypt:encrypt requestEncrypt:requestEncrypt forceMode:NO plainBlock:plainBlock httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                params:(nullable NSDictionary *)params
                             uppercase:(BOOL)uppercase
                                SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                         requestEncrypt:(BOOL)requestEncrypt
                              forceMode:(BOOL)forceMode
                            plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod {
    NSMutableURLRequest * request = [self requestWithURL:url params:params signConfig:^(PLVLiveSignCreator * _Nonnull signConfig) {
        signConfig.uppercase = uppercase;
        signConfig.SHA256 = SHA256;
        signConfig.signatureNonce = signatureNonce;
        signConfig.forceMode = forceMode;
        signConfig.encrypt = encrypt;
        signConfig.requestEncrypt = requestEncrypt;
    } plainBlock:plainBlock httpMethod:httpMethod];
    return request;
}

+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                             signConfig:(void(^ _Nullable)(PLVLiveSignCreator *signConfig))signConfigHandler
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                            httpMethod:(nonnull NSString *)httpMethod {
    PLVLiveSignCreator *signConfig = [[PLVLiveSignCreator alloc] init];
    signConfigHandler ? signConfigHandler(signConfig) : nil;
    
    BOOL configEnableSha256 = signConfig.forceMode ? [PLVLiveVideoConfig sharedInstance].enableSha256 : [PLVLiveVideoConfig sharedInstance].realEnableSha256;
    BOOL configEnableSignatureNonce = signConfig.forceMode ? [PLVLiveVideoConfig sharedInstance].enableSignatureNonce : [PLVLiveVideoConfig sharedInstance].realEnableSignatureNonce;
    BOOL configEnableResponseEncrypt = signConfig.forceMode ? [PLVLiveVideoConfig sharedInstance].enableResponseEncrypt : [PLVLiveVideoConfig sharedInstance].realEnableResponseEncrypt;
    BOOL configEnableRequestEncrypt = signConfig.forceMode ? [PLVLiveVideoConfig sharedInstance].enableRequestEncrypt : [PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt;
    
    NSMutableDictionary *signParams = [NSMutableDictionary dictionaryWithCapacity:5];
    if (plainBlock) {
        NSString *timestamp = [PLVFdUtil curTimeStamp];
        [signParams setObject:timestamp forKey:@"timestamp"];
        
        if (signConfig.SHA256 && configEnableSha256) {
            [signParams setObject:@"SHA256" forKey:@"signatureMethod"];
        }
        
        if (signConfig.signatureNonce && configEnableSignatureNonce) {
            NSString *signatureNonceString = [[NSUUID UUID] UUIDString];
            [signParams setObject:signatureNonceString forKey:@"signatureNonce"];
        }
        
        if (signConfig.encrypt && configEnableResponseEncrypt) {
            if (signConfig.encryptResponseType == PLVEncryptType_SM2) {
                [signParams setObject:@(2) forKey:@"encryptResponseType"];
            } else {
                [signParams setObject:@(1) forKey:@"encryptResponseType"];
            }
        }
        
        NSMutableDictionary *plainParams = [NSMutableDictionary dictionaryWithDictionary:signParams];
        if (!signConfig.signSplicedURL) {
            [plainParams addEntriesFromDictionary:params];
        }
        NSString *sign = plainBlock(timestamp, [plainParams copy]);
        if (signConfig.SHA256 && configEnableSha256) {
            sign = [PLVDataUtil sha256String:sign];
        } else {
            sign = [PLVDataUtil md5HexDigest:sign];
        }
        if (sign) {
            if (signConfig.uppercase) {
                sign = [sign uppercaseString];
            }
            [signParams setObject:sign forKey:@"sign"];
        }
    }
    
    NSMutableDictionary *paramDict = [NSMutableDictionary dictionaryWithDictionary:params];
    if (signConfig.signSplicedURL) {
        NSString *urlParamString = [PLVFNetworkUtil paramStr:signParams];
        if ([url containsString:@"?"]) {
            url = [url stringByAppendingFormat:@"&%@", urlParamString];
        } else {
            url = [url stringByAppendingFormat:@"?%@", urlParamString];
        }
    } else {
        [paramDict addEntriesFromDictionary:signParams];
    }
    
    NSURL * urlObj = [NSURL URLWithString:url];
    BOOL needHttpDNS = ![urlObj.scheme isEqualToString:@"https"] && [PLVLiveVideoConfig sharedInstance].enableHttpDNS && ![PLVLiveVideoConfig sharedInstance].enableIPV6;
    BOOL useIp = NO;
    
    if (needHttpDNS) { // 需使用HttpDNS
        NSString * httpDnsUrl = [self getHttpDNSWithURLString:url];
        if (httpDnsUrl && httpDnsUrl.length > 0) {
            url = httpDnsUrl;
            useIp = YES;
        }
    }
    
    NSMutableURLRequest * request;
    if (signConfig.requestEncrypt && configEnableRequestEncrypt &&
        httpMethod && [httpMethod isEqualToString:PLV_HM_POST]) {
        NSMutableDictionary *postParams = [NSMutableDictionary dictionary];
        BOOL shouldExternalEncrypt = NO;
        if ([url containsString:@"?appId="] || [url containsString:@"&appId="]) {
            shouldExternalEncrypt = YES;
        }
        for (NSString *key in paramDict.allKeys) {
            if ([key isEqualToString:@"appId"]) {
                if ([url containsString:@"?"]) {
                    url = [url stringByAppendingFormat:@"&appId=%@", paramDict[@"appId"]];
                } else {
                    url = [url stringByAppendingFormat:@"?appId=%@", paramDict[@"appId"]];
                }
                shouldExternalEncrypt = YES;
            } else {
                [postParams setObject:paramDict[key] forKey:key];
            }
        }
        NSString *xbodyString = [self jsonStringWithJSONObject:postParams];
        NSDictionary *headerParams = headerParams = @{@"x-e-type":@"1"};
        if (signConfig.encryptRequestType == PLVEncryptType_SM2) {
            headerParams = @{@"x-e-type":@"2"};
            if (shouldExternalEncrypt) {
                xbodyString = [PLVSM2Util formatEncryptString:xbodyString publicKey:[PLVFSignConfig sharedInstance].serverSM2PublicKey];
            } else {
                xbodyString = [PLVSM2Util formatEncryptString:xbodyString publicKey:[PLVKeyUtil getApiUtilsPublicKey]];
            }
        } else {
            if (shouldExternalEncrypt) {
                xbodyString = [[PLVDataUtil AES256EncryptData:[xbodyString dataUsingEncoding:NSUTF8StringEncoding] withKey:[PLVLiveVideoConfig sharedInstance].appSecret iv:[PLVKeyUtil getApiUtilsIv]] base64EncodedStringWithOptions:0];
            } else {
                xbodyString = [[PLVDataUtil AES128EncryptData:[xbodyString dataUsingEncoding:NSUTF8StringEncoding] withKey:[PLVKeyUtil getApiUtilsKey] iv:[PLVKeyUtil getApiUtilsIv]] base64EncodedStringWithOptions:0];
            }
        }
        if (xbodyString && xbodyString.length > 0) {
            NSString *xbody = [NSString stringWithFormat:@"xbody=%@",xbodyString];
            NSString *charactersToEscape = @"+";
            NSCharacterSet *allowedCharacters = [[NSCharacterSet characterSetWithCharactersInString:charactersToEscape] invertedSet];
            xbody = [xbody stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
            request = [PLVFNetworkUtil requestWithURLString:url params:nil httpMethod:httpMethod timeoutInterval:12.0 userAgent:[PLVLiveVideoConfig userAgent] headerParams:headerParams];
            [request setHTTPBody:[xbody dataUsingEncoding:NSUTF8StringEncoding]];
        } else {
            request = [PLVFNetworkUtil requestWithURLString:url params:postParams httpMethod:httpMethod timeoutInterval:12.0 userAgent:[PLVLiveVideoConfig userAgent]];
        }
    } else {
        request = [PLVFNetworkUtil requestWithURLString:url params:paramDict httpMethod:httpMethod timeoutInterval:12.0 userAgent:[PLVLiveVideoConfig userAgent]];
    }
    
    if (needHttpDNS && useIp) { // 需使用HttpDNS
        [request setValue:urlObj.host forHTTPHeaderField:@"host"];
    }
    return request;
}

/// 把字典数据按照key的字母排序拼接成key1value1key2value2的字符串，并前后拼上[PLVLiveVideoConfig sharedInstance].appSecret
+ (NSString *)createStringWithParamDict:(NSDictionary *)paramsDict {
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    return [self createStringWithParamDict:paramsDict appsecret:appSecret];
}

/// 把字典数据按照key的字母排序拼接成key1value1key2value2的字符串，并前后拼上参数appsecret
+ (NSString *)createStringWithParamDict:(NSDictionary *)paramsDict appsecret:(NSString *)appsecret {
    if (![PLVFdUtil checkDictionaryUseable:paramsDict]) {
        return @"";
    }
    
    if (![PLVFdUtil checkStringUseable:appsecret]) {
        return @"";
    }
    
    NSArray *keys = [paramsDict allKeys];
    NSArray *sortedArray = [keys sortedArrayUsingComparator:^NSComparisonResult(id obj1, id obj2) {
        return [obj1 compare:obj2 options:NSNumericSearch];
    }];
    
    NSMutableString *signString = [NSMutableString string];
    for (NSString *keyString in sortedArray) {
        id value = paramsDict[keyString];
        if (![self isStringEmpty:value]) {
            [signString appendFormat:@"%@%@", keyString, (NSString *)value];
        } else if (value && [value isKindOfClass:[NSNumber class]]) {
            NSNumber *valueNumber = (NSNumber *)value;
            [signString appendFormat:@"%@%zd", keyString, [valueNumber integerValue]];
        }
    }
    NSString *sign = [appsecret stringByAppendingFormat:@"%@%@",signString, appsecret];
    return sign;
}

/// 获取域名对应ip
+ (NSString *)getIPAddrWithHost:(NSString *)host{
    PLVLiveHttpDnsManager * manager = [PLVLiveHttpDnsManager sharedManager];
    return [manager getIpByHostAsyncInURLFormat:host];
}

/// 获取httpdns播放地址（ip替换host）
+ (NSString *)getHttpDNSWithURLString:(NSString *)urlString{
    if (urlString && [urlString isKindOfClass:NSString.class] && urlString.length > 0) {
        if ([PLVLiveVideoConfig sharedInstance].enableHttpDNS && ![PLVLiveVideoConfig sharedInstance].enableIPV6) {
            NSString * host = [NSURL URLWithString:urlString].host;
            NSString * ip = [self getIPAddrWithHost:host];
            if (ip && ip.length > 0) {
                NSString * ipURL = [urlString stringByReplacingOccurrencesOfString:host withString:ip];
                PLV_LOG_DEBUG(PLVConsoleLogModuleTypeNetwork, @"%@ -> %@",host,ip);
                return ipURL;
            }
        }
    }
    return @"";
}

/// 获取无换行无空格的jsonString
+ (NSString *)jsonStringWithJSONObject:(id)jsonObject {
    NSError *error;
    NSMutableString *mutStr = nil;
    NSData *jsonData;
    if (@available(iOS 11.0, *)) {
        jsonData = [NSJSONSerialization dataWithJSONObject:jsonObject options:NSJSONWritingSortedKeys error:&error];
    } else {
        jsonData = [NSJSONSerialization dataWithJSONObject:jsonObject options:kNilOptions error:&error];
    }
    if (jsonData) {
    NSString *jsonString = [[NSString alloc]initWithData:jsonData encoding:NSUTF8StringEncoding];
        mutStr = [NSMutableString stringWithString:jsonString];
    }
    return mutStr ? [mutStr copy] : nil;
}

+ (BOOL)isStringEmpty:(NSString *)str {
    if (![PLVFdUtil checkStringUseable:str]) {
        return YES;
    } else {
        NSCharacterSet *set = [NSCharacterSet whitespaceAndNewlineCharacterSet];
        NSString *trimedString = [str stringByTrimmingCharactersInSet:set];
        if ([trimedString length] == 0) {
            return YES;
        } else {
            return NO;
        }
    }
}

@end

@implementation PLVLiveSignCreator

- (instancetype)init {
    if (self = [super init]) {
        _encryptRequestType = [PLVLiveVideoConfig sharedInstance].realEncryptType;
        _encryptResponseType = [PLVLiveVideoConfig sharedInstance].realEncryptType;
    }
    return self;
}

- (void)setForceMode:(BOOL)forceMode {
    _forceMode = forceMode;
    if (forceMode) {
        _encryptRequestType = [PLVFSignConfig sharedInstance].encryptType;
        _encryptResponseType = [PLVFSignConfig sharedInstance].encryptType;
    }
}

@end
