//
//  PLVLiveVClassAPI+Private.h
//  PLVCloudClassSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/8/9.
//  Copyright © 2021 PLV. All rights reserved.
//

@class PLVLinkMicGetTokenModel;

NS_ASSUME_NONNULL_BEGIN

@interface PLVLiveVClassAPI (Private)

/// 讲师端获取RTC连麦配置
/// @param model 获取连麦Token数据模型
/// @param successHandler 调用成功时，触发该block
/// @param failureHandler 调用失败时，触发该block
+ (void)teacherRTCTokenWithModel:(PLVLinkMicGetTokenModel *)model
                         success:(void (^)(NSString *responseString))successHandler
                         failure:(void (^)(NSError *error))failureHandler;


/// 观看端获取RTC连麦配置
/// @param model 获取连麦Token数据模型
/// @param successHandler 调用成功时，触发该block
/// @param failureHandler 调用失败时，触发该block
+ (void)watcherRTCTokenWithModel:(PLVLinkMicGetTokenModel *)model
                         success:(void (^)(NSString *responseString))successHandler
                         failure:(void (^)(NSError *error))failureHandler;

@end

NS_ASSUME_NONNULL_END
