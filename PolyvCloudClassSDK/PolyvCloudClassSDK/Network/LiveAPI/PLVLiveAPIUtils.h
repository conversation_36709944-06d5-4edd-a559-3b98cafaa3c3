//
//  PLVLiveAPIUtils.h
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2019/9/24.
//  Copyright © 2019 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <PLVFoundationSDK/PLVFoundationSDK.h>

NS_ASSUME_NONNULL_BEGIN

@class PLVLiveSignCreator;
/// 网络请求公用方法
@interface PLVLiveAPIUtils : NSObject

/**
 创建无需签名的接口
 @param url 请求Url
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                             httpMethod:(nonnull NSString *)httpMethod;
/**
 创建无需签名的接口
 @param url 请求Url
 @param params 携带参数
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                             httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名的接口，签名默认MD5加密、大写，默认不开启防重放
 @param url 请求Url
 @param params 携带参数
 @param plainBlock 加密Block，需返回拼接签名串
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                             plainBlock:(nullable NSString* (^)(NSString *timestamp))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod;

/**
 创建无需签名的接口
 @param url 请求Url
 @param requestEncrypt 请求加密 YES-加密 NO-不加
 @param params 携带参数
 @param httpMethod 请求方式
密
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                         requestEncrypt:(BOOL)requestEncrypt
                             httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名的接口，签名默认MD5加密，默认不开启防重放
 @param url 请求Url
 @param params 携带参数
 @param uppercase sign字段是否大写
 @param plainBlock 加密Block，需返回拼接签名串
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                              uppercase:(BOOL)uppercase
                             plainBlock:(nullable NSString* (^)(NSString *timestamp))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名的接口，签名默认大写，默认不开启防重放
 @param url 请求Url
 @param params 携带参数
 @param SHA256 sign字段加密方式，YES-SHA256加密 NO-MD5加密
 @param plainBlock 加密Block，需返回拼接签名串
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                                 SHA256:(BOOL)SHA256
                             plainBlock:(nullable NSString* (^)(NSString *timestamp))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod;


 /**
  创建需要签名的接口，默认不开启防重放
  @param url 请求Url
  @param params 携带参数
  @param uppercase sign字段是否大写
  @param SHA256 sign字段加密方式，YES-SHA256加密 NO-MD5加密  
  @param plainBlock 加密Block，需返回拼接签名串
  @param httpMethod 请求方式
  @return Request对象
  */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                              uppercase:(BOOL)uppercase
                                 SHA256:(BOOL)SHA256
                             plainBlock:(nullable NSString* (^)(NSString *timestamp))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名的接口，自动使用前后拼接appsecret进行签名，签名默认大写
 @param url 请求Url
 @param params 携带参数
 @param SHA256 sign字段加密方式，YES-SHA256加密 NO-MD5加密
 @param signatureNonce 防重放 YES-开启 NO-关闭
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                params:(nullable NSDictionary *)params
                                SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                            httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名的接口，自动使用前后拼接appsecret进行签名
 @param url 请求Url
 @param params 携带参数
 @param uppercase sign字段是否大写
 @param SHA256 sign字段加密方式，YES-SHA256加密 NO-MD5加密
 @param signatureNonce 防重放 YES-开启 NO-关闭
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                params:(nullable NSDictionary *)params
                             uppercase:(BOOL)uppercase
                                SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                            httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名的接口，签名默认大写
 @param url 请求Url
 @param params 携带参数
 @param SHA256 sign字段加密方式，YES-SHA256加密 NO-MD5加密
 @param signatureNonce 防重放 YES-开启 NO-关闭
 @param plainBlock 加密Block，需返回拼接签名串
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                params:(nullable NSDictionary *)params
                                SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                            plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                            httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名的接口，签名默认大写
 @param url 请求Url
 @param params 携带参数
 @param SHA256 sign字段加密方式，YES-SHA256加密 NO-MD5加密
 @param signatureNonce 防重放 YES-开启 NO-关闭
 @param encrypt 响应加密 YES-加密 NO-不加密
 @param plainBlock 加密Block，需返回拼接签名串
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                                 SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名的接口，签名默认大写
 @param url 请求Url
 @param params 携带参数
 @param SHA256 sign字段加密方式，YES-SHA256加密 NO-MD5加密
 @param signatureNonce 防重放 YES-开启 NO-关闭
 @param encrypt 响应加密 YES-加密 NO-不加密
 @param forceMode 强制安全 YES-强制应用本地设置 NO-应用内部设置
 @param plainBlock 加密Block，需返回拼接签名串
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                                 SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                              forceMode:(BOOL)forceMode
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名的接口，签名默认大写
 @param url 请求Url
 @param params 携带参数
 @param SHA256 sign字段加密方式，YES-SHA256加密 NO-MD5加密
 @param signatureNonce 防重放 YES-开启 NO-关闭
 @param encrypt 响应加密 YES-加密 NO-不加密
 @param requestEncrypt 请求加密 YES-加密 NO-不加密
 @param plainBlock 加密Block，需返回拼接签名串
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                                 SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                         requestEncrypt:(BOOL)requestEncrypt
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名的接口，签名默认大写
 @param url 请求Url
 @param params 携带参数
 @param SHA256 sign字段加密方式，YES-SHA256加密 NO-MD5加密
 @param signatureNonce 防重放 YES-开启 NO-关闭
 @param encrypt 响应加密 YES-加密 NO-不加密
 @param requestEncrypt 请求加密 YES-加密 NO-不加密
 @param forceMode 强制安全 YES-强制本地设置 NO-应用内部设置
 @param plainBlock 加密Block，需返回拼接签名串
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                                 SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                         requestEncrypt:(BOOL)requestEncrypt
                              forceMode:(BOOL)forceMode
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                             httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名的接口
 @param url 请求Url
 @param params 携带参数
 @param uppercase sign字段是否大写
 @param SHA256 sign字段加密方式，YES-SHA256加密 NO-MD5加密
 @param signatureNonce 防重放 YES-开启 NO-关闭
 @param encrypt 响应加密 YES-加密 NO-不加密 
 @param plainBlock 加密Block，需返回拼接签名串
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                params:(nullable NSDictionary *)params
                             uppercase:(BOOL)uppercase
                                SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                            plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                            httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名且请求加密的接口
 @param url 请求Url
 @param params 携带参数
 @param uppercase sign字段是否大写
 @param SHA256 sign字段加密方式，YES-SHA256加密 NO-MD5加密
 @param signatureNonce 防重放 YES-开启 NO-关闭
 @param encrypt 响应加密 YES-加密 NO-不加密
 @param requestEncrypt 请求加密 YES-加密 NO-不加密
 @param plainBlock 加密Block，需返回拼接签名串
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                params:(nullable NSDictionary *)params
                             uppercase:(BOOL)uppercase
                                SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                         requestEncrypt:(BOOL)requestEncrypt
                            plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                            httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名且请求加密的接口
 @param url 请求Url
 @param params 携带参数
 @param uppercase sign字段是否大写
 @param SHA256 sign字段加密方式，YES-SHA256加密 NO-MD5加密
 @param signatureNonce 防重放 YES-开启 NO-关闭
 @param encrypt 响应加密 YES-加密 NO-不加密
 @param requestEncrypt 请求加密 YES-加密 NO-不加密
 @param forceMode 强制使用接口安全 YES-强制应用本地设置 NO-应用内部设置
 @param plainBlock 加密Block，需返回拼接签名串
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                params:(nullable NSDictionary *)params
                             uppercase:(BOOL)uppercase
                                SHA256:(BOOL)SHA256
                         signatureNonce:(BOOL)signatureNonce
                                encrypt:(BOOL)encrypt
                         requestEncrypt:(BOOL)requestEncrypt
                              forceMode:(BOOL)forceMode
                            plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                            httpMethod:(nonnull NSString *)httpMethod;

/**
 创建需要签名且请求加密的接口
 @param url 请求Url
 @param params 携带参数
 @param signConfigHandler 签名参数配置
 @param plainBlock 加密Block，需返回拼接签名串
 @param httpMethod 请求方式
 @return Request对象
 */
+ (NSMutableURLRequest *)requestWithURL:(nonnull NSString *)url
                                 params:(nullable NSDictionary *)params
                             signConfig:(void(^ _Nullable)(PLVLiveSignCreator *signConfig))signConfigHandler
                             plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                            httpMethod:(nonnull NSString *)httpMethod;

/// 把字典数据按照key的字母排序拼接成key1value1key2value2的字符串，并前后拼上[PLVLiveVideoConfig sharedInstance].appSecret
+ (NSString *)createStringWithParamDict:(NSDictionary *)paramsDict;

/// 把字典数据按照key的字母排序拼接成key1value1key2value2的字符串，并前后拼上参数appsecret
+ (NSString *)createStringWithParamDict:(NSDictionary *)paramsDict appsecret:(NSString *)appsecret;

/// 获取域名对应ip
+ (NSString *)getIPAddrWithHost:(NSString *)host;

/// 获取httpdns播放地址（ip替换host）
+ (NSString *)getHttpDNSWithURLString:(NSString *)urlString;


@end

@interface PLVLiveSignCreator: NSObject

/// sign字段是否大写
@property (nonatomic, assign) BOOL uppercase;
/// sign字段加密方式，YES-SHA256加密 NO-MD5加密
@property (nonatomic, assign) BOOL SHA256;
/// 请求加密 YES-加密 NO-不加
@property (nonatomic, assign) BOOL requestEncrypt;
/// 请求加密类型 默认使用配置的类型
@property (nonatomic, assign) PLVEncryptType encryptRequestType;
/// 响应加密 YES-加密 NO-不加密
@property (nonatomic, assign) BOOL encrypt;
/// 响应加密类型 默认使用配置的类型
@property (nonatomic, assign) PLVEncryptType encryptResponseType;
/// 防重放 YES-开启 NO-关闭
@property (nonatomic, assign) BOOL signatureNonce;
/// 强制使用接口安全 YES-强制应用本地设置 NO-应用内部设置
@property (nonatomic, assign) BOOL forceMode;
/// 签名是否拼接到URL后面 YES会将签名拼接至URL，并且在外部拼接参数 NO签名添加至参数中 默认NO
@property (nonatomic, assign) BOOL signSplicedURL;

@end

NS_ASSUME_NONNULL_END
