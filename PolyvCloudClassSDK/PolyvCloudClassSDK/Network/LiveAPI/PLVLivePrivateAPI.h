//
//  PLVLivePrivateAPI.h
//  PLVLiveScenesSDK
//
//  Created by zykhbl on 2018/7/27.
//  Copyright © 2018年 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "PLVLiveDefine.h"
#import "PLVChannelInfoModel.h"
#import "PLVLinkMicGetTokenModel.h"
#import "PLVPublicStreamGetInfoModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface PLVLivePrivateAPI : NSObject

/**
 获取直播频道信息
 
 @param userId 云直播账号 用户ID
 @param channelId 频道号
 @param isStandby 是否启用备用接口
 */
+ (void)loadChannelInfoRepeatedlyWithUserId:(NSString *)userId channelId:(NSString *)channelId isStandby:(BOOL)isStandby completion:(void (^)(PLVChannelInfoModel *))completion failure:(void (^)(NSError *))failure;

/**
 校验该用户是否有权限观看直播
 
 @param userId 云直播账号 用户ID
 @param channelId 频道号
 */
+ (void)getChannelRestrictInfoWithUserId:(NSString *)userId channelId:(NSUInteger)channelId completion:(void (^)(NSDictionary *))completion failure:(void (^)(NSError *))failure;

/**
检测直播流状态（v2版本）

@param streamID 流ID
*/
+ (void)liveStreamStatus:(NSString *)streamID completion:(void (^)(PLVChannelLiveStreamState streamState))completion failure:(void (^)(NSError * error))failure DEPRECATED_MSG_ATTRIBUTE("已废弃，请使用pptStartWithVideoId:channelId:params:completion:failure:");

/**
检测直播流状态及sessionId（v3版本）

@param streamID 流ID
@param channelId 频道号
@param params 观看页补充页面进出数据 （开播端登录可传递nil）
*/
+ (void)liveStreamStatus:(NSString *)streamID
               channelId:(NSString *)channelId
                  params:(nullable NSDictionary *)params
              completion:(void (^)(PLVChannelLiveStreamState streamState ,NSString *sessionId))completion
                 failure:(void (^)(NSError * error))failure;

/**
 获取当前频道的sessionId
 
 @param channelId 频道号
 */
+ (void)requestChannelSessionIdWithChannelId:(NSUInteger)channelId completion:(void (^)(NSString *))completion failure:(void (^)(NSError *error))failure;

/**
 获取直播回放视频的信息
 
 @param vid 回放视频vid
 @param channelId 回放视频频道号
 */
+ (void)loadVideoInfoWithVid:(NSString *)vid channelId:(NSString *)channelId completion:(void (^)(NSDictionary *))completion failure:(void (^)(NSError *))failure;

/// 获取直播回放视频的信息
/// @param vid 回放视频 vid
/// @param channelId 回放视频频道号
/// @param listType 列表类型，值：playback、vod；默认 playback
+ (void)loadVideoInfoWithVid:(NSString *)vid channelId:(NSString *)channelId listType:(NSString * _Nullable)listType completion:(void (^)(NSDictionary * data))completion failure:(void (^)(NSError *error))failure;

/// 获取直播暂存视频的信息
/// @param fileId 文件id
/// @param channelId 频道号
+ (void)loadRecordVideoInfoWithFileId:(NSString *)fileId channelId:(NSString *)channelId completion:(void (^)(NSDictionary * data))completion failure:(void (^)(NSError *error))failure;

/// 根据 teacher_login.json 接口返回数据拼接出推流地址
/// @param data teacher_login.json 接口返回数据
+ (void)getRtmpUrlWithTeacherLoginResponse:(NSDictionary *)data
                                completion:(void (^)(NSString *))completion;

/**
 获取 AgRTC连麦配置

 @param model 获取连麦Token数据模型
 */
+ (void)getAgRTCTokenWithModel:(PLVLinkMicGetTokenModel *)model success:(void (^)(NSDictionary *))success failure:(void (^)(NSError *))failure;

/**
 获取 ZeRTC连麦配置

 @param model 获取连麦Token数据模型
 */
+ (void)getZeRTCTokenWithModel:(PLVLinkMicGetTokenModel *)model success:(void (^)(NSDictionary *))success failure:(void (^)(NSError *error))failure;

/**
 获取 UcRTC连麦配置

 @param model 获取连麦Token数据模型
 */
+ (void)getUcRTCTokenWithModel:(PLVLinkMicGetTokenModel *)model success:(void (^)(NSDictionary *))success failure:(void (^)(NSError *error))failure;

/**
 获取 TRTC连麦配置

 @param model 获取连麦Token数据模型
 */
+ (void)getTRTCTokenWithModel:(PLVLinkMicGetTokenModel *)model success:(void (^)(NSDictionary *))success failure:(void (^)(NSError *error))failure;

/**
 获取 VOLC连麦配置

 @param model 获取连麦Token数据模型
 */
+ (void)getVOLCTokenWithModel:(PLVLinkMicGetTokenModel *)model success:(void (^)(NSDictionary *))success failure:(void (^)(NSError *error))failure;

/**
 获取 VOLC公共流配置

 @param model 获取连麦Token数据模型
 */
+ (void)getVOLCPublicStreamInfoWithModel:(PLVPublicStreamGetInfoModel *)model success:(void (^)(NSDictionary *))success failure:(void (^)(NSError *error))failure;

/**
 获取 PPT 文档上传 STS TOKEN
 @note 用于 手机开播（三分屏）场景
 @param channelId 频道号
 @param fileId 待上传文档ID
 @param fileName 待上传文档名称
 @param type 待上传文档转码类型
 */
+ (void)getUploadTokenWithChannelId:(NSString *)channelId
                             fileId:(NSString *)fileId
                           fileName:(NSString *)fileName
                        convertType:(NSString *)type
                            success:(void (^)(NSDictionary *responseObject, NSDictionary *callback))success
                            failure:(void (^)(NSError *error))failure;

/// 获取 PPT 文档上传 STS TOKEN
/// @note 用于 互动学堂 场景
/// @param channelId 频道号
/// @param channelToken 频道Token
/// @param appId appId
/// @param fileId 待上传文档ID
/// @param fileName 待上传文档名称
/// @param type 待上传文档转码类型
/// @param success 成功回调
/// @param failure 失败回调
+ (void)getUploadTokenWithChannelId:(NSString *)channelId
                       channelToken:(NSString *)channelToken
                       appId:(NSString *)appId
                             fileId:(NSString *)fileId
                           fileName:(NSString *)fileName
                        convertType:(NSString *)type
                            success:(void (^)(NSDictionary *responseObject, NSDictionary * _Nullable callback))success
                            failure:(void (^)(NSError *error))failure;

/// 获取美颜设置
/// @param packageName bundleId
/// @param success 成功回调
/// @param failure 失败回调
+ (void)getBeautySettingWithPackageName:(NSString *)packageName
                                success:(void (^)(NSDictionary *responseObject))success
                                failure:(void (^)(NSError *error))failure;


#pragma mark - 回放跨端续播

/// 获取回放列表跨端续播信息
/// @param vid 回放视频ID，回放列表使用videoId，点播列表使用videoPoolId不含_
/// @param viewerId 观看者ID
/// @param completion -1表示未获得进度
+ (void)requestPlaybackPositionWithVid:(NSString *)vid
                              viewerId:(NSString *)viewerId
                            completion:(void (^)(NSTimeInterval lastPosition))completion;

#pragma mark - 推流
/// 设置流模式
+ (void)notifyStreamModeWithChannelId:(NSString *)channelId stream:(NSString *)stream sessionId:(NSString *)sessionId videowidth:(NSUInteger)videowidt videoheight:(NSUInteger)videoheight continueLastLive:(BOOL)isContinue success:(void (^)(NSString * responseContent))success failure:(void (^)(NSError * error))failure;

/// 推流端根据流获取sessionId
+ (void)requestChannelSessionIdWithChannelId:(NSString *)channelId stream:(NSString *)stream success:(void (^)(NSString * responseContent))success failure:(void (^)(NSError * error))failure;

/// 转推混流
///
/// @param mixInfo 混流信息数据
/// @param success 请求成功
/// @param failure 请求失败
+ (void)requestPushStreamMixActionWitMixInfo:(NSDictionary *)mixInfo success:(void (^)(NSDictionary * responseDict))success failure:(void (^)(NSError * error))failure;

#pragma mark - 聊天消息回放

/// 回放时获取聊天历史数据分段信息
/// @param roomId 频道号
/// @param sessionId 场次ID
/// @param videoId 回放视频ID
/// @param page 第几页数据，大于等于1
/// @param success 请求成功
/// @param failure 请求失败
+ (void)requestPlaybakMessageHistoryIndexWithRoomId:(NSString *)roomId
                                          sessionId:(NSString *)sessionId
                                            videoId:(NSString *)videoId
                                               page:(NSUInteger)page
                                            success:(void (^)(NSDictionary * responseDict))success
                                            failure:(void (^ _Nullable)(NSError * error))failure;

/// 回放时根据分段id获取聊天历史记录
/// @param roomId 频道号
/// @param sessionId 场次ID
/// @param indexId 分段ID，大于等于1
/// @param success 请求成功
/// @param failure 请求失败
+ (void)requestPlaybakMessageHistoryWithRoomId:(NSString *)roomId
                                     sessionId:(NSString *)sessionId
                                       indexId:(NSUInteger)indexId
                                       success:(void (^)(NSArray *responseArray))success
                                       failure:(void (^ _Nullable)(NSError * error))failure;

#pragma mark - 日志上报

/// 获取用户上传日志任务
/// @param userId 用户id
/// @param completion 请求成功
/// @param failure 请求失败
+ (void)requestLoganReportInfoWithUserId:(NSString *)userId
                              completion:(void (^)(NSDictionary *responseDict))completion
                                 failure:(void (^)(NSError *error))failure;

/// 获取用户上传日志任务
/// @param userId 用户id
/// @param channelId 频道号
/// @param success 请求成功
/// @param failure 请求失败
+ (void)getUploadTokenWithUserId:(NSString *)userId
                       channelId:(NSString *)channelId
                         success:(void (^)(NSDictionary *responseObject))success
                         failure:(void (^)(NSError *error))failure;

@end

NS_ASSUME_NONNULL_END
