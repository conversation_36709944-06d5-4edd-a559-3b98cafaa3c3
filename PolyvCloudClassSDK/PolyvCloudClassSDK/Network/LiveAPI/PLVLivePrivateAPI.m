//
//  PLVLivePrivateAPI.m
//  PLVLiveScenesSDK
//
//  Created by zykhbl on 2018/7/27.
//  Copyright © 2018年 PLV. All rights reserved.
//

#import "PLVLivePrivateAPI.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVLiveVideoConfig.h"
#import "PLVLiveAPIUtils.h"
#import "PLVWErrorManager.h"
#import "PLVWELogEventDefine.h"
#import "PLVWLogReporterManager.h"
#import "PLVLinkMicGetTokenModel+PrivateInfo.h"
#import "PLVPublicStreamGetInfoModel+PrivateInfo.h"
#import "PLVLiveVideoConfig+PrivateInfo.h"

//extra 字典 key值
const NSString *plv_extra_time_interval_key = @"extra_time_interval_key";

@interface PLVLivePrivateAPI ()

@end

@implementation PLVLivePrivateAPI

+ (void)loadChannelInfoRepeatedlyWithUserId:(NSString *)userId channelId:(NSString *)channelId isStandby:(BOOL)isStandby completion:(void (^)(PLVChannelInfoModel *))completion failure:(void (^)(NSError *))failure {
    [PLVLivePrivateAPI loadChannelInfoWithUserId:userId channelId:channelId isStandby:isStandby completion:completion failure:failure];
}

+ (void)loadChannelInfo_BUWithUserId:(NSString *)userId channelId:(NSUInteger)channelId completion:(void (^)(PLVChannelInfoModel *))completion failure:(void (^)(NSError *))failure {
    if (!userId || [userId isKindOfClass:[NSNull class]] || !channelId) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        NSString *url = [NSString stringWithFormat:@"https://player.polyv.net/service/v2/channel_%@_%lu.json", [userId stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]], (unsigned long)channelId];//直播信息接口：内容非加密，缓存时间 3min
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:nil plainBlock:nil httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                PLVChannelInfoModel *channel = [PLVChannelInfoModel channelInfoModelWithDataDict:responseDict[@"body"]];
                if (completion) {
                    completion(channel);
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"body"]];
            }
        } fail:failure];
    }
}

+ (void)liveStreamStatus:(NSString *)streamID completion:(void (^)(PLVChannelLiveStreamState))completion failure:(void (^)(NSError * _Nonnull))failure {
    if (![PLVFdUtil checkStringUseable:streamID]) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPlay
                           code:PLVFPlayErrorCodeGetStreamState_ParameterError
                           info:nil
               errorDescription:[NSString stringWithFormat:PLVFDLocalizableString(@"直播流状态信息请求失败，参数错误 streamId:%@"),streamID]];
        return;
    }
    
    NSString * url = [NSString stringWithFormat:@"https://api.polyv.net/v2/live_status/query?stream=%@", streamID];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:nil plainBlock:nil httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestString:request completion:^(NSString *responseCont) {
        if (completion) {
            if (responseCont && [responseCont isKindOfClass:NSString.class]) {
                if ([responseCont isEqualToString:@"live"]) {
                    completion(PLVChannelLiveStreamState_Live);
                } else if ([responseCont isEqualToString:@"end"]) {
                    completion(PLVChannelLiveStreamState_End);
                } else if ([responseCont isEqualToString:@"stop"]){
                    completion(PLVChannelLiveStreamState_Stop);
                }
            } else {
                completion(PLVChannelLiveStreamState_Unknown);
            }
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulPlay
                               code:PLVFPlayErrorCodeGetStreamState_DataError
                               info:error.localizedDescription
                   errorDescription:[NSString stringWithFormat:PLVFDLocalizableString(@"请求解析失败 %@"),error.localizedDescription]];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)liveStreamStatus:(NSString *)streamID
               channelId:(NSString *)channelId
                  params:(nullable NSDictionary *)params
              completion:(void (^)(PLVChannelLiveStreamState streamState ,NSString *sessionId))completion
                 failure:(void (^)(NSError * error))failure {
    if (![PLVFdUtil checkStringUseable:streamID] || !channelId) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPlay
                           code:PLVFPlayErrorCodeGetStreamState_ParameterError
                           info:nil
               errorDescription:[NSString stringWithFormat:PLVFDLocalizableString(@"直播流状态信息请求失败，参数错误 streamId:%@,channelId:%@"),streamID,channelId]];
        return;
    }
    
    NSString * url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/live-status-sessionid/query?stream=%@&channelId=%@", streamID,channelId];
    if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
        [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
        url = [NSString stringWithFormat:@"%@&eType=2", url];
    }
    
    NSMutableDictionary *muParams = [NSMutableDictionary dictionaryWithDictionary:params];
    if ([PLVFdUtil checkDictionaryUseable:params]) {
        for (NSString *key in params.allKeys) {
            if ([key isEqualToString:@"p1"] ||
                [key isEqualToString:@"p2"] ||
                [key isEqualToString:@"p4"] ||
                [key isEqualToString:@"p5"] ) {
                NSString *value = params[key];
                if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
                    [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
                    value = [PLVSM2Util formatEncryptString:value publicKey:[PLVKeyUtil getApiUtilsPublicKey]];
                } else {
                    value = [PLVDataUtil urlSafeBase64String:value];
                }
                value = value ?: @"";
                [muParams setValue:value forKey:key];
            }
        }
    }
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:muParams plainBlock:nil httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (completion) {
            if ([PLVFdUtil checkDictionaryUseable:responseDict]) {
                NSString *status = PLV_SafeStringForDictKey(responseDict, @"status");
                NSString *sessionId = PLV_SafeStringForDictKey(responseDict, @"sessionId");
                if ([status isEqualToString:@"live"]) {
                    completion(PLVChannelLiveStreamState_Live,sessionId);
                } else if ([status isEqualToString:@"end"]) {
                    completion(PLVChannelLiveStreamState_End,sessionId);
                } else if ([status isEqualToString:@"stop"]){
                    completion(PLVChannelLiveStreamState_Stop,sessionId);
                }
            } else {
                completion(PLVChannelLiveStreamState_Unknown,@"");
            }
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulPlay
                               code:PLVFPlayErrorCodeGetStreamState_DataError
                               info:error.localizedDescription
                   errorDescription:[NSString stringWithFormat:PLVFDLocalizableString(@"请求解析失败 %@"),error.localizedDescription]];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}
/*
+ (void)getStreamStatusWithChannelId:(NSUInteger)channelId stream:(NSString *)stream completion:(void (^)(PLVLiveStreamState, NSString *))completion failure:(void (^)(NSError *))failure {
    if (!channelId) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v2/channels/%lu/live-status", (unsigned long)channelId];//直播状态及模式接口：使用 channel 参数
        if (stream) {// 参数 stream 为 nil 时结果无缓存即不带 ?stream= 会直接穿透到服务器后端
            url = [url stringByAppendingFormat:@"?stream=%@", stream];
        }
        
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:nil plainBlock:nil httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                NSString *data = responseDict[@"data"];
                NSArray *arr = [data componentsSeparatedByString:@","];
                if (completion) {
                    if ([data containsString:@"true"]) {
                        completion(PLVLiveStreamStateLive, arr.lastObject);
                    } else {
                        completion(PLVLiveStreamStateNoStream, arr.lastObject);
                    }
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}*/

+ (void)requestChannelSessionIdWithChannelId:(NSUInteger)channelId completion:(void (^)(NSString *))completion failure:(void (^)(NSError *error))failure {
    if (!channelId) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPlay
                           code:PLVFPlayErrorCodeGetSessionID_ParameterError
                           info:nil
               errorDescription:[NSString stringWithFormat:PLVFDLocalizableString(@"直播SessionID请求失败，参数错误 channelId:%lu"),(unsigned long)channelId]];
    } else {
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/channel-sessionid/query?channelId=%lu", (unsigned long)channelId];
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:nil plainBlock:nil httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestString:request completion:^(NSString *responseCont) {
            completion(responseCont);
        } fail:^(NSError *error) {
            if (error.code == PLVNetworkErrorCodeDecodeFail) {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulPlay
                                   code:PLVFPlayErrorCodeGetSessionID_RequestFailed
                                   info:error.localizedDescription
                       errorDescription:[NSString stringWithFormat:PLVFDLocalizableString(@"直播SessionID请求失败 %@"),error.localizedDescription]];
            } else {
                [self callBackFailBlock:failure networkError:error];
            }
        }];
    }
}

+ (void)loadVideoInfoWithVid:(NSString *)vid channelId:(NSString *)channelId completion:(void (^)(NSDictionary *))completion failure:(void (^)(NSError *))failure {
    return [self loadVideoInfoWithVid:vid channelId:channelId listType:nil completion:completion failure:failure];
}

+ (void)loadVideoInfoWithVid:(NSString *)vid channelId:(NSString *)channelId listType:(NSString *)listType completion:(void (^)(NSDictionary * data))completion failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:vid]) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPlay
                           code:PLVFPlayErrorCodeGetVideoInfo_ParameterError
                           info:nil
               errorDescription:[NSString stringWithFormat:PLVFDLocalizableString(@"回放信息请求失败，参数错误 vid:%@"),vid]];
    } else {
        if (!listType || ![listType isKindOfClass:NSString.class]) {
            listType = @"playback";
        }
        
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        NSMutableDictionary *mParam = [NSMutableDictionary dictionary];
        mParam[@"appId"] = liveConfig.appId;
        mParam[@"channelId"] = @(channelId.integerValue);
        mParam[@"vid"] = vid;
        mParam[@"listType"] = listType;
        mParam[@"ignoreScene"] = @"Y";
        
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:@"https://api.polyv.net/live/v3/channel/playback/get-video-by-vid" params:mParam SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            if (((NSNumber *)responseDict[@"code"]).integerValue == 200) {
                if (completion) {
                    NSDictionary * dataDict = responseDict[@"data"];
                    if ([PLVFdUtil checkDictionaryUseable:dataDict]) {
                        completion(dataDict);
                    }else{
                        [self callBackFailBlock:failure
                                          modul:PLVFErrorCodeModulPlay
                                           code:PLVFPlayErrorCodeGetVideoInfo_DataError
                                           info:nil
                               errorDescription:responseDict[@"message"]];
                    }
                }
            } else {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulPlay
                                   code:PLVFPlayErrorCodeGetVideoInfo_CodeError
                                   info:responseDict
                       errorDescription:responseDict[@"message"]];
            }
        } fail:^(NSError *error) {
            if (error.code == PLVNetworkErrorCodeDecodeFail) {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulPlay
                                   code:PLVFPlayErrorCodeGetVideoInfo_DataError
                                   info:error.localizedDescription
                       errorDescription:[NSString stringWithFormat:PLVFDLocalizableString(@"请求解析失败 %@"),error.localizedDescription]];
            } else {
                [self callBackFailBlock:failure networkError:error];
            }
        }];
    }
}

/// 获取直播暂存视频的信息
+ (void)loadRecordVideoInfoWithFileId:(NSString *)fileId channelId:(NSString *)channelId completion:(void (^)(NSDictionary * data))completion failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:fileId]) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPlay
                           code:PLVFPlayErrorCodeGetVideoInfo_ParameterError
                           info:nil
               errorDescription:[NSString stringWithFormat:PLVFDLocalizableString(@"暂存视频信息请求失败，参数错误 fileId:%@"),fileId]];
    } else {
        
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        NSMutableDictionary *mParam = [NSMutableDictionary dictionary];
        mParam[@"appId"] = liveConfig.appId;
        mParam[@"channelId"] = @(channelId.integerValue);
        mParam[@"fileId"] = fileId;
        
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:@"https://api.polyv.net/live/v3/channel/record/get" params:mParam plainBlock:^NSString *(NSString *timestamp) {
            return [NSString stringWithFormat:@"%@appId%@channelId%@fileId%@timestamp%@%@", liveConfig.appSecret, liveConfig.appId, channelId, fileId, timestamp, liveConfig.appSecret];
        } httpMethod:PLV_HM_GET];
        
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            if (((NSNumber *)responseDict[@"code"]).integerValue == 200) {
                if (completion) {
                    NSDictionary * dataDict = responseDict[@"data"];
                    if ([PLVFdUtil checkDictionaryUseable:dataDict]) {
                        completion(dataDict);
                    }else{
                        [self callBackFailBlock:failure
                                          modul:PLVFErrorCodeModulPlay
                                           code:PLVFPlayErrorCodeGetVideoInfo_DataError
                                           info:nil
                               errorDescription:responseDict[@"message"]];
                    }
                }
            } else {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulPlay
                                   code:PLVFPlayErrorCodeGetVideoInfo_CodeError
                                   info:responseDict
                       errorDescription:responseDict[@"message"]];
            }
        } fail:^(NSError *error) {
            if (error.code == PLVNetworkErrorCodeDecodeFail) {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulPlay
                                   code:PLVFPlayErrorCodeGetVideoInfo_DataError
                                   info:error.localizedDescription
                       errorDescription:[NSString stringWithFormat:PLVFDLocalizableString(@"请求解析失败 %@"),error.localizedDescription]];
            } else {
                [self callBackFailBlock:failure networkError:error];
            }
        }];
    }
}

/// 获取频道约束信息
+ (void)getChannelRestrictInfoWithUserId:(NSString *)userId channelId:(NSUInteger)channelId completion:(void (^)(NSDictionary *))completion failure:(void (^)(NSError *))failure {
    if (!userId || !channelId || [userId isKindOfClass:[NSNull class]]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else if ([PLVLiveVideoConfig sharedInstance].realEnableResourceAuth) {
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:[NSString stringWithFormat:@"https://livejson.polyv.net/service/v3/encrypted_restrict_%@_%ld.json",userId,channelId]  httpMethod:PLV_HM_GET]; //直播频道约束接口
        [PLVFNetworkUtil requestString:request completion:^(NSString *responseCont) {
            NSString *hexEncrypted = responseCont;
            NSData *encryptedData = [PLVDataUtil dataForHexString:hexEncrypted];
            NSData *decryptedData = [PLVDataUtil AES128DecryptData:encryptedData withKey:[PLVKeyUtil getChannelInfoKey] iv:[PLVKeyUtil getChannelInfoIv]];
            NSString *base64Str = [[NSString alloc] initWithData:decryptedData encoding:NSUTF8StringEncoding];
            decryptedData = [[NSData alloc] initWithBase64EncodedString:base64Str options:NSDataBase64DecodingIgnoreUnknownCharacters];
            NSError *jsonError = nil;
            NSDictionary *jsonDict = [NSJSONSerialization JSONObjectWithData:decryptedData options:NSJSONReadingMutableContainers error:&jsonError];
            if (!jsonError) {
                completion ? completion(jsonDict) : nil;
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeDecodeFail desc:jsonError.localizedDescription];
            }
        } fail:failure];
    } else {
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:[NSString stringWithFormat:@"https://livejson.polyv.net/service/v3/restrict_%@_%ld.json",userId,channelId] httpMethod:PLV_HM_GET];//直播频道约束接口
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            if (completion) {
                completion(responseDict);
            }
        } fail:failure];
    }
}

#pragma mark - 回放跨端续播

+ (void)requestPlaybackPositionWithVid:(NSString *)videoId
                              viewerId:(NSString *)viewerId
                            completion:(void (^)(NSTimeInterval lastPosition))completion {
    if (![PLVFdUtil checkStringUseable:videoId] || ![PLVFdUtil checkStringUseable:viewerId]) {
        completion ? completion(-1) : nil;
        return;
    }
    
    NSString *urlString = [NSString stringWithFormat:@"https://script.polyv.net/live/v1/play-position/get?videoId=%@&viewerId=%@", videoId, viewerId];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:urlString params:nil plainBlock:nil httpMethod:PLV_HM_GET];
    request.timeoutInterval = 3;
    
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if ([PLVFdUtil checkDictionaryUseable:responseDict]) {
            completion ? completion((NSTimeInterval)PLV_SafeIntegerForDictKey(responseDict, @"cts")) : nil;
        } else {
            completion ? completion(-1) : nil;
        }
    } fail:^(NSError *error) {
        completion ? completion(-1) : nil;
    }];
}

#pragma mark - 获取推流地址

+ (void)getRtmpUrlWithTeacherLoginResponse:(NSDictionary *)data
                                completion:(void (^)(NSString *))completion {
    BOOL isNgbEnabled = PLV_SafeBoolForDictKey(data, @"isNgbEnabled");
    BOOL isUrlProtected = PLV_SafeBoolForDictKey(data, @"isUrlProtected");
    NSString *stream = PLV_SafeStringForDictKey(data, @"stream");
    NSString *ngbUrl = PLV_SafeStringForDictKey(data, @"ngbUrl");
    NSString *bakUrl = PLV_SafeStringForDictKey(data, @"bakUrl");
    NSString *url = PLV_SafeStringForDictKey(data, @"url");
    NSString *suffix = PLV_SafeStringForDictKey(data, @"suffix");
    if (isNgbEnabled) {//NGB 地址
        NSString *streamUrl = [ngbUrl stringByAppendingString:stream];
        [PLVLivePrivateAPI requestNgbRtmpURL:streamUrl completion:^(NSString *rtmpUrl) {
            if (completion) { // NGB加防盗链推流地址
                completion(rtmpUrl);
            }
        } fail:^(NSError *error) { // Secondly Request NGB url
            NSString *streamUrl = [bakUrl stringByAppendingString:stream];
            [PLVLivePrivateAPI requestNgbRtmpURL:streamUrl completion:^(NSString *rtmpUrl) {
                if (completion) { // NGB加防盗链推流地址
                    completion(rtmpUrl);
                }
            } fail:^(NSError *error) { // 普通推流地址
                NSString *rtmpUrl = [url stringByAppendingString:stream];
                if (completion) { // 普通推流地址
                    completion(rtmpUrl);
                }
            }];
        }];
    } else if (isUrlProtected) {
        NSString *rtmpUrl = [NSString stringWithFormat:@"%@%@%@", bakUrl, stream, suffix];
        if (completion) { // 普通防盗链推流地址
            completion(rtmpUrl);
        }
    } else {
        NSString *rtmpUrl = [url stringByAppendingString:stream];
        if (completion) { // 普通推流地址
            completion(rtmpUrl);
        }
    }
}

/// 请求NGB加防盗链推流地址
+ (void)requestNgbRtmpURL:(NSString *)streamUrl
               completion:(void (^)(NSString *rtmpUrl))completion
                     fail:(void (^)(NSError *))fail {
    NSString *url = @"https://sdkoptedge.chinanetcenter.com";
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url] cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:6.0];//获取 NGB 推流地址接口
    [request setValue:streamUrl forHTTPHeaderField:@"WS_URL"];
    [request setValue:@"1" forHTTPHeaderField:@"WS_RETIP_NUM"];
    [request setValue:@"3" forHTTPHeaderField:@"WS_URL_TYPE"];
    
    [PLVFNetworkUtil requestString:request completion:^(NSString *responseCont) {
        if (responseCont.length) {
            completion(responseCont);
        } else {
            fail(nil);
        }
    } fail:^(NSError *error) {
        [self callBackFailBlock:fail networkError:error];
    }];
}

#pragma mark - 连麦授权接口
/// Agora声网AppId、连麦Token获取接口
+ (void)getAgRTCTokenWithModel:(PLVLinkMicGetTokenModel *)model success:(void (^)(NSDictionary *))success failure:(void (^)(NSError *))failure {
    if (![PLVFdUtil checkStringUseable:model.channelId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
        NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
        if (![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"appId或appSecret无效/未配置")];
            return;
        }
        NSString * url = [NSString stringWithFormat:@"https://api.polyv.net/live/v2/channels/%@/mic-auth", model.channelId]; // 连麦授权接口
        
        model.forGET = ![PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt;
        NSMutableDictionary * paramsMuDict = [model convertModelIntoDictionary];
        if (![paramsMuDict isKindOfClass:NSMutableDictionary.class]) {
            paramsMuDict = [NSMutableDictionary dictionaryWithDictionary:paramsMuDict];
        }
        [paramsMuDict setObject:appId forKey:@"appId"];
        if ([PLVFdUtil checkStringUseable:model.userId]) { // 声网接口，需以 uid 作为连麦ID字段名
            [paramsMuDict setObject:model.userId forKey:@"uid"];
        }
        
        model.forGET = NO;
        NSMutableDictionary * paramsMuDict_noEncode = [model convertModelIntoDictionary];
        if (![paramsMuDict_noEncode isKindOfClass:NSMutableDictionary.class]) {
            paramsMuDict_noEncode = [NSMutableDictionary dictionaryWithDictionary:paramsMuDict_noEncode];
        }
        [paramsMuDict_noEncode setObject:appId forKey:@"appId"];
        if ([PLVFdUtil checkStringUseable:model.userId]) { // 声网接口，需以 uid 作为连麦ID字段名
            [paramsMuDict_noEncode setObject:model.userId forKey:@"uid"];
        }
        
        NSMutableURLRequest *request;
        if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt) {
            request = [PLVLiveAPIUtils requestWithURL:url params:paramsMuDict_noEncode SHA256:YES signatureNonce:YES encrypt:YES requestEncrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
                return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
            } httpMethod:PLV_HM_POST];
        } else {
            request = [PLVLiveAPIUtils requestWithURL:url params:paramsMuDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
                if (paramDict[@"signatureMethod"]) {
                    [paramsMuDict_noEncode setObject:paramDict[@"signatureMethod"] forKey:@"signatureMethod"];
                }
                if (paramDict[@"signatureNonce"]) {
                    [paramsMuDict_noEncode setObject:paramDict[@"signatureNonce"] forKey:@"signatureNonce"];
                }
                if (paramDict[@"encryptResponseType"]) {
                    [paramsMuDict_noEncode setObject:paramDict[@"encryptResponseType"] forKey:@"encryptResponseType"];
                }
                [paramsMuDict_noEncode setObject:timestamp forKey:@"timestamp"];
                return [PLVLiveAPIUtils createStringWithParamDict:paramsMuDict_noEncode];
            } httpMethod:PLV_HM_POST];
        }
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                if (success) {
                    success(responseDict);
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

/// Zego即构AppId、连麦Token获取接口
+ (void)getZeRTCTokenWithModel:(PLVLinkMicGetTokenModel *)model success:(void (^)(NSDictionary *))success failure:(void (^)(NSError *error))failure{
    if (![PLVFdUtil checkStringUseable:model.channelId] || ![PLVFdUtil checkStringUseable:model.userId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
        NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
        if (![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"appId或appSecret无效/未配置")];
            return;
        }
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/zego/auth"]; // 即构AppId、连麦Token获取接口
        
        model.forGET = ![PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt;
        NSMutableDictionary * paramsMuDict = [model convertModelIntoDictionary];
        if (![paramsMuDict isKindOfClass:NSMutableDictionary.class]) {
            paramsMuDict = [NSMutableDictionary dictionaryWithDictionary:paramsMuDict];
        }
        [paramsMuDict setObject:appId forKey:@"appId"];
        
        model.forGET = NO;
        NSMutableDictionary * paramsMuDict_noEncode = [model convertModelIntoDictionary];
        if (![paramsMuDict_noEncode isKindOfClass:NSMutableDictionary.class]) {
            paramsMuDict_noEncode = [NSMutableDictionary dictionaryWithDictionary:paramsMuDict_noEncode];
        }
        [paramsMuDict_noEncode setObject:appId forKey:@"appId"];
        
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramsMuDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            if (paramDict[@"signatureMethod"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"signatureMethod"] forKey:@"signatureMethod"];
            }
            if (paramDict[@"signatureNonce"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"signatureNonce"] forKey:@"signatureNonce"];
            }
            if (paramDict[@"encryptResponseType"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"encryptResponseType"] forKey:@"encryptResponseType"];
            }
            [paramsMuDict_noEncode setObject:timestamp forKey:@"timestamp"];
            return [PLVLiveAPIUtils createStringWithParamDict:paramsMuDict_noEncode];
        } httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                if (success) { success(responseDict); }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

/// UcRTC即构AppId、连麦Token获取接口
+ (void)getUcRTCTokenWithModel:(PLVLinkMicGetTokenModel *)model success:(void (^)(NSDictionary *))success failure:(void (^)(NSError *error))failure{
    if (![PLVFdUtil checkStringUseable:model.channelId] || ![PLVFdUtil checkStringUseable:model.userId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
        NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
        if (![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"appId或appSecret无效/未配置")];
            return;
        }
        BOOL testMode = NO;
#ifdef DEBUG
        NSUserDefaults *userDefaults = NSUserDefaults.standardUserDefaults;
        id testModeObj = [userDefaults objectForKey:@"urtc_test_mode"];
        if (testModeObj) { testMode = [testModeObj boolValue]; }
#endif
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/ucloud/auth"]; // UCloud AppId、连麦Token获取接口
        
        model.forGET = ![PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt;
        NSMutableDictionary * paramsMuDict = [model convertModelIntoDictionary];
        if (![paramsMuDict isKindOfClass:NSMutableDictionary.class]) {
            paramsMuDict = [NSMutableDictionary dictionaryWithDictionary:paramsMuDict];
        }
        [paramsMuDict setObject:appId forKey:@"appId"];
        if (testMode) { [paramsMuDict setObject:@"test" forKey:@"type"]; }
        
        model.forGET = NO;
        NSMutableDictionary * paramsMuDict_noEncode = [model convertModelIntoDictionary];
        if (![paramsMuDict_noEncode isKindOfClass:NSMutableDictionary.class]) {
            paramsMuDict_noEncode = [NSMutableDictionary dictionaryWithDictionary:paramsMuDict_noEncode];
        }
        [paramsMuDict_noEncode setObject:appId forKey:@"appId"];
        if (testMode) { [paramsMuDict_noEncode setObject:@"test" forKey:@"type"]; }
        
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramsMuDict SHA256:YES signatureNonce:YES encrypt:YES requestEncrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            if (paramDict[@"signatureMethod"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"signatureMethod"] forKey:@"signatureMethod"];
            }
            if (paramDict[@"signatureNonce"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"signatureNonce"] forKey:@"signatureNonce"];
            }
            if (paramDict[@"encryptResponseType"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"encryptResponseType"] forKey:@"encryptResponseType"];
            }
            [paramsMuDict_noEncode setObject:timestamp forKey:@"timestamp"];
            return [PLVLiveAPIUtils createStringWithParamDict:paramsMuDict_noEncode];
        } httpMethod:PLV_HM_POST];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                if (success) { success(responseDict); }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

+ (void)getTRTCTokenWithModel:(PLVLinkMicGetTokenModel *)model success:(void (^)(NSDictionary * _Nonnull))success failure:(void (^)(NSError * _Nonnull))failure{
    if (![PLVFdUtil checkStringUseable:model.channelId] || ![PLVFdUtil checkStringUseable:model.userId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
        NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
        if (![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"appId或appSecret无效/未配置")];
            return;
        }
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/trtc/auth"]; // TRTC AppId、连麦Token获取接口
        model.forGET = ![PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt;
        NSMutableDictionary * paramsMuDict = [model convertModelIntoDictionary];
        if (![paramsMuDict isKindOfClass:NSMutableDictionary.class]) {
            paramsMuDict = [NSMutableDictionary dictionaryWithDictionary:paramsMuDict];
        }
        [paramsMuDict setObject:appId forKey:@"appId"];
        if ([PLVFdUtil checkStringUseable:model.userId]) {
            [paramsMuDict setObject:model.userId forKey:@"uid"]; // TRTC接口，需以 uid 作为连麦ID字段名
        }
        
        model.forGET = NO;
        NSMutableDictionary * paramsMuDict_noEncode = [model convertModelIntoDictionary];
        if (![paramsMuDict_noEncode isKindOfClass:NSMutableDictionary.class]) {
            paramsMuDict_noEncode = [NSMutableDictionary dictionaryWithDictionary:paramsMuDict_noEncode];
        }
        [paramsMuDict_noEncode setObject:appId forKey:@"appId"];
        if ([PLVFdUtil checkStringUseable:model.userId]) {
            [paramsMuDict_noEncode setObject:model.userId forKey:@"uid"]; // TRTC接口，需以 uid 作为连麦ID字段名
        }

        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramsMuDict SHA256:YES signatureNonce:YES encrypt:YES requestEncrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            if (paramDict[@"signatureMethod"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"signatureMethod"] forKey:@"signatureMethod"];
            }
            if (paramDict[@"signatureNonce"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"signatureNonce"] forKey:@"signatureNonce"];
            }
            if (paramDict[@"encryptResponseType"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"encryptResponseType"] forKey:@"encryptResponseType"];
            }
            [paramsMuDict_noEncode setObject:timestamp forKey:@"timestamp"];
            return [PLVLiveAPIUtils createStringWithParamDict:paramsMuDict_noEncode];
        } httpMethod:PLV_HM_POST];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                if (success) { success(responseDict); }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

+ (void)getVOLCTokenWithModel:(PLVLinkMicGetTokenModel *)model success:(void (^)(NSDictionary * _Nonnull))success failure:(void (^)(NSError * _Nonnull))failure{
    if (![PLVFdUtil checkStringUseable:model.channelId] || ![PLVFdUtil checkStringUseable:model.userId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
        NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
        if (![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:@"appId或appSecret无效/未配置"];
            return;
        }
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/volcengine/auth"]; // VOLC AppId、连麦Token获取接口
        
        model.forGET = YES;
        NSMutableDictionary * paramsMuDict = [model convertModelIntoDictionary];
        if (![paramsMuDict isKindOfClass:NSMutableDictionary.class]) {
            paramsMuDict = [NSMutableDictionary dictionaryWithDictionary:paramsMuDict];
        }
        [paramsMuDict setObject:appId forKey:@"appId"];
        if ([PLVFdUtil checkStringUseable:model.userId]) {
            [paramsMuDict setObject:model.userId forKey:@"uid"]; // VOLC接口，需以 uid 作为连麦ID字段名
        }
        
        model.forGET = NO;
        NSMutableDictionary * paramsMuDict_noEncode = [model convertModelIntoDictionary];
        if (![paramsMuDict_noEncode isKindOfClass:NSMutableDictionary.class]) {
            paramsMuDict_noEncode = [NSMutableDictionary dictionaryWithDictionary:paramsMuDict_noEncode];
        }
        [paramsMuDict_noEncode setObject:appId forKey:@"appId"];
        if ([PLVFdUtil checkStringUseable:model.userId]) {
            [paramsMuDict_noEncode setObject:model.userId forKey:@"uid"]; // VOLC接口，需以 uid 作为连麦ID字段名
        }
        
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramsMuDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            if (paramDict[@"signatureMethod"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"signatureMethod"] forKey:@"signatureMethod"];
            }
            if (paramDict[@"signatureNonce"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"signatureNonce"] forKey:@"signatureNonce"];
            }
            if (paramDict[@"encryptResponseType"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"encryptResponseType"] forKey:@"encryptResponseType"];
            }
            [paramsMuDict_noEncode setObject:timestamp forKey:@"timestamp"];
            return [PLVLiveAPIUtils createStringWithParamDict:paramsMuDict_noEncode];
        } httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                if (success) { success(responseDict); }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

+ (void)getVOLCPublicStreamInfoWithModel:(PLVPublicStreamGetInfoModel *)model success:(void (^)(NSDictionary *))success failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:model.channelId] || ![PLVFdUtil checkStringUseable:model.userId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
        NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
        if (![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:@"appId或appSecret无效/未配置"];
            return;
        }
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/volcengine/auth"]; // VOLC AppId、连麦Token获取接口
        
        model.forGET = YES;
        NSMutableDictionary * paramsMuDict = [model convertModelIntoDictionary];
        if (![paramsMuDict isKindOfClass:NSMutableDictionary.class]) {
            paramsMuDict = [NSMutableDictionary dictionaryWithDictionary:paramsMuDict];
        }
        [paramsMuDict setObject:appId forKey:@"appId"];
        if ([PLVFdUtil checkStringUseable:model.userId]) {
            [paramsMuDict setObject:model.userId forKey:@"uid"]; // VOLC接口，需以 uid 作为连麦ID字段名
        }
        
        model.forGET = NO;
        NSMutableDictionary * paramsMuDict_noEncode = [model convertModelIntoDictionary];
        if (![paramsMuDict_noEncode isKindOfClass:NSMutableDictionary.class]) {
            paramsMuDict_noEncode = [NSMutableDictionary dictionaryWithDictionary:paramsMuDict_noEncode];
        }
        [paramsMuDict_noEncode setObject:appId forKey:@"appId"];
        if ([PLVFdUtil checkStringUseable:model.userId]) {
            [paramsMuDict_noEncode setObject:model.userId forKey:@"uid"]; // VOLC接口，需以 uid 作为连麦ID字段名
        }
        
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramsMuDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            if (paramDict[@"signatureMethod"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"signatureMethod"] forKey:@"signatureMethod"];
            }
            if (paramDict[@"signatureNonce"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"signatureNonce"] forKey:@"signatureNonce"];
            }
            if (paramDict[@"encryptResponseType"]) {
                [paramsMuDict_noEncode setObject:paramDict[@"encryptResponseType"] forKey:@"encryptResponseType"];
            }
            [paramsMuDict_noEncode setObject:timestamp forKey:@"timestamp"];
            return [PLVLiveAPIUtils createStringWithParamDict:paramsMuDict_noEncode];
        } httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                if (success) { success(responseDict); }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

#pragma mark - 文档上传相关接口

+ (void)getUploadTokenWithChannelId:(NSString *)channelId
                             fileId:(NSString *)fileId
                           fileName:(NSString *)fileName
                        convertType:(NSString *)type
                            success:(void (^)(NSDictionary *responseObject, NSDictionary *callback))success
                            failure:(void (^)(NSError *error))failure {
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    if (![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *message = PLVFDLocalizableString(@"appId或appSecret无效/未配置");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulUpload code:PLVFUploadErrorCodeGetToken_ParameterError info:message errorDescription:message];
        return;
    }
    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:fileId] ||
        ![PLVFdUtil checkStringUseable:fileName] ||
        ![PLVFdUtil checkStringUseable:type]) {
        NSString *message = PLVFDLocalizableString(@"请求参数无效");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulUpload code:PLVFUploadErrorCodeGetToken_ParameterError info:message errorDescription:message];
        return;
    }
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    [params setObject:appId forKey:@"appId"];
    [params setObject:channelId forKey:@"channelId"];
    [params setObject:fileId forKey:@"fileId"];
    [params setObject:fileName forKey:@"fileName"];
    [params setObject:type forKey:@"type"];
    NSString *timestamp = [PLVFdUtil curTimeStamp];
    [params setObject:timestamp forKey:@"timestamp"];
    if ([PLVLiveVideoConfig sharedInstance].realEnableSha256) {
        [params setObject:@"SHA256" forKey:@"signatureMethod"];
    }
    if ([PLVLiveVideoConfig sharedInstance].realEnableSignatureNonce) {
        NSString *signatureNonceString = [[NSUUID UUID] UUIDString];
        [params setObject:signatureNonceString forKey:@"signatureNonce"];
    }
    if ([PLVLiveVideoConfig sharedInstance].realEnableResponseEncrypt &&
        [PLVFSignConfig sharedInstance].encryptType == PLVEncryptType_AES) {
        [params setObject:@(1) forKey:@"encryptResponseType"];
    }
    
    NSArray *keys = [params.allKeys sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
        return [obj1 compare:obj2 options:NSCaseInsensitiveSearch];
    }];
    NSMutableString *paramStr = [NSMutableString string];
    for (NSString *key in keys) {
        [paramStr appendFormat:@"%@%@", key, params[key]];
    }
    NSString *plain = [NSString stringWithFormat:@"%@%@%@", appSecret, paramStr, appSecret];
    if ([PLVLiveVideoConfig sharedInstance].realEnableSha256) {
        NSString *sign = [[PLVDataUtil sha256String:plain] uppercaseString];
        [params setObject:sign forKey:@"sign"];
    } else {
        NSString *sign = [[PLVDataUtil md5HexDigest:plain] uppercaseString];
        [params setObject:sign forKey:@"sign"];
    }
    NSString *encodeFileName = [fileName stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLUserAllowedCharacterSet]];
    [params setObject:encodeFileName forKey:@"fileName"];
    
    [self getUploadTokenWithParams:params success:success failure:failure];
}

+ (void)getUploadTokenWithChannelId:(NSString *)channelId
                       channelToken:(NSString *)channelToken
                              appId:(NSString *)appId
                             fileId:(NSString *)fileId
                           fileName:(NSString *)fileName
                        convertType:(NSString *)type
                            success:(nonnull void (^)(NSDictionary * _Nonnull, NSDictionary * _Nullable))success failure:(nonnull void (^)(NSError * _Nonnull))failure {
    
    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:channelToken] ||
        ![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:fileId] ||
        ![PLVFdUtil checkStringUseable:fileName] ||
        ![PLVFdUtil checkStringUseable:type]) {
        NSString *message = PLVFDLocalizableString(@"请求参数无效");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulUpload code:PLVFUploadErrorCodeGetToken_ParameterError info:message errorDescription:message];
        return;
    }
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    [params setObject:appId forKey:@"appId"];
    NSString *encodeChannelToken = [channelToken stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLUserAllowedCharacterSet]];
    [params setObject:encodeChannelToken forKey:@"channelToken"];
    [params setObject:channelId forKey:@"channelId"];
    [params setObject:fileId forKey:@"fileId"];
    [params setObject:type forKey:@"type"];
    NSString *timestamp = [PLVFdUtil curTimeStamp];
    [params setObject:timestamp forKey:@"timestamp"];
    NSString *encodeFileName = [fileName stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLUserAllowedCharacterSet]];
    [params setObject:encodeFileName forKey:@"fileName"];
    [self getUploadTokenWithParams:params success:success failure:failure];
}

+ (void)getUploadTokenWithParams:(NSDictionary *)params success:(void (^)(NSDictionary * _Nonnull, NSDictionary *))success failure:(void (^)(NSError *))failure {
    
    NSString *urlString = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/channel/document/get-server-token"];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:urlString params:params httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSString *)responseDict[@"code"]).integerValue == 200) {
            NSDictionary *data = responseDict[@"data"];
            NSData *jsonData = [data[@"callback"] dataUsingEncoding:NSUTF8StringEncoding];
            NSDictionary *callbackDict = nil;
            if (jsonData) {
                callbackDict = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
            }
            NSString *convertStatus = data[@"convertStatus"];
            if (data && [data isKindOfClass:[NSDictionary class]] &&
                callbackDict && [callbackDict isKindOfClass:[NSDictionary class]]) {
                if (success) {
                    success(data, callbackDict);
                }
            } else if (data && [data isKindOfClass:[NSDictionary class]] &&
                       convertStatus && [convertStatus isKindOfClass:[NSString class]] &&
                       ([convertStatus isEqualToString:@"normal"] || [convertStatus isEqualToString:@"waitConvert"])) {
                if (success) {
                    success(data, nil);
                }
            } else {
                [self callBackFailBlock:failure modul:PLVFErrorCodeModulUpload code:PLVFUploadErrorCodeGetToken_DataError info:responseDict];
            }
        } else {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulUpload code:PLVFUploadErrorCodeGetToken_CodeError info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulUpload code:PLVFUploadErrorCodeGetToken_DataError info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

#pragma mark - 互动学堂相关接口
+ (void)requestAuthorizationForInteractiveClassWithChannelId:(NSUInteger)channelId invitaionCode:(NSString *)invitationCode success:(void (^)(NSString *))success failure:(void (^)(NSError *))failure {
    if (!invitationCode || [invitationCode isKindOfClass:[NSNull class]]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:[NSString stringWithFormat:@"https://live.polyv.cn/auth/%lu/pc-code", (unsigned long)channelId] params:@{@"code" : invitationCode} plainBlock:nil  httpMethod:PLV_HM_POST];//效验验证码接口
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            if ([responseDict[@"status"] isEqualToString:@"success"]) {
                success(responseDict[@"data"]);
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

#pragma mark - 美颜相关接口
/// 获取美颜设置
+ (void)getBeautySettingWithPackageName:(NSString *)packageName
                                success:(void (^)(NSDictionary *responseObject))success
                                failure:(void (^)(NSError *error))failure {
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    if (![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *message = PLVFDLocalizableString(@"appId或appSecret无效/未配置");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulBeauty code:PLVFBeautyErrorCodeBeautySetting_ParameterError info:message errorDescription:message];
        return;
    }
    if (![PLVFdUtil checkStringUseable:packageName]) {
        NSString *message = PLVFDLocalizableString(@"请求参数无效");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulBeauty code:PLVFBeautyErrorCodeBeautySetting_ParameterError info:message errorDescription:message];
        return;
    }
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    [params setObject:appId forKey:@"appId"];
    [params setObject:packageName forKey:@"packageName"];
    extern NSString *PLVSDKGlobalBeautySDKVersion;
    if ([PLVFdUtil checkStringUseable:PLVSDKGlobalBeautySDKVersion]){
        [params setObject:PLVSDKGlobalBeautySDKVersion forKey:@"version"]; //增加版本号用以对应新版美颜SDK
    }
    
    NSString *urlString = [NSString stringWithFormat:@"https://api.polyv.net/live/v4/user/beauty-setting/get"];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:urlString params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSString *)responseDict[@"code"]).integerValue == 200) {
            NSDictionary *data = responseDict[@"data"];
            if (data && [data isKindOfClass:[NSDictionary class]]) {
                if (success) {
                    success(data);
                }
            } else {
                [self callBackFailBlock:failure modul:PLVFErrorCodeModulBeauty code:PLVFBeautyErrorCodeBeautySetting_DataError info:responseDict];
            }
        } else {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulBeauty code:PLVFBeautyErrorCodeBeautySetting_CodeError info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulBeauty code:PLVFBeautyErrorCodeBeautySetting_DataError info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

#pragma mark - Private methods
+ (void)loadChannelInfoWithUserId:(NSString *)userId channelId:(NSString *)channelId isStandby:(BOOL)isStandby completion:(void (^)(PLVChannelInfoModel *))completion failure:(void (^)(NSError *))failure {
    BOOL userId_OK = [PLVFdUtil checkStringUseable:userId];
    BOOL channelId_OK = [PLVFdUtil checkStringUseable:channelId];
    if (!userId_OK || !channelId_OK) {
        NSString * paramErrorString = (userId_OK == NO ? [NSString stringWithFormat:@"userId:%@",userId] : [NSString stringWithFormat:@"channelId:%@",channelId]);
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPlay
                           code:PLVFPlayErrorCodeGetChannelInfo_ParameterError
                           info:nil
               errorDescription:[NSString stringWithFormat:PLVFDLocalizableString(@"频道信息请求失败，参数错误 %@"),paramErrorString]];
    }else {
        userId = [userId stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]];
        NSString *url;
        if (isStandby) {
            url = [NSString stringWithFormat:@"https://live.polyv.net/service/lts3/enc_%@_%@.json", userId,channelId];
        } else {
            url = [NSString stringWithFormat:@"https://player.polyv.net/service/lts3/enc_%@_%@.json", userId,channelId];
        }
        [self loadChannelInfoWithUrl:url userId:userId channelId:channelId extra:@{plv_extra_time_interval_key:@(6)} completion:completion failure:failure];
    }
}

+ (void)loadChannelInfoWithUrl:(NSString *)url userId:(NSString *)userId channelId:(NSString *)channelId extra:(NSDictionary *)extra completion:(void (^)(PLVChannelInfoModel *))completion failure:(void (^)(NSError *))failure {
    NSMutableURLRequest *mRequest = [PLVLiveAPIUtils requestWithURL:url params:nil plainBlock:nil httpMethod:PLV_HM_GET];
    NSNumber *interval = extra[plv_extra_time_interval_key];
    if (interval) {
        mRequest.timeoutInterval = [interval doubleValue];
    }
    [PLVFNetworkUtil requestDictionary:mRequest completion:^(NSDictionary *responseDict) {
        NSNumber *code = responseDict[@"code"];
        if (code.integerValue == 200) {
            @try {
                NSString *hexEncrypted = [responseDict objectForKey:@"body"];
                NSData *encryptedData = [PLVDataUtil dataForHexString:hexEncrypted];
                NSData *decryptedData = [PLVDataUtil AES128DecryptData:encryptedData withKey:[PLVKeyUtil getChannelInfoKey] iv:[PLVKeyUtil getChannelInfoIv]];
                NSString *base64Str = [[NSString alloc] initWithData:decryptedData encoding:NSUTF8StringEncoding];
                decryptedData = [[NSData alloc] initWithBase64EncodedString:base64Str options:NSDataBase64DecodingIgnoreUnknownCharacters];
                NSError *jsonError = nil;
                NSDictionary *jsonDict = [NSJSONSerialization JSONObjectWithData:decryptedData options:NSJSONReadingMutableContainers error:&jsonError];
                if (jsonError) {
                    [self callBackFailBlock:failure
                                      modul:PLVFErrorCodeModulPlay
                                       code:PLVFPlayErrorCodeGetChannelInfo_DataError
                                       info:nil
                           errorDescription:jsonError.localizedDescription];
                } else {
                    PLVChannelInfoModel *channel = [PLVChannelInfoModel channelInfoModelWithDataDict:jsonDict];
                    if (completion) {
                        completion(channel);
                    }
                }
            } @catch (NSException *exception) {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulPlay
                                   code:PLVFPlayErrorCodeGetChannelInfo_DataError
                                   info:nil
                       errorDescription:exception.reason];
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulPlay
                               code:PLVFPlayErrorCodeGetChannelInfo_CodeError
                               info:responseDict
                   errorDescription:responseDict[@"body"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulPlay
                               code:PLVFPlayErrorCodeGetChannelInfo_DataError
                               info:error.localizedDescription
                   errorDescription:[NSString stringWithFormat:PLVFDLocalizableString(@"请求解析失败 %@"),error.localizedDescription]];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}


#pragma mark - 推流
+ (void)notifyStreamModeWithChannelId:(NSString *)channelId
                               stream:(NSString *)stream
                            sessionId:(NSString *)sessionId
                           videowidth:(NSUInteger)videowidth
                          videoheight:(NSUInteger)videoheight
                     continueLastLive:(BOOL)isContinue
                              success:(void (^)(NSString * responseContent))success
                              failure:(void (^)(NSError * error))failure {
    if(![PLVFdUtil checkStringUseable:channelId] ||
       ![PLVFdUtil checkStringUseable:stream] ||
       videowidth == 0 ||
       videoheight == 0) {
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulLink code:PLVFLinkErrorCodeNotifyStreamFailed_ParameterError info:nil];
        return;
    }
    
    NSMutableDictionary * mParam = [NSMutableDictionary dictionary];
    mParam[@"channelId"] = channelId;
    mParam[@"stream"] = stream;
    mParam[@"videoWidth"] = @(videowidth);
    mParam[@"videoHeight"] = @(videoheight);
    mParam[@"rtcEnabled"] = @"Y";
    mParam[@"goOn"] = isContinue ? @"Y" : @"N";
    mParam[@"pushClient"] = @"iOSSDK";
    mParam[@"appId"] = [PLVLiveVideoConfig sharedInstance].appId;

    if ([PLVFdUtil checkStringUseable:sessionId]) { mParam[@"sessionId"] = sessionId; }
    
    NSMutableURLRequest * mRequest = [PLVLiveAPIUtils requestWithURL:@"https://api.polyv.net/live/v3/client/notify-stream2" params:mParam SHA256:YES signatureNonce:YES encrypt:YES requestEncrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_POST]; // 设置流模式
    [PLVFNetworkUtil requestDictionary:mRequest completion:^(NSDictionary *responseDict) {
        NSNumber *code = responseDict[@"code"];
        if (code.integerValue == 200) {
            if (success) {
                NSString *dataString = PLV_SafeStringForDictKey(responseDict, @"data");
                success(dataString);
            }
        } else {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulLink code:PLVFLinkErrorCodeNotifyStreamFailed_DataError info:responseDict[@"message"]];
        }
    } fail:^(NSError * error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulLink code:PLVFLinkErrorCodeNotifyStreamFailed_DataError info:nil];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)requestChannelSessionIdWithChannelId:(NSString *)channelId
                                      stream:(NSString *)stream
                                     success:(void (^)(NSString *))success
                                     failure:(void (^)(NSError * error))failure {
    [self requestChannelSessionIdWithChannelId:channelId stream:stream tryCount:0 success:success failure:failure];
}

+ (void)requestChannelSessionIdWithChannelId:(NSString *)channelId
                                      stream:(NSString *)stream
                                    tryCount:(int)tryCount
                                     success:(void (^)(NSString *))success
                                     failure:(void (^)(NSError * error))failure {
    if(![PLVFdUtil checkStringUseable:channelId] ||
       ![PLVFdUtil checkStringUseable:stream]) {
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulLink code:PLVFLinkErrorCodeChannelSessionIDFailed_ParameterError info:nil];
        return;
    }
    
    __block int index = tryCount;
    NSMutableDictionary * params = [NSMutableDictionary dictionary];
    params[@"channelId"] = channelId;
    params[@"stream"] = stream;
    params[@"appId"] = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *url = @"https://api.polyv.net/live/v3/channel/session/client-query";
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        NSNumber *code = responseDict[@"code"];
        if (code.integerValue == 200) {
            if (success) {
                NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
                NSString *sessionId = PLV_SafeStringForDictKey(data , @"sessionId");
                success(sessionId);
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
        }
    } fail:^(NSError * error) {
        index++;
        if (index >= 3) {
            [self callBackFailBlock:failure networkError:error];
        } else {
            [self callBackFailBlock:nil networkError:error];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self requestChannelSessionIdWithChannelId:channelId stream:stream tryCount:index success:success failure:failure];
            });
        }
    }];
}

+ (void)requestPushStreamMixActionWitMixInfo:(NSDictionary *)mixInfo success:(void (^)(NSDictionary * responseDict))success failure:(void (^)(NSError * error))failure{
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    if (![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *message = PLVFDLocalizableString(@"appId或appSecret无效/未配置");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulLink code:PLVFLinkErrorCodeMixActionFailed_ParameterError info:message errorDescription:message];
        return;
    }
    
    if(![PLVFdUtil checkDictionaryUseable:mixInfo]) {
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulLink code:PLVFLinkErrorCodeMixActionFailed_ParameterError info:nil];
        return;
    }
    
    if(![PLVFdUtil checkDictionaryUseable:mixInfo[@"outputParam"]] ||
       ![PLVFdUtil checkDictionaryUseable:mixInfo[@"encodeParam"]] ||
       ![PLVFdUtil checkDictionaryUseable:mixInfo[@"publishCdnParam"]] ||
       ![PLVFdUtil checkArrayUseable:mixInfo[@"userList"]] ||
       ![PLVFdUtil checkStringUseable:mixInfo[@"action"]] ||
       ![PLVFdUtil checkStringUseable:mixInfo[@"roomId"]]) {
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulLink code:PLVFLinkErrorCodeMixActionFailed_ParameterError info:nil];
        return;
    }
    
    NSString * cdnUrl = mixInfo[@"publishCdnParam"][@"cdnUrl"];
    NSString * charactersToEscape = @"&";
    NSCharacterSet * allowedCharacters = [[NSCharacterSet characterSetWithCharactersInString:charactersToEscape] invertedSet];
    NSString * encodeStr = [cdnUrl stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
    NSMutableDictionary * encodeMixInfo = [NSMutableDictionary dictionaryWithDictionary:mixInfo];
    if ([PLVFdUtil checkStringUseable:encodeStr]) {
        encodeMixInfo[@"publishCdnParam"] = @{@"cdnUrl" : encodeStr};
    }
    
    NSError * parseError;
    NSData * jsonData = [NSJSONSerialization dataWithJSONObject:mixInfo options:NSJSONWritingPrettyPrinted error:&parseError];
    NSString * mixInfoString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    NSData * encodeJsonData = [NSJSONSerialization dataWithJSONObject:encodeMixInfo options:NSJSONWritingPrettyPrinted error:&parseError];
    NSString * encodeMixInfoString = [[NSString alloc] initWithData:encodeJsonData encoding:NSUTF8StringEncoding];
    
    if (![PLVFdUtil checkStringUseable:mixInfoString] ||
        jsonData == nil ||
        parseError != nil) {
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulLink code:PLVFLinkErrorCodeMixActionFailed_ParameterError info:nil];
    }
    
    NSMutableDictionary * mParam = [NSMutableDictionary dictionary];
    mParam[@"data"] = [PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt ? mixInfoString : encodeMixInfoString;
    mParam[@"appId"] = appId;

    NSMutableURLRequest * mRequest = [PLVLiveAPIUtils requestWithURL:@"https://api.polyv.net/live/v3/channel/mcu/mix/action" params:mParam SHA256:YES signatureNonce:YES encrypt:YES requestEncrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        NSMutableDictionary * params = [NSMutableDictionary dictionaryWithDictionary:paramDict];
        params[@"data"] = mixInfoString;
        return [PLVLiveAPIUtils createStringWithParamDict:params];
    } httpMethod:PLV_HM_POST]; // 混流配置
    [PLVFNetworkUtil requestDictionary:mRequest completion:^(NSDictionary *responseDict) {
        NSNumber *code = responseDict[@"code"];
        NSDictionary * dataDict = responseDict[@"data"];
        BOOL requestResult = [(NSNumber *)dataDict[@"res"] boolValue];
        NSString * message = responseDict[@"message"];
        if (code.integerValue == 200) {
            if (requestResult) {
                if (success) { success(responseDict); }
            }else{
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulLink
                                   code:PLVFLinkErrorCodeMixActionFailed_DataError
                                   info:responseDict
                       errorDescription:message];
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulLink
                               code:PLVFLinkErrorCodeMixActionFailed_CodeError
                               info:responseDict
                   errorDescription:message];
        }
    } fail:^(NSError * error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulLink code:PLVFLinkErrorCodeMixActionFailed_DataError info:nil];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

#pragma mark - 聊天消息回放

+ (void)requestPlaybakMessageHistoryIndexWithRoomId:(NSString *)roomId
                                          sessionId:(NSString *)sessionId
                                            videoId:(NSString *)videoId
                                               page:(NSUInteger)page
                                            success:(void (^)(NSDictionary * responseDict))success
                                            failure:(void (^)(NSError * error))failure {
    if (![PLVFdUtil checkStringUseable:roomId] ||
        ![PLVFdUtil checkStringUseable:sessionId] ||
        ![PLVFdUtil checkStringUseable:videoId]) {
        NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"参数错误") };
        NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120103 userInfo:userInfo];
        if (failure) {
            failure(failError);
        }
        return;
    }
    
    NSMutableURLRequest *request;
    if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
        [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
        NSString *urlString = [NSString stringWithFormat:@"https://apichat.polyv.net/front/channel/playback/history-part/%@/%@/%@/%zd", roomId, sessionId, videoId, (page > 0 ? page : 1)];
        request = [PLVLiveAPIUtils requestWithURL:urlString params:nil plainBlock:nil httpMethod:PLV_HM_GET];
    } else if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi) {
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        if (![PLVFdUtil checkStringUseable:liveConfig.appId] || ![PLVFdUtil checkStringUseable:liveConfig.appSecret]) {
            NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"appId或appSecret参数无效") };
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120103 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
            return;
        }
        NSDictionary *params = @{
            @"channelId" : roomId,
            @"sessionId" : sessionId,
            @"videoId" : videoId,
            @"page" : @(page),
            @"appId" : liveConfig.appId
        };
        NSString *url = @"https://api.polyv.net/live/v4/chat/playback-history-part";
        request = [PLVLiveAPIUtils requestWithURL:url params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_GET];
    } else {
        NSString *urlString = [NSString stringWithFormat:@"https://apichat.polyv.net/front/channel/playback-history-part/%@/%@/%@/%zd", roomId, sessionId, videoId, (page > 0 ? page : 1)];
        request = [PLVLiveAPIUtils requestWithURL:urlString params:nil plainBlock:nil httpMethod:PLV_HM_GET];
    }
    [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
         if ([PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2 &&
             ![PLVFdUtil checkDictionaryUseable:responseDict]) {
             responseDict = [PLVFNetworkUtil parseDecryptData:responseCont];
         }
        NSString *status = PLV_SafeStringForDictKey(responseDict, @"status");
        if (![status isEqualToString:@"success"]) {
            NSDictionary *userInfo = nil;
            NSString *errorMsg = PLV_SafeStringForDictKey(responseDict, @"message");
            if (errorMsg) {
                userInfo = @{ NSLocalizedDescriptionKey : errorMsg };
            }
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120103 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
            return;
        }
        
        NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
        NSArray *partDetail = PLV_SafeArraryForDictKey(data, @"partDetail");
        if (![partDetail isKindOfClass:[NSArray class]]) {
            NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"返回数据格式异常") };
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120103 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
            return;
        }
        
        success(data);
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"数据解析失败") };
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120103 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)requestPlaybakMessageHistoryWithRoomId:(NSString *)roomId
                                     sessionId:(NSString *)sessionId
                                       indexId:(NSUInteger)indexId
                                       success:(void (^)(NSArray *responseArray))success
                                       failure:(void (^)(NSError * error))failure {
    if (![PLVFdUtil checkStringUseable:roomId] ||
        ![PLVFdUtil checkStringUseable:sessionId] ||
        indexId <= 0) {
        NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"参数错误") };
        NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120104 userInfo:userInfo];
        if (failure) {
            failure(failError);
        }
        return;
    }
    
    NSMutableURLRequest *request;
    if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
        [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
        NSString *urlString = [NSString stringWithFormat:@"https://apichat.polyv.net/front/channel/playback/history-list/%@/%@/%zd", roomId, sessionId, indexId];
        request = [PLVLiveAPIUtils requestWithURL:urlString params:nil plainBlock:nil httpMethod:PLV_HM_GET];
    } else if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi) {
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        if (![PLVFdUtil checkStringUseable:liveConfig.appId] || ![PLVFdUtil checkStringUseable:liveConfig.appSecret]) {
            NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"appId或appSecret无效") };
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120104 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
            return;
        }
        NSDictionary *params = @{
            @"channelId" : roomId,
            @"sessionId" : sessionId,
            @"page" : @(indexId),
            @"appId" : liveConfig.appId
        };
        NSString *url = @"https://api.polyv.net/live/v4/chat/playback-history-list";
        request = [PLVLiveAPIUtils requestWithURL:url params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_GET];
    } else {
        NSString *urlString = [NSString stringWithFormat:@"https://apichat.polyv.net/front/channel/playback-history-list/%@/%@/%zd", roomId, sessionId, indexId];
        request = [PLVLiveAPIUtils requestWithURL:urlString params:nil plainBlock:nil httpMethod:PLV_HM_GET];
    }
    [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
        if ([PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2 &&
            ![PLVFdUtil checkDictionaryUseable:responseDict]) {
            responseDict = [PLVFNetworkUtil parseDecryptData:responseCont];
        }
        NSString *status = PLV_SafeStringForDictKey(responseDict, @"status");
        if (![status isEqualToString:@"success"]) {
            NSDictionary *userInfo = nil;
            NSString *errorMsg = PLV_SafeStringForDictKey(responseDict, @"message");
            if (errorMsg) {
                userInfo = @{ NSLocalizedDescriptionKey : errorMsg };
            }
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120104 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
            return;
        }
        
        NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
        NSArray *list = PLV_SafeArraryForDictKey(data, @"list");
        if (!list) {
            NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"返回数据格式异常") };
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120104 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
            return;
        }
        
        success(list);
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"数据解析失败") };
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120104 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

#pragma mark - 日志上报

+ (void)requestLoganReportInfoWithUserId:(NSString *)userId completion:(void (^)(NSDictionary *responseDict))completion failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:userId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    }
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    [params setObject:userId forKey:@"userId"];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:@"https://api.polyv.net/live/inner/v3/client/sdk/upload-log-task/get" params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:@"polyv_sdk_api_innor"];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        NSNumber *code = responseDict[@"code"];
        if (code.integerValue == 200) {
            if (completion) {
                completion(responseDict);
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
        }
    } fail:failure];
}

+ (void)getUploadTokenWithUserId:(NSString *)userId
                       channelId:(NSString *)channelId
                         success:(void (^)(NSDictionary *responseObject))success
                         failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:userId]) {
        NSString *message = @"请求参数无效";
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulUpload code:PLVFUploadErrorCodeGetToken_ParameterError info:message errorDescription:message];
        return;
    }
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    [params setObject:userId forKey:@"userId"];
    if ([PLVFdUtil checkStringUseable:channelId]) {
        [params setObject:channelId forKey:@"channelId"];
    }
    
    NSString *urlString = [NSString stringWithFormat:@"https://api.polyv.net/live/inner/v3/client/sdk/upload-token"];

    NSMutableURLRequest *request = [self requestForcedSafeModeWithURL:urlString params:params plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:[PLVKeyUtil getApiLogUtilsKey]];
    } httpMethod:PLV_HM_POST];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSString *)responseDict[@"code"]).integerValue == 200) {
            NSDictionary *data = responseDict[@"data"];
            if (data && [data isKindOfClass:[NSDictionary class]]) {
                if (success) {
                    success(data);
                }
            } else {
                [self callBackFailBlock:failure modul:PLVFErrorCodeModulUpload code:PLVFUploadErrorCodeGetToken_DataError info:responseDict];
            }
        } else {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulUpload code:PLVFUploadErrorCodeGetToken_CodeError info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulUpload code:PLVFUploadErrorCodeGetToken_DataError info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

#pragma mark - Utils
/// 调用接口（非网络问题）失败时使用此方法上报错误码及执行回调 block。
+ (void)callBackFailBlock:(void (^)(NSError *))failBlock
                    modul:(PLVFErrorCodeModul)modul
                     code:(NSInteger)code
                     info:(id)info {
    [self callBackFailBlock:failBlock modul:modul code:code info:info errorDescription:nil];
}

/// 调用接口（非网络问题）失败时使用此方法上报错误码及执行回调 block。同时使用 errorDescription 字段替换原先默认的 localizedDescription 属性
+ (void)callBackFailBlock:(void (^)(NSError *))failBlock
                    modul:(PLVFErrorCodeModul)modul
                     code:(NSInteger)code
                     info:(id)info
         errorDescription:(NSString *)errorDesc {
    NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:modul code:code];
    
    if (errorDesc && [errorDesc isKindOfClass:[NSString class]] && errorDesc.length > 0) {
        NSDictionary *userInfo = [NSDictionary dictionaryWithObject:errorDesc forKey:NSLocalizedDescriptionKey];
        NSError *userError = [NSError errorWithDomain:failError.domain code:failError.code userInfo:userInfo];
        failError = userError;
    }

    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:info];
    
    if (failBlock) {
        failBlock(failError);
    }
}

/// 调用接口（因网络问题）失败时使用此方法生成错误码并上报及执行回调 block
+ (void)callBackFailBlock:(void (^)(NSError *))failBlock
             networkError:(NSError *)error {
    NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulHttp code:PLVFHttpErrorCodeAllNetworkFailure];
    NSString *information = [error.description stringByReplacingOccurrencesOfString:@"&" withString:@" "];
    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:information];
    
    if (failBlock) {
        NSString *errorMessage = error.localizedDescription ?: PLVFDLocalizableString(@"网络错误");
        NSDictionary *userInfo = [NSDictionary dictionaryWithObject:errorMessage forKey:NSLocalizedDescriptionKey];
        NSError *UserError = [NSError errorWithDomain:myError.domain code:myError.code userInfo:userInfo];
        failBlock(UserError);
    }
}

/// 创建需要强制开启安全四件套的接口，签名默认大写
+ (NSMutableURLRequest *)requestForcedSafeModeWithURL:(nonnull NSString *)url
                                               params:(nullable NSDictionary *)params
                                           plainBlock:(nullable NSString* (^)(NSString *timestamp, NSDictionary *paramDict))plainBlock
                                           httpMethod:(nonnull NSString *)httpMethod {
    NSMutableDictionary *paramDict = [NSMutableDictionary dictionaryWithDictionary:params];
    
    if (plainBlock) {
        NSString *timestamp = [PLVFdUtil curTimeStamp];
        [paramDict setObject:timestamp forKey:@"timestamp"];
        [paramDict setObject:@"SHA256" forKey:@"signatureMethod"];
        NSString *signatureNonceString = [[NSUUID UUID] UUIDString];
        [paramDict setObject:signatureNonceString forKey:@"signatureNonce"];
        [paramDict setObject:@(1) forKey:@"encryptResponseType"];
        
        NSString *sign = plainBlock(timestamp, [paramDict copy]);
        sign = [PLVDataUtil sha256String:sign];
        if (sign) {
            sign = [sign uppercaseString];
            [paramDict setObject:sign forKey:@"sign"];
        }
    }
    
    NSURL * urlObj = [NSURL URLWithString:url];
    BOOL needHttpDNS = ![urlObj.scheme isEqualToString:@"https"] && [PLVLiveVideoConfig sharedInstance].enableHttpDNS && ![PLVLiveVideoConfig sharedInstance].enableIPV6;
    BOOL useIp = NO;
    
    if (needHttpDNS) { // 需使用HttpDNS
        NSString * httpDnsUrl = [PLVLiveAPIUtils getHttpDNSWithURLString:url];
        if (httpDnsUrl && httpDnsUrl.length > 0) {
            url = httpDnsUrl;
            useIp = YES;
        }
    }
    
    NSMutableURLRequest * request;
    if (httpMethod && [httpMethod isEqualToString:PLV_HM_POST]) {
        NSMutableDictionary *postParams = [NSMutableDictionary dictionary];
        BOOL shouldAES256Encrypt = NO;
        if ([url containsString:@"?appId="] || [url containsString:@"&appId="]) {
            shouldAES256Encrypt = YES;
        }
        for (NSString *key in paramDict.allKeys) {
            if ([key isEqualToString:@"appId"]) {
                if ([url containsString:@"?"]) {
                    url = [url stringByAppendingFormat:@"&appId=%@", paramDict[@"appId"]];
                } else {
                    url = [url stringByAppendingFormat:@"?appId=%@", paramDict[@"appId"]];
                }
                shouldAES256Encrypt = YES;
            } else {
                [postParams setObject:paramDict[key] forKey:key];
            }
        }
        NSString *xbodyString = [self jsonStringWithJSONObject:postParams];
        if (shouldAES256Encrypt) {
            xbodyString = [[PLVDataUtil AES256EncryptData:[xbodyString dataUsingEncoding:NSUTF8StringEncoding] withKey:[PLVLiveVideoConfig sharedInstance].appSecret iv:[PLVKeyUtil getApiUtilsIv]] base64EncodedStringWithOptions:0];
        } else {
            xbodyString = [[PLVDataUtil AES128EncryptData:[xbodyString dataUsingEncoding:NSUTF8StringEncoding] withKey:[PLVKeyUtil getApiUtilsKey] iv:[PLVKeyUtil getApiUtilsIv]] base64EncodedStringWithOptions:0];
        }
        if (xbodyString && xbodyString.length > 0) {
            NSString *xbody = [NSString stringWithFormat:@"xbody=%@",xbodyString];
            NSString *charactersToEscape = @"+";
            NSCharacterSet *allowedCharacters = [[NSCharacterSet characterSetWithCharactersInString:charactersToEscape] invertedSet];
            xbody = [xbody stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
            request = [PLVFNetworkUtil requestWithURLString:url params:nil httpMethod:httpMethod timeoutInterval:12.0 userAgent:[PLVLiveVideoConfig userAgent] headerParams:@{@"x-e-type":@"1"}];
            [request setHTTPBody:[xbody dataUsingEncoding:NSUTF8StringEncoding]];
        } else {
            request = [PLVFNetworkUtil requestWithURLString:url params:postParams httpMethod:httpMethod timeoutInterval:12.0 userAgent:[PLVLiveVideoConfig userAgent]];
        }
    } else {
        request = [PLVFNetworkUtil requestWithURLString:url params:paramDict httpMethod:httpMethod timeoutInterval:12.0 userAgent:[PLVLiveVideoConfig userAgent]];
    }
    
    if (needHttpDNS && useIp) { // 需使用HttpDNS
        [request setValue:urlObj.host forHTTPHeaderField:@"host"];
    }
    return request;
}

/// 获取无换行无空格的jsonString
+ (NSString *)jsonStringWithJSONObject:(id)jsonObject {
    NSError *error;
    NSMutableString *mutStr = nil;
    NSData *jsonData;
    if (@available(iOS 11.0, *)) {
        jsonData = [NSJSONSerialization dataWithJSONObject:jsonObject options:NSJSONWritingSortedKeys error:&error];
    } else {
        jsonData = [NSJSONSerialization dataWithJSONObject:jsonObject options:kNilOptions error:&error];
    }
    if (jsonData) {
    NSString *jsonString = [[NSString alloc]initWithData:jsonData encoding:NSUTF8StringEncoding];
        mutStr = [NSMutableString stringWithString:jsonString];
    }
    return mutStr ? [mutStr copy] : nil;
}

@end
