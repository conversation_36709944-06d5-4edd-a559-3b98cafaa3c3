//
//  PLVLiveVClassAPI.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/7/1.
//  Copyright © 2021 PLV. All rights reserved.
//

#import "PLVLiveVClassAPI.h"
#import "PLVLiveVideoConfig.h"
#import "PLVLiveAPIUtils.h"
#import "PLVLiveHttpDnsManager.h"
#import "PLVLinkMicGetTokenModel.h"
#import "PLVLinkMicGetTokenModel+PrivateInfo.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

static CGFloat kTimeOutInterval = 12.0;
static NSString *kVClassDomain = @"https://api.vclass.com/hi-class-api";
static NSString *kVClassTeachToken = nil;
static NSString *kVClassWatchToken = nil;

@interface PLVLiveVClassAPI ()

@property (class, nonatomic, strong) NSString *watchToken;

@end

@implementation PLVLiveVClassAPI

#pragma mark - [ Public Method ]

+ (void)courseOrLessonSimpleInfoWithCourseCode:(NSString *)courseCode
                                      lessonId:(NSString *)lessonId
                                       success:(void (^)(NSDictionary *responseDict))successHandler
                                       failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:courseCode] &&
        ![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课程号或课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    if ([PLVFdUtil checkStringUseable:courseCode]) {
        paramDict[@"courseCode"] = [PLVFdUtil URLEncodedString:courseCode];
    }
    if ([PLVFdUtil checkStringUseable:lessonId]) {
        paramDict[@"lessonId"] = lessonId;
    }
    [self setSignWithParamDictionary:paramDict];
    NSString *url = [NSString stringWithFormat:@"%@/common/v1/get-simple-info", kVClassDomain];
    NSMutableURLRequest *request = [self GETRequestWithURLString:url params:paramDict];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleCourseOrLessonSimpleInfoResponse:responseObject
                                                 success:successHandler
                                                 failure:failureHandler];
    } failure:failureHandler];
}

+ (void)lessonFinishInfoWithLessonId:(NSString *)lessonId
                             isTeach:(BOOL)isTeach
                             success:(void (^)(NSDictionary *responseDict))successHandler
                             failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:lessonId]) {
         NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课节ID不可为空")];
         if (failureHandler) {
             failureHandler(error);
         }
         return;
     }
     NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
     paramDict[@"lessonId"] = lessonId;
     [self setSignWithParamDictionary:paramDict];
     NSString *url = [NSString stringWithFormat:@"%@/common/v1/get-lesson-finish", kVClassDomain];
     NSMutableURLRequest *request = [self GETRequestWithURLString:url params:paramDict needToken:YES teach:isTeach];
     __weak typeof(self) weakSelf = self;
     [PLVFNetworkUtil request:request success:^(id responseObject) {
         [weakSelf handleLessonFinishInfoResponse:responseObject
                                          success:successHandler
                                          failure:failureHandler];
     } failure:failureHandler];
}

#pragma mark - [ Private Method ]

#pragma mark Getter & Setter

+ (NSString *)teachToken {
    return kVClassTeachToken;
}

+ (void)setTeachToken:(NSString *)teachToken {
    kVClassTeachToken = teachToken;
}

+ (NSString *)watchToken {
    return kVClassWatchToken;
}

+ (void)setWatchToken:(NSString *)watchToken {
    kVClassWatchToken = watchToken;
}

#pragma mark Response Handler

+ (void)handleResponse:(id)responseObject success:(void (^)(id responseObject))successHandler failure:(void (^)(NSError *error))failureHandler {
    if (responseObject && [responseObject isKindOfClass:[NSDictionary class]]) {
        NSString *status = responseObject[@"status"];
        BOOL success = [responseObject[@"success"] boolValue];
        if ([status isEqualToString:@"success"] && success) {
            if (successHandler) {
                successHandler(responseObject);
            }
        } else {
            NSInteger errorCode = -1;
            NSString *errorDesc = PLVFDLocalizableString(@"接口请求失败");
            id errorDict = responseObject[@"error"];
            if (errorDict && [errorDict isKindOfClass:[NSDictionary class]] && [errorDict count] > 0) {
                if ([PLVFdUtil checkStringUseable:errorDict[@"desc"]]) {
                    errorDesc = errorDict[@"desc"];
                }
                if ([errorDict[@"code"] integerValue] != 0) {
                    errorCode = [errorDict[@"code"] integerValue];
                    errorDesc = [errorDesc stringByAppendingFormat:@"(%zd)", errorCode];
                }
            }
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeRequestFail desc:errorDesc];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } else {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
        if (failureHandler) {
            failureHandler(error);
        }
    }
}

+ (void)handleCourseOrLessonSimpleInfoResponse:(id)responseObject
                                       success:(void (^)(NSDictionary *responseDict))successHandler
                                       failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSDictionary class]]) {
            if (successHandler) {
                successHandler((NSDictionary *)data);
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

+ (void)handleLessonFinishInfoResponse:(id)responseObject
                               success:(void (^)(NSDictionary *responseDict))successHandler
                               failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSDictionary class]]) {
            if (successHandler) {
                successHandler((NSDictionary *)data);
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

#pragma mark Utils

/// POST接口工具方法，无需鉴权
/// PLVLiveVClassAPI 里面的POST方法统一使用该私有方法生成 NSMutableURLRequest 对象，方便日后可能发生的底层方法的替换
+ (NSMutableURLRequest *)POSTRequestWithURLString:(NSString *)url
                                           params:(NSDictionary *)paramDict {
    return [self POSTRequestWithURLString:url params:paramDict needToken:NO teach:NO];
}

/// GET接口工具方法，无需鉴权
/// PLVLiveVClassAPI 里面的GET方法统一使用该私有方法生成 NSMutableURLRequest 对象，方便日后可能发生的底层方法的替换
+ (NSMutableURLRequest *)GETRequestWithURLString:(NSString *)url
                                          params:(NSDictionary *)paramDict {
    return [self GETRequestWithURLString:url params:paramDict needToken:NO teach:NO];
}

/// POST接口工具方法，需鉴权
+ (NSMutableURLRequest *)POSTRequestWithURLString:(NSString *)url
                                           params:(NSDictionary *)paramDict
                                        needToken:(BOOL)needToken
                                            teach:(BOOL)teach {
    NSString *paramString = [self jsonStringFromDictionary:paramDict];
    NSMutableURLRequest *request = [PLVFNetworkUtil jsonBodyPostRequestWithURLString:url
                                                                         paramString:paramString
                                                                     timeoutInterval:kTimeOutInterval
                                                                           userAgent:[PLVLiveVideoConfig userAgent]];
    [request setValue:@"application/json;charset=utf8" forHTTPHeaderField:@"Content-Type"];
    
    if (needToken) {
        NSString *token = teach ? PLVLiveVClassAPI.teachToken : PLVLiveVClassAPI.watchToken;
        if ([PLVFdUtil checkStringUseable:token]) {
            [request setValue:token forHTTPHeaderField:@"Authorization"];
        }
    }
    return request;
}

/// GET接口工具方法，需鉴权
+ (NSMutableURLRequest *)GETRequestWithURLString:(NSString *)url
                                          params:(NSDictionary *)paramDict
                                       needToken:(BOOL)needToken
                                           teach:(BOOL)teach {
    NSMutableURLRequest *request = [PLVFNetworkUtil requestWithURLString:url
                                                                  params:paramDict
                                                              httpMethod:PLV_HM_GET
                                                         timeoutInterval:kTimeOutInterval
                                                               userAgent:[PLVLiveVideoConfig userAgent]];
    
    if (needToken) {
        NSString *token = teach ? PLVLiveVClassAPI.teachToken : PLVLiveVClassAPI.watchToken;
        if ([PLVFdUtil checkStringUseable:token]) {
            [request setValue:token forHTTPHeaderField:@"Authorization"];
        }
    }
    return request;
}

/// POST接口工具方法专用：把字典类型的参数改为json格式的字符串
+(NSString *)jsonStringFromDictionary:(NSDictionary *)dict {
    NSString *jsonString = nil;
    if ([NSJSONSerialization isValidJSONObject:dict]) {
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dict options:NSJSONWritingPrettyPrinted error:&error];
        jsonString =[[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        if (error) {
//            NSLog(@"Error:%@" , error);
        }
    }
    return jsonString;
}

/// 签名：MD5(${plv} + ${lessonId}或${courseCode} + ${timestamp}) 32位MD5编码-小写
+ (void)setSignWithParamDictionary:(NSMutableDictionary *)muDict {
    NSString *signPartString = @"";
    
    NSString *courseCode = muDict[@"courseCode"];
    NSString *lessonId = muDict[@"lessonId"];
    if ([PLVFdUtil checkStringUseable:lessonId]) {
        signPartString = lessonId;
    } else if ([PLVFdUtil checkStringUseable:courseCode]) {
        signPartString = courseCode;
    }
    NSString *timeStamp = [PLVFdUtil curTimeStamp];
    muDict[@"timestamp"] = timeStamp;
    NSString *signString = [NSString stringWithFormat:@"plv%@%@", signPartString, timeStamp];
    muDict[@"sign"] = [PLVDataUtil md5HexDigest:signString];
}

@end

@implementation PLVLiveVClassAPI (Teach)

#pragma mark - [ Public Method ]

+ (void)teacherLoginWithMobile:(NSString *)mobile
                      password:(NSString *)password
                          code:(NSString *)code
                        userId:(NSString *)userId
                mutipleCompany:(void (^)(NSArray *responseArray))mutipleCompanyHandler
                       success:(void (^)(NSDictionary *responseDict, NSArray *lessonArray))successHandler
                       failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:mobile] ||
        ![PLVFdUtil checkStringUseable:password]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"账号密码不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] initWithCapacity:4];
    paramDict[@"clientType"] = @"IOS";
    paramDict[@"mobile"] = mobile;
    paramDict[@"passwd"]  = password;
    if ([PLVFdUtil checkStringUseable:code]) {
        paramDict[@"code"]  = code;
    }
    if ([PLVFdUtil checkStringUseable:userId]) {
        paramDict[@"userId"]  = userId;
    }
    NSString *url = [NSString stringWithFormat:@"%@/teach/v1/login", kVClassDomain];
    NSMutableURLRequest *request = [self POSTRequestWithURLString:url params:paramDict];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleTeacherLoginResponse:responseObject
                              mutipleCompany:mutipleCompanyHandler
                                     success:successHandler
                                     failure:failureHandler];
    } failure:failureHandler];
}

+ (void)teacherReloginVerifyWithLessonId:(NSString *)lessonId
                                 relogin:(void (^)(NSString *errorDesc))reloginHandler
                                 success:(void (^)(NSDictionary *responseDict))successHandler
                                 failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:PLVLiveVClassAPI.teachToken]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeAuthParamsInvalid desc:PLVFDLocalizableString(@"请先进行登录")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSDictionary *paramDict = @{ @"lessonId" : lessonId };
    NSString *url = [NSString stringWithFormat:@"%@/teach/lesson/v1/verify", kVClassDomain];
    NSMutableURLRequest *request = [self GETRequestWithURLString:url params:paramDict needToken:YES teach:YES];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleTeacherReloginVerifyResponse:responseObject
                                             relogin:reloginHandler
                                             success:successHandler
                                             failure:failureHandler];
    } failure:failureHandler];
}

+ (void)teacherLessonStatusWithLessonId:(NSString *)lessonId
                                success:(void (^)(NSDictionary *responseDict))successHandler
                                failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:PLVLiveVClassAPI.teachToken]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeAuthParamsInvalid desc:PLVFDLocalizableString(@"请先进行登录")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSDictionary *paramDict = @{ @"lessonId" : lessonId };
    NSString *url = [NSString stringWithFormat:@"%@/teach/lesson/v1/status", kVClassDomain];
    NSMutableURLRequest *request = [self GETRequestWithURLString:url params:paramDict needToken:YES teach:YES];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleTeacherLessonStatusResponse:responseObject
                                            success:successHandler
                                            failure:failureHandler];
    } failure:failureHandler];
}

+ (void)teacherLessonListWithUserId:(NSString *)userId
                            success:(void (^)(NSArray *responseArray))successHandler
                            failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:PLVLiveVClassAPI.teachToken]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeAuthParamsInvalid desc:PLVFDLocalizableString(@"请先进行登录")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSDictionary *paramDict = nil;
    if ([PLVFdUtil checkStringUseable:userId]) {
        paramDict = @{ @"userId" : userId };
    }
    NSString *url = [NSString stringWithFormat:@"%@/teach/lesson/v1/list", kVClassDomain];
    NSMutableURLRequest *request = [self GETRequestWithURLString:url params:paramDict needToken:YES teach:YES];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleTeacherLessonListResponse:responseObject
                                          success:successHandler
                                          failure:failureHandler];
    } failure:failureHandler];
}

+ (void)teacherLessonDetailWithLessonId:(NSString *)lessonId
                                success:(void (^)(NSDictionary *responseDict))successHandler
                                failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:PLVLiveVClassAPI.teachToken]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeAuthParamsInvalid desc:PLVFDLocalizableString(@"请先进行登录")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSDictionary *paramDict = @{ @"lessonId" : lessonId };
    NSString *url = [NSString stringWithFormat:@"%@/teach/lesson/v1/detail", kVClassDomain];
    NSMutableURLRequest *request = [self GETRequestWithURLString:url params:paramDict needToken:YES teach:YES];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleTeacherLessonDetailResponse:responseObject
                                            success:successHandler
                                            failure:failureHandler];
    } failure:failureHandler];
}

+ (void)teacherChangeLessonStatusWithLessonId:(NSString *)lessonId
                                       status:(NSInteger)status
                                      success:(void (^)(id responseObject))successHandler
                                      failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:PLVLiveVClassAPI.teachToken]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeAuthParamsInvalid desc:PLVFDLocalizableString(@"请先进行登录")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (status < 0 || status > 2) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课节状态参数错误")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSDictionary *paramDict = @{ @"lessonId" : lessonId, @"status" : @(status) };
    NSString *url = [NSString stringWithFormat:@"%@/teach/lesson/v1/change-status", kVClassDomain];
    NSMutableURLRequest *request = [self POSTRequestWithURLString:url params:paramDict needToken:YES teach:YES];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleResponse:responseObject
                         success:successHandler
                         failure:failureHandler];
    } failure:failureHandler];
}

+ (void)teacherChatTokenWithLessonId:(NSString *)lessonId
                             success:(void (^)(NSDictionary *responseDict))successHandler
                             failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:PLVLiveVClassAPI.teachToken]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeAuthParamsInvalid desc:PLVFDLocalizableString(@"请先进行登录")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSDictionary *paramDict = @{ @"lessonId" : lessonId };
    NSString *url = [NSString stringWithFormat:@"%@/teach/chat/v1/get-chat-token", kVClassDomain];
    NSMutableURLRequest *request = [self GETRequestWithURLString:url params:paramDict needToken:YES teach:YES];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleTeacherChatTokenResponse:responseObject
                                         success:successHandler
                                         failure:failureHandler];
    } failure:failureHandler];
}

+ (void)teacherGetLiveAPIChannelTokenWithLessonId:(NSString *)lessonId
                                          success:(void (^)(NSDictionary *))successHandler
                                          failure:(void (^)(NSError *))failureHandler {
    if (![PLVFdUtil checkStringUseable:PLVLiveVClassAPI.teachToken]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeAuthParamsInvalid desc:PLVFDLocalizableString(@"请先进行登录")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSDictionary *paramDict = @{ @"lessonId" : lessonId };
    NSString *url = [NSString stringWithFormat:@"%@/teach/common/v1/get-live-api-channel-token", kVClassDomain];
    NSMutableURLRequest *request = [self POSTRequestWithURLString:url params:paramDict needToken:YES teach:YES];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleTeacherGetLiveAPIChannelTokenResponse:responseObject
                                                      success:successHandler
                                                      failure:failureHandler];
    } failure:failureHandler];
}

+ (void)teacherDocumentTokenWithLessonId:(NSString *)lessonId
                                 success:(void (^)(NSDictionary *))successHandler
                                 failure:(void (^)(NSError *))failureHandler {
    [self teacherGetLiveAPIChannelTokenWithLessonId:lessonId success:successHandler failure:failureHandler];
}

+ (void)teacherLogout {
    PLVLiveVClassAPI.teachToken = nil;
}

#pragma mark - [ Private Method ]

#pragma mark Response Handler

+ (void)handleTeacherLoginResponse:(id)responseObject
                    mutipleCompany:(void (^)(NSArray *responseArray))mutipleCompanyHandler
                           success:(void (^)(NSDictionary *responseDict, NSArray *lessonArray))successHandler
                           failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        NSString *token = nil;
        if (data && [data isKindOfClass:[NSDictionary class]] && [data count] > 0) {
            if ([PLVFdUtil checkStringUseable:data[@"token"]]) {
                token = (NSString *)data[@"token"];
            }
        }
        if (token) {
            [PLVLiveVClassAPI setTeachToken:token];
            id list = data[@"lessonList"];
            NSArray *lessonArray = @[];
            if (list && [list isKindOfClass:[NSArray class]] && [list count] > 0) {
                lessonArray = (NSArray *)list;
            }
            if (successHandler) {
                successHandler(data, lessonArray);
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"token不可为空")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:^(NSError *error) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSArray class]] && [data count] > 0) {
            if (mutipleCompanyHandler) {
                NSArray *companyArray = (NSArray *)data;
                mutipleCompanyHandler(companyArray);
            }
        } else {
            if (failureHandler) {
                failureHandler(error);
            }
        }
    }];
}

+ (void)handleTeacherReloginVerifyResponse:(id)responseObject
                                   relogin:(void (^)(NSString *errorDesc))reloginHandler
                                   success:(void (^)(NSDictionary *responseDict))successHandler
                                   failure:(void (^)(NSError *error))failureHandler {
    if (responseObject && [responseObject isKindOfClass:[NSDictionary class]]) {
        NSString *status = responseObject[@"status"];
        BOOL success = [responseObject[@"success"] boolValue];
        if ([status isEqualToString:@"success"] && success) {
            if (successHandler) {
                successHandler(responseObject);
            }
        } else {
            NSInteger errorCode = -1;
            NSString *errorDesc = PLVFDLocalizableString(@"当前账号已在别处登录");
            id errorDict = responseObject[@"error"];
            if (errorDict && [errorDict isKindOfClass:[NSDictionary class]] && [errorDict count] > 0) {
                if ([PLVFdUtil checkStringUseable:errorDict[@"desc"]]) {
                    errorDesc = errorDict[@"desc"];
                }
                if ([errorDict[@"code"] integerValue] != 0) {
                    errorCode = [errorDict[@"code"] integerValue];
                    errorDesc = [errorDesc stringByAppendingFormat:@"(%zd)", errorCode];
                }
            }
            
            if (errorCode == 20016) {
                if (reloginHandler) {
                    reloginHandler(errorDesc);
                }
            } else {
                NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeRequestFail desc:errorDesc];
                if (failureHandler) {
                    failureHandler(error);
                }
            }
        }
    } else {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
        if (failureHandler) {
            failureHandler(error);
        }
    }
}

+ (void)handleTeacherLessonStatusResponse:(id)responseObject
                                  success:(void (^)(NSDictionary *responseDict))successHandler
                                  failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSDictionary class]]) {
            if (successHandler) {
                successHandler((NSDictionary *)data);
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

+ (void)handleTeacherLessonListResponse:(id)responseObject
                                success:(void (^)(NSArray *responseArray))successHandler
                                failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSArray class]]) {
            NSArray *responseArray = (NSArray *)data;
            if ([data count] > 0) {
                if (successHandler) {
                    successHandler(responseArray);
                }
            } else {
                NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"课节列表为空")];
                if (failureHandler) {
                    failureHandler(error);
                }
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

+ (void)handleTeacherLessonDetailResponse:(id)responseObject
                                  success:(void (^)(NSDictionary *responseDict))successHandler
                                  failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSDictionary class]]) {
            NSString *httpDnsKey = data[@"httpDnsKey"];
            if ([PLVFdUtil checkStringUseable:httpDnsKey]) {
                [[PLVLiveHttpDnsManager sharedManager] setupHttpDNSWithSecretKey:httpDnsKey];
            }
            if (successHandler) {
                successHandler((NSDictionary *)data);
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

+ (void)handleTeacherChatTokenResponse:(id)responseObject
                               success:(void (^)(NSDictionary *responseDict))successHandler
                               failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSDictionary class]]) {
            if (successHandler) {
                successHandler((NSDictionary *)data);
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

+ (void)handleTeacherGetLiveAPIChannelTokenResponse:(id)responseObject
                                            success:(void (^)(NSDictionary *responseDict))successHandler
                                            failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSDictionary class]]) {
            if (successHandler) {
                successHandler((NSDictionary *)data);
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

@end

@implementation PLVLiveVClassAPI (Watch)

#pragma mark - [ Public Method ]

+ (void)watcherVerifyNoneWithCourseCode:(NSString *)courseCode
                               lessonId:(NSString *)lessonId
                                   name:(NSString *)name
                                success:(void (^)(NSDictionary *responseDict, NSArray *lessonArray))successHandler
                                failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:courseCode] &&
        ![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课程号或课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (![PLVFdUtil checkStringUseable:name]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"昵称不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] initWithCapacity:3];
    paramDict[@"clientType"] = @"IOS";
    paramDict[@"name"] = name;
    if ([PLVFdUtil checkStringUseable:courseCode]) {
        paramDict[@"courseCode"] = courseCode;
    }
    if ([PLVFdUtil checkStringUseable:lessonId]) {
        paramDict[@"lessonId"] = lessonId;
    }
    NSString *url = [NSString stringWithFormat:@"%@/watch/auth/v1/verify/none", kVClassDomain];
    NSMutableURLRequest *request = [self POSTRequestWithURLString:url params:paramDict];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleWatcherVerifyResponse:responseObject
                                      success:successHandler
                                      failure:failureHandler];
    } failure:failureHandler];
}

+ (void)watcherVerifyCodeWithCourseCode:(NSString *)courseCode
                               lessonId:(NSString *)lessonId
                                   name:(NSString *)name
                                   code:(NSString *)code
                                success:(void (^)(NSDictionary *responseDict, NSArray *lessonArray))successHandler
                                failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:courseCode] &&
        ![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课程号或课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (![PLVFdUtil checkStringUseable:name] ||
        ![PLVFdUtil checkStringUseable:code]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"昵称与验证码不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] initWithCapacity:4];
    paramDict[@"clientType"] = @"IOS";
    paramDict[@"name"] = name;
    paramDict[@"code"] = code;
    if ([PLVFdUtil checkStringUseable:courseCode]) {
        paramDict[@"courseCode"] = courseCode;
    }
    if ([PLVFdUtil checkStringUseable:lessonId]) {
        paramDict[@"lessonId"] = lessonId;
    }
    NSString *url = [NSString stringWithFormat:@"%@/watch/auth/v1/verify/code", kVClassDomain];
    NSMutableURLRequest *request = [self POSTRequestWithURLString:url params:paramDict];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleWatcherVerifyResponse:responseObject
                                      success:successHandler
                                      failure:failureHandler];
    } failure:failureHandler];
}

+ (void)watcherVerifyWhiteListWithCourseCode:(NSString *)courseCode
                                    lessonId:(NSString *)lessonId
                                 studentCode:(NSString *)studentCode
                                     success:(void (^)(NSDictionary *responseDict, NSArray *lessonArray))successHandler
                                     failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:courseCode] &&
        ![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课程号或课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (![PLVFdUtil checkStringUseable:studentCode]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"学生码不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] initWithCapacity:4];
    paramDict[@"clientType"] = @"IOS";
    paramDict[@"studentCode"] = studentCode;
    if ([PLVFdUtil checkStringUseable:courseCode]) {
        paramDict[@"courseCode"] = courseCode;
    }
    if ([PLVFdUtil checkStringUseable:lessonId]) {
        paramDict[@"lessonId"] = lessonId;
    }
    NSString *url = [NSString stringWithFormat:@"%@/watch/auth/v1/verify/white", kVClassDomain];
    NSMutableURLRequest *request = [self POSTRequestWithURLString:url params:paramDict];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleWatcherVerifyResponse:responseObject
                                      success:successHandler
                                      failure:failureHandler];
    } failure:failureHandler];
}

+ (void)watcherLessonListWithCourseCode:(NSString *)courseCode
                               lessonId:(NSString *)lessonId
                                success:(void (^)(NSArray *responseArray))successHandler
                                failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:courseCode] &&
        ![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课程号或课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] initWithCapacity:4];
    paramDict[@"clientType"] = @"IOS";
    if ([PLVFdUtil checkStringUseable:courseCode]) {
        paramDict[@"courseCode"] = courseCode;
    }
    if ([PLVFdUtil checkStringUseable:lessonId]) {
        paramDict[@"lessonId"] = lessonId;
    }
    NSString *url = [NSString stringWithFormat:@"%@/watch/lesson/v1/list", kVClassDomain];
    NSMutableURLRequest *request = [self GETRequestWithURLString:url params:paramDict needToken:YES teach:NO];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleWatcherLessonListResponse:responseObject
                                          success:successHandler
                                          failure:failureHandler];
    } failure:failureHandler];
}

+ (void)watcherLessonDetailWithCourseCode:(NSString *)courseCode
                                 lessonId:(NSString *)lessonId
                                  success:(void (^)(NSDictionary *responseDict))successHandler
                                  failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:PLVLiveVClassAPI.watchToken]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeAuthParamsInvalid desc:PLVFDLocalizableString(@"请先进行登录")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSMutableDictionary *muParamDict = [[NSMutableDictionary alloc] initWithCapacity:2];
    muParamDict[@"lessonId"] = lessonId;
    if ([PLVFdUtil checkStringUseable:courseCode]) {
        muParamDict[@"courseCode"] = courseCode;
    }
    NSString *url = [NSString stringWithFormat:@"%@/watch/lesson/v1/detail", kVClassDomain];
    NSMutableURLRequest *request = [self GETRequestWithURLString:url params:muParamDict needToken:YES teach:NO];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleWatcherLessonDetailResponse:responseObject
                                            success:successHandler
                                            failure:failureHandler];
    } failure:failureHandler];
}

+ (void)watcherChatTokenWithCourseCode:(NSString *)courseCode
                              lessonId:(NSString *)lessonId
                               success:(void (^)(NSDictionary *responseDict))successHandler
                               failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:PLVLiveVClassAPI.watchToken]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeAuthParamsInvalid desc:PLVFDLocalizableString(@"请先进行登录")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    NSMutableDictionary *muParamDict = [[NSMutableDictionary alloc] initWithCapacity:2];
    muParamDict[@"lessonId"] = lessonId;
    if ([PLVFdUtil checkStringUseable:courseCode]) {
        muParamDict[@"courseCode"] = courseCode;
    }
    NSString *url = [NSString stringWithFormat:@"%@/watch/chat/v1/get-chat-token", kVClassDomain];
    NSMutableURLRequest *request = [self GETRequestWithURLString:url params:muParamDict needToken:YES teach:NO];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleWatcherChatTokenResponse:responseObject
                                         success:successHandler
                                         failure:failureHandler];
    } failure:failureHandler];
}

+ (void)watcherGetLiveAPIChannelTokenWithLessonId:(NSString *)lessonId
                                       courseCode:(NSString * _Nullable)courseCode
                                          success:(void (^)(NSDictionary *responseDict))successHandler
                                          failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:PLVLiveVClassAPI.watchToken]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeAuthParamsInvalid desc:PLVFDLocalizableString(@"请先进行登录")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    if (![PLVFdUtil checkStringUseable:lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    
    NSMutableDictionary *paramDictM = [[NSMutableDictionary alloc] init];
    [paramDictM setValue:lessonId forKey:@"lessonId"];
    NSString *url = [NSString stringWithFormat:@"%@/watch/common/v1/get-live-api-channel-token", kVClassDomain];
    
    if ([PLVFdUtil checkStringUseable:courseCode]) { // 处理使用课节号登录的情况
        [paramDictM setValue:courseCode forKey:@"courseCode"];
        url = [NSString stringWithFormat:@"%@?courseCode=%@", url, courseCode];
    } else { // 处理使用课程ID登录的情况
        url = [NSString stringWithFormat:@"%@?lessonId=%@", url, lessonId];
    }
    NSMutableURLRequest *request = [self POSTRequestWithURLString:url params:paramDictM needToken:YES teach:NO];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleWatcherGetLiveAPIChannelTokenResponse:responseObject
                                                      success:successHandler
                                                      failure:failureHandler];
    } failure:failureHandler];
}

+ (void)watcherLogout {
    PLVLiveVClassAPI.watchToken = nil;
}

#pragma mark - [ Private Method ]

#pragma mark Response Handler

+ (void)handleWatcherVerifyResponse:(id)responseObject
                            success:(void (^)(NSDictionary *responseDict, NSArray *lessonArray))successHandler
                            failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        NSString *token = nil;
        if (data && [data isKindOfClass:[NSDictionary class]] && [data count] > 0) {
            if ([PLVFdUtil checkStringUseable:data[@"token"]]) {
                token = (NSString *)data[@"token"];
            }
        }
        if (token) {
            [PLVLiveVClassAPI setWatchToken:token];
            id list = data[@"list"];
            NSArray *lessonArray = @[];
            if (list && [list isKindOfClass:[NSArray class]] && [list count] > 0) {
                lessonArray = (NSArray *)list;
            }
            if (successHandler) {
                successHandler(data, lessonArray);
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"token不可为空")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

+ (void)handleWatcherLessonListResponse:(id)responseObject
                                success:(void (^)(NSArray *responseArray))successHandler
                                failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSArray class]]) {
            NSArray *responseArray = (NSArray *)data;
            if ([data count] > 0) {
                if (successHandler) {
                    successHandler(responseArray);
                }
            } else {
                NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"课节列表为空")];
                if (failureHandler) {
                    failureHandler(error);
                }
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

+ (void)handleWatcherLessonDetailResponse:(id)responseObject
                                    success:(void (^)(NSDictionary *responseDict))successHandler
                                    failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSDictionary class]]) {
            NSString *httpDnsKey = data[@"httpDnsKey"];
            if ([PLVFdUtil checkStringUseable:httpDnsKey]) {
                [[PLVLiveHttpDnsManager sharedManager] setupHttpDNSWithSecretKey:httpDnsKey];
            }
            if (successHandler) {
                successHandler((NSDictionary *)data);
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

+ (void)handleWatcherChatTokenResponse:(id)responseObject
                               success:(void (^)(NSDictionary *responseDict))successHandler
                               failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSDictionary class]]) {
            if (successHandler) {
                successHandler((NSDictionary *)data);
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

+ (void)handleWatcherGetLiveAPIChannelTokenResponse:(id)responseObject
                                            success:(void (^)(NSDictionary *responseDict))successHandler
                                            failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSDictionary class]]) {
            if (successHandler) {
                successHandler((NSDictionary *)data);
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

@end

@implementation PLVLiveVClassAPI (Private)

#pragma mark - [ Public Method ]

+ (void)teacherRTCTokenWithModel:(PLVLinkMicGetTokenModel *)model
                         success:(void (^)(NSString *responseString))successHandler
                         failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:model.lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课节ID不可为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    model.forGET = YES;
    NSMutableDictionary *muDict = [model convertModelIntoDictionary];
    if (![muDict isKindOfClass:[NSMutableDictionary class]]) {
        muDict = [NSMutableDictionary dictionaryWithDictionary:muDict];
    }
    if ([PLVFdUtil checkStringUseable:model.userId]) {
        [muDict setObject:model.userId forKey:@"uid"];
    }
    [self setSignWithParamDictionary:muDict];
    
    NSString *url = [NSString stringWithFormat:@"%@/teach/chat/v1/get-app-mic-token", kVClassDomain];
    NSMutableURLRequest *request = [self GETRequestWithURLString:url params:muDict needToken:YES teach:YES];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleRTCTokenResponse:responseObject success:successHandler failure:failureHandler];
    } failure:failureHandler];
}

+ (void)watcherRTCTokenWithModel:(PLVLinkMicGetTokenModel *)model
                         success:(void (^)(NSString *responseString))successHandler
                         failure:(void (^)(NSError *error))failureHandler {
    if (![PLVFdUtil checkStringUseable:model.courseCode] &&
        ![PLVFdUtil checkStringUseable:model.lessonId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"课程号或课节ID不可都为空")];
        if (failureHandler) {
            failureHandler(error);
        }
        return;
    }
    model.forGET = YES;
    NSMutableDictionary *muDict = [model convertModelIntoDictionary];
    if (![muDict isKindOfClass:[NSMutableDictionary class]]) {
        muDict = [NSMutableDictionary dictionaryWithDictionary:muDict];
    }
    if ([PLVFdUtil checkStringUseable:model.userId]) {
        [muDict setObject:model.userId forKey:@"uid"];
    }
    [self setSignWithParamDictionary:muDict];
    
    NSString *url = [NSString stringWithFormat:@"%@/watch/chat/v1/get-app-mic-token", kVClassDomain];
    NSMutableURLRequest *request = [self GETRequestWithURLString:url params:muDict needToken:YES teach:NO];
    __weak typeof(self) weakSelf = self;
    [PLVFNetworkUtil request:request success:^(id responseObject) {
        [weakSelf handleRTCTokenResponse:responseObject success:successHandler failure:failureHandler];
    } failure:failureHandler];
}

#pragma mark - [ Private Method ]

#pragma mark Response Handler

+ (void)handleRTCTokenResponse:(id)responseObject
                       success:(void (^)(NSString *responseString))successHandler
                       failure:(void (^)(NSError *error))failureHandler {
    [self handleResponse:responseObject success:^(id responseObject) {
        id data = responseObject[@"data"];
        if (data && [data isKindOfClass:[NSString class]]) {
            if (successHandler) {
                successHandler((NSString *)data);
            }
        } else {
            NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeDecodeFail desc:PLVFDLocalizableString(@"接口数据出错")];
            if (failureHandler) {
                failureHandler(error);
            }
        }
    } failure:failureHandler];
}

@end
