//
//  PLVLiveHttpDnsManager.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2022/3/1.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVLiveHttpDnsManager.h"
#import "PLVConsoleLogger.h"

#import <AlicloudHttpDNS/AlicloudHttpDNS.h>
#import <PLVFoundationSDK/PLVFoundationSDK.h>

static NSInteger const PLVLiveHttpDnsAccountID = 123018;
static NSString * const PLVLiveHttpDnsSecretKey = @"40cf4fac2f4d167a8d9568ffe1cbef90b30cb5cfa8c177ffc7030f622a83f0b9ab002560ee69630eeef8ca923215377b";
static NSString * const PLVLiveHttpDnsSecretKeyCacheKey = @"net.plv.sdk.live.httpdnsSecretKey";
static NSInteger const PLVLiveHttpDnsSecretKeyCacheTime = 86400; //!< 缓存时效 1 天
//!
@interface PLVLiveHttpDnsManager ()

/// httpdns是否已经初始化（只有第一次初始化有用）
@property (nonatomic, assign) BOOL hadSetupHttpDns;

@end

@implementation PLVLiveHttpDnsManager

#pragma mark - [ Life Cycle ]

- (instancetype)init {
    self = [super init];
    if (self) {
        self.hadSetupHttpDns = NO;
    }
    return self;
}

#pragma mark - [ Public Method ]

+ (instancetype)sharedManager {
    static dispatch_once_t onceToken;
    static PLVLiveHttpDnsManager *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[self alloc] init];
    });
    return manager;
}

- (void)setupHttpDNSWithSecretKey:(NSString *)key {
    NSString *secretKey = key;
    if ([PLVFdUtil checkStringUseable:secretKey]) {
        [PLVLiveHttpDnsManager setHttpDnsSecretKeyFromLocalCache:secretKey];
    } else {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypeNetwork, @"Network HttpDnskey is nil");
        secretKey = [PLVLiveHttpDnsManager getHttpDnsSecretKeyFromLocalCache];
    }
    
    if (![PLVFdUtil checkStringUseable:secretKey]) {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypeNetwork, @"Local httpDnskey is nil");
        secretKey = PLVLiveHttpDnsSecretKey;
    }
    
    NSString *decrptedKey = [PLVLiveHttpDnsManager decryptedHttpDnsSecretKey:secretKey];
    if (![PLVFdUtil checkStringUseable:decrptedKey]) {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypeNetwork, @"HttpDnskey decryption failed");
        return;
    }
    
    if (self.hadSetupHttpDns) {
        return;
    }
    
    HttpDnsService * dnsService = [[HttpDnsService alloc] initWithAccountID:PLVLiveHttpDnsAccountID secretKey:decrptedKey]; // 配置账号
    self.hadSetupHttpDns = YES;
    [dnsService setNetworkingTimeoutInterval:3]; // 网络请求的超时时间
    [dnsService setReuseExpiredIPEnabled:YES]; // 允许返回过期的IP
    [dnsService setPreResolveAfterNetworkChanged:YES]; // 设置网络切换时是否自动刷新所有域名解析结果
    [dnsService setPersistentCacheIPEnabled:YES]; // 开启持久化缓存
    [dnsService setPreResolveHosts:[PLVLiveHttpDnsManager needPreResolveHosts]]; // 设置需要预解析的域名
    [dnsService setDegradeToLocalDNSEnabled:YES]; // 允许降级
    //[dnsService setLogEnabled:YES]; // 打开HTTPDNS Log，线上建议关闭
}

- (NSString *)getIpByHostAsyncInURLFormat:(NSString *)host {
    NSString *ipAddress = nil;
    if (!self.hadSetupHttpDns) {
        [self setupHttpDNSWithSecretKey:@""];
    }
    
    HttpdnsRequest *newRequest = [[HttpdnsRequest alloc] initWithHost:host queryIpType:HttpdnsQueryIPTypeAuto];
    HttpdnsResult *result = [[HttpDnsService sharedInstance] resolveHostSyncNonBlocking:newRequest];
    if (result.hasIpv4Address){
        ipAddress = [result firstIpv4Address];
    }
    else if (result.hasIpv6Address){
        ipAddress = [result firstIpv6Address];
    }
    ipAddress = [self convertIpV6Format:ipAddress];
    return ipAddress;
}

#pragma mark - [ Private Method ]

/// 从本地缓存中读取HttpDnsSecretKey密文
+ (NSString *)getHttpDnsSecretKeyFromLocalCache {
    NSDictionary *httpdnsDict = [[NSUserDefaults standardUserDefaults] dictionaryForKey:PLVLiveHttpDnsSecretKeyCacheKey];
    NSTimeInterval time = [httpdnsDict[@"time"] doubleValue];
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    if (currentTime - time > PLVLiveHttpDnsSecretKeyCacheTime) {
        // 缓存超时
        return @"";
    }
    NSString *secretKey = httpdnsDict[@"secretKey"];
    return secretKey;
}

/// 设置HttpDnsSecretKey密文到本地缓存中
/// @param secretKey 密钥
+ (void)setHttpDnsSecretKeyFromLocalCache:(NSString *)secretKey {
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    NSDictionary *dict = @{@"time" : @(currentTime),
                           @"secretKey" : secretKey};
    [[NSUserDefaults standardUserDefaults] setObject:dict forKey:PLVLiveHttpDnsSecretKeyCacheKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

/// 解密HttpDnsSecretKey
/// @param secretKey 密文
+ (NSString *)decryptedHttpDnsSecretKey:(NSString *)secretKey {
    NSData *enAesData = [PLVDataUtil dataWithHexString:secretKey];
    NSData *deAesData = [PLVDataUtil AES128DecryptedDataWithKey:[PLVKeyUtil getAliKey] iv:[PLVKeyUtil getAliIv] data:enAesData];
    NSData *deBase64Data = [[NSData alloc] initWithBase64EncodedData:deAesData options:0];
    NSString *deBase64String = [[NSString alloc] initWithData:deBase64Data encoding:NSUTF8StringEncoding];
    return deBase64String;
}

/// 需要提前解析的域名
+ (NSArray <NSString *>*)needPreResolveHosts{
    /**
     chat.polyv.net SocketIO服务器地址
     api.polyv.net 大部分直播接口
     live.polyv.net 互动页面webview、ppt
     livejson.polyv.net 获取频道约束信息
     apichat.polyv.net 聊天室、连麦相关接口
     player.polyv.net 加载频道信息
     
     liveimages.videocc.net 上传图片
     www.polyv.net 默认用户头像
     livestatic.polyv.net 默认讲师头像、头像地址
     rtas.videocc.net 日志统计
     
     pull-c1.videocc.net 直播地址
     pull2.videocc.net 多线路直播地址
     oss-live-2.videocc.net 回放

     20190923整理
     */
    return @[
             @"pull-c1.videocc.net",
             @"pull2.videocc.net",
             @"oss-live-2.videocc.net",
             @"chat-d.polyv.net",
             @"api.polyv.net",
             @"live.polyv.net",
             @"livejson.polyv.net",
             @"apichat.polyv.net",
             @"player.polyv.net",
             @"ts.videocc.net",
             @"hls.videocc.net",
             ];
}

- (NSString *)convertIpV6Format:(NSString *)ipAddress {
    NSString *rightFormat = ipAddress;
    if (![PLVFdUtil checkStringUseable:ipAddress]){
        if (![ipAddress hasPrefix:@"["] && [ipAddress rangeOfString:@":"].length > 0){
            rightFormat = [NSString stringWithFormat:@"[%@]", ipAddress];
        }
    }
    return rightFormat;
}

@end
