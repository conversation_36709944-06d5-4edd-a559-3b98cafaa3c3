//
//  PLVLiveHttpDnsManager.h
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2022/3/1.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface PLVLiveHttpDnsManager : NSObject

#pragma mark - [ 方法 ]

+ (instancetype)sharedManager;

/// 配置HTTPDNS信息
/// @param key 密钥，为nil时将使用缓存
- (void)setupHttpDNSWithSecretKey:(NSString * _Nullable)key;

/// 获取域名对应ip
/// @param host 域名
- (NSString *)getIpByHostAsyncInURLFormat:(NSString *)host;

@end

NS_ASSUME_NONNULL_END
