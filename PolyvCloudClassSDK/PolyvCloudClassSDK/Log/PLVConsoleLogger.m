//
//  PLVConsoleLogger.m
//  PLVCloudClassStreamerSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/2/20.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVConsoleLogger.h"

@implementation PLVConsoleLogger

#pragma mark - Public

+ (instancetype)defaultLogger {
    static PLVConsoleLogger *logger = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        logger = [[PLVConsoleLogger alloc] init];
    });
    return logger;
}

- (void)logWithModuleType:(PLVConsoleLogModuleType)moduleType type:(PLVFConsoleLogType)type format:(NSString *)frmt, ... {
    va_list args;
    va_start(args, frmt);
    NSString *message = [[NSString alloc] initWithFormat:frmt arguments:args];
    va_end(args);
    // %符号处理
    message = [message stringByReplacingOccurrencesOfString:@"%" withString:@"%%"];
    NSString *module = [self stringWithLogModule:moduleType];
    
    [self logWithModule:module type:type print:[self enableModule:moduleType] format:message];
}

- (void)logInfoWithKey:(NSString * _Nullable)key info:(NSString * _Nullable)info {
    [self logWithKey:key info:info];
}

#pragma mark - Private

- (NSString *)stringWithLogModule:(PLVConsoleLogModuleType)type {
    NSString *prefix = @"PLVS";
    NSString *moduleString;
    
    if (self.logPrefix) {
        prefix = self.logPrefix;
    }
    
    switch (type) {
        case PLVConsoleLogModuleTypeJSBridge:
            moduleString = @"JS";
            break;
        case PLVConsoleLogModuleTypeSocket:
            moduleString = @"SOCKET";
            break;
        case PLVConsoleLogModuleTypePPT:
            moduleString = @"PPT";
            break;
        case PLVConsoleLogModuleTypeInteract:
            moduleString = @"Interact";
            break;
        case PLVConsoleLogModuleTypePlayer:
            moduleString = @"Player";
            break;
        case PLVConsoleLogModuleTypeLinkMic:
            moduleString = @"LinkMic";
            break;
        case PLVConsoleLogModuleTypeRoom:
            moduleString = @"Room";
            break;
        case PLVConsoleLogModuleTypeChatRoom:
            moduleString = @"Chatroom";
            break;
        case PLVConsoleLogModuleTypeStreamer:
            moduleString = @"Streamer";
            break;
        case PLVConsoleLogModuleTypeDownload:
            moduleString = @"Download";
            break;
        case PLVConsoleLogModuleTypeNetwork:
            moduleString = @"Network";
            break;
        case PLVConsoleLogModuleTypeLogan:
            moduleString = @"Logan";
            break;
        case PLVConsoleLogModuleTypeVerbose:
            moduleString = @"Verbose";
            break;
        default:
            moduleString = @"";
            break;
    }
    return [NSString stringWithFormat:@"%@-%@",prefix,moduleString];
}

- (BOOL)enableModule:(PLVConsoleLogModuleType)type {
    switch (type) {
        case PLVConsoleLogModuleTypeJSBridge:
            return !self.closeJSBridgeLog;
        case PLVConsoleLogModuleTypeSocket:
            return !self.closeSocketLog;
        case PLVConsoleLogModuleTypePPT:
            return !self.closePPTLog;
        case PLVConsoleLogModuleTypeInteract:
            return !self.closeInteractLog;
        case PLVConsoleLogModuleTypePlayer:
            return !self.closePlayerLog;
        case PLVConsoleLogModuleTypeLinkMic:
            return !self.closeLinkMicLog;
        case PLVConsoleLogModuleTypeRoom:
            return !self.closeChatRoomLog;
        case PLVConsoleLogModuleTypeChatRoom:
            return !self.closeChatRoomLog;
        case PLVConsoleLogModuleTypeStreamer:
            return !self.closeStreamerLog;
        case PLVConsoleLogModuleTypeDownload:
            return !self.closeDownloadLog;
        case PLVConsoleLogModuleTypeNetwork:
            return !self.closeNetworkLog;
        case PLVConsoleLogModuleTypeLogan:
            return !self.closeLoganLog;
        case PLVConsoleLogModuleTypeVerbose:
            return !self.closeVerboseLog;
        default:
            return YES;
    }
}

@end
