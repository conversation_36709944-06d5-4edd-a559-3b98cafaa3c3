//
//  PLVLoganUploadModel.h
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2022/12/13.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 日志上传数据模型
@interface PLVLoganUploadModel : NSObject

/// 文件Id
@property (nonatomic, copy) NSString *fileId;

/// 文件名 (时间戳)
@property (nonatomic, copy) NSString *fileName;

/// 文件路径
@property (nonatomic, copy) NSString *filePath;

///// 文件上传进度
//@property (nonatomic, assign) float progress;
//
///// 文件上传状态
//@property (nonatomic, assign) PLVLoganUploadStatus status;
//
//// 设置 PLVLoganUploadStatus 状态
///// @param statusString 状态字符串
//- (void)setupUploadStatusWithStatusStirng:(NSString *)statusString;

@end

NS_ASSUME_NONNULL_END
