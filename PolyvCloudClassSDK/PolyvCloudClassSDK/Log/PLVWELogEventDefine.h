//
//  PLVWELogEventDefine.h
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/24.
//  Copyright © 2019 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

//**************** Modul:Play ****************//

extern NSString *PLVWELogModulPlay;



//**************** Modul:Download ****************//

extern NSString *PLVWELogModulDownload;



//**************** Modul:Upload ****************//

extern NSString *PLVWELogModulUpload;



//**************** Modul:Record ****************//

extern NSString *PLVWELogModulRecord;



//**************** Modul:Rtmp ****************//

extern NSString *PLVWELogModulRtmp;



//**************** Modul:Chat ****************//

extern NSString *PLVWELogModulChat;



//**************** Modul:Link ****************//

extern NSString *PLVWELogModulLink;



//**************** Modul:PPT ****************//

extern NSString *PLVWELogModulPPT;


//**************** Modul:Init ****************//

extern NSString *PLVWELogModulInit;


//**************** Modul:Socket ****************//

extern NSString *PLVWELogModulSocket;


//**************** Modul:Http ****************//

extern NSString *PLVWELogModulHttp;


//**************** Modul:Interaction ****************//

extern NSString *PLVWELogModulInteraction;

//**************** Modul:Beauty ****************//

extern NSString *PLVWELogModulBeauty;


@interface PLVWELogEventDefine : NSObject

+ (NSString *)elogErrorModulString:(NSInteger)errorCode;

+ (NSString *)elogErrorEventString:(NSInteger)errorCode;

@end

NS_ASSUME_NONNULL_END
