//
//  PLVLoganUploadTokenModel.h
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2022/12/14.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 文档上传 token 模型
@interface PLVLoganUploadTokenModel : NSObject

/// 保利威用户Id
@property (nonatomic, copy) NSString *userId;

/// oss 上传OSSAccessId（上传所需 tAccessKey）
@property (nonatomic, copy, readonly) NSString *accessid;

/// oss 上传的 host
@property (nonatomic, copy, readonly) NSString *host;

/// 加密串过期时间，如 "1545289049"
@property (nonatomic, copy, readonly) NSString *expire;

/// 签名
@property (nonatomic, copy, readonly) NSString *signature;

/// 目录
@property (nonatomic, copy, readonly) NSString *dir;

/// 上传策略
@property (nonatomic, copy, readonly) NSString *policy;


@end

NS_ASSUME_NONNULL_END
