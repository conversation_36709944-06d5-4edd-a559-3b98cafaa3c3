//
//  PLVLoganUploadManager.h
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2022/12/8.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PLVLoganUploadModel.h"

NS_ASSUME_NONNULL_BEGIN

/// 日志上传管理器
@interface PLVLoganUploadManager : NSObject

@property (nonatomic, assign) BOOL allowAutoUpload;

/// 上传中任务队列
@property (nonatomic, copy, readonly) NSMutableArray <PLVLoganUploadModel *> *uploadingArray;

+ (instancetype)shareManager;

// 根据userId读取上传队列重启上传
- (void)getCache;

// 返回所有文件时间戳
- (NSArray * _Nullable)localAllLogfileTimeStamp;

// 查找指定时间文件是否存在
- (BOOL)lookupLocalLoganFileWithTimeStamp:(NSString * _Nullable)timeStamp;

// 查找指定时间段文件是否存在，存在则返回时间戳数组
- (NSArray * _Nullable)lookupLocalLoganFileWithStartTimestamp:(NSString * _Nullable)startTimeStamp endTimeStamp:(NSString * _Nullable)endTimeStamp;

- (BOOL)lookupLocalLoganFileWithTimeStamp:(NSString * _Nullable)timeStamp infoDict:(NSDictionary * _Nullable)infoDict;

- (NSArray * _Nullable)lookupLocalLoganFileWithStartTimestamp:(NSString * _Nullable)startTimeStamp endTimeStamp:(NSString * _Nullable)endTimeStamp infoDict:(NSDictionary * _Nullable)infoDict;

// 上传指定时间
- (void)uploadLoganFileWithTimestamp:(NSString * _Nullable)timestamp;

// 上传指定时间段
- (void)uploadLogfileWithStartTimestamp:(NSString * _Nullable)startTimestamp endTimestamp:(NSString * _Nullable)endTimestamp;

// 立即上传
- (void)uploadCurrentLog;

// 上传指定时间
- (void)uploadLoganFileWithTimestamp:(NSString * _Nullable)timestamp infoDict:(NSDictionary * _Nullable)infoDict;

// 上传指定时间段
- (void)uploadLogfileWithStartTimestamp:(NSString * _Nullable)startTimestamp endTimestamp:(NSString * _Nullable)endTimestamp infoDict:(NSDictionary * _Nullable)infoDict;

// 立即上传
- (void)uploadCurrentLogCheckWithInfoDict:(NSDictionary * _Nullable)infoDict;

@end

NS_ASSUME_NONNULL_END
