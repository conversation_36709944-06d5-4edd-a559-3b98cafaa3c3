//
//  PLVLoganManager.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2022/12/19.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVLoganManager.h"
#import "PLVLoganManager+Private.h"
#import "PLVLoganUploadManager.h"
#import "PLVLiveVideoConfig.h"
#import "PLVLivePrivateAPI.h"
#import "PLVConsoleLogger.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

static NSInteger timeBase = 5; // 调整为每5秒一次
static NSInteger MAX_RETRYNUM = 3; // 最大重试次数

@interface PLVLoganManager ()

@property (nonatomic, assign) BOOL userHadStart; //用户已启动

@property (nonatomic, assign) NSInteger retryNum; // 重试次数

@property (nonatomic, strong) NSMutableDictionary *muInfoDictionary;
@property (nonatomic, strong) NSMutableDictionary *currentUplaodInfoDict;

@end

@implementation PLVLoganManager

- (instancetype)init {
    self = [super init];
    if (self) {
        _muInfoDictionary = [[NSMutableDictionary alloc] init];
        _currentUplaodInfoDict = [[NSMutableDictionary alloc] init];
        _retryNum = 0;
        _userHadStart = NO;
        [self getCache];
    }
    return self;
}

#pragma mark - [ Public Methods ]

+ (instancetype)sharedManager {
    static PLVLoganManager *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken,^{
        manager = [[PLVLoganManager alloc] init];
    });
    return manager;
}

- (void)checkAndReportIfNeed {
    if (self.logUploadDisable) {
        return;
    }
    
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLogan,@"%s",__FUNCTION__);
    if (!self.userHadStart) {
        [self checkAndReportIfNeedInside];
        self.userHadStart = YES;
    }
}

- (void)checkAndReportCurrentLogan {
    if (self.logUploadDisable) {
        return;
    }
    
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLogan,@"%s",__FUNCTION__);
    if ([PLVFdUtil checkDictionaryUseable:self.currentUplaodInfoDict]) {
        NSString *endTimeStamp = PLV_SafeStringForDictKey(self.currentUplaodInfoDict, @"endTime");
        NSDictionary * info = PLV_SafeDictionaryForDictKey(self.currentUplaodInfoDict, @"info");
        if ([PLVFdUtil checkStringUseable:endTimeStamp]) {
            if ([endTimeStamp doubleValue] - [PLVLoganTodaysTime() doubleValue] >= 0) {
                [[PLVLoganUploadManager shareManager] uploadCurrentLogCheckWithInfoDict:info];
                NSString *SHA256 = [self SHA256withDictionary:self.currentUplaodInfoDict];
                NSMutableDictionary *muDict = [NSMutableDictionary dictionaryWithDictionary:self.muInfoDictionary];
                [muDict setValue:@(1) forKey:SHA256];
                [self saveCacheDictionary:muDict];
                self.muInfoDictionary = muDict;
            }
        }
    }
}

#pragma mark - [Private Methods]

- (void)checkAndReportIfNeedInside {
    if (self.retryNum > MAX_RETRYNUM || self.logUploadDisable) {
        return;
    }
    self.retryNum += 1;
    __weak typeof(self) weakSelf = self;
    [self requestLoganReportInfoWithCompletion:^(NSDictionary *responseDict){
        PLV_LOG_INFO(PLVConsoleLogModuleTypeLogan,@"manager - request report info success");
        // 请求成功后先上报缓存文件
        [[PLVLoganUploadManager shareManager] getCache];
        NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
        NSArray *taskArray = PLV_SafeArraryForDictKey(data, @"tasks");
        for (NSDictionary * infoDict in taskArray) {
            [self setUpWithInfoDict:infoDict];
        }
    } failure:^(NSError *error) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeLogan,@"manager - request report info failed by %ld time with %@",self.retryNum,error.localizedDescription);
        if (self.retryNum == MAX_RETRYNUM && self.delegate && [self.delegate respondsToSelector:@selector(loganManagerCheckAndReportIfNeedWithError:)]) {
            [self.delegate loganManagerCheckAndReportIfNeedWithError:error];
        }
        if (self.retryNum < MAX_RETRYNUM) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(timeBase * (self.retryNum - 1) * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf checkAndReportIfNeedInside];
            });
        }
    }];
}

- (void)setUpWithInfoDict:(NSDictionary *)infoDict {
    if ([PLVFdUtil checkDictionaryUseable:infoDict]) {
        NSMutableDictionary *muInfoDict = [NSMutableDictionary dictionaryWithDictionary:self.muInfoDictionary];
        NSString *SHA256 = [self SHA256withDictionary:infoDict];
        BOOL didHandle = [self infoDidHandle:SHA256];
        if (!didHandle) {
            BOOL didUpload = [self startUploadWithInfoDictionary:infoDict];
            NSNumber *value = didUpload ? @(1) : @(0);
            [muInfoDict setValue:value forKey:SHA256];
        }
        self.muInfoDictionary = muInfoDict;
        [self saveCacheDictionary:muInfoDict];
    }
}

- (void)setLogDisable:(BOOL)logDisable {
    _logDisable = logDisable;
    PLVLoganDisable(logDisable);
}

#pragma mark 缓存

- (void)getCache {
    NSDictionary *cacheDictionary = [self getCacheDictionary];
    if (cacheDictionary == nil || [cacheDictionary count] == 0) {
        return;
    }
    self.muInfoDictionary = [NSMutableDictionary dictionaryWithDictionary:cacheDictionary];
}

- (NSDictionary *)getCacheDictionary {
    return [[NSUserDefaults standardUserDefaults] objectForKey:@"plv_logan_manager_cache_Dict"];
}

- (void)saveCacheDictionary:(NSDictionary *)dictionary {
    [[NSUserDefaults standardUserDefaults] setObject:dictionary forKey:@"plv_logan_manager_cache_Dict"];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

- (BOOL)infoDidHandle:(NSString *)SHA256String {
    if ([self.muInfoDictionary allKeys].count > 0 && [[self.muInfoDictionary allKeys] containsObject:SHA256String]) {
        return PLV_SafeBoolForDictKey(self.muInfoDictionary, SHA256String);
    } else {
        return NO;
    }
}

#pragma mark 上传

- (BOOL)startUploadWithInfoDictionary:(NSDictionary *)dict {
    NSString *firstTimeStamp = PLV_SafeStringForDictKey(dict, @"startTime");
    NSString *endTimeStamp = PLV_SafeStringForDictKey(dict, @"endTime");
    NSMutableDictionary *newInfoDict = [[NSMutableDictionary alloc] init];
    NSMutableDictionary *info = [[NSMutableDictionary alloc] init];
    NSMutableArray *mediaIdList = [[NSMutableArray alloc] init];
    NSArray *channelIdArray = PLV_SafeArraryForDictKey(dict, @"channelIds");
    if ([PLVFdUtil checkArrayUseable:channelIdArray]) {
        [mediaIdList addObjectsFromArray:channelIdArray];
    }
    NSArray *viewerIdArray = PLV_SafeArraryForDictKey(dict, @"viewerIds");
    if ([PLVFdUtil checkArrayUseable:viewerIdArray]) {
        [mediaIdList addObjectsFromArray:viewerIdArray];
    }
    if ([PLVFdUtil checkArrayUseable:mediaIdList]) {
        [info setValue:mediaIdList forKey:@"mediaIdList"];
        [newInfoDict setValue:mediaIdList forKey:@"mediaIdList"];
    }
    
    if ([PLVFdUtil checkStringUseable:firstTimeStamp]) {
        [newInfoDict setValue:firstTimeStamp forKey:@"startTime"];
    }
    
    if ([PLVFdUtil checkStringUseable:endTimeStamp]) {
        [newInfoDict setValue:endTimeStamp forKey:@"endTime"];
        if ([endTimeStamp doubleValue] - [[PLVFdUtil curTimeStamp] doubleValue] > 0) {
            self.currentUplaodInfoDict = newInfoDict;
            return NO;
        }
    }
    [[PLVLoganUploadManager shareManager] uploadLogfileWithStartTimestamp:firstTimeStamp endTimestamp:endTimeStamp infoDict:info];
    return YES;
}

#pragma mark Setting

- (void)setStart:(BOOL)start {
    if (self.logUploadDisable) {
        return;
    }
    
    _start = start;
    if (start && !_userHadStart) {
        [self checkAndReportIfNeedInside];
        _userHadStart = YES;
    }
}

#pragma mark Utils

- (void)requestLoganReportInfoWithCompletion:(void (^)(NSDictionary *responseDict))completion failure:(void (^)(NSError *error))failure {
    [PLVLivePrivateAPI requestLoganReportInfoWithUserId:[PLVLiveVideoConfig sharedInstance].userId completion:completion failure:failure];
}

- (NSString *)SHA256withDictionary:(NSDictionary *)dictionary {
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dictionary options:NSJSONWritingPrettyPrinted error:nil];
    NSString *str = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    return [PLVDataUtil sha256String:str];
}

@end
