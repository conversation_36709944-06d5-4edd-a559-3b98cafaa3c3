//
//  PLVWLogReporterManager.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/23.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVWLogReporterManager.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVLiveVideoConfig.h"
#import "PLVChannelInfoModel.h"
#import "PLVWELogEventDefine.h"
#import "PLVViewLogCustomParam.h"
#import "PLVLiveVideoConfig+PrivateInfo.h"

@interface PLVWLogReporterManager ()

@property (nonatomic, assign) BOOL hasRegistered;

// 直播回放的ViewLog日志上报器，目前直播回放只能使用PLVProductTypeVod类型
@property (nonatomic, strong) PLVFViewLogReporter *vodVlogReporter;
// 直播回放的Qos日志上报器，目前直播回放只能使用PLVProductTypeVod类型
@property (nonatomic, strong) PLVFQosLogReporter *vodQlogReporter;

// 直播的ViewLog日志上报器
@property (nonatomic, strong) PLVFViewLogReporter *liveVlogReporter;
// 直播的Qos日志上报器
@property (nonatomic, strong) PLVFQosLogReporter *liveQlogReporter;

// ELog日志上报器，根据登录接口再确定productType类型
@property (nonatomic, strong) PLVFELogReporter *elogReporter;

// track日志上报器
@property (nonatomic, strong) PLVFTrackLogReporter *trackReporter;
// track日志超管开关是否开启，默认NO
@property (nonatomic, assign) BOOL trackEventEnabled;

@end

@implementation PLVWLogReporterManager

#pragma mark - Life Cycle

- (instancetype)init {
    self = [super init];
    if (self) {
        _vodVlogReporter = [[PLVFViewLogReporter alloc] init];
        _vodVlogReporter.productType = PLVProductTypeVod;
        
        _vodQlogReporter = [[PLVFQosLogReporter alloc] init];
        _vodQlogReporter.productType = PLVProductTypeVod;
        
        _liveVlogReporter = [[PLVFViewLogReporter alloc] init];
        _liveVlogReporter.productType = PLVProductTypeLive;
        
        _liveQlogReporter = [[PLVFQosLogReporter alloc] init];
        _liveQlogReporter.productType = PLVProductTypeLive;

        _elogReporter = [[PLVFELogReporter alloc] init];
        
        BOOL enableSha256 = [PLVLiveVideoConfig sharedInstance].realEnableSha256;
        BOOL enableSignatureNonce = [PLVLiveVideoConfig sharedInstance].realEnableSignatureNonce;
        _trackReporter = [[PLVFTrackLogReporter alloc] init];
        _trackReporter.enableSha256 = enableSha256;
        _trackReporter.enableSignatureNonce = enableSignatureNonce;
        
        [self updateSdkSafetyEnabled];
    }
    return self;
}

#pragma mark - Public

+ (instancetype)sharedManager {
    static PLVWLogReporterManager *mananger = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        mananger = [[PLVWLogReporterManager alloc] init];
    });
    return mananger;
}

- (void)registerReporterWithChannelId:(NSString *)channelId
                                appId:(NSString *)appId
                            appSecret:(NSString *)appSecret
                               userId:(NSString *)userId {
    [self registerReporterWithChannelId:channelId userId:userId];
}

- (void)registerReporterWithChannelId:(NSString *)channelId
                               userId:(NSString *)userId {
    self.elogReporter.productType = PLVProductTypeLive;
    [self.elogReporter registerWithChannelId:channelId userId:userId];
    
    self.trackReporter.productType = PLVProductTypeLive;
    [self.trackReporter registerWithChannelId:channelId userId:userId];
    [self updateSdkSafetyEnabled];
    
    self.hasRegistered = YES;
}

- (void)registerReporterWithChannelId:(NSString *)channelId
                                appId:(NSString *)appId
                            appSecret:(NSString *)appSecret
                               userId:(NSString *)userId
                                  vId:(NSString *)vId {
    [self registerReporterWithChannelId:channelId userId:userId vId:vId];
}

- (void)registerReporterWithChannelId:(NSString *)channelId
                               userId:(NSString *)userId
                                  vId:(NSString *)vId {
    self.elogReporter.productType = PLVProductTypeLive;
    [self.elogReporter registerWithChannelId:channelId userId:userId vId:vId];
    
    self.trackReporter.productType = PLVProductTypeLive;
    [self.trackReporter registerWithChannelId:channelId userId:userId];
    [self updateSdkSafetyEnabled];
    
    self.hasRegistered = YES;
}

- (void)registerReporterWithChannelId:(NSString *)channelId productType:(PLVFLogProductType)productType {
    self.elogReporter.productType = productType;
    [self.elogReporter registerWithChannelId:channelId];
    [self updateSdkSafetyEnabled];
    self.hasRegistered = YES;
}

- (void)clear {
    self.hasRegistered = NO;
    [self.elogReporter unregister];
}

#pragma mark Qos Log

- (void)reportLiveLoadingQosWithChannel:(PLVChannelInfoModel *)channel
                                   time:(int)time
                            time_player:(int)time_player
                          time_business:(int)time_business
                               playerId:(NSString *)playerId {
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    PLVFQosLogModel *model = [[PLVFQosLogModel alloc] init];
    model.pid = playerId;
    model.uid = channel.accountUserId;
    model.cid = [NSString stringWithFormat:@"%@", channel.channelId];
    model.session_id = channel.sessionId;
    model.time = [NSString stringWithFormat:@"%d", time];
    model.time_player = [NSString stringWithFormat:@"%d", time_player];
    model.time_business = [NSString stringWithFormat:@"%d", time_business];
    model.type = @"loading";
    model.client = [NSString stringWithFormat:@"ios-%@", liveConfig.playerVersion];
    [self.liveQlogReporter reportModel:model];
}

- (void)reportVodLoadingQosWithChannel:(PLVChannelInfoModel *)channel
                                  time:(int)time
                           time_player:(int)time_player
                         time_business:(int)time_business
                              playerId:(NSString *)playerId
                                   vid:(NSString *)vid {
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    PLVFQosLogModel *model = [[PLVFQosLogModel alloc] init];
    model.pid = playerId;
    model.uid = channel.accountUserId;
    model.cid = [NSString stringWithFormat:@"%@", channel.channelId];
    model.session_id = channel.sessionId;
    model.time = [NSString stringWithFormat:@"%d", time];
    model.time_player = [NSString stringWithFormat:@"%d", time_player];
    model.time_business = [NSString stringWithFormat:@"%d", time_business];
    model.type = @"loading";
    model.client = [NSString stringWithFormat:@"ios-%@", liveConfig.playerVersion];
    model.vid = vid;
    [self.vodQlogReporter reportModel:model];
}

- (void)reportLiveBufferQosWithChannel:(PLVChannelInfoModel *)channel
                                  time:(int)time
                              playerId:(NSString *)playerId {
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    PLVFQosLogModel *model = [[PLVFQosLogModel alloc] init];
    model.pid = playerId;
    model.uid = channel.accountUserId;
    model.cid = [NSString stringWithFormat:@"%@", channel.channelId];
    model.session_id = channel.sessionId;
    model.time = [NSString stringWithFormat:@"%d", time];
    model.type = @"buffer";
    model.client = [NSString stringWithFormat:@"ios-%@", liveConfig.playerVersion];
    [self.liveQlogReporter reportModel:model];
}

    
- (void)reportVodBufferQosWithChannel:(PLVChannelInfoModel *)channel
                                 time:(int)time
                             playerId:(NSString *)playerId
                                  vid:(NSString *)vid {
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    PLVFQosLogModel *model = [[PLVFQosLogModel alloc] init];
    model.pid = playerId;
    model.uid = channel.accountUserId;
    model.cid = [NSString stringWithFormat:@"%@", channel.channelId];
    model.session_id = channel.sessionId;
    model.time = [NSString stringWithFormat:@"%d", time];
    model.type = @"buffer";
    model.client = [NSString stringWithFormat:@"ios-%@", liveConfig.playerVersion];
    model.vid = vid;
    [self.vodQlogReporter reportModel:model];
}

- (void)reportLiveStallingQosWithChannel:(PLVChannelInfoModel *)channel
                                    time:(int)time
                                playerId:(NSString *)playerId {
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    PLVFQosLogModel *model = [[PLVFQosLogModel alloc] init];
    model.pid = playerId;
    model.uid = channel.accountUserId;
    model.cid = [NSString stringWithFormat:@"%@", channel.channelId];
    model.session_id = channel.sessionId;
    model.time = [NSString stringWithFormat:@"%d", time];
    model.type = @"stalling";
    model.client = [NSString stringWithFormat:@"ios-%@", liveConfig.playerVersion];
    [self.liveQlogReporter reportModel:model];
}

- (void)reportLiveErrorQosWithChannel:(PLVChannelInfoModel *)channel
                                  uri:(NSString *)uri
                               status:(NSString *)status
                            errorcode:(NSString *)errorcode
                             errormsg:(NSString *)errormsg
                             playerId:(NSString *)playerId {
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    PLVFQosLogModel *model = [[PLVFQosLogModel alloc] init];
    model.pid = playerId;
    model.uid = channel.accountUserId;
    model.cid = [NSString stringWithFormat:@"%@", channel.channelId];
    model.session_id = channel.sessionId;
    model.uri = uri;
    model.status = status;
    model.errorcode = errorcode;
    model.errormsg = errormsg;
    model.type = @"error";
    model.client = [NSString stringWithFormat:@"ios-%@", liveConfig.playerVersion];
    [self.liveQlogReporter reportModel:model];
}

- (void)reportVodErrorQosWithChannel:(PLVChannelInfoModel *)channel
                                 uri:(NSString *)uri
                              status:(NSString *)status
                           errorcode:(NSString *)errorcode
                            errormsg:(NSString *)errormsg
                            playerId:(NSString *)playerId
                                 vid:(NSString *)vid {
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    PLVFQosLogModel *model = [[PLVFQosLogModel alloc] init];
    model.pid = playerId;
    model.uid = channel.accountUserId;
    model.cid = [NSString stringWithFormat:@"%@", channel.channelId];
    model.session_id = channel.sessionId;
    model.uri = uri;
    model.status = status;
    model.errorcode = errorcode;
    model.errormsg = errormsg;
    model.type = @"error";
    model.client = [NSString stringWithFormat:@"ios-%@", liveConfig.playerVersion];
    model.vid = vid;
    [self.vodQlogReporter reportModel:model];
}

#pragma mark View Log

- (void)reportLiveViewLogWithParam:(PLVViewLogCustomParam *)param
                          playerId:(NSString *)playerId
               logModelConfigBlock:(PLVFViewLogModel *(^)(void))configBlock {
    PLVFViewLogModel *configModel = configBlock();
    if (configModel == nil) {
        return;
    }

    configModel.pid = playerId;
    
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    configModel.pn = liveConfig.playerName;
    configModel.pv = liveConfig.playerVersion;
    configModel.uid = liveConfig.userId;
    
    if (self.liveVlogReporter.requestXbody &&
        [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
        configModel.param1 = [PLVSM2Util formatEncryptString:param.liveParam1 publicKey:[PLVKeyUtil getApiUtilsPublicKey]];
        configModel.param2 = [PLVSM2Util formatEncryptString:param.liveParam2 publicKey:[PLVKeyUtil getApiUtilsPublicKey]];
        configModel.param4 = [PLVSM2Util formatEncryptString:param.liveParam4 publicKey:[PLVKeyUtil getApiUtilsPublicKey]];
        configModel.param5 = [PLVSM2Util formatEncryptString:param.liveParam5 publicKey:[PLVKeyUtil getApiUtilsPublicKey]];
    } else {
        configModel.param1 = [PLVDataUtil safeUrlBase64Encode:param.liveParam1];
        configModel.param2 = [PLVDataUtil safeUrlBase64Encode:param.liveParam2];
        configModel.param4 = [PLVDataUtil safeUrlBase64Encode:param.liveParam4];
        configModel.param5 = [PLVDataUtil safeUrlBase64Encode:param.liveParam5];
    }
    
    [self.liveVlogReporter reportModel:configModel];
}

- (void)reportVodViewLogWithParam:(PLVViewLogCustomParam *)param
                         playerId:(NSString *)playerId
              logModelConfigBlock:(PLVFViewLogModel *(^)(void))configBlock {
    PLVFViewLogModel *configModel = configBlock();
    if (configModel == nil) {
        return;
    }
    
    configModel.pid = playerId;
    
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    configModel.pn = liveConfig.playerName;
    configModel.pv = liveConfig.playerVersion;
    configModel.uid = liveConfig.userId;
    
    configModel.sid = param.vodSid;
    configModel.viewerAvatar = param.vodViewerAvatar;
    configModel.param3 = param.vodParam3;
    configModel.key1 = param.vodKey1;
    configModel.key2 = param.vodKey2;
    configModel.key3 = param.vodKey3;
    
    if (self.vodVlogReporter.requestXbody &&
        [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
        configModel.param1 = [PLVSM2Util formatEncryptString:param.vodParam1 publicKey:[PLVKeyUtil getApiUtilsPublicKey]];
        configModel.param2 = [PLVSM2Util formatEncryptString:param.vodParam2 publicKey:[PLVKeyUtil getApiUtilsPublicKey]];
        configModel.param4 = [PLVSM2Util formatEncryptString:param.vodParam4 publicKey:[PLVKeyUtil getApiUtilsPublicKey]];
        configModel.param5 = [PLVSM2Util formatEncryptString:param.vodParam5 publicKey:[PLVKeyUtil getApiUtilsPublicKey]];
    } else {
        configModel.param1 = [PLVDataUtil safeUrlBase64Encode:param.vodParam1];
        configModel.param2 = [PLVDataUtil safeUrlBase64Encode:param.vodParam2];
        configModel.param4 = [PLVDataUtil safeUrlBase64Encode:param.vodParam4];
        configModel.param5 = [PLVDataUtil safeUrlBase64Encode:param.vodParam5];
    }
    
    [self.vodVlogReporter reportModel:configModel];
}

#pragma mark ELog

- (void)reportWithErrorCode:(NSInteger)errorCode information:(id)information {
    NSString *modulString = [PLVWELogEventDefine elogErrorModulString:errorCode];
    NSString *eventString = [PLVWELogEventDefine elogErrorEventString:errorCode];
    if (modulString.length == 0) {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypeVerbose, @"请到类 PLVWELogEventDefine 的方法 elogErrorModulString 中增加错误码 %zd 对应模块的 string 值", errorCode);
    }
    if (eventString.length == 0) {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypeVerbose, @"请到类 PLVWELogEventDefine 的方法 elogErrorEventString 中增加错误码 %zd 对应一级标识的 string 值", errorCode);
    }
    [self reportWithEvent:eventString modul:modulString errorCode:errorCode information:information patch:NO];
}

- (void)reportWithEvent:(NSString *)event modul:(NSString *)modul information:(id)information {
    [self reportWithEvent:event modul:modul information:information patch:NO];
}

- (void)reportWithEvent:(NSString *)event modul:(NSString *)modul informationType:(NSString *)type informationData:(id)data {
    [self reportWithEvent:event modul:modul informationType:type informationData:data patch:NO];
}

- (void)reportWithEvent:(NSString *)event modul:(NSString *)modul informationType:(NSString *)type informationData:(id)data patch:(BOOL)patch {
    NSDictionary *dict = @{@"type":(type ?: @""), @"data":(data ?: @"")};
    [self reportWithEvent:event modul:modul information:dict patch:patch];
}

- (void)reportWithEvent:(NSString *)event modul:(NSString *)modul information:(id)information patch:(BOOL)patch {
    [self reportWithEvent:event modul:modul errorCode:-1 information:information patch:patch];
}

#pragma mark track Log

- (void)enableTrackEventReport:(BOOL)enable {
    self.trackEventEnabled = enable;
}

- (void)setupViewerId:(NSString *)viewerId viewerName:(NSString *)viewerName role:(NSString *)role {
    [self.trackReporter setupViewerId:viewerId viewerName:viewerName role:role];
}

- (void)setupSessionId:(NSString *)sessionId {
    [self.trackReporter setupSessionId:sessionId];
}

- (void)reportTrackWithEventId:(NSString *)eventId eventType:(NSString *)eventType specInformation:(NSDictionary *)specInformation {
    if (!self.hasRegistered ||
        !self.trackEventEnabled) {
        return;
    }
    
    if (![PLVFdUtil checkStringUseable:eventId] ||
        ![PLVFdUtil checkStringUseable:eventType] ||
        ![PLVFdUtil checkDictionaryUseable:specInformation]) {
        return;
    }
    
    PLVFTrackLogModel *logModel = [self.trackReporter generateTemplateModel];
    PLVETrackDataParamModel *data_paramElement = [self.trackReporter generateDataParamModel];
    logModel.data_paramElement = data_paramElement;
    
    data_paramElement.event_id = eventId;
    data_paramElement.event_type = eventType;
    data_paramElement.spec_attrs = specInformation;
    NSTimeInterval interval = [[NSDate date] timeIntervalSince1970];
    data_paramElement.occur_time = lround(interval);
    
    [self.trackReporter reportModel:logModel];
}

- (void)reportTrackWithEventId:(NSString *)eventId eventType:(NSString *)eventType specInformationArray:(NSArray *)specInformationArray {
    if (!self.hasRegistered ||
        !self.trackEventEnabled) {
        return;
    }
    
    if (![PLVFdUtil checkStringUseable:eventId] ||
        ![PLVFdUtil checkStringUseable:eventType] ||
        ![PLVFdUtil checkArrayUseable:specInformationArray]) {
        return;
    }
    
    PLVFTrackLogModel *logModel = [self.trackReporter generateTemplateModel];
    NSMutableArray *muArray = [[NSMutableArray alloc] initWithCapacity:specInformationArray.count];
    for (NSDictionary *info in specInformationArray) {
        PLVETrackDataParamModel *data_paramElement = [self.trackReporter generateDataParamModel];
        data_paramElement.event_id = eventId;
        data_paramElement.event_type = eventType;
        data_paramElement.spec_attrs = info;
        NSTimeInterval interval = [[NSDate date] timeIntervalSince1970];
        data_paramElement.occur_time = lround(interval);
        [muArray addObject:data_paramElement];
    }
    logModel.data_param = [muArray copy];
    [self.trackReporter reportModel:logModel];
}

#pragma mark - Private

- (void)reportWithEvent:(NSString *)event modul:(NSString *)modul errorCode:(NSInteger)errorCode information:(id)information patch:(BOOL)patch {
    if (self.hasRegistered == NO) {
        return;
    }
    
    NSString *dataStr = nil;
    if (information && [information isKindOfClass:[NSDictionary class]]) {
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:information options:0 error:0];
        dataStr = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    } else if (information && [information isKindOfClass:[NSString class]]) {
        dataStr = information;
    } else if (information) {
        dataStr = [NSString stringWithFormat:@"%@", information];
    }
    PLVFELogModel *elogModel = [self.elogReporter generateTemplateModel];
    elogModel.patch = patch;
    elogModel.log.event = event;
    elogModel.log.module = modul;
    elogModel.log.logFile.infomation = dataStr;
    NSString *loganString = [NSString stringWithFormat:@"[Elog]modul:%@ event:%@ data:%@", modul, event, dataStr];
    if (errorCode != -1) {
        elogModel.log.errorCode = [NSString stringWithFormat:@"%zd", errorCode];
        loganString = [NSString stringWithFormat:@"[Elog]modul:%@ error:%@%zd data:%@", modul, event, errorCode, dataStr];
    }
    [self.elogReporter reportModel:elogModel];
    //elog上报补充本地日志记录，等级3为DEBUG
    PLVLogan(3, loganString);
}

- (void)updateSdkSafetyEnabled {
    BOOL requestXbody = [PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt;
    self.vodVlogReporter.requestXbody = requestXbody;
    self.vodQlogReporter.requestXbody = requestXbody;
    self.liveVlogReporter.requestXbody = requestXbody;
    self.liveQlogReporter.requestXbody = requestXbody;
    self.elogReporter.requestXbody = requestXbody;
    self.trackReporter.requestXbody = requestXbody;
}

@end
