//
//  PLVLoganUploadManager.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2022/12/8.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVLoganUploadManager.h"
#import "PLVLoganUploadTokenModel.h"
#import "PLVLiveVideoConfig.h"
#import "PLVLivePrivateAPI.h"
#import "PLVWErrorManager.h"
#import "PLVWLogReporterManager.h"
#import "PLVWELogEventDefine.h"
#import "PLVConsoleLogger.h"
#import <PLVModel/PLVModel.h>
#import <CommonCrypto/CommonDigest.h>
#import <PLVFoundationSDK/PLVFoundationSDK.h>

static NSString *kBoundary = @"Boundary+88D30654D5F34641";
static NSInteger timeBase = 5; // 调整为每5秒一次
static NSInteger MAX_RETRYNUM = 3; // 最大重试次数

#define PLVLoganUploadCacheKey(userId) [NSString stringWithFormat:@"plv_logaan_upload_%@", userId]

@interface PLVLoganUploadManager ()

@property (nonatomic, copy) NSString *userId;

@property (nonatomic, copy) NSMutableArray <PLVLoganUploadModel *> *uploadingArray;

@property (nonatomic, assign) BOOL isUploadingQueue; // 标志当前上传队列是否有任务在进行

@property (nonatomic, assign) NSInteger retryNum; // 重试次数

@end


@implementation PLVLoganUploadManager

#pragma mark - [ Life Cycle ]

- (instancetype)init {
    self = [super init];
    if (self) {
        _uploadingArray = [[NSMutableArray alloc] init];
        _userId = [PLVLiveVideoConfig sharedInstance].userId;
        _retryNum = 0;
    }
    return self;
}

#pragma mark - [ Public Methods ]
/// 单例方法
+ (instancetype)shareManager {
    static dispatch_once_t onceToken;
    static PLVLoganUploadManager *mananger = nil;
    dispatch_once(&onceToken, ^{
        mananger = [[self alloc] init];
    });
    return mananger;
}

#pragma mark 文件寻找

- (NSArray * _Nullable)localAllLogfileTimeStamp {
    return [PLVLoganAllFilesInfo() allKeys];
}

- (BOOL)lookupLocalLoganFileWithTimeStamp:(NSString * _Nullable)timeStamp {
    return [self hitFileInfoWithTimeStamp:timeStamp infoDict:nil];
}

- (BOOL)lookupLocalLoganFileWithTimeStamp:(NSString * _Nullable)timeStamp infoDict:(NSDictionary * _Nullable)infoDict {
    return [self hitFileInfoWithTimeStamp:timeStamp infoDict:infoDict];
}

- (NSArray * _Nullable)lookupLocalLoganFileWithStartTimestamp:(NSString * _Nullable)startTimeStamp endTimeStamp:(NSString * _Nullable)endTimeStamp {
    return [self lookupLocalLoganFileWithStartTimestamp:startTimeStamp endTimeStamp:endTimeStamp infoDict:nil];
}

- (NSArray * _Nullable)lookupLocalLoganFileWithStartTimestamp:(NSString * _Nullable)startTimeStamp endTimeStamp:(NSString * _Nullable)endTimeStamp infoDict:(NSDictionary * _Nullable)infoDict {
    BOOL startTimeStampExist = [self checkTimeStamp:startTimeStamp];
    BOOL endTimeStampExist = [self checkTimeStamp:endTimeStamp];
    if (startTimeStampExist && endTimeStampExist) {
        if ([startTimeStamp doubleValue] - [endTimeStamp doubleValue] > 0) {
            return nil;
        }
    }
    
    if (![PLVFdUtil checkArrayUseable:[self localAllLogfileTimeStamp]]) {
        return nil;
    }
    
    NSMutableArray *fileMuArray = [NSMutableArray array];
    NSString *closestTimeStamp = nil;
    for (NSString * currentFileTimeStamp in [self localAllLogfileTimeStamp]) {
        if (startTimeStampExist) {
            if ([startTimeStamp doubleValue] - [currentFileTimeStamp doubleValue] >= 0) {
                if (!closestTimeStamp) {
                    closestTimeStamp = currentFileTimeStamp;
                } else if ([closestTimeStamp doubleValue] < [currentFileTimeStamp doubleValue]){
                    closestTimeStamp = currentFileTimeStamp;
                }
                continue;
            }
        }
        if (endTimeStampExist) {
            if ([currentFileTimeStamp doubleValue] - [endTimeStamp doubleValue] >= 0) {
                continue;
            }
        }
        [fileMuArray addObject:currentFileTimeStamp];
    }
    
    // 最接近startTime的且同属于一天的日志也上报，避免因为时间设定模糊导致的未上报
    if (closestTimeStamp && startTimeStampExist) {
        NSDate *date1 = [NSDate dateWithTimeIntervalSince1970:[closestTimeStamp doubleValue] / 1000.0];
        NSDate *date2 = [NSDate dateWithTimeIntervalSince1970:[startTimeStamp doubleValue] / 1000.0];
        
        NSCalendar *calendar = [NSCalendar currentCalendar];
        NSDateComponents *components1 = [calendar components:NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay fromDate:date1];
        NSDateComponents *components2 = [calendar components:NSCalendarUnitYear | NSCalendarUnitMonth | NSCalendarUnitDay fromDate:date2];
        if (components1.year == components2.year && components1.month == components2.month && components1.day == components2.day) {
            [fileMuArray addObject:closestTimeStamp];
        }
    }
    
    if (![PLVFdUtil checkDictionaryUseable:infoDict]) {
        return fileMuArray;
    } else {
        NSMutableArray *hitFileMuArray = [NSMutableArray array];
        for (NSString * timeStamp in fileMuArray) {
            BOOL hitFileInfo = [self hitFileInfoWithTimeStamp:timeStamp infoDict:infoDict];
            if (hitFileInfo) {
                [hitFileMuArray addObject:timeStamp];
            }
        }
        return hitFileMuArray;
    }
}

#pragma mark 缓存

// 根据userId读取上传队列重启上传
- (void)getCache {
    NSArray *cacheArray = [self getCacheArray];
    if (cacheArray == nil || [cacheArray count] == 0) {
        return;
    }
    
    for (NSDictionary *cacheDict in cacheArray) {
        PLVLoganUploadModel *model = [PLVLoganUploadModel plv_modelWithJSON:cacheDict];
        [self uploadWithTimeStamp:model.fileName];
    }
}

/// 读取缓存数据
- (NSArray *)getCacheArray {
    return [[NSUserDefaults standardUserDefaults] objectForKey:PLVLoganUploadCacheKey(self.userId)];
}

///  写入缓存数据
- (void)saveCacheArray:(NSArray *)array {
    NSMutableArray *muArray = [[NSMutableArray alloc] init];
    for (PLVLoganUploadModel *model in array) {
        NSDictionary *dict = [model plv_modelToJSONObject];
        [muArray addObject:dict];
    }
    [[NSUserDefaults standardUserDefaults] setObject:muArray forKey:PLVLoganUploadCacheKey(self.userId)];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

#pragma mark 文件上传

- (void)uploadLoganFileWithTimestamp:(NSString * _Nullable)timestamp {
    [self uploadLoganFileWithTimestamp:timestamp infoDict:nil];
}

// 上传指定时间段
- (void)uploadLogfileWithStartTimestamp:(NSString * _Nullable)startTimestamp endTimestamp:(NSString * _Nullable)endTimestamp {
    [self uploadLogfileWithStartTimestamp:startTimestamp endTimestamp:endTimestamp infoDict:nil];
}

// 立即上传
- (void)uploadCurrentLog {
    [self uploadCurrentLogCheckWithInfoDict:nil];
}

// 上传指定时间
- (void)uploadLoganFileWithTimestamp:(NSString * _Nullable)timestamp infoDict:(NSDictionary * _Nullable)infoDict {
    if ([self lookupLocalLoganFileWithTimeStamp:timestamp infoDict:infoDict]) {
        [self uploadWithTimeStamp:timestamp];
    }
}

// 上传指定时间段
- (void)uploadLogfileWithStartTimestamp:(NSString * _Nullable)startTimestamp endTimestamp:(NSString * _Nullable)endTimestamp infoDict:(NSDictionary * _Nullable)infoDict {
    NSArray *uploadArray = [self lookupLocalLoganFileWithStartTimestamp:startTimestamp endTimeStamp:endTimestamp infoDict:infoDict];
    if ([PLVFdUtil checkArrayUseable:uploadArray]) {
        for (NSString *timeStamp in uploadArray) {
            [self uploadWithTimeStamp:timeStamp];
        }
    }
}

// 立即上传
- (void)uploadCurrentLogCheckWithInfoDict:(NSDictionary * _Nullable)infoDict {
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLogan,@"%s",__FUNCTION__);
    [self uploadLoganFileWithTimestamp:PLVLoganTodaysTime() infoDict:infoDict];
}

#pragma mark 文件上传模型

-(void)uploadWithTimeStamp:(NSString * _Nullable)timeStamp {
    PLVLoganUploadFilePath(timeStamp, ^(NSString * _Nullable filePath) {
        if (!filePath) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeLogan, @"uoload manager - upload failed !file does not exist");
            return;
        }
        
        PLVLoganUploadModel *model = [[PLVLoganUploadModel alloc]init];
        model.filePath = filePath;
        model.fileName = timeStamp;
        model.fileId = [self md5WithFilePath:filePath];
        if (self.uploadingArray.count > 0 && [self.uploadingArray containsObject:model]) {
            return;
        }
        [self.uploadingArray addObject:model];
        [self startUploadFromUploadingArray]; // 检查上传队列
    });
}

/// 上传时含有 当前时间的日志时要注意

#pragma mark - [ Private Methods ]

#pragma mark 文件查询

/// 是否命中日志文件关键文件
- (BOOL)hitFileInfoWithTimeStamp:(NSString * _Nullable)timeStamp infoDict:(NSDictionary * _Nullable)infoDict {
    if(![self checkTimeStamp:timeStamp]) {
        return NO;
    } else {
    if (![[self localAllLogfileTimeStamp] containsObject:timeStamp]) {
            return NO;
        } else {
            if (![PLVFdUtil checkDictionaryUseable:infoDict]) {
                return YES;
            } else {
                NSDictionary *fileKeyInfoDict = PLVLoganFileKeyInfo(timeStamp);
                for (NSString * keyInfo in infoDict ) {
                    NSArray *infoArray = PLV_SafeArraryForDictKey(infoDict, keyInfo);
                    NSArray *fileKeyInfoArray = PLV_SafeArraryForDictKey(fileKeyInfoDict, keyInfo);
                    if (![PLVFdUtil checkArrayUseable:infoArray]) {
                        continue;
                    }
                    
                    if (![PLVFdUtil checkArrayUseable:fileKeyInfoArray]) {
                        return NO;
                    }
                    int i = 0;
                    for (NSString *info in infoArray) {
                        NSString *infoString = PLV_SafeStringForValue(info);
                        if ([fileKeyInfoArray containsObject:infoString]) {
                            i++;
                        }
                    }
                    if (i == 0) {
                        return NO;// 日志文件需要匹配至少一个数组中的数据，否则认为是匹配失败
                    }
                }
                return YES;
            }
        }
    }
}

#pragma mark 上传相关

- (void)getTokenWithModel:(PLVLoganUploadModel *)model userId:(NSString *)c success:(void (^)(PLVLoganUploadTokenModel *token))success
                  failure:(void (^)(NSError *error))failure {
    [PLVLivePrivateAPI getUploadTokenWithUserId:self.userId channelId:@"" success:^(NSDictionary * _Nonnull responseObject) {
        PLVLoganUploadTokenModel *token = [PLVLoganUploadTokenModel plv_modelWithJSON:responseObject];
        token.userId = self.userId;
        success(token);
    } failure:^(NSError * _Nonnull error) {
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:error.code information:error.localizedDescription];
        failure(error);
    }];
}

- (void)startUploadFromUploadingArray {
    // 如果上传队列为空或者队列正在上传任务，则直接返回
    if (self.uploadingArray.count == 0 || self.isUploadingQueue) {
        return;
    }
    
    // 获取队列中第一个任务
    PLVLoganUploadModel *model = [self.uploadingArray firstObject];
    
    // 开始上传队列中第一个任务
    self.isUploadingQueue = YES;
    [self uploadWithModel:model];
}

- (void)uploadWithModel:(PLVLoganUploadModel *)model {
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLogan, @"upload manager - logan start upload log.");
    self.retryNum = 0;
    [self retryUploadWithModel:model];
}

- (void)retryUploadWithModel:(PLVLoganUploadModel *)model {
    if (self.retryNum > MAX_RETRYNUM) {
        self.isUploadingQueue = NO;
        [self.uploadingArray removeObject:model];
        [self saveCacheArray:[self.uploadingArray copy]];
        if (self.uploadingArray.count > 0) {
            [self startUploadFromUploadingArray];
        }
        return;
    }
    self.retryNum += 1;
    __weak typeof(self) weakSelf = self;
    [self getTokenWithModel:model userId:self.userId success:^(PLVLoganUploadTokenModel *token) {
        [weakSelf uploadWithModel:model token:token completed:^(BOOL success, NSError *error) {
            if (success) {
                PLVLoganKeyInfoWithTimestamp(@"uploadState", @"success", model.fileName);
                PLVLoganClearLog(model.fileName);
                self.isUploadingQueue = NO;
                [weakSelf.uploadingArray removeObject:model];
                [weakSelf saveCacheArray:[weakSelf.uploadingArray copy]];
                PLV_LOG_INFO(PLVConsoleLogModuleTypeNetwork, @"logan upload log success");
                
                // 检查上传队列
                if (weakSelf.uploadingArray.count > 0) {
                    [weakSelf startUploadFromUploadingArray];
                }
            } else {
                if (weakSelf.retryNum == MAX_RETRYNUM) {
                    NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulUpload
                                                                                     code:PLVFUploadErrorCodeDocumentOSSTaskError];
                    NSString *description = [NSString stringWithFormat:@"%@(#%zd)", error.localizedDescription, error.code];
                    NSDictionary *userInfo = [NSDictionary dictionaryWithObject:description forKey:NSLocalizedDescriptionKey];
                    NSError *userError = [NSError errorWithDomain:failError.domain code:failError.code userInfo:userInfo];
                    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:userError.code information:userError.localizedDescription];
                    [weakSelf.uploadingArray removeObject:model];
                    [weakSelf saveCacheArray:[weakSelf.uploadingArray copy]];
                    PLV_LOG_ERROR(PLVConsoleLogModuleTypeNetwork, @"logan upload log failed");
                    self.isUploadingQueue = NO;
                    
                    // 检查上传队列
                    if (weakSelf.uploadingArray.count > 0) {
                        [weakSelf startUploadFromUploadingArray];
                    }
                } else if (weakSelf.retryNum < MAX_RETRYNUM) {
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(timeBase * (weakSelf.retryNum - 1) * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [weakSelf retryUploadWithModel:model];
                    });
                }
            }
        }];
    } failure:^(NSError *error) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(timeBase * (weakSelf.retryNum - 1) * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [weakSelf retryUploadWithModel:model];
        });
    }];
}

- (void)uploadWithModel:(PLVLoganUploadModel *)model token:(PLVLoganUploadTokenModel *)token completed:(void(^)(BOOL success, NSError *error))completedBlock {
    NSString *uploadFileName = [model.filePath lastPathComponent];
    //上传时添加日志中第一个viewerIds的base64作为后缀名称
    NSDictionary *loganKeyInfo = PLVLoganFileKeyInfo(model.fileName);
    NSArray *viewerIdArray = PLV_SafeArraryForDictKey(loganKeyInfo, @"viewerIds");
    if ([PLVFdUtil checkArrayUseable:viewerIdArray]) {
        NSString *viewerId = PLV_SafeStringForValue(viewerIdArray.firstObject);
        NSString *encodedViewerId = [PLVDataUtil safeUrlBase64Encode:viewerId];
        uploadFileName = ![PLVFdUtil checkStringUseable:encodedViewerId] ? uploadFileName :[NSString stringWithFormat:@"%@_%@", uploadFileName, encodedViewerId];
        //文件名最多不超过255个字符
        uploadFileName = [uploadFileName substringToIndex:MIN(uploadFileName.length, 255)];
    }
    NSString *key = [NSString stringWithFormat:@"%@%@",token.dir, uploadFileName];
    NSDictionary *params = @{@"key" : key, @"policy" : token.policy, @"OSSAccessKeyId" :  token.accessid, @"signature" : token.signature, @"success_action_status" : @"200"};
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:params options:0 error:0];
    NSURL *url = [NSURL URLWithString:[NSString stringWithFormat:@"https://%@/",token.host]];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url cachePolicy:NSURLRequestReloadIgnoringLocalCacheData timeoutInterval:60];
    [request setHTTPMethod:@"POST"];
    
    //拼接请求体数据
    NSMutableData *requestMutableData = [self requestBodyWithFilePath:model.filePath params:params];
    
    //设置请求体
    request.HTTPBody=requestMutableData;
    
    //设置请求头
    NSString *headStr=[NSString stringWithFormat:@"multipart/form-data; boundary=%@",kBoundary];
    [request setValue:headStr forHTTPHeaderField:@"Content-Type"];
    [request setValue:[self userAgent] forHTTPHeaderField:@"User-Agent"];
    
    __block NSURLSessionTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        NSInteger responseCode = [(NSHTTPURLResponse *)response statusCode];
        if (responseCode != 200) {
            NSString * errorCodeString = @"";
            if (responseCode == 403) {
                errorCodeString = @"upload_logan_403_error";
            } else {
                errorCodeString = @"upload_logan_NOT200_error";
            }
            NSDictionary * userInfo = [NSDictionary dictionaryWithObject:errorCodeString forKey:NSLocalizedDescriptionKey];
            NSError * uploadErr = [NSError errorWithDomain:@"net.polyv.live" code:-100 userInfo:userInfo];
            !completedBlock ?: completedBlock(NO, uploadErr);
        } else {
            !completedBlock ?: completedBlock(!error, error);
        }
    }];
    [task resume];
}

- (NSMutableData *)requestBodyWithFilePath:(NSString *)filePath params:(NSDictionary *)params {
    NSMutableData *requestMutableData=[NSMutableData data];
    //0.拼接参数
    /*--------------------------------------------------------------------------*/
    for (NSString *key in params) {
        NSString *pair = [NSString stringWithFormat:@"--%@\r\nContent-Disposition: form-data; name=\"%@\"\r\n\r\n",kBoundary,key];
        [requestMutableData appendData:[pair dataUsingEncoding:NSUTF8StringEncoding]];
        
        id value = [params objectForKey:key];
        if ([value isKindOfClass:[NSString class]]) {
            [requestMutableData appendData:[value dataUsingEncoding:NSUTF8StringEncoding]];
        }else if ([value isKindOfClass:[NSData class]]){
            [requestMutableData appendData:value];
        }
        [requestMutableData appendData:[@"\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
    }
    /*--------------------------------------------------------------------------*/
    
    //1.\r\n--Boundary+72D4CD655314C423\r\n
    // 分割符以“--”开头，后面的字随便写，只要不写中文即可，这里无需写开头的 "\r\n", 上面拼接参数结束会多出来一个 “\r\n”
    NSMutableString *myString=[NSMutableString stringWithFormat:@"--%@\r\n",kBoundary];
    
    NSString *fileName = [filePath lastPathComponent];
    //2. Content-Disposition: form-data; name="file"; filename="001.jpeg"\r\n
    // 这里注明服务器接收图片的参数（类似于接收用户名的userName）及服务器上保存图片的文件名
    [myString appendString:[NSString stringWithFormat:@"Content-Disposition: form-data; name=\"file\"; filename=\"%@\"\r\n", fileName]];
    
    //3. Content-Type: text/plain\r\n
    [myString appendString:[NSString stringWithFormat:@"Content-Type: text/plain\r\n\r\n"]];
    
    //4. Content-Transfer-Encoding: binary\r\n\r\n  // 编码方式
    // [myString appendString:@"Content-Transfer-Encoding: binary\r\n\r\n"];
    
    // 转换成为二进制数据
    [requestMutableData appendData:[myString dataUsingEncoding:NSUTF8StringEncoding]];
    
    //5.文件数据部分
    NSData *uploadData = [NSData dataWithContentsOfFile:filePath];
    
    //转换成为二进制数据
    [requestMutableData appendData:uploadData];
    
    //6. \r\n--Boundary+72D4CD655314C423--\r\n  // 分隔符后面以"--"结尾，表明结束
    [requestMutableData appendData:[[NSString stringWithFormat:@"\r\n--%@--\r\n",kBoundary] dataUsingEncoding:NSUTF8StringEncoding]];
    /*--------------------------------------------------------------------------*/
    
    return requestMutableData;
}

#pragma mark 工具类

/// 检查时间戳是否可用
- (BOOL)checkTimeStamp:(NSString * _Nullable)timeStamp {
    NSString *regex = @"[0-9]*";
    NSPredicate *pred = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    if (![PLVFdUtil checkStringUseable:timeStamp] ||
        timeStamp.length != 13 ||
        ![pred evaluateWithObject:timeStamp]) {
        return NO;
    } else {
        return YES;
    }
}

/// 生成文件的 MD5 值
- (NSString *)md5WithFilePath:(NSString *)filePath {
    NSFileHandle *handle = [NSFileHandle fileHandleForReadingAtPath:filePath];
    if (handle == nil) {
        return nil;
    }

    CC_MD5_CTX md5;
    CC_MD5_Init(&md5);
    BOOL done = NO;
    while (done == NO) {
        NSData* fileData = [handle readDataOfLength: 256 ];
        CC_MD5_Update(&md5, [fileData bytes], (CC_LONG)[fileData length]);
        if( [fileData length] == 0 ) {
            done = YES;
        }
    }
    
    unsigned char digest[CC_MD5_DIGEST_LENGTH];
    CC_MD5_Final(digest, &md5);
    NSString *s = [NSString stringWithFormat: @"%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x",
                   digest[0], digest[1], digest[2], digest[3], digest[4], digest[5],
                   digest[6], digest[7], digest[8], digest[9], digest[10], digest[11],
                   digest[12], digest[13], digest[14], digest[15]];
    return s;
}

- (NSString *)userAgent {
    NSString *appName = [NSBundle mainBundle].bundleIdentifier;
    NSBundle *currentBundle = [NSBundle bundleForClass:[self class]];
    NSString *sdkVersion = [[currentBundle infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    NSString *deviceName = [[UIDevice currentDevice] model];
    NSString *systemName = [[UIDevice currentDevice] systemName];
    NSString *systemVersion = [[UIDevice currentDevice] systemVersion];
    NSString *userAgent = [NSString stringWithFormat:@"%@/%@ (%@; %@ %@)", appName, sdkVersion, deviceName, systemName, systemVersion];
    return userAgent;
}

@end
