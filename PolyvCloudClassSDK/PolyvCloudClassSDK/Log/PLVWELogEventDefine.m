//
//  PLVWELogEventDefine.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/24.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVWELogEventDefine.h"
#import <PLVFoundationSDK/PLVFErrorBaseCodeDefine.h>

//**************** Modul:Play ****************//

NSString *PLVWELogModulPlay = @"play";



//**************** Modul:Download ****************//

NSString *PLVWELogModulDownload = @"download";

//*********** download modul 下的 错误事件 event ****************//

// 一级错误码 1 统一 event
static NSString *PLVWElogDownloadDefaultError = @"downloadDefaultError";
// 一级错误码 2 统一 event
static NSString *PLVWElogDownloadAccountError = @"downloadAccountError";
// 一级错误码 3 统一 event
static NSString *PLVWElogDownloadNetworkError = @"downloadNetworkError";
// 一级错误码 4 统一 event
static NSString *PLVWElogDownloadVideoInfoError = @"downloadVideoInfoError";
// 一级错误码 5 统一 event
static NSString *PLVWElogDownloadVideoStatusError = @"downloadVideoStatusError";
// 一级错误码 6 统一 event
static NSString *PLVWElogDownloadFileError = @"downloadFileError";
// 一级错误码 7 统一 event
static NSString *PLVWElogDownloadMemoryError = @"downloadMemoryError";
// 一级错误码 8 统一 event
static NSString *PLVWElogDownloadLocalVideoError = @"downloadLocalVideoError";



//**************** Modul:Upload ****************//

NSString *PLVWELogModulUpload = @"upload";



//**************** Modul:Record ****************//

NSString *PLVWELogModulRecord = @"record";



//**************** Modul:Rtmp ****************//

NSString *PLVWELogModulRtmp = @"rtmp";



//**************** Modul:Chat ****************//

NSString *PLVWELogModulChat = @"chat";

//*********** chat modul 下的 错误事件 event ****************//

// 一级错误码 1\2\3 统一 event
static NSString *PLVWElogChatHttpError = @"chatHttpError";

// 一级错误码 4 统一 event
static NSString *PLVWElogChatSocketAckError = @"chatSocketAckError";
// 一级错误码 5 统一 event
static NSString *PLVWElogChatMessageError = @"chatMessageError";
// 一级错误码 6 统一 event
static NSString *PLVWElogChatImageError = @"chatImageError";



//**************** Modul:Link ****************//

NSString *PLVWELogModulLink = @"link";



//**************** Modul:PPT ****************//

NSString *PLVWELogModulPPT = @"ppt";

//*********** ppt modul 下的 错误事件 event ****************//

// 一级错误码 1\2\3 统一 event
static NSString *PLVWElogPPTHttpError = @"pptHttpError";

// 一级错误码 4 统一 event
static NSString *PLVWElogPPTWebError = @"pptWebError";


//**************** Modul:Init ****************//

NSString *PLVWELogModulInit = @"init";

//*********** init modul 下的 错误事件 event ****************//

// 一级错误码 1/2/3 统一 event
static NSString *PLVWElogInitHttpError = @"initHttpError";


//**************** Modul:Socket ****************//

NSString *PLVWELogModulSocket = @"socket";

//*********** socket modul 下的 错误事件 event ****************//

// 一级错误码 1\2\3 统一 event
static NSString *PLVWElogSocketHttpError = @"socketHttpError";
// 一级错误码 5 统一 event
static NSString *PLVWElogSocketConnectError = @"socketConnectError";
// 一级错误码 6 统一 event
static NSString *PLVWElogSocketLoginError = @"socketLoginError";


//**************** Modul:Http ****************//

NSString *PLVWELogModulHttp = @"http";

//**************** Modul:Interaction ****************//

NSString *PLVWELogModulInteraction = @"interaction";

//*********** interaction modul 下的 错误事件 event ****************//

// 一级错误码 0 统一 event
static NSString *PLVWElogInteractionWebError = @"interactionWebError";

// 一级错误码 1/2/3 统一 event
static NSString *PLVWElogInteractionHttpError = @"interactionHttpError";

//**************** Modul:beauty ****************//

NSString *PLVWELogModulBeauty = @"beauty";

//*********** beauty modul 下的 错误事件 event ****************//

// 一级错误码 1/2/3 统一 event
static NSString *PLVWElogBeautyHttpError = @"beautyHttpError";
// 一级错误码 4 统一 event
static NSString *PLVWElogBeautyResourceHandleError = @"beautyResourceHandleError";
// 一级错误码 5 统一 event
static NSString *PLVWElogBeautySDKError = @"beautySDKError";


@implementation PLVWELogEventDefine

+ (NSString *)elogErrorModulString:(NSInteger)errorCode {
    NSInteger modulCode = PLVFErrorModulCode(errorCode);
    if (modulCode < 0) {
        return nil;
    }
    NSString *modulString = @"";
    switch (modulCode) {
        case PLVFErrorCodeModulPlay:
            modulString = PLVWELogModulPlay;
            break;
        case PLVFErrorCodeModulDownload:
            modulString = PLVWELogModulDownload;
            break;
        case PLVFErrorCodeModulUpload:
            modulString = PLVWELogModulUpload;
            break;
        case PLVFErrorCodeModulRecord:
            modulString = PLVWELogModulRecord;
            break;
        case PLVFErrorCodeModulRtmp:
            modulString = PLVWELogModulRtmp;
            break;
        case PLVFErrorCodeModulChat:
            modulString = PLVWELogModulChat;
            break;
        case PLVFErrorCodeModulLink:
            modulString = PLVWELogModulLink;
            break;
        case PLVFErrorCodeModulPPT:
            modulString = PLVWELogModulPPT;
            break;
        case PLVFErrorCodeModulInit:
            modulString = PLVWELogModulInit;
            break;
        case PLVFErrorCodeModulSocket:
            modulString = PLVWELogModulSocket;
            break;
        case PLVFErrorCodeModulHttp:
            modulString = PLVWELogModulHttp;
            break;
        case PLVFErrorCodeModulInteraction:
            modulString = PLVWELogModulInteraction;
            break;
        case PLVFErrorCodeModulBeauty:
            modulString = PLVWELogModulBeauty;
            break;
        default:
            break;
    }
    return modulString;
}

+ (NSString *)elogErrorEventString:(NSInteger)errorCode {
    NSInteger modulCode = PLVFErrorModulCode(errorCode);
    if (modulCode < 0) {
        return nil;
    }
    
    NSString *topLevelString = @"";
    NSInteger topLevelCode = PLVFErrorModulTopLevelCode(errorCode);
    if (modulCode == PLVFErrorCodeModulPlay) {
        
    } else if (modulCode == PLVFErrorCodeModulDownload) {
        if (topLevelCode == 1) {
            topLevelString = PLVWElogDownloadDefaultError;
        } else if (topLevelCode == 2) {
            topLevelString = PLVWElogDownloadAccountError;
        } else if (topLevelCode == 3) {
            topLevelString = PLVWElogDownloadNetworkError;
        } else if (topLevelCode == 4) {
            topLevelString = PLVWElogDownloadVideoInfoError;
        } else if (topLevelCode == 5) {
            topLevelString = PLVWElogDownloadVideoStatusError;
        } else if (topLevelCode == 6) {
            topLevelString = PLVWElogDownloadFileError;
        } else if (topLevelCode == 7) {
            topLevelString = PLVWElogDownloadMemoryError;
        } else if (topLevelCode == 8) {
            topLevelString = PLVWElogDownloadLocalVideoError;
        }
        
    } else if (modulCode == PLVFErrorCodeModulUpload) {
        
    } else if (modulCode == PLVFErrorCodeModulRecord) {
        
    } else if (modulCode == PLVFErrorCodeModulRtmp) {
           
    } else if (modulCode == PLVFErrorCodeModulChat) {
        if (topLevelCode == 1 || topLevelCode == 2 || topLevelCode == 3) {
            topLevelString = PLVWElogChatHttpError;
        } else if (topLevelCode == 4) {
            topLevelString = PLVWElogChatSocketAckError;
        } else if (topLevelCode == 5) {
            topLevelString = PLVWElogChatMessageError;
        } else if (topLevelCode == 6) {
            topLevelString = PLVWElogChatImageError;
        }
    } else if (modulCode == PLVFErrorCodeModulLink) {
           
    } else if (modulCode == PLVFErrorCodeModulPPT) {
        if (topLevelCode == 1 || topLevelCode == 2 || topLevelCode == 3) {
            topLevelString = PLVWElogPPTHttpError;
        } else if (topLevelCode == 4) {
            topLevelString = PLVWElogPPTWebError;
        }
    } else if (modulCode == PLVFErrorCodeModulInit) {
        if (topLevelCode == 1 || topLevelCode == 2 || topLevelCode == 3) {
            topLevelString = PLVWElogInitHttpError;
        }
    } else if (modulCode == PLVFErrorCodeModulSocket) {
        if (topLevelCode == 1 || topLevelCode == 2 || topLevelCode == 3) {
            topLevelString = PLVWElogSocketHttpError;
        } else if (topLevelCode == 5) {
            topLevelString = PLVWElogSocketConnectError;
        } else if (topLevelCode == 6) {
            topLevelString = PLVWElogSocketLoginError;
        }
    } else if (modulCode == PLVFErrorCodeModulHttp) {
        topLevelString = @"http";
    } else if (modulCode == PLVFErrorCodeModulInteraction) {
        if (topLevelCode == 0) {
            topLevelString = PLVWElogInteractionWebError;
        } else if (topLevelCode == 1 || topLevelCode == 2 || topLevelCode == 3) {
            topLevelString = PLVWElogInteractionHttpError;
        }
    } else if (modulCode == PLVFErrorCodeModulBeauty) {
        if (topLevelCode == 1 || topLevelCode == 2 || topLevelCode == 3) {
            topLevelString = PLVWElogBeautyHttpError;
        } else if (topLevelCode == 4) {
            topLevelString = PLVWElogBeautyResourceHandleError;
        } else if (topLevelCode == 5) {
            topLevelString = PLVWElogBeautySDKError;
        }
    }
    return topLevelString;
}

@end
