//
//  PLVLinkMicGetTokenModel.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVLinkMicGetTokenModel.h"

#import <objc/runtime.h>
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVLinkMicGetTokenModel+PrivateInfo.h"

@implementation PLVLinkMicGetTokenModel

- (instancetype)init{
    if (self = [super init]) {
        BOOL VCLASSScene = [[[NSBundle mainBundle] bundleIdentifier] containsString:@"vclass"];
        self.client = VCLASSScene ? @"Vclass" : @"iOS SDK";
        
        /// SDK版本（非端版本）
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        self.clientVersion = liveConfig.playerVersion;
        
        /// 请求连麦Token那一刻的时间戳
        self.clientTs = [PLVFdUtil curTimeStamp];
        
        /// 设备类型（pad、phone）
        self.deviceType = (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPad) ? @"pad" : @"phone";
        
        /// 设备型号（如:iPhone 12）
        self.model = [PLVFdUtil getiPhoneTypeName];
        
        /// 操作系统（iOS）
        self.os = @"iOS";

        /// 手机系统版本（如:14.2）
        self.osVersion = [[UIDevice currentDevice] systemVersion];
        
        PLVNetworkStatus networkStatus = [PLVReachability reachabilityForInternetConnection].currentReachabilityStatus;
        if (networkStatus == PLVReachableViaWWAN) {
            self.networkType = @"4G";
        } else if (networkStatus == PLVReachableViaWiFi){
            self.networkType = @"Wifi";
        }
        
        /// 用户手机的屏幕宽高
        CGSize screenSize = [[UIScreen mainScreen] bounds].size;
        CGFloat screenScale = [UIScreen mainScreen].scale;
        self.screenWidth = [NSString stringWithFormat:@"%.0lf",screenSize.width * screenScale];
        self.screenHeight = [NSString stringWithFormat:@"%.0lf",screenSize.height * screenScale];
    }
    return self;
}

#pragma mark - [ Public Methods ]
- (NSMutableDictionary *)convertModelIntoDictionary{
    NSMutableDictionary * dic = [NSMutableDictionary dictionary];
    unsigned int count;
    objc_property_t * propertyList = class_copyPropertyList([self class], &count);
    for (int i = 0; i < count; i++) {
        objc_property_t property = propertyList[i];
        const char * cName = property_getName(property);
        NSString * name = [NSString stringWithUTF8String:cName];
        NSObject * value = [self valueForKey:name];
        
        if ([name isEqualToString:@"channelType"]) { continue; }
        if ([name isEqualToString:@"forGET"]) { continue; }

        if ([value isKindOfClass:[NSString class]] || [value isKindOfClass:[NSNumber class]]) {
            // string、bool、int、NSinteger
            [dic setObject:value forKey:name];
        } else if ([value isKindOfClass:[NSArray class]]) {
            // array
            // 暂不转换
        } else if ([value isKindOfClass:[NSDictionary class]]) {
            // dict
            // 暂不转换
        } else if (value == nil) {
            // null
            // 根据业务逻辑，空值暂不添加
        } else {
            // model
            // 暂不转换
        }
    }
    free(propertyList);
    return dic;
}

#pragma mark Setter
- (void)setChannelType:(PLVChannelType)channelType{
    _channelType = channelType;
    if (_channelType == PLVChannelTypePPT) {
        self.scene = @"ppt";
    } else if (_channelType == PLVChannelTypeAlone){
        self.scene = @"alone";
    } else if (_channelType == PLVChannelTypeSeminar){
        self.scene = @"seminar";
    }
}

#pragma mark Getter
- (NSString *)userId{
    NSString * retureValue = _forGET ? [self URLEncodedString:_userId] : _userId;
    return retureValue;
}

- (NSString *)viewerId{
    NSString * retureValue = _forGET ? [self URLEncodedString:_viewerId] : _viewerId;
    return retureValue;
}

- (NSString *)nickname{
    NSString * retureValue = _forGET ? [self URLEncodedString:_nickname] : _nickname;
    return retureValue;
}

- (NSString *)model{
    NSString * retureValue = _forGET ? [self URLEncodedString:_model] : _model;
    return retureValue;
}

- (NSString *)client{
    NSString * retureValue = _forGET ? [self URLEncodedString:_client] : _client;
    return retureValue;
}

- (NSString *)clientVersion{
    NSString * retureValue = _forGET ? [self URLEncodedString:_clientVersion] : _clientVersion;
    return retureValue;
}

- (NSString *)URLEncodedString:(NSString *)url {
    if (![PLVFdUtil checkStringUseable:url]) { return @""; }
    NSString *charactersToEscape = @"<>|{}^`!*'();:@&=+$,/?%#[]\" ";
    NSCharacterSet *allowedCharacters = [[NSCharacterSet characterSetWithCharactersInString:charactersToEscape] invertedSet];
    NSString *encodeStr = [url stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
    return encodeStr;
}

@end
