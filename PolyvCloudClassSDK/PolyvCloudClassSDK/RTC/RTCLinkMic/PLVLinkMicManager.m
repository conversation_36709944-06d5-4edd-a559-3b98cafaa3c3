//
//  PLVLinkMicManager.m
//  PLVLiveScenesSDK
//
//  Created by Lincal on 2020/2/3.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVLinkMicManager.h"

#import "PLVLiveVideoAPI.h"
#import "PLVLiveVideoConfig.h"
#import "PLVLivePrivateAPI.h"
#import "PLVConsoleLogger.h"
#import "PLVLiveVClassAPI.h"
#import "PLVLiveVClassAPI+Private.h"
#import "PLVSocketManager.h"
#import "PLVWELogEventDefine.h"
#import "PLVWLogReporterManager.h"

#import <PLVFoundationSDK/PLVFoundationSDK.h>

@interface PLVLinkMicManager ()<PLVBLinkMicManagerDelegate>

@property (nonatomic, strong) PLVBLinkMicManager * rtcManager;

@property (nonatomic, assign) BOOL micDefaultFinalOpen;
@property (nonatomic, assign) BOOL cameraDefaultFinalOpen;
@property (nonatomic, assign) BOOL cameraDefaultFinalFront;
@property (nonatomic, assign) BOOL hadSetupDefaultState;

@property (nonatomic, copy) NSString * channelId;    /// 频道号ID（房间号）
@property (nonatomic, copy) NSString * userLinkMicId;/// 本地用户连麦ID
@property (nonatomic, assign) NSTimeInterval reconnectingTimestamp; /// 单位：秒；单次开始重连时间戳
@property (nonatomic, assign) NSTimeInterval reconnectingDuration; ///  单位：秒；重连的已结算时长 (包含每一次重连的总时长，重连结束后结算)

@property (nonatomic, strong) NSTimer * reconnectingTimer;

@property (nonatomic, strong) PLVLinkMicGetTokenModel * getTokenModel;

@property (nonatomic, assign) PLVBLinkMicStreamQuality streamQuality; /// 当前 流清晰度 (默认值:PLVBLinkMicStreamQuality180P)
@property (nonatomic, assign, readonly) PLVBLinkMicStreamQuality defaultStreamQuality; /// 默认推流 清晰度 (默认值:PLVBLinkMicStreamQuality180P)
@property (nonatomic, assign, readonly) PLVBLinkMicStreamQuality firstScreenStreamQuality; /// 直播连麦时 观众设置第一画面时推流视频清晰度

@end

@implementation PLVLinkMicManager

#pragma mark - [ Life Period ]
- (void)dealloc{
    [self stopReconnectingTimer];
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"%s", __FUNCTION__);
}

- (instancetype)init{
    if (self = [super init]) {
        [self resetData];
    }
    return self;
}


#pragma mark - [ Public Methods ]
+ (instancetype)linkMicManagerWithRTCType:(NSString *)rtcType{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"linkMicManager linkMicManagerWithRTCType"); /// 一般情况下不允许打印 rtcType
    
    PLVLinkMicManager * manager;
    
    /// 创建 核心RTC管理器
    PLVBLinkMicManager * rtcManager = [PLVBLinkMicManager linkMicManagerWithRTCType:rtcType];
    if (rtcManager) {
        /// 创建 RTC连麦管理器
        manager = [[PLVLinkMicManager alloc]init];
        manager.rtcManager = rtcManager;

        /// 配置 核心RTC管理器
        rtcManager.delegate = manager;
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"%s linkMicManager init failed with (rtcType:%@)", __FUNCTION__, rtcType);
    }
    return manager;
}

/// 更新互动学堂 RTC Token
- (void)updateVClassRTCTokenWith:(PLVLinkMicGetTokenModel *)model completion:(nullable void (^)(BOOL updateResult))completion {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"%s with (model:%@ completion:%@)",__FUNCTION__, model, completion);
    
    __weak typeof(self) weakSelf = self;
    void(^getRTCTokenSuccessBlock)(NSString * _Nonnull data) = ^(NSString * _Nonnull data){
        if (![PLVFdUtil checkStringUseable:data]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"%s get token failed with 【param illegal】(data:%@)", __FUNCTION__, data);
            if (completion) { completion(NO); }
        }else{
            int result = [weakSelf.rtcManager updateLinkMicTokenWithStr:data];
            if (completion) { completion(result == 0 ? YES : NO); }
        }
    };
    
    void(^getRTCTokenFailBlock)(NSError * _Nonnull error) = ^(NSError * _Nonnull error){
        if (completion) { completion(NO); }
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"%s get token failed with 【%@】(VClassRTCToken)", __FUNCTION__, error);
    };

    self.getTokenModel = model;
    if ([model.userType isEqualToString:@"teacher"]) {
        [PLVLiveVClassAPI teacherRTCTokenWithModel:model success:getRTCTokenSuccessBlock failure:getRTCTokenFailBlock];
    } else {
        [PLVLiveVClassAPI watcherRTCTokenWithModel:model success:getRTCTokenSuccessBlock failure:getRTCTokenFailBlock];
    }
}

/// TODO: 后期该请求逻辑可能移至Business层
- (void)updateLinkMicTokenWith:(PLVLinkMicGetTokenModel *)model completion:(nullable void (^)(BOOL updateResult))completion{
    PLVBLinkMicRTCType rtcType = self.rtcManager.rtcType;
    NSString * rtcTypeName = @"";
    if (rtcType == PLVBLinkMicRTCType_AG) {
        rtcTypeName = @"ARTC";
    }else if (rtcType == PLVBLinkMicRTCType_UC) {
        rtcTypeName = @"URTC";
    }else if (rtcType == PLVBLinkMicRTCType_ZE) {
        rtcTypeName = @"ZRTC";
    }else if (rtcType == PLVBLinkMicRTCType_TX) {
        rtcTypeName = @"TRTC";
    }else if (rtcType == PLVBLinkMicRTCType_BD) {
        rtcTypeName = @"VOLC";
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"%s update failed with 【param illegal】 (rtcType:%@)", __FUNCTION__, rtcType);
    }
    
    __weak typeof(self) weakSelf = self;
    void(^getRTCTokenSuccessBlock)(NSDictionary * _Nonnull dict) = ^(NSDictionary * _Nonnull dict){
        NSString *data = dict[@"data"];
        if (![PLVFdUtil checkStringUseable:data]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"%s get token failed with 【param illegal】(rtcTypeName:%@, responseDict:%@)", __FUNCTION__, rtcTypeName, dict);
            if (completion) { completion(NO); }
        }else{
            int result = [weakSelf.rtcManager updateLinkMicTokenWithStr:data];
            if (completion) { completion(result == 0 ? YES : NO); }
        }
    };
    
    void(^getRTCTokenFailBlock)(NSError * _Nonnull error) = ^(NSError * _Nonnull error){
        if (completion) { completion(NO); }
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"%s get token failed with 【%@】(rtcTypeName:%@)", __FUNCTION__, error, rtcTypeName);
    };

    self.getTokenModel = model;
    if (rtcType == PLVBLinkMicRTCType_AG) {
        [PLVLivePrivateAPI getAgRTCTokenWithModel:model success:getRTCTokenSuccessBlock failure:getRTCTokenFailBlock];
    }else if (rtcType == PLVBLinkMicRTCType_UC) {
        [PLVLivePrivateAPI getUcRTCTokenWithModel:model success:getRTCTokenSuccessBlock failure:getRTCTokenFailBlock];
    }else if (rtcType == PLVBLinkMicRTCType_ZE) {
        [PLVLivePrivateAPI getZeRTCTokenWithModel:model success:getRTCTokenSuccessBlock failure:getRTCTokenFailBlock];
    }else if (rtcType == PLVBLinkMicRTCType_TX) {
        [PLVLivePrivateAPI getTRTCTokenWithModel:model success:getRTCTokenSuccessBlock failure:getRTCTokenFailBlock];
    }else if (rtcType == PLVBLinkMicRTCType_BD) {
        [PLVLivePrivateAPI getVOLCTokenWithModel:model success:getRTCTokenSuccessBlock failure:getRTCTokenFailBlock];
    }
}

- (int)joinRtcChannelWithChannelId:(NSString *)channelId userLinkMicId:(NSString *)userLinkMicId{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"linkMicManager joinRtcChannel");
    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:userLinkMicId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"%s join rtc channel failed with 【param illegal】(channelId:%@, userLinkMicId:%@)", __FUNCTION__, channelId, userLinkMicId);
        return -1;
    }else{
        self.channelId = channelId;
        self.userLinkMicId = userLinkMicId;
    }
    
    [self prepareRTCEngine];
    
    if (self.linkMicOnAudio) {
        [self.rtcManager enableLocalVideo:NO];
    } else {
        [self.rtcManager enableLocalVideo:YES];
        if (self.viewer && !self.viewerAllow) {
            [self.rtcManager muteLocalVideoStream:YES];
        }
    }
    if (self.viewer) {
        if (self.viewerAllow) {
            [self.rtcManager enableLocalAudio:YES];
        } else {
            [self.rtcManager enableLocalAudio:NO];
        }
    }
    
    if (self.rtcManager.rtcType == PLVBLinkMicRTCType_TX) {
        [self.rtcManager enableLocalAudio:YES]; // 处理本地禁用音视频时，远端无法收到回调导致状态不同步的问题
    }

    int code = [self.rtcManager joinRtcChannelWithChannelId:self.channelId userLinkMicId:self.userLinkMicId];
    return code;
}

- (void)leaveRtcChannel{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"%s", __FUNCTION__);
    self.hadSetupDefaultState = NO;
    [self.rtcManager enableLocalVideo:NO];
    [self.rtcManager startLocalPreview:NO];
    [self.rtcManager leaveRtcChannel];
    [self resetData];
    [self stopReconnectingTimer];
}

- (void)setTeacherUserId:(NSString *)teacherId {
    if (![PLVFdUtil checkStringUseable:teacherId]) {
        return;
    }
    
    [self.rtcManager setTeacherUserId:teacherId];
}

- (void)switchClientRoleTo:(PLVBLinkMicRoleType)role{
    if (_rtcManager && self.viewer) {
        [_rtcManager setUserRoleTo:role];
    }else{
        PLV_LOG_WARN(PLVConsoleLogModuleTypeLinkMic, @"%s switch client roleTo failed with【param illegal】(_rtcManager:%@, viewer:%@)", __FUNCTION__, _rtcManager, self.viewer);
    }
}

- (void)subscribeStreamWithRTCUserId:(NSString *)rtcUserId renderOnView:(UIView *)renderSuperView mediaType:(PLVBRTCSubscribeStreamMediaType)mediaType{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"subscribe stream with (rtcUserId:%@, renderSuperView:%@, mediaType:%lu)", rtcUserId, renderSuperView, mediaType);
    [self subscribeStreamWithRTCUserId:rtcUserId renderOnView:renderSuperView mediaType:mediaType subscribeMode:PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix];
}

- (void)subscribeStreamWithRTCUserId:(NSString *)rtcUserId renderOnView:(UIView *)renderSuperView mediaType:(PLVBRTCSubscribeStreamMediaType)mediaType subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"subscribe stream with (rtcUserId:%@, renderSuperView:%@, mediaType:%lu, subscribeMode:%lu)", rtcUserId, renderSuperView, mediaType, subscribeMode);
    
    /// 统一 ’角色切换API‘ 的调用时机，兼容无延迟频道观看的场景
    if ([rtcUserId isEqualToString:self.userLinkMicId]) {
        [self.rtcManager setUserRoleTo:PLVBLinkMicRoleBroadcaster];
    }
    [self.rtcManager subscribeStreamWithRTCUserId:rtcUserId renderOnView:renderSuperView mediaType:mediaType subscribeMode:subscribeMode];
}

- (void)unsubscribeStreamWithRTCUserId:(NSString *)rtcUserId{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"unsubscribe stream with (rtcUserId:%@)", rtcUserId);
    [self unsubscribeStreamWithRTCUserId:rtcUserId subscribeMode:PLVBRTCSubscribeStreamSubscribeMode_ScreenFirst_Mix];
}

- (void)unsubscribeStreamWithRTCUserId:(NSString *)rtcUserId subscribeMode:(PLVBRTCSubscribeStreamSubscribeMode)subscribeMode{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"unsubscribe stream with (rtcUserId:%@, subscribeMode:%lu)", rtcUserId,subscribeMode);
    if ([rtcUserId isEqualToString:self.userLinkMicId]) {
        self.hadSetupDefaultState = NO;
        [self.rtcManager setUserRoleTo:PLVBLinkMicRoleAudience];
    }
    [self.rtcManager unsubscribeStreamWithRTCUserId:rtcUserId subscribeMode:subscribeMode];
}

- (void)switchSubscribeStreamMediaTypeWithRTCUserId:(NSString *)rtcUserId mediaType:(PLVBRTCSubscribeStreamMediaType)toMediaType{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"switch subscribe stream with (rtcUserId:%@, mediaType:%lu)", rtcUserId, toMediaType);
    [self.rtcManager switchSubscribeStreamMediaTypeWithRTCUserId:rtcUserId mediaType:toMediaType];
}

- (void)muteSubscribedRemoteStreamInLocalWithMute:(BOOL)mute {
    [self.rtcManager muteSubscribedRemoteStreamInLocalWithMute:mute];
}

- (void)linkMicLocalUserFirstScreenDidChanged:(BOOL)firstScreen {
    PLVBLinkMicStreamQuality switchStreamQuality = firstScreen ? self.firstScreenStreamQuality : self.defaultStreamQuality;
    if (switchStreamQuality != self.streamQuality) {
        [self setupStreamQuality:switchStreamQuality];
    }
}

+ (void)requestLinkMicStatusWithChannelId:(NSString *)channelId
                               completion:(void (^)(NSString *status, NSString *type))completion
                                  failure:(void (^)(NSError *))failure {
    // 为了防止高并发，首选用socket获取
    [[PLVSocketManager sharedManager] emitEvent:@"mic" content:@{@"EVENT" : @"MIC_SETTING"} timeout:5.0 callback:^(NSArray *ackArray) {
        NSString *status = nil;
        NSString *type = nil;
        if ([PLVFdUtil checkArrayUseable:ackArray]) {
            id jsonObject = nil;
            if ([ackArray.firstObject isKindOfClass:[NSString class]]) {
                NSData *jsonData = [ackArray.firstObject dataUsingEncoding:NSUTF8StringEncoding];
                if (jsonData && jsonData.length > 0) {
                    jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
                }
            } else if ([ackArray.firstObject isKindOfClass:[NSDictionary class]]) {
                jsonObject = ackArray.firstObject;
            }
            
            NSInteger code = PLV_SafeIntegerForDictKey(jsonObject, @"code");
            if (code == 200) {
                NSDictionary *dataDict = PLV_SafeDictionaryForDictKey(jsonObject, @"data");
                status = PLV_SafeStringForDictKey(dataDict, @"status");
                type = PLV_SafeStringForDictKey(dataDict, @"type");
            }
        }
        
        if (status && type) {
            if (completion) {
                completion(status, type);
            }
        } else { // socket获取连麦状态异常时再通过http接口获取
            [PLVLiveVideoAPI requestLinkMicStatusWithRoomId:channelId.integerValue completion:completion failure:^(NSError *error) {
                if (failure) {
                    failure(error);
                }
            }];
        }
    }];
}

#pragma mark 本地硬件管理

- (void)setupLocalVideoPreviewMirrorMode:(PLVBRTCVideoMirrorMode)mirrorMode {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"set local video preview mirrorMode : %ld", mirrorMode);
    [self.rtcManager setupLocalVideoPreviewMirrorMode:mirrorMode];
}

- (void)switchLocalUserCamera:(BOOL)frontCamera{
    if (!self.hadSetupDefaultState) {
        _cameraDefaultFinalFront = frontCamera;
        return;
    }
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"switch local user camera : %@", frontCamera ? @"YES" : @"NO");
    [self.rtcManager switchLocalUserCamera:frontCamera];
    [[PLVWLogReporterManager sharedManager] reportWithEvent:@"switchCamera" modul:PLVWELogModulLink information:nil patch:YES];
}

- (void)openLocalUserCamera:(BOOL)openCamera{
    if (!self.hadSetupDefaultState) {
        _cameraDefaultFinalOpen = openCamera;
        return;
    }
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"open local user camera : %@", openCamera ? @"YES" : @"NO");
    NSString *event = openCamera ? @"enableLocalVideo" : @"disableLocalVideo";
    [[PLVWLogReporterManager sharedManager] reportWithEvent:event modul:PLVWELogModulLink information:nil patch:YES];
    [self.rtcManager openLocalUserCamera:openCamera];
}

- (void)openLocalUserMic:(BOOL)openMic{
    if (!self.hadSetupDefaultState) {
        _micDefaultFinalOpen = openMic;
        return;
    }
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"open local user mic : %@", openMic ? @"YES" : @"NO");
    NSString *event = openMic ? @"enableLocalAudio" : @"disableLocalAudio";
    [[PLVWLogReporterManager sharedManager] reportWithEvent:event modul:PLVWELogModulLink information:nil patch:YES];
    [self.rtcManager openLocalUserMic:openMic];
}

#pragma mark 远端连麦用户管理

- (void)joinResponseWithRemoteUserDict:(NSDictionary *)userDict needAnswer:(BOOL)needAnswer completed:(void (^)(BOOL success))completedBlock {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"allow user join linkMic with【userDict: %@】", userDict);
    
    BOOL userDictValid = [self checkRemoteUserDict:userDict];
    BOOL socketAllow = [self checkSocketState];
    if (userDictValid && socketAllow) {
        
        NSMutableDictionary *jsonDict = [NSMutableDictionary dictionary];
        jsonDict[@"roomId"] = self.channelId ?: @"";
        jsonDict[@"value"] = @"1";
        jsonDict[@"toEmitAll"]  = @(1);
        jsonDict[@"needAnswer"] = needAnswer ? @(1) : @(0);
        jsonDict[@"user"] = userDict;

        [[PLVSocketManager sharedManager] emitEvent:@"joinResponse" content:jsonDict timeout:5.0 callback:^(NSArray *ackArray) {
            BOOL success = NO;
            if ([PLVFdUtil checkArrayUseable:ackArray] &&
                [PLVFdUtil checkStringUseable:ackArray[0]]) {
                success = ([PLVFdUtil checkStringUseable:ackArray[0]] && [ackArray[0] isEqualToString:@"joinResponse"]);
            }
            if (!success) {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"emit joinResponse failed with 【callback illegal】(ack:%@)", ackArray);
            }
            
            if (completedBlock) {
                plv_dispatch_main_async_safe(^{
                    completedBlock(success);
                })
            }
        }];
        
    } else if (!userDictValid) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"allow user join linkMic failed with 【param illegal】(userDict:%@)", userDict);
        if (completedBlock) {
            plv_dispatch_main_async_safe(^{
                completedBlock(NO);
            })
        }
    } else if (!socketAllow) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"allow user join linkMic failed with socket state");
        if (completedBlock) {
            plv_dispatch_main_async_safe(^{
                completedBlock(NO);
            })
        }
    }
}

- (void)closeLinkMicWithRemoteUserId:(NSString *)userId completed:(void (^)(BOOL success))completedBlock {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"close user linkMic with【userId: %@】", userId);
    
    BOOL userIdValid = [PLVFdUtil checkStringUseable:userId];
    BOOL socketAllow = [self checkSocketState];
    if (userIdValid && socketAllow) {

        NSMutableDictionary *jsonDict = [NSMutableDictionary dictionary];
        jsonDict[@"EVENT"] = @"OPEN_MICROPHONE";
        jsonDict[@"roomId"] = self.channelId ?: @"";
        jsonDict[@"status"] = @"close";
        jsonDict[@"type"] = @"video";
        jsonDict[@"userId"] = userId;

        [[PLVSocketManager sharedManager] emitEvent:@"message" content:jsonDict timeout:5.0 callback:^(NSArray *ackArray) {
            if ([PLVFdUtil checkArrayUseable:ackArray]) {
                if (completedBlock) {
                    plv_dispatch_main_async_safe(^{
                        completedBlock(YES);
                    })
                }
            }else{
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"emit OPEN_MICROPHONE close failed with 【callback illegal】(ack:%@)", ackArray);
                if (completedBlock) {
                    plv_dispatch_main_async_safe(^{
                        completedBlock(NO);
                    })
                }
            }
        }];
        
    } else if (!userIdValid) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"close user linkMic failed with 【param illegal】(userId:%@)", userId);
        if (completedBlock) {
            plv_dispatch_main_async_safe(^{
                completedBlock(NO);
            })
        }
    } else if (!socketAllow) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"close user linkMic failed with socket state");
        if (completedBlock) {
            plv_dispatch_main_async_safe(^{
                completedBlock(NO);
            })
        }
    }
}

- (void)muteMicrophoneWithRemoteUserId:(NSString *)userId mute:(BOOL)mute completed:(void (^)(BOOL success))completedBlock {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"mute remote user mic with【userId: %@, mute: %@】", userId, (mute ? @"YES" : @"NO"));
    if (![PLVFdUtil checkStringUseable:userId] ||
        [userId isEqualToString:self.getTokenModel.userId]) {
        if (completedBlock) {
            plv_dispatch_main_async_safe(^{
                completedBlock(NO);
            })
        }
        return;
    }
    
    if (![PLVSocketManager sharedManager].login ||
        [PLVSocketManager sharedManager].status != PLVSocketConnectStatusConnected) {
        if (completedBlock) {
            plv_dispatch_main_async_safe(^{
                completedBlock(NO);
            })
        }
        return;
    }
    
    [[PLVSocketManager sharedManager] emitPermissionMessageWithUserId:userId type:PLVSocketPermissionTypeMicrophone status:!mute timeout:5.0 callback:^(NSArray * _Nonnull ackArray) {
        if ([PLVFdUtil checkArrayUseable:ackArray]) {
            if (completedBlock) {
                plv_dispatch_main_async_safe(^{
                    completedBlock(YES);
                })
            }
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"emit TEACHER_SET_PERMISSION with audio type failed with 【callback illegal】(ack:%@, mute: %@)", ackArray,  (mute ? @"YES" : @"NO"));
            if (completedBlock) {
                plv_dispatch_main_async_safe(^{
                    completedBlock(NO);
                })
            }
        }
    }];
}

- (void)muteCameraWithRemoteUserId:(NSString *)userId mute:(BOOL)mute completed:(void (^)(BOOL success))completedBlock {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"mute remote user camera with【userId: %@, mute: %@】", userId, (mute ? @"YES" : @"NO"));
    
    if (![PLVFdUtil checkStringUseable:userId] ||
        [userId isEqualToString:self.getTokenModel.userId]) {
        if (completedBlock) {
            plv_dispatch_main_async_safe(^{
                completedBlock(NO);
            })
        }
        return;
    }
    
    if (![PLVSocketManager sharedManager].login ||
        [PLVSocketManager sharedManager].status != PLVSocketConnectStatusConnected) {
        if (completedBlock) {
            plv_dispatch_main_async_safe(^{
                completedBlock(NO);
            })
        }
        return;
    }
    
    [[PLVSocketManager sharedManager] emitPermissionMessageWithUserId:userId type:PLVSocketPermissionTypeCamera status:!mute timeout:5.0 callback:^(NSArray * _Nonnull ackArray) {
        if ([PLVFdUtil checkArrayUseable:ackArray]) {
            if (completedBlock) {
                plv_dispatch_main_async_safe(^{
                    completedBlock(YES);
                })
            }
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"emit TEACHER_SET_PERMISSION with video type failed with 【callback illegal】(ack:%@, mute: %@)", ackArray,  (mute ? @"YES" : @"NO"));
            if (completedBlock) {
                plv_dispatch_main_async_safe(^{
                    completedBlock(NO);
                })
            }
        }
    }];
}

#pragma mark 流管理

- (void)setupStreamQuality:(PLVBLinkMicStreamQuality)streamQuality {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"%s with (streamQuality:%d)", __FUNCTION__, streamQuality);
    _streamQuality = streamQuality;
    [self.rtcManager setupVideoEncoderConfiguration:[self updateVideoEncoderConfiguration]];
}

#pragma mark Setter
- (void)setMicDefaultOpen:(BOOL)micDefaultOpen{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"set micDefaultOpen : %@", micDefaultOpen ? @"YES" : @"NO");
    _micDefaultOpen = micDefaultOpen;
    _micDefaultFinalOpen = _micDefaultOpen;
}

- (void)setCameraDefaultOpen:(BOOL)cameraDefaultOpen{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"set cameraDefaultOpen : %@", cameraDefaultOpen ? @"YES" : @"NO");
    _cameraDefaultOpen = cameraDefaultOpen;
    _cameraDefaultFinalOpen = _cameraDefaultOpen;
}

- (void)setCameraDefaultFront:(BOOL)cameraDefaultFront{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"set cameraDefaultFront : %@", cameraDefaultFront ? @"YES" : @"NO");
    _cameraDefaultFront = cameraDefaultFront;
    _cameraDefaultFinalFront = _cameraDefaultFront;
}

#pragma mark Getter
- (BOOL)hadJoinedRTC{
    return self.rtcManager.hadJoinedRTC;
}

- (PLVBLinkMicNetworkQuality)networkQuality{
    return self.rtcManager.networkQuality;
}

- (BOOL)subscribedRemoteStreamNeedMute{
    return self.rtcManager.subscribedRemoteStreamNeedMute;
}

/// 重连时间计算
- (NSTimeInterval)reconnectingThisTimeDuration{
    NSTimeInterval thisTime_reconnectingDuration = 0;
    if (self.reconnectingTimestamp > 0) {
        thisTime_reconnectingDuration = [self getCurrentTimestampSec] - self.reconnectingTimestamp;
    }
    return thisTime_reconnectingDuration;
}

- (PLVBLinkMicStreamQuality)defaultStreamQuality {
    return PLVBLinkMicStreamQuality180P;
}

- (PLVBLinkMicStreamQuality)firstScreenStreamQuality {
    NSString *qualityLevel = [PLVLiveVideoConfig sharedInstance].audienceFirstScreenQualityLevel;
    if ([PLVFdUtil checkStringUseable:qualityLevel]) {
        if ([qualityLevel isEqualToString:@"360p"]) {
            return PLVBLinkMicStreamQuality360P;
        } else if ([qualityLevel isEqualToString:@"720p"]) {
            return PLVBLinkMicStreamQuality720P;
        } else if ([qualityLevel isEqualToString:@"1080p"]) {
            return PLVBLinkMicStreamQuality1080P;
        }
    }
    return PLVBLinkMicStreamQuality180P;
}

#pragma mark - [ Private Method ]
/// 准备 RTC引擎
- (void)prepareRTCEngine{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeLinkMic, @"%s", __FUNCTION__);
    if (!self.rtcManager.engineIsReady) {
        [self.rtcManager createRTCEngine];
        [self.rtcManager setupVideoEncoderConfiguration:[self updateVideoEncoderConfiguration]];
    }
}

/// 更新视频编码配置
- (PLVBRTCVideoEncoderConfiguration *)updateVideoEncoderConfiguration{
    PLVBRTCVideoEncoderConfiguration * videoEncoderConfiguration = self.rtcManager.currentVideoEncoderConfiguration;
    if (!videoEncoderConfiguration) { videoEncoderConfiguration = [[PLVBRTCVideoEncoderConfiguration alloc] init]; }
    
    PLVBLinkMicStreamQuality currentStreamQuality = self.streamQuality;
    PLVBLinkMicStreamScale currentStreamScale = self.streamScale;

    PLVBRTCVideoOutputOrientationMode orientationMode;
    if (currentStreamScale == PLVBLinkMicStreamScale9_16 ||
        currentStreamScale == PLVBLinkMicStreamScale3_4) {
        orientationMode = PLVBRTCVideoOutputOrientationMode_Portrait;
    }else{
        orientationMode = PLVBRTCVideoOutputOrientationMode_Landscape;
    }
    
    CGSize videoResolution;
    NSInteger videoBitrate;
    NSInteger videoFrameRate;
    if (currentStreamQuality == PLVBLinkMicStreamQuality1080P) {
        /// 1080P
        if (currentStreamScale == PLVBLinkMicStreamScale4_3 ||
            currentStreamScale == PLVBLinkMicStreamScale3_4) {
            videoResolution = PLVBRTCVideoResolution1440x1080;
            videoBitrate = 3620;
            videoFrameRate = 15;
        }else{
            videoResolution = PLVBRTCVideoResolution1920x1080;
            videoBitrate = 4160;
            videoFrameRate = 15;
        }
    } else if (currentStreamQuality == PLVBLinkMicStreamQuality720P) {
        /// 720P
        if (currentStreamScale == PLVBLinkMicStreamScale4_3 ||
            currentStreamScale == PLVBLinkMicStreamScale3_4) {
            videoResolution = PLVBRTCVideoResolution960x720;
            videoBitrate = 1820;
            videoFrameRate = 15;
        }else{
            videoResolution = PLVBRTCVideoResolution1280x720;
            videoBitrate = 2260;
            videoFrameRate = 15;
        }
    }else if (currentStreamQuality == PLVBLinkMicStreamQuality360P){
        /// 360P
        if (currentStreamScale == PLVBLinkMicStreamScale4_3 ||
            currentStreamScale == PLVBLinkMicStreamScale3_4) {
            videoResolution = PLVBRTCVideoResolution480x360;
            videoBitrate = 640;
            videoFrameRate = 15;
        }else{
            videoResolution = PLVBRTCVideoResolution640x360;
            videoBitrate = 800;
            videoFrameRate = 15;
        }
    }else{
        /// 其他 (180P)
        if (currentStreamScale == PLVBLinkMicStreamScale4_3 ||
            currentStreamScale == PLVBLinkMicStreamScale3_4) {
            videoResolution = PLVBRTCVideoResolution240x180;
            videoBitrate = 240;
            videoFrameRate = 15;
        }else{
            videoResolution = PLVBRTCVideoResolution320x180;
            videoBitrate = 280;
            videoFrameRate = 15;
        }
    }
    videoEncoderConfiguration.videoResolution = videoResolution;
    videoEncoderConfiguration.videoBitrate = videoBitrate;
    videoEncoderConfiguration.videoFrameRate = videoFrameRate;
    videoEncoderConfiguration.videoOutputOrientationMode = orientationMode;

    return videoEncoderConfiguration;
}

- (void)resetData{
    /// 初始化 数据
    self.reconnectingTimestamp = 0;
    self.reconnectingDuration = 0;
    self.streamQuality = self.defaultStreamQuality;
}

#pragma mark Timer
- (void)reconnectingTimerEvent{
    if (self.reconnectingTimestamp <= 0) { return; }
    if (self.hadJoinedRTC) {
        [self callbackForCurrentReconnectingThisTimeDuration];
    }
}
/// 重连计时器
- (void)startReconnectingTimer{
    if (_reconnectingTimer) { [self stopReconnectingTimer]; }

    self.reconnectingTimestamp = [self getCurrentTimestampSec];
    self.reconnectingTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(reconnectingTimerEvent) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:self.reconnectingTimer forMode:NSRunLoopCommonModes];
}

- (void)stopReconnectingTimer{
    [self.reconnectingTimer invalidate];
    self.reconnectingTimer = nil;
    
    if (self.reconnectingTimestamp > 0) {
        if (self.hadJoinedRTC) {
            _reconnectingDuration += self.reconnectingThisTimeDuration;
        }
    }
    
    if (self.reconnectingTimestamp != 0) {
        [self callbackForCurrentReconnectingThisTimeDuration];
        self.reconnectingTimestamp = 0;
        [self callbackForCurrentReconnectingThisTimeDuration];
    }
}

- (NSTimeInterval)getCurrentTimestampSec{
    return [[NSDate date] timeIntervalSince1970];
}

#pragma mark Callback
- (void)callbackForNetworkQualityDidChanged{
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvLinkMicManager:networkQualityDidChanged:)]) {
            [self.delegate plvLinkMicManager:self networkQualityDidChanged:self.networkQuality];
        }
    })
}

#pragma mark Utils

/// 判断当前 socket 状态是否正常
- (BOOL)checkSocketState {
    if ([PLVSocketManager sharedManager].login &&
        [PLVSocketManager sharedManager].status == PLVSocketConnectStatusConnected) {
        return YES;
    } else {
        return NO;
    }
}

/// 判断连麦的远端用户的 userDict 是否为有效数据，且该远端用户不为自己时才返回 YES
- (BOOL)checkRemoteUserDict:(NSDictionary *)userDict {
    if (![PLVFdUtil checkDictionaryUseable:userDict]) {
        return NO;
    }
    
    NSString *userId = PLV_SafeStringForDictKey(userDict, @"userId");
    if (![PLVFdUtil checkStringUseable:userId]) {
        return NO;
    }
    
    if ([userId isEqualToString:self.getTokenModel.userId]) {
        return NO;
    }
    
    return YES;
}

- (void)callbackForCurrentReconnectingThisTimeDuration{
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvLinkMicManager:currentReconnectingThisTimeDuration:)]) {
            [self.delegate plvLinkMicManager:self currentReconnectingThisTimeDuration:self.reconnectingThisTimeDuration];
        }
    })
}

#pragma mark - [ Delegate ]
#pragma mark PLVBLinkMicManagerDelegate
- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager joinRTCChannelComplete:(NSString *)channelId uid:(NSString *)uid{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"self join rtc channel with (channelId:%@, uid:%@)", channelId, uid);
    [[PLVWLogReporterManager sharedManager] reportWithEvent:@"joinChannel" modul:PLVWELogModulLink information:nil patch:YES];
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:joinRTCChannelComplete:uid:)]) {
        [self.delegate plvLinkMicManager:self joinRTCChannelComplete:channelId uid:uid];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager joinRTCChannelFailure:(NSString *)channelId uid:(NSString *)uid {
    PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"self join rtc channel failure with (channelId:%@, uid:%@)", channelId, uid);
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:joinRTCChannelFailure:uid:)]) {
        [self.delegate plvLinkMicManager:self joinRTCChannelFailure:channelId uid:uid];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager leaveRTCChannelComplete:(NSString *)channelId{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"self leave rtc channel with (channelId:%@)", channelId);
    [[PLVWLogReporterManager sharedManager] reportWithEvent:@"leaveChannel" modul:PLVWELogModulLink information:nil patch:YES];
    [self.rtcManager destroyRTCEngine];
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:leaveRTCChannelComplete:)]) {
        [self.delegate plvLinkMicManager:self leaveRTCChannelComplete:channelId];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager leaveRTCChannelByServerComplete:(NSString *)channelId {
    
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager * _Nonnull)manager rtcConnectionStateDidChanged:(PLVBLinkMicConnectionStateType)connectionState{
    
    if (connectionState == PLVBLinkMicConnectionStateDisconnected ||
        connectionState == PLVBLinkMicConnectionStateReconnecting ||
        connectionState == PLVBLinkMicConnectionStateFailed) {
        if (!_reconnectingTimer && self.hadJoinedRTC) {
            [self startReconnectingTimer];
        }
    }else{
        [self stopReconnectingTimer];
    }
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:rtcConnectionStateDidChanged:)]) {
        [self.delegate plvLinkMicManager:self rtcConnectionStateDidChanged:connectionState];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didOccurError:(NSError *)error{
    PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"self rtc linkMic error with (errorCode:%ld)", error.code);
    PLVBLinkMicRTCType rtcType = self.rtcManager.rtcType;
    NSString * rtcTypeName = @"unknown";
    if (rtcType == PLVBLinkMicRTCType_AG) {
        rtcTypeName = @"ARTC";
    }else if (rtcType == PLVBLinkMicRTCType_UC) {
        rtcTypeName = @"URTC";
    }else if (rtcType == PLVBLinkMicRTCType_ZE) {
        rtcTypeName = @"ZRTC";
    }else if (rtcType == PLVBLinkMicRTCType_TX) {
        rtcTypeName = @"TRTC";
    }else if (rtcType == PLVBLinkMicRTCType_BD) {
        rtcTypeName = @"VOLC";
    }
    NSString *eventString = [NSString stringWithFormat:@"linkMicdidOccurError:%@,%ld",rtcTypeName, error.code];
    [[PLVWLogReporterManager sharedManager] reportWithEvent:eventString modul:PLVWELogModulLink information:nil];
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:didOccurError:)]) {
        [self.delegate plvLinkMicManager:self didOccurError:error.code];
    }
}

- (void)plvbLinkMicManagerTokenExpires:(PLVBLinkMicManager *)manager{
    [[PLVWLogReporterManager sharedManager] reportWithEvent:@"onTokenExpired" modul:PLVWELogModulLink information:nil];
    [self updateLinkMicTokenWith:self.getTokenModel completion:^(BOOL updateResult) {
        if (!updateResult) { PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"linkMic token renew failed") }
    }];
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didJoinedOfUid:(NSString *)uid{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"other join rtc channel with (uid:%@)", uid);
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:didJoinedOfUid:)]) {
        [self.delegate plvLinkMicManager:self didJoinedOfUid:uid];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didOfflineOfUid:(NSString *)uid{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"other leave rtc channel with (uid:%@)", uid);
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:didOfflineOfUid:)]) {
        [self.delegate plvLinkMicManager:self didOfflineOfUid:uid];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didAudioMuted:(BOOL)muted byUid:(NSString *)uid{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"other did audio muted with (uid:%@)", uid);
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:didAudioMuted:byUid:)]) {
        [self.delegate plvLinkMicManager:self didAudioMuted:muted byUid:uid];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didVideoMuted:(BOOL)muted byUid:(NSString *)uid{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"other did video muted with (uid:%@)", uid);
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:didVideoMuted:byUid:)]) {
        [self.delegate plvLinkMicManager:self didVideoMuted:muted byUid:uid];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didAudioMuted:(BOOL)muted streamSourceType:(PLVBRTCSubscribeStreamSourceType)streamSourceType byUid:(NSString *)uid{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"other did audio muted with (streamSourceType:%ld uid:%@)", streamSourceType, uid);
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:didAudioMuted:streamSourceType:byUid:)]) {
        [self.delegate plvLinkMicManager:self didAudioMuted:muted streamSourceType:streamSourceType byUid:uid];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didVideoMuted:(BOOL)muted streamSourceType:(PLVBRTCSubscribeStreamSourceType)streamSourceType byUid:(NSString *)uid{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"other did video muted with (streamSourceType:%ld uid:%@)", streamSourceType, uid);
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:didVideoMuted:streamSourceType:byUid:)]) {
        [self.delegate plvLinkMicManager:self didVideoMuted:muted streamSourceType:streamSourceType byUid:uid];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager * _Nonnull)manager didNewStreamJoinRoomOfUid:(NSString *)uid {
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"did other stream join room with (uid:%@)", uid);
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:didNewStreamJoinRoomOfUid:)]) {
        [self.delegate plvLinkMicManager:self didNewStreamJoinRoomOfUid:uid];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager * _Nonnull)manager streamJoinRoom:(PLVBRTCSubscribeStreamSourceType)streamSourceType userRTCId:(NSString *)userRTCId{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"stream join room with (streamSourceType:%ld uid:%@)",streamSourceType ,userRTCId);
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:streamJoinRoom:userRTCId:)]) {
        [self.delegate plvLinkMicManager:self streamJoinRoom:streamSourceType userRTCId:userRTCId];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager * _Nonnull)manager streamLeaveRoom:(PLVBRTCSubscribeStreamSourceType)streamSourceType userRTCId:(NSString *)userRTCId{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"stream leave room with (streamSourceType:%ld uid:%@)",streamSourceType ,userRTCId);
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:streamLeaveRoom:userRTCId:)]) {
        [self.delegate plvLinkMicManager:self streamLeaveRoom:streamSourceType userRTCId:userRTCId];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager * _Nonnull)manager remoteUserTotalStreamsDidLeaveRoom:(NSString *)uid{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeLinkMic, @"remote user total streams did leave room with (uid:%@)", uid);
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:remoteUserTotalStreamsDidLeaveRoom:)]) {
        [self.delegate plvLinkMicManager:self remoteUserTotalStreamsDidLeaveRoom:uid];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager networkQualityDidChanged:(PLVBLinkMicNetworkQuality)networkQuality{
    [self callbackForNetworkQualityDidChanged];
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager * _Nonnull)manager reportAudioVolumeOfSpeakers:(NSDictionary<NSString *, NSNumber *> * _Nonnull)volumeDict{
    if ([self.delegate respondsToSelector:@selector(plvLinkMicManager:reportAudioVolumeOfSpeakers:)]) {
        [self.delegate plvLinkMicManager:self reportAudioVolumeOfSpeakers:volumeDict];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager userNetworkQualityDidChanged:(NSString *)userRTCId txQuality:(PLVBLinkMicNetworkQuality)txQuality rxQuality:(PLVBLinkMicNetworkQuality)rxQuality{
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvLinkMicManager:userNetworkQualityDidChanged:txQuality:rxQuality:)]) {
        [self.delegate plvLinkMicManager:self userNetworkQualityDidChanged:userRTCId txQuality:txQuality rxQuality:rxQuality];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager * _Nonnull)manager userRttDict:(NSDictionary <NSString *, NSNumber *> *)rttDict {
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(plvLinkMicManager:userRttDict:)]) {
        [self.delegate plvLinkMicManager:self userRttDict:rttDict];
    }
}

- (NSString *)plvbLinkMicManagerGetUCStreamAuthority:(PLVBLinkMicManager *)manager{
    extern BOOL SDKInnerGlobalChannelWatchNoDelay;
    if (SDKInnerGlobalChannelWatchNoDelay) {
        return @"Upload";
    }else{
        return @"All";
    }
}

- (void)plvbLinkMicManagerCanSetupLocalHardwareDefaultState:(PLVBLinkMicManager *)manager{
    self.hadSetupDefaultState = YES;
    [self.rtcManager setUserRoleTo:PLVBLinkMicRoleBroadcaster];
    [self openLocalUserMic:self.micDefaultFinalOpen];
    [self openLocalUserCamera:self.cameraDefaultFinalOpen];
    [self switchLocalUserCamera:self.cameraDefaultFinalFront];
    self.cameraDefaultFinalFront = self.cameraDefaultFront;
    self.cameraDefaultFinalOpen = self.cameraDefaultOpen;
    self.micDefaultFinalOpen = self.micDefaultOpen;
}

@end
