//
//  PLVLinkMicGetTokenModel+PrivateInfo.h
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2021/4/19.
//  Copyright © 2021 PLV. All rights reserved.
//

#import <PLVLiveScenesSDK/PLVLiveScenesSDK.h>

NS_ASSUME_NONNULL_BEGIN

@interface PLVLinkMicGetTokenModel ()

@property (nonatomic, copy) NSString * scene;
@property (nonatomic, copy) NSString * client;
@property (nonatomic, copy) NSString * clientVersion; /// SDK版本（非端版本）
@property (nonatomic, copy) NSString * clientTs; /// 请求连麦Token那一刻的时间戳
@property (nonatomic, copy) NSString * deviceType; /// 设备类型（pad、phone）
@property (nonatomic, copy) NSString * model; /// 设备型号（如:iPhone 12）
@property (nonatomic, copy) NSString * os; /// 操作系统（iOS）
@property (nonatomic, copy) NSString * osVersion; /// 手机系统版本（如:14.2）
@property (nonatomic, copy) NSString * networkType;
@property (nonatomic, copy) NSString * screenWidth; /// 用户手机的屏幕宽
@property (nonatomic, copy) NSString * screenHeight; /// 用户手机的屏幕高

@property (nonatomic, assign) BOOL forGET;

/// 将该模型转出为字典
- (NSMutableDictionary *)convertModelIntoDictionary;

@end

NS_ASSUME_NONNULL_END
