//
//  PLVDownloadDatabaseManager.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVDownloadDatabaseManager.h"
#import "PLVDownloadManager.h"
#import "PLVDownloadPathManager.h"

#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVConsoleLogger.h"

#import "PLVDownloadTaskInfo+PrivateExtension.h"
#import "PLVDownloadDatabaseManager+Playback.h"

#import <PLVFDB/PLVFDatabase.h>

static NSString * PLVDownloadPlaybackTaskInfoTable = @"PLVDownloadPlaybackTaskInfoTable";

@interface PLVDownloadDatabaseManager ()

@property (nonatomic, assign) UIBackgroundTaskIdentifier backgroundTaskId;

/// 数据库
@property (nonatomic, strong) PLVFDatabase * database;

// 状态
@property (nonatomic, assign) BOOL willTerminate;
@property (nonatomic, assign) NSUInteger waitTimes;

// 回调
@property (nonatomic, strong) NSMutableDictionary <NSString *, PLVDownloadDatabaseTaskInfoArrayRefreshBlock> * refreshBlockDic;

#pragma mark [云课堂回放]
/// 所有的回放下载任务 数组
@property (nonatomic, strong) NSMutableArray <PLVDownloadPlaybackTaskInfo *> * totalPlaybackTaskInfoArr_inner;
/// 下载完成的回放下载 数组
@property (nonatomic, strong) NSMutableArray <PLVDownloadPlaybackTaskInfo *> * completedPlaybackTaskInfoArr_inner;
/// 下载未完成的回放下载 数组
@property (nonatomic, strong) NSMutableArray <PLVDownloadPlaybackTaskInfo *> * unfinishedPlaybackTaskInfoArr_inner;

@end


@implementation PLVDownloadDatabaseManager

#pragma mark - [ Life Cycle ]

static PLVDownloadDatabaseManager * manager = nil;

+ (instancetype)allocWithZone:(struct _NSZone *)zone
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        if (manager == nil) manager = [super allocWithZone:zone];
    });
    return manager;
}

- (instancetype)init{
    if (self = [super init]) {
        if ([self createTable]) {
            [self setupData];
            [self setupNotification];
        }
    }
    return self;
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [_database close];
    _database = nil;
    
    PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadDatabaseManager - [[[ Had Dealloc ]]]");
}

#pragma mark - [ Private Method ]

- (BOOL)createTable{
    BOOL result = [self.database createTable:PLVDownloadPlaybackTaskInfoTable withClass:PLVDownloadPlaybackTaskInfo.class]; // 回放数据创建
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 回放数据表创建 %@",result ? @"成功" : @"失败");
    return result;
}

- (void)setupData{
    self.refreshBlockDic = [[NSMutableDictionary alloc]init];
    
    // 读取数据
    self.totalPlaybackTaskInfoArr_inner = [[NSMutableArray alloc]init];

    self.completedPlaybackTaskInfoArr_inner = [NSMutableArray arrayWithArray:[self.database objectsFromClass:PLVDownloadPlaybackTaskInfo.class table:PLVDownloadPlaybackTaskInfoTable where:@"where state = 8"]];
    
    self.unfinishedPlaybackTaskInfoArr_inner = [NSMutableArray arrayWithArray:[self.database objectsFromClass:PLVDownloadPlaybackTaskInfo.class table:PLVDownloadPlaybackTaskInfoTable where:@"where state != 8"]];
    
    for (PLVDownloadPlaybackTaskInfo * obj in self.completedPlaybackTaskInfoArr_inner) {
        [self handleTaskInfoFromDatabase:obj];
        [self.totalPlaybackTaskInfoArr_inner addObject:obj];
    }
    for (PLVDownloadPlaybackTaskInfo * obj in self.unfinishedPlaybackTaskInfoArr_inner) {
        [self handleTaskInfoFromDatabase:obj];
        [self.totalPlaybackTaskInfoArr_inner addObject:obj];
    }
    
    // 事件
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        for (PLVDownloadPlaybackTaskInfo * playbackI in self->_totalPlaybackTaskInfoArr_inner) {
            [self addCompletedBlockWithTaskInfo:playbackI];
        }
    });
    
    // 路径改变
    __weak typeof(self) weakSelf = self;
    [[PLVDownloadPathManager shareManager] addViewerIdChangeBlock:^(PLVDownloadPathManager * _Nonnull manager, NSString * _Nonnull viewerId) {
        [weakSelf handleViewerIdChange];
    } key:@"com.polyv.PLVDownloadDatabaseManager"];
}

- (void)handleViewerIdChange{
    NSUInteger runningTask = [PLVDownloadManager shareManager].currentDownloadCount;
    if (runningTask == 0) {
        [self rebuildDatabase];
    }else if (runningTask > 0 && self.waitTimes <= 25) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            self.waitTimes ++;
            [self handleViewerIdChange];
        });
    }else{
        [self rebuildDatabase];
    }
}

- (void)rebuildDatabase{
    self.willTerminate = YES;
    [self saveAllData];
    self.willTerminate = NO;
    
    [_database close];
    _database = nil;
    
    _refreshBlockDic = nil;
    _totalPlaybackTaskInfoArr_inner = nil;
    _completedPlaybackTaskInfoArr_inner = nil;
    _unfinishedPlaybackTaskInfoArr_inner = nil;
    
    if ([self createTable]) {
        [self setupData];
        [self setupNotification];
        PLV_LOG_INFO(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 数据库已重建");
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 数据库重建失败");
    }
    
    self.waitTimes = 0;
}

- (void)handleTaskInfoFromDatabase:(PLVDownloadPlaybackTaskInfo *)taskInfo{
    // 极端情况下，状态需额外修正
    if (taskInfo.state != PLVDownloadStateDefault &&
        taskInfo.state != PLVDownloadStateSuccess &&
        taskInfo.state != PLVDownloadStateFailed &&
        taskInfo.state != PLVDownloadStateStopped) {
        taskInfo.state = PLVDownloadStateDefault;
    }
}

- (void)addCompletedBlockWithTaskInfo:(PLVDownloadPlaybackTaskInfo *)targetTaskInfo{
    __weak typeof(self) weakSelf = self;
    [targetTaskInfo addDownloadCompletedBlock:^(PLVDownloadTaskInfo * _Nullable taskInfo, NSError * _Nullable error) {
        if (!error && taskInfo.state == PLVDownloadStateSuccess) {
            // 更新
            [weakSelf updateOneData:(PLVDownloadPlaybackTaskInfo *)taskInfo];
            // 数组间迁移
            PLVDownloadPlaybackTaskInfo * playbackTaskInfo = (PLVDownloadPlaybackTaskInfo *)taskInfo;
            if ([weakSelf.unfinishedPlaybackTaskInfoArr_inner containsObject:playbackTaskInfo]) {
                [weakSelf arrayRemoveObj:PLVDownloadDatabaseTaskInfoArray_UnfinishedPlayback taskInfo:playbackTaskInfo];
                [weakSelf arrayAddObj:PLVDownloadDatabaseTaskInfoArray_CompletedPlayback taskInfo:playbackTaskInfo];
            }else{
                //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 提示：数据迁移失败，taskInfo不在下载未完成数组中");
            }
        }
    } key:@"com.polyv.PLVDownloadDatabaseManager"];
}

- (void)setupNotification{
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationWillTerminate:) name:UIApplicationWillTerminateNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationDidReceiveMemoryWarning:) name:UIApplicationDidReceiveMemoryWarningNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationWillResignActive:) name:UIApplicationWillResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationDidBecomeActive:) name:UIApplicationDidBecomeActiveNotification object:nil];
}

#pragma mark Array
- (NSMutableArray *)arrayWithType:(PLVDownloadDatabaseTaskInfoArrayType)arrayType{
    NSMutableArray * targetMuArray;
    switch (arrayType) {
        case PLVDownloadDatabaseTaskInfoArray_TotalPlayback:
            targetMuArray = (NSMutableArray *)self.totalPlaybackTaskInfoArr_inner;
            break;
        case PLVDownloadDatabaseTaskInfoArray_CompletedPlayback:
            targetMuArray = (NSMutableArray *)self.completedPlaybackTaskInfoArr_inner;
            break;
        case PLVDownloadDatabaseTaskInfoArray_UnfinishedPlayback:
            targetMuArray = (NSMutableArray *)self.unfinishedPlaybackTaskInfoArr_inner;
            break;
        default:
            break;
    }
    if (targetMuArray) {
        return targetMuArray;
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 内部错误，无对应数组");
        return nil;
    }
}

- (void)arrayAddObj:(PLVDownloadDatabaseTaskInfoArrayType)arrayType taskInfo:(PLVDownloadTaskInfo *)taskInfo{
    if (!taskInfo) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 内部错误，对象添加失败");
        return;
    }
    NSMutableArray * targetMuArray = [self arrayWithType:arrayType];
    if (targetMuArray) {
        if (![targetMuArray containsObject:taskInfo]) {
            [targetMuArray addObject:taskInfo];
            [self callbackArrayRefreshBlock:arrayType];
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 对象添加失败，内部错误，已存在于数组中");
        }
    }
}

- (PLVDownloadPlaybackTaskInfo *)findPlaybackTaskInfoWithFileId:(NSString *)fileId{
    PLVDownloadPlaybackTaskInfo * reTaskInfo;
    for (PLVDownloadPlaybackTaskInfo * taskInfo in self.totalPlaybackTaskInfoArr_inner) {
        if ([taskInfo.fileId isEqualToString:fileId]) {
            reTaskInfo = taskInfo;
            break;
        }
    }
    return reTaskInfo;
}

/// 根据videopoolId 查找TaskInfo，只有回放列表、点播列表存在videopoolId
- (PLVDownloadPlaybackTaskInfo *)findPlaybackTaskInfoWithVideoPoolId:(NSString *)videopoolId {
    PLVDownloadPlaybackTaskInfo * reTaskInfo;
    for (PLVDownloadPlaybackTaskInfo * taskInfo in self.totalPlaybackTaskInfoArr_inner) {
        if ([taskInfo.videoPoolId isEqualToString:videopoolId]) {
            reTaskInfo = taskInfo;
            break;
        }
    }
    return reTaskInfo;
}

- (void)arrayRemoveObj:(PLVDownloadDatabaseTaskInfoArrayType)arrayType taskInfo:(PLVDownloadTaskInfo *)taskInfo{
    if (!taskInfo) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 内部错误，对象移除失败");
        return;
    }
    NSMutableArray * targetMuArray = [self arrayWithType:arrayType];
    if (targetMuArray) {
        if ([targetMuArray containsObject:taskInfo]) {
            [targetMuArray removeObject:taskInfo];
            [self callbackArrayRefreshBlock:arrayType];
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 对象删除失败，内部错误，已不存在于数组中");
        }
    }
}

- (void)callbackArrayRefreshBlock:(PLVDownloadDatabaseTaskInfoArrayType)arrayType{
    dispatch_async(dispatch_get_main_queue(), ^{
        __weak typeof(self) weakSelf = self;

        if (weakSelf.taskInfoArrayRefreshBlock) {
            weakSelf.taskInfoArrayRefreshBlock(weakSelf, arrayType);
        }
        
        if (weakSelf.refreshBlockDic.allKeys.count > 0) {
            NSArray <PLVDownloadDatabaseTaskInfoArrayRefreshBlock> * blocks = weakSelf.refreshBlockDic.allValues;
            for (PLVDownloadDatabaseTaskInfoArrayRefreshBlock block in blocks) {
                block(weakSelf, arrayType);
            }
        }
    });
}

#pragma mark Data
- (void)saveAllData{
    for (PLVDownloadPlaybackTaskInfo * taskInfo in self.totalPlaybackTaskInfoArr_inner) {
        if (_willTerminate) {
            if (taskInfo.state != PLVDownloadStateSuccess &&
                taskInfo.state != PLVDownloadStateFailed &&
                taskInfo.state != PLVDownloadStateStopped) {
                taskInfo.state = PLVDownloadStateDefault;
            }
        }
        [self updateOneData:taskInfo];
    }
}

- (void)updateOneData:(PLVDownloadPlaybackTaskInfo *)taskInfo{
    [self.database beginTransaction];
    BOOL result = [self.database insertOrReplaceFromClass:PLVDownloadPlaybackTaskInfo.class object:taskInfo table:PLVDownloadPlaybackTaskInfoTable where:[NSString stringWithFormat:@"where taskInfoId = '%@'",taskInfo.taskInfoId]];
    if (result) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 数据已更新保存");
        [self.database commitOrRollbackTransaction];
    } else {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 内部错误，数据更新保存失败");
        [self.database rollbackTransaction];
    }
}

#pragma mark Getter
- (PLVFDatabase *)database{
    if (!_database) {
        NSString * databaseViewerIdPath = [PLVDownloadPathManager shareManager].databaseViewerFilePath;
        if (!databaseViewerIdPath || ![databaseViewerIdPath isKindOfClass:NSString.class] || databaseViewerIdPath.length == 0) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 数据库创建失败");
            return nil;
        }else{
            _database = [PLVFDatabase databaseWithPath:databaseViewerIdPath];
            PLV_LOG_DEBUG(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 数据库创建%@", !_database?@"失败":@"成功");
        }
    }
    return _database;
}

#pragma mark Setter
- (void)setTotalPlaybackTaskInfoArr_inner:(NSMutableArray<PLVDownloadPlaybackTaskInfo *> *)totalPlaybackTaskInfoArr_inner{
    if (!_totalPlaybackTaskInfoArr_inner) {
        _totalPlaybackTaskInfoArr_inner = totalPlaybackTaskInfoArr_inner;
        PLV_LOG_INFO(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 获取 所有的 回放任务:%@",totalPlaybackTaskInfoArr_inner);
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 数据库内部错误，多次写入数组");
    }
}

- (void)setCompletedPlaybackTaskInfoArr_inner:(NSMutableArray<PLVDownloadPlaybackTaskInfo *> *)completedPlaybackTaskInfoArr_inner{
    if (!_completedPlaybackTaskInfoArr_inner) {
        _completedPlaybackTaskInfoArr_inner = completedPlaybackTaskInfoArr_inner;
        PLV_LOG_INFO(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 获取 下载完成的 回放任务:%@",completedPlaybackTaskInfoArr_inner);
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 数据库内部错误，多次写入数组");
    }
}

- (void)setUnfinishedPlaybackTaskInfoArr_inner:(NSMutableArray<PLVDownloadPlaybackTaskInfo *> *)unfinishedPlaybackTaskInfoArr_inner{
    if (!_unfinishedPlaybackTaskInfoArr_inner) {
        _unfinishedPlaybackTaskInfoArr_inner = unfinishedPlaybackTaskInfoArr_inner;
        PLV_LOG_INFO(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 获取 下载未完成的 回放任务:%@",unfinishedPlaybackTaskInfoArr_inner);
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 数据库内部错误，多次写入数组");
    }
}


#pragma mark - [ Event ]

#pragma mark NSNotification
- (void)applicationWillTerminate:(NSNotification *)noti{
    self.willTerminate = YES;
    [self saveAllData];
}

- (void)applicationDidReceiveMemoryWarning:(NSNotification *)noti{
    [self saveAllData];
}

- (void)applicationWillResignActive:(NSNotification *)noti{
    [self saveAllData];
    
    Class UIApplicationClass = NSClassFromString(@"UIApplication");
    BOOL hasApplication = UIApplicationClass && [UIApplicationClass respondsToSelector:@selector(sharedApplication)];
    if (hasApplication ) {
        __weak __typeof__ (self) wself = self;
        UIApplication * app = [UIApplicationClass performSelector:@selector(sharedApplication)];
        self.backgroundTaskId = [app beginBackgroundTaskWithExpirationHandler:^{
            __strong __typeof (wself) sself = wself;
            
            if (sself) {
                sself.willTerminate = YES;
                [sself saveAllData];
                
                [app endBackgroundTask:sself.backgroundTaskId];
                sself.backgroundTaskId = UIBackgroundTaskInvalid;
            }
        }];
    }
}

- (void)applicationDidBecomeActive:(NSNotification *)noti{
    Class UIApplicationClass = NSClassFromString(@"UIApplication");
    if(!UIApplicationClass || ![UIApplicationClass respondsToSelector:@selector(sharedApplication)]) {
        return;
    }
    
    if (self.backgroundTaskId != UIBackgroundTaskInvalid) {
        UIApplication * app = [UIApplication performSelector:@selector(sharedApplication)];
        [app endBackgroundTask:self.backgroundTaskId];
        self.backgroundTaskId = UIBackgroundTaskInvalid;
    }

}

#pragma mark - [ Public Method ]

+ (instancetype)shareManager{
    if (manager) { return manager; }
    else { return [[self alloc] init]; }
}

- (void)addTaskInfoArrayRefreshBlock:(PLVDownloadDatabaseTaskInfoArrayRefreshBlock)block key:(NSString *)blockKey{
    if (!block) { return; }
    if (blockKey == nil || ![blockKey isKindOfClass:NSString.class] || blockKey.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 添加arrayRefresh block失败，参数错误blockKey:%@",blockKey);
        return;
    }
    if (self.refreshBlockDic.allKeys.count > 20) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 添加arrayRefresh block失败，block注册数已到限制");
        return;
    }
    [self.refreshBlockDic setObject:block forKey:blockKey];
}

- (void)removeTaskInfoArrayRefreshBlockWithKey:(NSString *)blockKey{
    if (blockKey == nil || ![blockKey isKindOfClass:NSString.class] || blockKey.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 移除arrayRefresh block失败，参数错误blockKey:%@",blockKey);
        return;
    }
    [self.refreshBlockDic removeObjectForKey:blockKey];
}


#pragma mark - [ Public Method + Playback ]
- (BOOL)addPlaybackTaskInfo:(PLVDownloadPlaybackTaskInfo *)taskInfo{
    if (!taskInfo || ![taskInfo isKindOfClass:PLVDownloadPlaybackTaskInfo.class]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 添加回放下载任务失败，参数错误:%@",taskInfo);
        return NO;
    }
    
    NSArray <PLVDownloadPlaybackTaskInfo *> *resArray = [self.database objectsFromClass:PLVDownloadPlaybackTaskInfo.class table:PLVDownloadPlaybackTaskInfoTable where:[NSString stringWithFormat:@"where taskInfoId = '%@'",taskInfo.taskInfoId]];
    
    BOOL result;
    if (resArray.count == 0) {
        result = [self.database insert:taskInfo table:PLVDownloadPlaybackTaskInfoTable];
        if (result) {
            [self addCompletedBlockWithTaskInfo:taskInfo];
            [self arrayAddObj:PLVDownloadDatabaseTaskInfoArray_TotalPlayback taskInfo:taskInfo];
            [self arrayAddObj:PLVDownloadDatabaseTaskInfoArray_UnfinishedPlayback taskInfo:taskInfo];
        }
    }else{
        result = NO;
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 添加数据 失败，数据已存在于数据库");
        return NO;
    }
    PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 添加数据 %@",result ? @"成功" : @"失败");
    return result;
}

- (BOOL)deletePlaybackTaskInfo:(PLVDownloadPlaybackTaskInfo *)taskInfo{
    if (!taskInfo || ![taskInfo isKindOfClass:PLVDownloadPlaybackTaskInfo.class]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 删除回放下载任务失败，参数错误:%@",taskInfo);
        return NO;
    }
    
    NSArray <PLVDownloadPlaybackTaskInfo *> *resArray = [self.database objectsFromClass:PLVDownloadPlaybackTaskInfo.class table:PLVDownloadPlaybackTaskInfoTable where:[NSString stringWithFormat:@"where taskInfoId = '%@'",taskInfo.taskInfoId]];
    
    BOOL result;
    if (resArray.count == 0) {
        result = NO;
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 删除数据 失败，数据已不存在于数据库");
        return NO;
    }else{
        result = [self.database deleteTable:PLVDownloadPlaybackTaskInfoTable where:[NSString stringWithFormat:@"where taskInfoId = '%@'",taskInfo.taskInfoId]];
        if (result) {
            [self arrayRemoveObj:PLVDownloadDatabaseTaskInfoArray_TotalPlayback taskInfo:taskInfo];
            if (taskInfo.state == PLVDownloadStateSuccess) {
                [self arrayRemoveObj:PLVDownloadDatabaseTaskInfoArray_CompletedPlayback taskInfo:taskInfo];
            }else{
                [self arrayRemoveObj:PLVDownloadDatabaseTaskInfoArray_UnfinishedPlayback taskInfo:taskInfo];
            }
            PLVDownloadPathManager * pathM = [PLVDownloadPathManager shareManager];
            NSString * fileIdPath = [[PLVDownloadPathManager shareManager].viewerPlaybackPath stringByAppendingPathComponent:taskInfo.fileId];
            [pathM removeFileWithPath:fileIdPath];
        }
    }
    PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 删除数据 %@",result ? @"成功" : @"失败");
    return result;
}

- (BOOL)checkPlaybackTaskInfoWithFileId:(NSString *)fileId{
    NSArray <PLVDownloadPlaybackTaskInfo *> *resArray = [self.database objectsFromClass:PLVDownloadPlaybackTaskInfo.class table:PLVDownloadPlaybackTaskInfoTable where:[NSString stringWithFormat:@"where taskInfoId = '%@'",fileId]];
    if (resArray.count == 0) {
        return NO;
    }else{
        return YES;
    }
}

/// 检查数据库是否存在videoPoolId 的taskInfo，只有回放列表、点播列表才存在videoPoolId
- (BOOL)checkPlaybackTaskInfoWithVideoPoolId:(NSString *)videoPoolId{
    NSArray <PLVDownloadPlaybackTaskInfo *> *resArray = [self.database objectsFromClass:PLVDownloadPlaybackTaskInfo.class table:PLVDownloadPlaybackTaskInfoTable where:[NSString stringWithFormat:@"where videoPoolId = '%@'",videoPoolId]];
    if (resArray.count == 0) {
        return NO;
    }else{
        return YES;
    }
}

- (PLVDownloadPlaybackTaskInfo *)checkAndGetPlaybackTaskInfoWithFileId:(NSString *)fileId{
    if ([self checkPlaybackTaskInfoWithFileId:fileId]) {
        PLVDownloadPlaybackTaskInfo * taskInfoInCache = [self findPlaybackTaskInfoWithFileId:fileId];
        return taskInfoInCache;
    }
    return nil;
}

/// 判断一个回放，是否已存在下载记录（不考虑下载状态,只有回放列表、点播列表的TaskInfo才能通过videopoolId查找）
- (PLVDownloadPlaybackTaskInfo *)checkAndGetPlaybackTaskInfoWithVideoPoolId:(NSString *)videopoolId {
    if ([self checkPlaybackTaskInfoWithVideoPoolId:videopoolId]) {
        PLVDownloadPlaybackTaskInfo * taskInfoInCache = [self findPlaybackTaskInfoWithVideoPoolId:videopoolId];
        return taskInfoInCache;
    }
    return nil;
}

#pragma mark Getter
- (NSArray<PLVDownloadPlaybackTaskInfo *> *)totalPlaybackTaskInfoArray{
    return [self.totalPlaybackTaskInfoArr_inner copy];
}

- (NSArray<PLVDownloadPlaybackTaskInfo *> *)completedPlaybackTaskInfoArray{
    return [self.completedPlaybackTaskInfoArr_inner copy];
}

- (NSArray<PLVDownloadPlaybackTaskInfo *> *)unfinishedPlaybackTaskInfoArray{
    return [self.unfinishedPlaybackTaskInfoArr_inner copy];
}

@end
