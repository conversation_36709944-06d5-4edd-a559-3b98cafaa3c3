//
//  PLVDownloadTaskInfo+PrivateExtension.h
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVDownloadTaskInfo.h"

NS_ASSUME_NONNULL_BEGIN

/// 下载任务信息内部类扩展
@interface PLVDownloadTaskInfo ()

#pragma mark - [ 属性 ]

#pragma mark 基础信息
/// 下载任务的Id
@property (nonatomic, copy) NSString *taskInfoId;
/// 文件路径
@property (nonatomic, copy) NSString *filePath;
/// 文件名
@property (nonatomic, copy) NSString *fileName;
/// 下载状态
@property (nonatomic, assign) PLVDownloadState state;

#pragma mark 过程信息
/// 下载速度值（KB/s）
@property (nonatomic, assign) float speedValue;
/// 下载速度（附带单位字符串，bytes/s、KB/s、MB/s、GB/s）
@property (nonatomic, copy) NSString *speed;
/// 下载进度值（范围，0~1）
@property (nonatomic, assign) float progress;

#pragma mark 结果信息
/// 下载错误（若下载成功，则为nil)
@property (nonatomic, strong, nullable) NSError *error;

#pragma mark 辅助属性
/// 该下载任务对应的下载处理器（weak）
@property (nonatomic, weak) NSOperation * relatedOperation;
/// 比重（描述子任务，在总任务中占比；任务被包含于抽象的总任务中，才需此属性）
@property (nonatomic, assign) float ratio;
/// 原资源是否为压缩包
@property (nonatomic, assign) BOOL sourceZip;
/// 距离上一秒已存数据大小
@property (nonatomic, assign) NSUInteger totalRead;
/// 上一秒时间戳
@property (nonatomic, strong, nullable) NSDate *date;

#pragma mark - [ 方法 ]

#pragma mark 辅助方法
/// 清空速度
- (void)resetSpeed;

@end

NS_ASSUME_NONNULL_END
