//
//  PLVDownloadPlaybackTaskInfo+PrivateExtension.h
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVDownloadPlaybackTaskInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface PLVDownloadPlaybackTaskInfo ()

#pragma mark - [ 属性 ]

#pragma mark 过程详细信息
/// 已下载数据大小数值（单位，bytes）（仅抽象总任务需赋值此属性）
@property (nonatomic, assign) unsigned long long totalBytesWritten;

#pragma mark 下载后信息
/// 视频文件路径字典（key:码率，value:文件路径）
@property (nonatomic, copy) NSDictionary *videoPathDic;
/// ppt文件夹路径
@property (nonatomic, copy) NSString *pptFolderPath;
/// ppt文件路径
@property (nonatomic, copy) NSString *pptFilePath;
/// js文件夹路径
@property (nonatomic, copy) NSString *JsFolderPath;
/// js文件路径
@property (nonatomic, copy) NSString *JsFilePath;
/// html文件路径
@property (nonatomic, copy) NSString *htmlFilePath;
/// subtitle文件夹路径
@property (nonatomic, copy) NSString *subtitleFolderPath;

/// 视频文件名字典（key:码率，value:文件名）
@property (nonatomic, copy) NSDictionary *videoFileNameDic;
/// ppt文件名
@property (nonatomic, copy) NSString *pptFileName;
/// js文件名
@property (nonatomic, copy) NSString *JsFileName;
/// html文件名
@property (nonatomic, copy) NSString *htmlFileName;
/// 字幕文件名数组
@property (nonatomic, copy) NSArray *subtitleFileNameArray;

#pragma mark 辅助属性
/// 下载资源是否为多个资源包，NO则为单个资源包
@property (nonatomic, assign) BOOL multiResource;

#pragma mark 多资源包信息
/** 以下信息仅在multiResource为YES时可用 */
/// 全部资源的总大小（视频、ppt、js；附带单位字符串，bytes、KB、MB、GB）
@property (nonatomic, copy, readonly) NSString *totalFileSizeString;

@end

NS_ASSUME_NONNULL_END
