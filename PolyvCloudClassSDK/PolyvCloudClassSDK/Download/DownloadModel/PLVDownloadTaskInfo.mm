//
//  PLVDownloadTaskInfo.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVDownloadTaskInfo.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVConsoleLogger.h"
#import "PLVDownloadPathManager.h"
#import "PLVDownloadTaskInfo+PrivateExtension.h"
#import <PLVFDB/PLVFDatabase.h>

@interface PLVDownloadTaskInfo () <PLVFDatabaseProtocol>

@property (nonatomic, strong) NSMutableDictionary <NSString *, PLVDownloadStateChangeBlock> * stateChangeBlockDic;
@property (nonatomic, strong) NSMutableDictionary <NSString *, PLVDownloadCompletedBlock> * comletedBlockDic;

@end

@implementation PLVDownloadTaskInfo

#pragma mark - [ Private Method ]
- (void)resetSpeed{
    self.speedValue = 0;
    self.speed = [[NSByteCountFormatter stringFromByteCount:0 countStyle:NSByteCountFormatterCountStyleFile]stringByAppendingString:@"/s"];
    self.date = nil;
}

#pragma mark Getter
- (NSMutableDictionary<NSString *,PLVDownloadStateChangeBlock> *)stateChangeBlockDic{
    if (!_stateChangeBlockDic) {
        _stateChangeBlockDic = [[NSMutableDictionary alloc]init];
    }
    return _stateChangeBlockDic;
}

- (NSMutableDictionary<NSString *,PLVDownloadCompletedBlock> *)comletedBlockDic{
    if (!_comletedBlockDic) {
        _comletedBlockDic = [[NSMutableDictionary alloc]init];
    }
    return _comletedBlockDic;
}


#pragma mark - [ Public Method ]
- (void)addDownloadStateChangeBlock:(PLVDownloadStateChangeBlock)block key:(NSString *)blockKey{
    if (!block) { return; }
    if (![PLVFdUtil checkStringUseable:blockKey]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadTaskInfo - 添加statechange block失败，参数错误blockKey:%@", blockKey);
        return;
    }
    if (self.stateChangeBlockDic.allKeys.count > 20) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadTaskInfo - 添加statechange block失败，block注册数已到限制");
        return;
    }
    [self.stateChangeBlockDic setObject:block forKey:blockKey];
}

- (void)removeDownloadStateChangeBlockWithKey:(NSString *)blockKey{
    if (![PLVFdUtil checkStringUseable:blockKey]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadTaskInfo - 移除statechange block失败，参数错误blockKey:%@",blockKey);
        return;
    }
    [self.stateChangeBlockDic removeObjectForKey:blockKey];
}

- (void)addDownloadCompletedBlock:(PLVDownloadCompletedBlock)block key:(NSString *)blockKey{
    if (!block) { return; }
    if (![PLVFdUtil checkStringUseable:blockKey]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadTaskInfo - 添加completed block失败，参数错误blockKey:%@",blockKey);
        return;
    }
    if (self.comletedBlockDic.allKeys.count > 20) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadTaskInfo - 添加completed block失败，block注册数已到限制");
        return;
    }
    [self.comletedBlockDic setObject:block forKey:blockKey];
}

- (void)removeDownloadCompletedBlockWithKey:(NSString *)blockKey{
    if (![PLVFdUtil checkStringUseable:blockKey]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadTaskInfo - 移除completed block失败，参数错误blockKey:%@",blockKey);
        return;
    }
    [self.comletedBlockDic removeObjectForKey:blockKey];
}

#pragma mark - [Getter & Setter]

#pragma mark Getter
- (NSString *)fileName {
    if (_fileName == nil) {
        NSString * lastPathComponent = self.url.lastPathComponent;
        if (lastPathComponent.length) {
            _fileName = lastPathComponent;
        } else {
            _fileName = [PLVDataUtil md5HexDigest:self.url];
        }
    }
    return _fileName;
}

- (NSString *)filePath {
    if (![PLVFdUtil checkStringUseable:_filePath]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadTaskInfo - 文件路径未配置");
    }
    return _filePath;
}

- (NSString *)speed{
    NSString * reStr = _speed;
    if (!reStr || ![reStr isKindOfClass:NSString.class] || reStr.length == 0){
        reStr = @"0 KB/s";
    }else if ([reStr containsString:@"Zero"]) {
        reStr = [reStr stringByReplacingOccurrencesOfString:@"Zero" withString:@"0"];
    }
    return reStr;
}

- (unsigned long long)totalBytesWritten {
    if (self.state == PLVDownloadStateSuccess) {
        return _totalBytesExpectedToWrite;
    }else{
        return [[PLVDownloadPathManager shareManager] fileSizeForPath:self.filePath];
    }
}

- (NSString *)totalBytesWrittenString{
   NSString * sizeStr =  [NSByteCountFormatter stringFromByteCount:self.totalBytesWritten countStyle:NSByteCountFormatterCountStyleFile];
    if ([sizeStr containsString:@"Zero"]) {
        sizeStr = [sizeStr stringByReplacingOccurrencesOfString:@"Zero" withString:@"0"];
    }
    return sizeStr;
}

- (NSString *)totalBytesExpectedToWriteString{
    NSString * sizeStr =  [NSByteCountFormatter stringFromByteCount:self.totalBytesExpectedToWrite countStyle:NSByteCountFormatterCountStyleFile];
    if ([sizeStr containsString:@"Zero"]) {
        sizeStr = [sizeStr stringByReplacingOccurrencesOfString:@"Zero" withString:@"0"];
    }
    return sizeStr;
}

#pragma mark Setter
- (void)setFileId:(NSString *)fileId {
    if (_fileId && !fileId){
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 置空fileId，fileId不可置空");
        return;
    }else if(!_fileId && !fileId){
        return;
    }
    if(![fileId isKindOfClass:NSString.class] || fileId.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 设置fileId失败，参数错误:%@",fileId);
        return;
    }
    if (!_fileId) {
        _fileId = [fileId copy];
        self.taskInfoId = _fileId;
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 设置fileId失败，fileId已有值:%@",_fileId);
    }
}

- (void)setUrl:(NSString *)url{
    if (_url && !url){
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadTaskInfo - 置空url，url不可置空");
        return;
    }else if(!_url && !url){
        return;
    }
    if(![url isKindOfClass:NSString.class] || url.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadTaskInfo - 设置url失败，参数错误:%@",url);
        return;
    }
    if (!_url) {
        _url = [url copy];
        self.taskInfoId = [PLVDataUtil md5HexDigest:_url];
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadTaskInfo - 设置url失败，url已有值:%@",_url);
    }
}

- (void)setState:(PLVDownloadState)state{
    BOOL needCallBack = NO;
    if (_state != state) { needCallBack = YES; }
    
    _state = state;
    
    __weak typeof(self) weakSelf = self;
    
    if (needCallBack) {
        if (_state != PLVDownloadStateDownloading) { [self resetSpeed]; }
        
        if (_state != PLVDownloadStateFailed) { self.error = nil; }

        if (_state == PLVDownloadStateSuccess ||
            _state == PLVDownloadStateFailed ||
            _state == PLVDownloadStateStopped) {
            self.relatedOperation = nil;
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            // download state change
            if (weakSelf.downloadStateChangeBlock) {
                weakSelf.downloadStateChangeBlock(weakSelf, state);
            }
            if (weakSelf.stateChangeBlockDic.allKeys.count > 0) {
                NSArray <PLVDownloadStateChangeBlock> * blocks = weakSelf.stateChangeBlockDic.allValues;
                for (PLVDownloadStateChangeBlock block in blocks) {
                    block(weakSelf, state);
                }
            }
            
            // download completed
            if (weakSelf.state == PLVDownloadStateFailed ||
                weakSelf.state == PLVDownloadStateSuccess) {
                if (weakSelf.downloadCompletedBlock) {
                    weakSelf.downloadCompletedBlock(weakSelf, weakSelf.error);
                }
                if (weakSelf.comletedBlockDic.allKeys.count > 0) {
                    NSArray <PLVDownloadCompletedBlock> * blocks = weakSelf.comletedBlockDic.allValues;
                    for (PLVDownloadCompletedBlock block in blocks) {
                        block(weakSelf, weakSelf.error);
                    }
                }
            }
        });
    }
}

- (void)setProgress:(float)progress{
    if (progress<0 || progress>1) { progress = 0; }
    _progress = progress;
    
    if (self.downloadProgressChangeBlock) {
        __weak typeof(self) weakSelf = self;
        dispatch_async(dispatch_get_main_queue(), ^{
            self.downloadProgressChangeBlock(weakSelf, weakSelf.totalBytesWritten, weakSelf.totalBytesExpectedToWrite, weakSelf.progress, weakSelf.speed.integerValue);
        });
    }
}

#pragma mark PLVFDatabaseProtocol

+ (nonnull NSString *)primaryKey {
    return @"taskInfoId";
}

+ (nonnull NSArray<NSString *> *)propertyKeys { 
    return @[
        @"taskInfoId",
        @"fileId",
        @"url",
        @"fileName",
        @"state",
        @"filePath",
        @"progress",
        @"totalBytesExpectedToWrite",
        @"customModel",
        @"ratio"
    ];
}

@end
