//
//  PLVDownloadPlaybackTaskInfo.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVDownloadPlaybackTaskInfo.h"
#import "PLVConsoleLogger.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVDownloadPathManager.h"
#import "PLVDownloadTaskInfo+PrivateExtension.h"
#import "PLVDownloadPlaybackTaskInfo+PrivateExtension.h"
#import <PLVFDB/PLVFDatabase.h>

@interface PLVDownloadPlaybackTaskInfo () <PLVFDatabaseProtocol>

@end

@implementation PLVDownloadPlaybackTaskInfo

@synthesize url = _url;
@synthesize filePath = _filePath;
@synthesize totalBytesWritten = _totalBytesWritten;
@synthesize videoPoolId = _videoPoolId;
@synthesize subtitleList = _subtitleList;

#pragma mark - [ Life Cycle ]
- (void)dealloc{
    //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - [[[ Had Dealloc ]]] taskInfoId:%@ 指针:%p",self.taskInfoId,self);
}

#pragma mark - [ Public Method ]

#pragma mark Getter
/// 基础信息
- (NSString *)filePath{
    if (self.multiResource) {
        return [super filePath];
    }else{
        if (_filePath) {
            return _filePath;
        }else{
            NSString * viewerPlaybackPath = [PLVDownloadPathManager shareManager].viewerPlaybackPath;
            
            NSString * playbackFilePath = [viewerPlaybackPath stringByAppendingPathComponent:self.fileName];
        
            if (self.state == PLVDownloadStateSuccess) {
                playbackFilePath = [playbackFilePath stringByDeletingPathExtension];
            }
            _filePath = playbackFilePath;
            return _filePath;
        }
    }
}

/// 过程详细信息
- (unsigned long long)totalBytesWritten {
    if (self.state == PLVDownloadStateSuccess) {
        return self.totalBytesExpectedToWrite;
    }else{
        if (self.multiResource) {
            return _totalBytesWritten;
        }else{
            return [super totalBytesWritten];
        }
    }
}

- (unsigned long long)totalBytesExpectedToWrite{
    if (self.multiResource) {
        unsigned long long totalSize = self.videoSize + self.pptZipSize;
        return totalSize;
    }else{
        return [super totalBytesExpectedToWrite];
    }
}

- (NSString *)totalBytesExpectedToWriteString{
    if (self.multiResource) {
        return self.totalFileSizeString;
    }else{
        return [super totalBytesExpectedToWriteString];
    }
}

/// 回放信息
- (NSString *)videoPoolId{
    if(![PLVFdUtil checkStringUseable:_videoPoolId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - videoPoolId为空");
    }
    return _videoPoolId;
}

- (NSString *)definition{
    if (![PLVFdUtil checkStringUseable:_definition]){
        _definition = @"1"; // 默认码率1
    }
    return _definition;
}

/// 多资源包信息
- (NSString *)totalFileSizeString{
    unsigned long long totalSize = self.videoSize + self.pptZipSize;
    if (totalSize > 0) {
        NSString * totalSizeString = [NSByteCountFormatter stringFromByteCount:totalSize countStyle:NSByteCountFormatterCountStyleFile];
        if ([totalSizeString containsString:@"Zero"]) {
            totalSizeString = [totalSizeString stringByReplacingOccurrencesOfString:@"Zero" withString:@"0"];
        }
        return totalSizeString;
    }else{
        return @"0 KB";
    }
}

/// 辅助
- (BOOL)multiResource{
    if ([PLVFdUtil checkStringUseable:self.url]) { // 含有url且含有字幕时按多资源包处理
        return [PLVFdUtil checkArrayUseable:self.subtitleList];
    }else{
        if (self.videoUrl || self.pptZipUrl || self.JsZipUrl || [PLVFdUtil checkArrayUseable:self.subtitleList]) {
            return YES;
        }
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - url错误，无可用链接，按单资源包处理");
        return NO;
    }
}

/// video
- (NSDictionary *)videoFileNameDic{
    if (!self.multiResource && self.state != PLVDownloadStateSuccess) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 读取错误，任务未下载完成");
        return nil;
    }
    if (_videoFileNameDic) {
        return _videoFileNameDic;
    }else{
        NSString * defaultDefinitionPath = [[[PLVDownloadPathManager shareManager] playbackFileIdVideoPath:self.fileId] stringByAppendingPathComponent:self.definition];
        NSArray * videoFileArr = [[PLVDownloadPathManager shareManager] getAllFileAtFolderPath:defaultDefinitionPath];
        NSString * videoFilePath = videoFileArr.firstObject;
        if ([PLVFdUtil checkStringUseable:videoFilePath]) {
            NSString * videoFileName = videoFilePath.lastPathComponent;
            _videoFileNameDic = @{self.definition:videoFileName};
            return _videoFileNameDic;
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - video文件遍历失败");
            return nil;
        }
    }
}

- (NSDictionary *)videoPathDic{
    if (!self.multiResource && self.state != PLVDownloadStateSuccess) {
        //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 读取错误，任务未下载完成");
        return nil;
    }
    if (_videoPathDic) {
        return _videoPathDic;
    }
    if (!self.videoFileNameDic) {
        return nil;
    }else{
        NSMutableDictionary * currentVideoPathDic = [[NSMutableDictionary alloc]init];
        NSArray * allKeys = self.videoFileNameDic.allKeys;
        for (NSString * key in allKeys) {
            NSString * fileName = [self.videoFileNameDic objectForKey:key];
            NSString * path = [[[[PLVDownloadPathManager shareManager] playbackFileIdVideoPath:self.fileId] stringByAppendingPathComponent:key] stringByAppendingPathComponent:fileName];
            [currentVideoPathDic setObject:path forKey:key];
        }
        _videoPathDic = currentVideoPathDic.copy;
        return _videoPathDic;
    }
}

/// ppt
- (NSString *)pptFolderPath{
    if (!self.multiResource && self.state != PLVDownloadStateSuccess) {
        //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 读取错误，任务未下载完成");
        return @"";
    }
    if (_pptFolderPath) {
        return _pptFolderPath;
    }else{
        _pptFolderPath = [[PLVDownloadPathManager shareManager] playbackFileIdPPTPath:self.fileId];;
        return _pptFolderPath;
    }
}

- (NSString *)pptFileName{
    if (!self.multiResource && self.state != PLVDownloadStateSuccess) {
        //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 读取错误，任务未下载完成");
        return @"";
    }
    if (_pptFileName) {
        return _pptFileName;
    }else{
        NSArray * pptFileArr = [[PLVDownloadPathManager shareManager] getAllFileAtFolderPath:self.pptFolderPath];
        NSString * pptFilePath = pptFileArr.firstObject;
        if (pptFilePath && [pptFilePath isKindOfClass:NSString.class] && pptFilePath.length >= 0) {
            _pptFileName = pptFilePath.lastPathComponent;
            return _pptFileName;
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - ppt文件遍历失败");
            return @"";
        }
    }
}

- (NSString *)pptFilePath{
    if (!self.multiResource && self.state != PLVDownloadStateSuccess) {
        //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 读取错误，任务未下载完成");
        return @"";
    }
    if (_pptFilePath) {
        return _pptFilePath;
    }else{
        if (self.pptFileName) {
            _pptFilePath = [self.pptFolderPath stringByAppendingPathComponent:self.pptFileName];
            return _pptFilePath;
        }else{
            return @"";
        }
    }
}

/// js
- (NSString *)JsFolderPath{
    if (!self.multiResource && self.state != PLVDownloadStateSuccess) {
        //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 读取错误，任务未下载完成");
        return @"";
    }
    if (_JsFolderPath) {
        return _JsFolderPath;
    }else{
        _JsFolderPath = [[PLVDownloadPathManager shareManager] playbackFileIdJsPath:self.fileId];
        return _JsFolderPath;
    }
}

- (NSString *)JsFileName{
    if (!self.multiResource && self.state != PLVDownloadStateSuccess) {
        //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 读取错误，任务未下载完成");
        return @"";
    }
    if (_JsFileName) {
        return _JsFileName;
    }else{
        NSArray * jsFileArr = [[PLVDownloadPathManager shareManager] findFileAtFolderPath:self.JsFolderPath stringInFileName:@".js"];
        NSString * jsFilePath = jsFileArr.firstObject;
        if ([PLVFdUtil checkStringUseable:jsFilePath]) {
            _JsFileName = jsFilePath.lastPathComponent;
            return _JsFileName;
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - js文件遍历失败");
            return @"";
        }
    }
}

- (NSString *)JsFilePath{
    if (!self.multiResource && self.state != PLVDownloadStateSuccess) {
        //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 读取错误，任务未下载完成");
        return @"";
    }
    if (_JsFilePath) {
        return _JsFilePath;
    }else{
        if (self.JsFileName) {
            _JsFilePath = [self.JsFolderPath stringByAppendingPathComponent:self.JsFileName];
            return _JsFilePath;
        }else{
            return @"";
        }
    }
}

/// html
- (NSString *)htmlFileName{
    if (!self.multiResource && self.state != PLVDownloadStateSuccess) {
        //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 读取错误，任务未下载完成");
        return @"";
    }
    if (_htmlFileName) {
        return _htmlFileName;
    }else{
        NSArray * htmlFileArr = [[PLVDownloadPathManager shareManager] findFileAtFolderPath:self.JsFolderPath stringInFileName:@".html"];
        NSString * htmlFilePath = htmlFileArr.firstObject;
        if ([PLVFdUtil checkStringUseable:htmlFilePath]) {
            _htmlFileName = htmlFilePath.lastPathComponent;
            return _htmlFileName;
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - html文件遍历失败");
            return @"";
        }
    }
}

- (NSString *)htmlFilePath{
    if (!self.multiResource && self.state != PLVDownloadStateSuccess) {
        //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 读取错误，任务未下载完成");
        return @"";
    }
    if (_htmlFilePath) {
        return _htmlFilePath;
    }else{
        if (self.htmlFileName) {
            _htmlFilePath = [self.JsFolderPath stringByAppendingPathComponent:self.htmlFileName];
            return _htmlFilePath;
        }else{
            return @"";
        }
    }
}

- (NSString *)subtitleFolderPath {
    if (!self.multiResource && self.state != PLVDownloadStateSuccess) {
        //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 读取错误，任务未下载完成");
        return @"";
    }
    if (_subtitleFolderPath) {
        return _subtitleFolderPath;
    }else{
        _subtitleFolderPath = [[PLVDownloadPathManager shareManager] playbackFileIdSubtitlePath:self.fileId];
        return _subtitleFolderPath;
    }
}

#pragma mark Setter
- (void)setUrl:(NSString *)url{
    if (_url && !url){
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 置空url，url不可置空");
        return;
    }else if(!_url && !url){
        return;
    }
    if(![url isKindOfClass:NSString.class] || url.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 设置url失败，参数错误:%@",url);
        return;
    }
    if (!_url) {
        _url = [url copy];
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPlaybackTaskInfo - 设置url失败，url已有值:%@",_url);
    }
}

- (void)setVideoPoolId:(NSString *)videoPoolId{
    _videoPoolId = videoPoolId;
}

- (void)setSubtitleList:(NSArray *)subtitleList {
    _subtitleList = subtitleList;
    self.subtitleListJsonString = [PLVDataUtil jsonStringWithJSONObject:subtitleList];;
}

- (NSArray *)subtitleList {
    if (_subtitleList) {
        return _subtitleList;
    } else if ([PLVFdUtil checkStringUseable:self.subtitleListJsonString]) {
        NSData *jsonData = [self.subtitleListJsonString dataUsingEncoding:NSUTF8StringEncoding];
        if (!jsonData) {
            return nil;
        }
        @try {
            id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData
                                                            options:NSJSONReadingMutableContainers
                                                              error:nil];
            if (![PLVFdUtil checkArrayUseable:jsonObject]) {
                return @[];
            }
            _subtitleList = (NSArray *)jsonObject;
            return _subtitleList;
        } @catch (NSException *exception) {
            return @[];
        } @finally {
            return @[];
        }
        return @[];
    }
    return @[];
}

#pragma mark PLVFDatabaseProtocol

+ (nonnull NSString *)primaryKey {
    return @"taskInfoId";
}

+ (nonnull NSArray<NSString *> *)propertyKeys { 
    return @[
        @"taskInfoId",
        /// 回放信息
        @"liveType",
        @"listType",
        @"title",
        @"duration",
        @"definition",
        
        @"vid",
        @"videoId",
        @"videoPoolId",
        @"channelId",
        @"channelSessionId",
        @"originSessionId",
        @"coverUrl",
        @"playbackCacheEnabled",
        @"subtitleListJsonString", // 存储字幕json
        
        /// 下载后信息
        @"videoFileNameDic",
        @"pptFileName",
        @"JsFileName",
        @"htmlFileName",
        
        /// 多资源包信息
        @"videoUrl",
        @"videoSize",
        @"pptZipUrl",
        @"pptZipSize",
        @"JsZipUrl",
        
        /// 下载信息
        @"taskInfoId",
        @"fileId",
        @"url",
        @"fileName",
        @"state",
        @"filePath",
        
        @"progress",
        @"totalBytesWritten",
        @"totalBytesExpectedToWrite",
        @"ratio",
        
        /// 账号信息
        @"viewerId",
        @"viewerName",
        @"viewerAvatar"
    ];
}

@end
