//
//  PLVDownloadPathManager.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVDownloadPathManager.h"
#import "PLVConsoleLogger.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

/**
 路径说明
 - Documents/PLVDownloadDB/ （下载任务信息数据库目录，iTune会备份）
 - Documents/PLVDownloadDB/{viewerId} （下载任务信息数据库目录，多用户目录，iTune会备份）
 
 - Library/Caches/PLVDownloadFileCache/ (下载文件存放目录，iTune不会备份)
 - Library/Caches/PLVDownloadFileCache/{viewerId}/ （多用户目录，该用户的全部下载文件都在此目录下，iTune不会备份）
 - Library/Caches/PLVDownloadFileCache/{viewerId}/CloudClass/playback/  (该用户的云课堂的回放文件存储区)
 - Library/Caches/PLVDownloadFileCache/{viewerId}/CloudClass/playback/{fileId}/ （某个视频相关文件，该视频的相关资源文件都在此目录下）
 - Library/Caches/PLVDownloadFileCache/{viewerId}/CloudClass/playback/{fileId}/video/{bitrate}/ （某个码率的视频）
 - Library/Caches/PLVDownloadFileCache/{viewerId}/CloudClass/playback/{fileId}/ppt/  （ppt压缩包解压后的文件）
 - Library/Caches/PLVDownloadFileCache/{viewerId}/CloudClass/playback/{fileId}/js/   （js代码）
 
 - Library/Caches/PLVDownloadFileCache/{viewerId}/Vod/singleVideo/{vid}/{bitrate}/  拓展示例：[业务]点播，[类型]单视频，[id]某个视频，[分辨率]某个码率
 */

/// 下载总目录名定义
NSString * const PLVDownloadFileCacheFolderName = @"PLVDownloadFileCache"; // 下载文件存放目录名
NSString * const PLVDownloadDatabaseFolderName = @"PLVDownloadDB"; // 下载数据库目录名
NSString * const PLVCloudClassFileCache_ComponentName = @"CloudClass/playback"; // 云课堂下载文件存放子路径名

@interface PLVDownloadPathManager ()

/// 当前用户Id（可通过 setupDownloadViewerId: 配置）
@property (nonatomic, copy) NSString * viewerId;

/// 下载数据库总目录
@property (nonatomic, copy) NSString * databaseHomePath;
/// 下载信息数据库的用户目录
@property (nonatomic, copy) NSString * databaseViewerFilePath;

/// 下载文件存放总目录（未来可能的业务扩展，可在此目录下拓展）
@property (nonatomic, copy) NSString * fileCacheHomePath;
/// 下载文件存放的用户目录
@property (nonatomic, copy) NSString * fileCacheViewerPath;

/// 当前用户的云课堂回放路径
@property (nonatomic, copy) NSString * viewerPlaybackPath;

// 回调
@property (nonatomic, strong) NSMutableDictionary <NSString *, PLVDownloadViewerIdChangeBlock> * viewerIdChangeBlockDic;

@end

@implementation PLVDownloadPathManager

static PLVDownloadPathManager * manager = nil;

+ (instancetype)allocWithZone:(struct _NSZone *)zone
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        if (manager == nil) manager = [super allocWithZone:zone];
    });
    return manager;
}


#pragma mark - [ Private Method ]
- (instancetype)init{
    if (self = [super init]) {
        [self setupData];
    }
    return self;
}

- (void)setupData{
    self.viewerIdChangeBlockDic = [[NSMutableDictionary alloc]init];
}

#pragma mark - [ Public Method ]
+ (instancetype)shareManager{
    if (manager) { return manager; }
    else { return [[self alloc] init]; }
}

- (void)setupDownloadViewerId:(NSString *)viewerId{
    if ([PLVFdUtil checkStringUseable:viewerId]) {
        viewerId = [PLVDataUtil md5HexDigest:viewerId];
        if (!_viewerId) {
            self.viewerId = viewerId;
        }else{
            if ([_viewerId isEqualToString:viewerId]) {
                PLV_LOG_WARN(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 用户Id无更新，与当前Id相同");
                return;
            }
            
            self.viewerId = viewerId;
            
            _databaseHomePath = nil;
            _databaseViewerFilePath = nil;
            _fileCacheHomePath = nil;
            _fileCacheViewerPath = nil;
            _viewerPlaybackPath = nil;
            
            // callback
            __weak typeof(self) weakSelf = self;
            dispatch_async(dispatch_get_main_queue(), ^{
                if (weakSelf.viewerIdChangeBlockDic.allKeys.count > 0) {
                    NSArray <PLVDownloadViewerIdChangeBlock> * blocks = weakSelf.viewerIdChangeBlockDic.allValues;
                    for (PLVDownloadViewerIdChangeBlock block in blocks) {
                        block(weakSelf, weakSelf.viewerId);
                    }
                }
            });
        }
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 配置用户Id失败，参数错误");
    }
}

#pragma mark 辅助方法
- (unsigned long long)fileSizeForPath:(NSString *)path{
    unsigned long long fileSize = 0;
    NSFileManager * fileManager = [NSFileManager defaultManager];
    if ([fileManager fileExistsAtPath:path]) {
        NSError *error = nil;
        NSDictionary *fileDict = [fileManager attributesOfItemAtPath:path error:&error];
        if (!error && fileDict) {
            fileSize = [fileDict fileSize];
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 获取路径文件大小失败 Error:%@",error);
        }
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 获取路径文件大小失败，路径不存在");
    }
    return fileSize;
}

- (BOOL)checkPathExsit:(NSString *)path{
    if ([PLVFdUtil checkStringUseable:path])  {
        if (![[NSFileManager defaultManager] fileExistsAtPath:path]) {
            return NO;
        }else{
            return YES;
        }
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 路径无法确认，参数错误");
        return YES; // 返回yes
    }
}

- (void)ensureFolderExsit:(NSString *)folderPath{
    if ([PLVFdUtil checkStringUseable:folderPath])  {
        if (![[NSFileManager defaultManager] fileExistsAtPath:folderPath]) {
            NSError * err;
            BOOL res = [[NSFileManager defaultManager] createDirectoryAtPath:folderPath withIntermediateDirectories:YES attributes:nil error:&err];
            if (!res) {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 目录:%@ 创建失败 err:%@",folderPath,err);
            }else{
                PLV_LOG_INFO(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 目录 创建成功 path:%@",folderPath);
            }
        }
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 路径无法确认，参数错误");
    }
}

- (NSError *)removeFileWithPath:(NSString *)filePath{
    NSError * err;
    if ([PLVFdUtil checkStringUseable:filePath]) {
        [[NSFileManager defaultManager] removeItemAtPath:filePath error:&err];
        if (err) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 移除文件失败，Error:%@",err);
        }
        return err;
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 移除文件错误，参数错误:%@",filePath);
        return err;
    }
}

- (NSArray<NSString *> *)getAllFileAtFolderPath:(NSString *)folderPath{
    return [self findFileAtFolderPath:folderPath stringInFileName:@""];
}

- (NSArray <NSString *> *)findFileAtFolderPath:(NSString *)folderPath
                              stringInFileName:(NSString *)string{
    
    NSFileManager * fm = [NSFileManager defaultManager];
    BOOL isDir = YES;
    if (![fm fileExistsAtPath:folderPath isDirectory:&isDir]) {
        return nil;
    };
    
    NSMutableArray <NSString *> * muArray = [[NSMutableArray alloc] init];
    if (isDir) {
        NSDirectoryEnumerator * enumerator = [fm enumeratorAtPath:folderPath];
        NSString * path;
        while ((path = enumerator.nextObject)) {
            if ([enumerator.fileAttributes.fileType isEqualToString:NSFileTypeDirectory]) {
                [enumerator skipDescendants];
            }
            
            if ([PLVFdUtil checkStringUseable:string]) {
                if ([path containsString:string]) {
                    [muArray addObject:[folderPath stringByAppendingPathComponent:path]];
                }
            }else{
                [muArray addObject:[folderPath stringByAppendingPathComponent:path]];
            }
        }
    } else {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 查找文件错误，路径不是文件夹");
        return nil;
    }
    return muArray;
}

/// 字幕路径
- (NSString *)playbackFileIdSubtitlePath:(NSString *)fileId{
    if (![PLVFdUtil checkStringUseable:fileId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 未配置fileId，路径错误");
        return @"";
    }
    return [[self playbackFileIdPath:fileId] stringByAppendingPathComponent:@"subtitle"];
}

#pragma mark 云课堂业务
/// fileId路径
- (NSString *)playbackFileIdPath:(NSString *)fileId{
    if (![PLVFdUtil checkStringUseable:fileId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 未配置fileId，路径错误");
        return @"";
    }
    return [self.viewerPlaybackPath stringByAppendingPathComponent:fileId];
}

/// video路径
- (NSString *)playbackFileIdVideoPath:(NSString *)fileId{
    if (![PLVFdUtil checkStringUseable:fileId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 未配置fileId，路径错误");
        return @"";
    }
    return [[self playbackFileIdPath:fileId] stringByAppendingPathComponent:@"video"];
}

/// ppt路径
- (NSString *)playbackFileIdPPTPath:(NSString *)fileId{
    if (![PLVFdUtil checkStringUseable:fileId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 未配置fileId，路径错误");
        return @"";
    }
    return [[self playbackFileIdPath:fileId] stringByAppendingPathComponent:@"ppt"];
}

/// js路径
- (NSString *)playbackFileIdJsPath:(NSString *)fileId{
    if (![PLVFdUtil checkStringUseable:fileId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 未配置fileId，路径错误");
        return @"";
    }
    return [[self playbackFileIdPath:fileId] stringByAppendingPathComponent:@"js"];
}

#pragma mark Getter
- (NSString *)databaseHomePath{
    if (!_databaseHomePath) {
        NSString * documentsDir = [PLVFFileUtil documentPath];
        NSString * databasePath = [documentsDir stringByAppendingPathComponent:PLVDownloadDatabaseFolderName];
        [self ensureFolderExsit:databasePath];
        _databaseHomePath = databasePath;
    }
    return _databaseHomePath;
}

- (NSString *)databaseViewerFilePath{
    if (!_viewerId) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 未配置用户Id，路径错误");
        return @"";
    }
    if (!_databaseViewerFilePath) {
        NSString * databaseViewerIdPath = [self.databaseHomePath stringByAppendingPathComponent:self.viewerId];
        [self ensureFolderExsit:databaseViewerIdPath];
        NSString * databaseViewerIdFilePath = [databaseViewerIdPath stringByAppendingPathComponent:self.viewerId];
        _databaseViewerFilePath = databaseViewerIdFilePath;
    }
    return _databaseViewerFilePath;
}

/// 下载文件存放的总目录
- (NSString *)fileCacheHomePath{
    if (!_fileCacheHomePath) {
        NSString * cachesDir = [PLVFFileUtil cachesPath];
        NSString * fileCachePath = [cachesDir stringByAppendingPathComponent:PLVDownloadFileCacheFolderName];
        [self ensureFolderExsit:fileCachePath];
        _fileCacheHomePath = fileCachePath;
    }
    return _fileCacheHomePath;
}

- (NSString *)fileCacheViewerPath{
    if (!_viewerId) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 未配置用户Id，路径错误");
        return @"";
    }
    if (!_fileCacheViewerPath) {
        NSString * fileCacheViewerPath = [self.fileCacheHomePath stringByAppendingPathComponent:self.viewerId] ;
        [self ensureFolderExsit:fileCacheViewerPath];
        _fileCacheViewerPath = fileCacheViewerPath;
    }
    return _fileCacheViewerPath;
}

- (NSString *)viewerPlaybackPath{
    if (!_viewerId) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadPathManager - 未配置用户Id，路径错误");
        return @"";
    }
    if (!_viewerPlaybackPath) {
        NSString * viewerPlaybackPath = [self.fileCacheViewerPath stringByAppendingPathComponent:PLVCloudClassFileCache_ComponentName] ;
        [self ensureFolderExsit:viewerPlaybackPath];
        _viewerPlaybackPath = viewerPlaybackPath;
    }
    return _viewerPlaybackPath;
}

#pragma mark 多接收方事件
- (void)addViewerIdChangeBlock:(PLVDownloadViewerIdChangeBlock)block key:(NSString *)blockKey{
    if (!block) { return; }
    if (![PLVFdUtil checkStringUseable:blockKey]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 添加viewerIdChange block失败，参数错误blockKey:%@",blockKey);
        return;
    }
    if (self.viewerIdChangeBlockDic.allKeys.count > 20) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 添加viewerIdChange block失败，block注册数已到限制");
        return;
    }
    [self.viewerIdChangeBlockDic setObject:block forKey:blockKey];
}

- (void)removeViewerIdChangeWithKey:(NSString *)blockKey{
    if (![PLVFdUtil checkStringUseable:blockKey]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDatabaseManager - 移除viewerIdChange block失败，参数错误blockKey:%@",blockKey);
        return;
    }
    [self.viewerIdChangeBlockDic removeObjectForKey:blockKey];
}

@end
