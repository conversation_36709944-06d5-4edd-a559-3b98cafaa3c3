//
//  PLVDownloadCommonOperation.h
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "PLVDownloadTaskInfo.h"

NS_ASSUME_NONNULL_BEGIN

/// 普通下载处理器（下载单个文件）
@interface PLVDownloadCommonOperation : NSOperation

/// 下载视频信息模型（weak）
@property (nonatomic, weak) PLVDownloadTaskInfo * generalTaskInfo;

/**
 创建实例
 
 @param taskInfo 下载视频信息模型
 @return 普通下载处理器实例
 */
- (instancetype)initWithTaskInfo:(PLVDownloadTaskInfo *)taskInfo;

@end

NS_ASSUME_NONNULL_END
