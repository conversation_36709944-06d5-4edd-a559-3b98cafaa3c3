//
//  PLVDownloadPlaybackOperation.h
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "PLVDownloadPlaybackTaskInfo.h"

NS_ASSUME_NONNULL_BEGIN

@class PLVDownloadPlaybackOperation;

/// 云课堂回放下载处理器（兼容多个资源包、单个资源包）
@interface PLVDownloadPlaybackOperation : NSOperation

/// 回放下载视频信息模型（weak）
@property (nonatomic, weak) PLVDownloadPlaybackTaskInfo * generalTaskInfo;

/**
 创建实例

 @param taskInfo 回放下载视频信息模型
 @return 回放下载处理器实例
 */
- (instancetype)initWithTaskInfo:(PLVDownloadPlaybackTaskInfo *)taskInfo;

@end

NS_ASSUME_NONNULL_END
