//
//  PLVDownloadDataTasker.h
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "PLVDownloadTaskInfo.h"

NS_ASSUME_NONNULL_BEGIN

/// 下载数据任务器，对 NSURLSessionDataTask 的封装
@interface PLVDownloadDataTasker : NSObject <NSURLSessionTaskDelegate,NSURLSessionDataDelegate>

#pragma mark - [ 属性 ]

/// 具体的下载任务信息模型（weak）
@property (nonatomic, weak, readonly) PLVDownloadTaskInfo * detailTaskInfo;

/// 下载数据任务Id
@property (nonatomic, assign, readonly) NSUInteger taskIdentifier;

#pragma mark - [ 方法 ]

/**
 创建 下载数据任务器 实例

 @param detailTaskInfo 具体的下载任务信息模型（非抽象的，有对应实际的下载链接）
 */
- (instancetype)initWithDetailTaskInfo:(PLVDownloadTaskInfo *)detailTaskInfo;

/// 开始下载
- (void)dataTask_startDownload;

/// 停止下载
- (void)dataTask_cancelDownload;

/// 销毁
- (void)destroyDataTask;

@end

NS_ASSUME_NONNULL_END
