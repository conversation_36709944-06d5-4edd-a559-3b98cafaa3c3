//
//  PLVDownloadDataTasker.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVDownloadDataTasker.h"

#import <SSZipArchive/ZipArchive.h>

#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVConsoleLogger.h"

#import "PLVDownloadPathManager.h"
#import "PLVDownloadURLSession.h"
#import "PLVDownloadTaskInfo+PrivateExtension.h"
#import "PLVWErrorManager.h"
#import "PLVWLogReporterManager.h"

@interface PLVDownloadDataTasker ()

/// 具体的下载任务信息模型（weak）
@property (nonatomic, weak) PLVDownloadTaskInfo * detailTaskInfo;

/// 下载会话（weak）
@property (nonatomic, weak) NSURLSession * urlSession;
/// 下载请求超时时间
@property (nonatomic, assign) NSInteger timeoutInterval;
/// 下载数据任务
@property (nonatomic, strong) NSURLSessionTask * dataTask;
/// 回调
@property (nonatomic, strong) void (^completionHandler)(NSURLSessionResponseDisposition disposition);
/// 是否需要开始
@property (nonatomic, assign) BOOL needResume;
/// 等待回调时间戳
@property (nonatomic, strong) NSDate * waitHandlerDate;

@end

@implementation PLVDownloadDataTasker

#pragma mark - [ Life Cycle ]

- (instancetype)initWithDetailTaskInfo:(PLVDownloadTaskInfo *)detailTaskInfo{
    if (detailTaskInfo && [detailTaskInfo isKindOfClass:PLVDownloadTaskInfo.class]) {
        NSString * filePath = detailTaskInfo.filePath;
        if ([PLVFdUtil checkStringUseable:filePath]) {
            if (self = [super init]) {
                // 状态
                self.detailTaskInfo = detailTaskInfo;
                self.detailTaskInfo.state = PLVDownloadStatePreparing;
                
                self.urlSession = [PLVDownloadURLSession shareManager].session;
                
                self.timeoutInterval = 10;
                [self setupDataTask];
                [self registerDataTask];
                
                if (self.detailTaskInfo.preGetFileSize) {
                    [self.dataTask resume];
                }else{
                    self.detailTaskInfo.state = PLVDownloadStatePrepared;
                }
            }
            return self;
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - Failed 任务器创建失败，文件存放路径错误");
            return nil;
        }
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - Failed 创建失败，参数错误");
        return nil;
    }
}

#pragma mark - [ Private Method ]
- (void)setupDataTask{
    NSMutableURLRequest * request = [self createURLRequest:self.detailTaskInfo.url];
    self.dataTask = [self.urlSession dataTaskWithRequest:request];
}

- (NSMutableURLRequest *)createURLRequest:(NSString *)urlStr{
    if ([PLVFdUtil checkStringUseable:urlStr]) {
        NSMutableURLRequest * request = [[NSMutableURLRequest alloc] initWithURL:[NSURL URLWithString:urlStr]];
        request.HTTPShouldUsePipelining = YES;
        request.allHTTPHeaderFields = nil;
        request.timeoutInterval = self.timeoutInterval;
        unsigned long long totalBytesWritten = self.detailTaskInfo.totalBytesWritten;
        if (totalBytesWritten > 0) {
            NSString *range = [NSString stringWithFormat:@"bytes=%llu-", totalBytesWritten];
            [request setValue:range forHTTPHeaderField:@"Range"];
        }
        return request;
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - 下载链接非法 %@",urlStr);
        [self reportAndCallbackFailedWithErrorCode:PLVFDownloadErrorCodeGetVideoInfo_videoUrlError underlyingError:nil];
        return nil;
    }
}

- (void)unzipFile:(NSString *)filePath
    completeBlock:(void(^)(BOOL succeeded, NSError * _Nullable error))completeBlock{
    if (![PLVFdUtil checkStringUseable:filePath]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - 解压失败，解压目标路径错误");
        return;
    }
        
    __weak typeof(self) weakSelf = self;
    NSString * destinationPath = [NSString stringWithFormat:@"%@/",filePath.stringByDeletingLastPathComponent];
    [SSZipArchive unzipFileAtPath:filePath toDestination:destinationPath progressHandler:^(NSString * _Nonnull entry, unz_file_info zipInfo, long entryNumber, long total) {
        weakSelf.detailTaskInfo.state = PLVDownloadStateUnzipping;
        if (weakSelf.detailTaskInfo.downloadUnzipProgressChangeBlock) {
            weakSelf.detailTaskInfo.downloadUnzipProgressChangeBlock(weakSelf.detailTaskInfo, entryNumber, total);
        }
    } completionHandler:^(NSString * _Nonnull path, BOOL succeeded, NSError * _Nullable error) {
        if (succeeded && !error) {
            if (weakSelf.detailTaskInfo.deleteZipFileWhenUnzip) {
                [[PLVDownloadPathManager shareManager] removeFileWithPath:filePath];
                weakSelf.detailTaskInfo.filePath = [destinationPath stringByAppendingPathComponent:[weakSelf.detailTaskInfo.fileName stringByDeletingPathExtension]];
            }
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - 文件解压成功");
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - 文件解压失败 err:%@",error);
        }
        weakSelf.detailTaskInfo.state = PLVDownloadStateUnzipped;
        if (weakSelf.detailTaskInfo.downloadUnzipCompletedBlock) {
            weakSelf.detailTaskInfo.downloadUnzipCompletedBlock(weakSelf.detailTaskInfo, succeeded, error);
        }
        if (completeBlock) { completeBlock(succeeded, error); }
    }];
}

- (void)reportAndCallbackFailedWithErrorCode:(NSInteger)code underlyingError:(NSError *)error {
    NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulDownload code:code];
    if (error) {
        failError = PLVErrorWithUnderlyingError(failError, error);
    }
    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:error.description];
    
    self.detailTaskInfo.error = failError; // 需在设置为失败状态前，赋值error
    self.detailTaskInfo.state = PLVDownloadStateFailed;
}

- (void)callbackForSuccess{
    self.detailTaskInfo.error = nil;
    self.detailTaskInfo.state = PLVDownloadStateSuccess;
}

#pragma mark - [ Public Method ]
- (void)dataTask_startDownload{
    if (self.waitHandlerDate) {
        NSDate * currentDate = [NSDate date];
        if ([currentDate timeIntervalSinceDate:self.waitHandlerDate] > self.timeoutInterval) {
            [self destroyDataTask];
            [self setupDataTask];
            [self registerDataTask];
            self.detailTaskInfo.state = PLVDownloadStateDownloading;
            self.needResume = YES;
            [self.dataTask resume];
        }else{
            self.detailTaskInfo.state = PLVDownloadStateDownloading;
            self.detailTaskInfo.date = [NSDate date];
            self.completionHandler(NSURLSessionResponseAllow);
        }
    }else{
        if (self.detailTaskInfo.preGetFileSize) {
            self.detailTaskInfo.state = PLVDownloadStateDownloading;
            self.needResume = YES;
        }else{
            self.detailTaskInfo.state = PLVDownloadStateDownloading;
            self.needResume = YES;
            [self.dataTask resume];
        }
    }
}

- (void)dataTask_cancelDownload{
    if (self.dataTask.state == NSURLSessionTaskStateRunning) {
        [self.dataTask cancel];
    }
}

- (void)registerDataTask{
    [[PLVDownloadURLSession shareManager] registerDataTaskerWith:self];
}

- (void)destroyDataTask{
    [[PLVDownloadURLSession shareManager] removeDataTaskerWith:self];
}

#pragma mark Getter
- (NSUInteger)taskIdentifier{
    return self.dataTask.taskIdentifier;
}


#pragma mark - [ Delegate ]
#pragma NSURLSessionTaskDelegate
- (void)URLSession:(NSURLSession *)session
              task:(NSURLSessionTask *)task
didCompleteWithError:(NSError *)error {
    if (task != self.dataTask) { return; }
    
    if (error) {
        if (error.code == -999) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - 下载错误原因:被取消");
            self.detailTaskInfo.state = PLVDownloadStateStopped;
        }else if(error.code == -1001){
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - 下载错误原因:网络超时");
            [self reportAndCallbackFailedWithErrorCode:PLVFDownloadErrorCodeNetWork_noNetWork underlyingError:error];
        }else if(error.code == -1003){
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - 下载错误原因:链接非法");
            [self reportAndCallbackFailedWithErrorCode:PLVFDownloadErrorCodeGetVideoInfo_videoUrlError underlyingError:error];
        }else if(error.code == -997){
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - 下载错误原因:后台失去网络连接");
            [self reportAndCallbackFailedWithErrorCode:PLVFDownloadErrorCodeNetWork_noNetWork underlyingError:error];
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - didCompleteWithError下载错误 %@",error);
            [self reportAndCallbackFailedWithErrorCode:PLVFDownloadErrorCodeNetWork_noNetWork underlyingError:error];
        }
    } else {
        if ([self.detailTaskInfo.fileName.pathExtension isEqualToString:@"zip"]) {
            self.detailTaskInfo.sourceZip = YES;
            __weak typeof(self) weakSelf = self;
            [self unzipFile:self.detailTaskInfo.filePath completeBlock:^(BOOL succeeded, NSError * _Nullable error) {
                if (succeeded && !error) {
                    [weakSelf callbackForSuccess];
                }else{
                    [weakSelf reportAndCallbackFailedWithErrorCode:PLVFDownloadErrorCodeDownloadFile_unZipFailed underlyingError:error];
                }
            }];
        }else{
            [self callbackForSuccess];
        }
    }
}

#pragma NSURLSessionDataDelegate
/// 请求反馈
- (void)URLSession:(NSURLSession *)session
          dataTask:(NSURLSessionDataTask *)dataTask
didReceiveResponse:(NSURLResponse *)response
 completionHandler:(void (^)(NSURLSessionResponseDisposition disposition))completionHandler {
    if (dataTask != self.dataTask) { return; }
    
    NSUInteger code = ((NSHTTPURLResponse *)response).statusCode;
    PLVDownloadTaskInfo * taskInfo = self.detailTaskInfo;
    
    if (code < 400 && code != 304){
        NSInteger expectedLength = response.expectedContentLength > 0 ? (NSInteger)response.expectedContentLength : 0;
        taskInfo.totalBytesExpectedToWrite = expectedLength + taskInfo.totalBytesWritten;
    }else if (code == 416) {
        self.detailTaskInfo.progress = 1;
        [self callbackForSuccess];
    }else{
        [self dataTask_cancelDownload];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - 未知错误，response:%@",response);
    }
    
    if (completionHandler) {
        if (self.needResume) {
            self.detailTaskInfo.date = [NSDate date];
            completionHandler(NSURLSessionResponseAllow);
        }else{
            self.waitHandlerDate = [NSDate date];
            self.completionHandler = completionHandler;
            if (self.detailTaskInfo.state == PLVDownloadStateWaiting) {
                self.detailTaskInfo.state = PLVDownloadStatePrepared;
                self.detailTaskInfo.state = PLVDownloadStateWaiting;
            }else{
                self.detailTaskInfo.state = PLVDownloadStatePrepared;
            }
        }
    }else{
        self.detailTaskInfo.state = PLVDownloadStateFailed;
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - Failed 启动下载失败");
    }
}

/// 下载数据实时回调
- (void)URLSession:(NSURLSession *)session
          dataTask:(NSURLSessionDataTask *)dataTask
    didReceiveData:(NSData *)data {
    if (dataTask != self.dataTask) { return; }

    PLVDownloadTaskInfo * taskInfo = self.detailTaskInfo;
    taskInfo.state = PLVDownloadStateDownloading;
    
    // Speed
    taskInfo.totalRead += data.length;
    NSDate *currentDate = [NSDate date];
    if ([currentDate timeIntervalSinceDate:taskInfo.date] >= 1) {
        double time = [currentDate timeIntervalSinceDate:taskInfo.date];
        long long speedPerSec = taskInfo.totalRead / time;
        taskInfo.speedValue = (float)speedPerSec / 1024.0;
        taskInfo.speed =  [[NSByteCountFormatter stringFromByteCount:speedPerSec countStyle:NSByteCountFormatterCountStyleFile]stringByAppendingString:@"/s"];
        taskInfo.totalRead = 0.0;
        taskInfo.date = currentDate;
    }else if (!taskInfo.date){
        // 被延后的任务date会为空
        taskInfo.date = currentDate;
    }
    
    [[PLVDownloadPathManager shareManager] ensureFolderExsit:taskInfo.filePath.stringByDeletingLastPathComponent];
    
    if (taskInfo.totalBytesWritten <= taskInfo.totalBytesExpectedToWrite) {
        NSString * filePath = taskInfo.filePath;
        if (filePath && [filePath isKindOfClass:NSString.class] && filePath.length > 0) {
            NSInputStream *inputStream =  [[NSInputStream alloc] initWithData:data];
            NSOutputStream *outputStream = [[NSOutputStream alloc] initWithURL:[NSURL fileURLWithPath:taskInfo.filePath] append:YES];
            [inputStream scheduleInRunLoop:[NSRunLoop currentRunLoop] forMode:NSDefaultRunLoopMode];
            [outputStream scheduleInRunLoop:[NSRunLoop currentRunLoop] forMode:NSDefaultRunLoopMode];
            
            [inputStream open];
            [outputStream open];
            
            __block NSError *error = nil;
            while ([inputStream hasBytesAvailable] && [outputStream hasSpaceAvailable]) {
                uint8_t buffer[1024];
                
                NSInteger bytesRead = [inputStream read:buffer maxLength:1024];
                if (inputStream.streamError || bytesRead < 0) {
                    error = inputStream.streamError;
                    break;
                }
                
                NSInteger bytesWritten = [outputStream write:buffer maxLength:(NSUInteger)bytesRead];
                if (outputStream.streamError || bytesWritten < 0) {
                    error = outputStream.streamError;
                    break;
                }
                
                if (bytesRead == 0 && bytesWritten == 0) {
                    break;
                }
            }
            [outputStream close];
            [inputStream close];
            
            if (error) {
                [self reportAndCallbackFailedWithErrorCode:PLVFDownloadErrorCodeDownloadFile_writeDataFailed underlyingError:error];
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - 数据写入错误 err:%@",error);
            }
        }else{
            [self reportAndCallbackFailedWithErrorCode:PLVFDownloadErrorCodeDownloadFile_writeDataFailed underlyingError:nil];
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - 无法写入数据，路径非法 filePath:%@ taskInfo:%@",filePath,taskInfo);
        }
    }else{
        [self reportAndCallbackFailedWithErrorCode:PLVFDownloadErrorCodeDownloadFile_notEnoughMemory underlyingError:nil];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"Tasker - data out"); // 数据已写满
    }
    
    // progress
    if (taskInfo.progress > 0.98 && taskInfo.speedValue == 0) {
        taskInfo.speedValue = (float)taskInfo.totalRead / 1024.0;
        taskInfo.speed = [[NSByteCountFormatter stringFromByteCount:taskInfo.totalRead countStyle:NSByteCountFormatterCountStyleFile]stringByAppendingString:@"/s"];
        taskInfo.date = [NSDate date];
    }
    taskInfo.progress = (float)taskInfo.totalBytesWritten / (float)taskInfo.totalBytesExpectedToWrite;
    
}

/// 即将缓存
- (void)URLSession:(NSURLSession *)session
          dataTask:(NSURLSessionDataTask *)dataTask
 willCacheResponse:(NSCachedURLResponse *)proposedResponse
 completionHandler:(void (^)(NSCachedURLResponse *cachedResponse))completionHandler {
    if (dataTask != self.dataTask) { return; }

    NSCachedURLResponse *cachedResponse = proposedResponse;
    if (completionHandler) {
        completionHandler(cachedResponse);
    }
}

@end
