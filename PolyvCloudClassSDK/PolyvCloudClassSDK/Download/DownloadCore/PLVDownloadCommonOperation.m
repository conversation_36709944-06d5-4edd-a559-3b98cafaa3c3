//
//  PLVDownloadCommonOperation.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVDownloadCommonOperation.h"

#import "PLVConsoleLogger.h"

#import "PLVDownloadDataTasker.h"
#import "PLVDownloadPathManager.h"
#import "PLVDownloadTaskInfo+PrivateExtension.h"
#import "PLVDownloadPlaybackTaskInfo+PrivateExtension.h"

@interface PLVDownloadCommonOperation ()

@property (nonatomic, strong) PLVDownloadDataTasker * dataTasker; // 任务器

@property (nonatomic, assign) BOOL needStart;
@property (nonatomic, assign) BOOL needCancel;

@end

@implementation PLVDownloadCommonOperation

@synthesize executing = _executing;
@synthesize finished = _finished;

#pragma mark - [ Life Cycle ]
- (instancetype)initWithTaskInfo:(PLVDownloadTaskInfo *)taskInfo{
    if (taskInfo && [taskInfo isKindOfClass:PLVDownloadTaskInfo.class]) {
        if (self = [super init]) {
            self.generalTaskInfo = taskInfo;
            self.generalTaskInfo.deleteZipFileWhenUnzip = YES;
            NSString *fileIdPath = [[PLVDownloadPathManager shareManager].viewerPlaybackPath stringByAppendingPathComponent:self.generalTaskInfo.fileId];
            self.generalTaskInfo.filePath = [NSString stringWithFormat:@"%@/video/1/%@",fileIdPath, self.generalTaskInfo.fileName];
            
            [self setup];
            [self setupTaskInfo];
        }
        return self;
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVOperation - 任务创建失败,TaskInfo非法");
        return nil;
    }
}

- (void)dealloc{
    PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVOperation - [[[ Had Dealloc ]]]");
}

#pragma mark - [ Private Method ]
- (void)setup{
    self.dataTasker = [[PLVDownloadDataTasker alloc]initWithDetailTaskInfo:self.generalTaskInfo];
}

- (void)setupTaskInfo{
    PLVDownloadTaskInfo * taskInfo = self.generalTaskInfo;

    // 事件
    __weak typeof(self) weakSelf = self;
    
    /// 状态改变
    [taskInfo addDownloadStateChangeBlock:^(PLVDownloadTaskInfo * _Nullable taskInfo, PLVDownloadState state) {
        if (state == PLVDownloadStateStopped){
            [weakSelf downloadDone];
        }
    } key:NSStringFromClass(self.class)];
    
    /// 下载完成
    [taskInfo addDownloadCompletedBlock:^(PLVDownloadTaskInfo * _Nullable taskInfo, NSError * _Nullable error) {
        [weakSelf downloadDone];
    } key:NSStringFromClass(self.class)];
    
}

- (void)downloadCancel{
    if (self.isCancelled) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVOperation - 处理器被非法取消");
        return;
    }else{
        [super cancel];
        [self.dataTasker dataTask_cancelDownload];
    }
}

- (void)downloadDone{
    if (self.needStart) {
        [self.dataTasker destroyDataTask];
        
        [self.generalTaskInfo removeDownloadStateChangeBlockWithKey:NSStringFromClass(self.class)];
        [self.generalTaskInfo removeDownloadCompletedBlockWithKey:NSStringFromClass(self.class)];
        
        if (self.isExecuting) self.executing = NO;
        if (!self.isFinished) self.finished = YES;
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVOperation - 处理器被非法停止运行");
    }
}

#pragma mark Setter
- (void)setFinished:(BOOL)finished {
    [self willChangeValueForKey:@"isFinished"];
    _finished = finished;
    [self didChangeValueForKey:@"isFinished"];
}

- (void)setExecuting:(BOOL)executing {
    [self willChangeValueForKey:@"isExecuting"];
    _executing = executing;
    [self didChangeValueForKey:@"isExecuting"];
}

#pragma mark Getter
- (BOOL)isAsynchronous{
    return YES;
}

- (BOOL)isExecuting{
    return _executing;
}

- (BOOL)isFinished{
    return _finished;
}

#pragma mark Super Method
- (void)start{
    self.needStart = YES;
    
    if (self.needCancel) {
        [self downloadDone];
        return;
    }else{
        if (!self.isExecuting) self.executing = YES;
        [self.dataTasker dataTask_startDownload];
    }
}

- (void)cancel{
    self.needCancel = YES;
    
    if (self.isExecuting == NO) { // 任务未曾启动
        // 提前销毁对象
        [self.dataTasker destroyDataTask];
        self.dataTasker = nil;
        
        self.generalTaskInfo.relatedOperation = nil;
        [self.generalTaskInfo removeDownloadStateChangeBlockWithKey:NSStringFromClass(self.class)];
        [self.generalTaskInfo removeDownloadCompletedBlockWithKey:NSStringFromClass(self.class)];
        self.generalTaskInfo.state = PLVDownloadStateStopped;
        self.generalTaskInfo = nil;
    }else{                      // 任务处理中
        [self downloadCancel];
    }
}

@end
