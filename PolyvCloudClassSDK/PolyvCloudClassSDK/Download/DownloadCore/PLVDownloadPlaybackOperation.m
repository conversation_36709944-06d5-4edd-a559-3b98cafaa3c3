//
//  PLVDownloadPlaybackOperation.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVDownloadPlaybackOperation.h"

#import "PLVConsoleLogger.h"

#import "PLVDownloadManager.h"
#import "PLVDownloadPathManager.h"
#import "PLVDownloadDataTasker.h"
#import "PLVDownloadTaskInfo+PrivateExtension.h"
#import "PLVDownloadPlaybackTaskInfo+PrivateExtension.h"

@interface PLVDownloadPlaybackOperation ()

/// 具体下载任务信息
@property (nonatomic, strong) PLVDownloadTaskInfo * sonTaskInfo_JsZip; // Js代码压缩包
@property (nonatomic, strong) PLVDownloadTaskInfo * sonTaskInfo_pptZip; // ppt压缩包
@property (nonatomic, strong) PLVDownloadTaskInfo * sonTaskInfo_videoFile; // 视频文件
@property (nonatomic, strong) NSMutableDictionary *subtitleTaskInfoDic; // 字幕下载任务字典

/// 下载数据任务器
@property (nonatomic, strong) PLVDownloadDataTasker * dataTasker_JsZip; // JS代码压缩包 任务器
@property (nonatomic, strong) PLVDownloadDataTasker * dataTasker_pptZip; // ppt压缩包 任务器
@property (nonatomic, strong) PLVDownloadDataTasker * dataTasker_video; // 视频文件 任务器
@property (nonatomic, strong) NSMutableDictionary *subtitleDataTaskerDic; // 字幕下载任务器字典

@property (nonatomic, strong) dispatch_semaphore_t sem_1;
@property (nonatomic, strong) dispatch_semaphore_t sem_2;

@property (nonatomic, assign) BOOL needCancel;

@end

@implementation PLVDownloadPlaybackOperation

@synthesize executing = _executing;
@synthesize finished = _finished;

#pragma mark - [ Life Cycle ]
- (instancetype)initWithTaskInfo:(PLVDownloadPlaybackTaskInfo *)taskInfo{
    if (self = [super init]) {
        self.generalTaskInfo = taskInfo;
        self.generalTaskInfo.deleteZipFileWhenUnzip = NO;
        
        self.sem_1 = dispatch_semaphore_create(1);
        self.sem_2 = dispatch_semaphore_create(1);

        [self createPlaybackDetailTaskInfo];
        [self createPlaybackDataTasker];
    }
    return self;
}

- (void)dealloc{
    PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVOperation - [[[ Had Dealloc ]]]");
}

#pragma mark - [ Private Method ]
- (void)createPlaybackDetailTaskInfo{
    NSString *fileIdPath = [[PLVDownloadPathManager shareManager].viewerPlaybackPath stringByAppendingPathComponent:self.generalTaskInfo.fileId];

    self.sonTaskInfo_videoFile = [self createDefaultTaskInfo];
    self.sonTaskInfo_videoFile.url = self.generalTaskInfo.videoUrl;
    self.sonTaskInfo_videoFile.ratio = 0.5;
    self.sonTaskInfo_videoFile.totalBytesExpectedToWrite = self.generalTaskInfo.videoSize;
    
    NSString * videoSavePath = [NSString stringWithFormat:@"%@/video/%@/%@",fileIdPath,self.generalTaskInfo.definition,self.sonTaskInfo_videoFile.fileName];
    self.sonTaskInfo_videoFile.filePath = videoSavePath;
    
    if ([self.generalTaskInfo.liveType isEqualToString:@"ppt"]) {
        // 三分屏回放
        
        if ([PLVFdUtil checkStringUseable:self.generalTaskInfo.JsZipUrl]) {
            self.sonTaskInfo_JsZip = [self createDefaultTaskInfo];
            self.sonTaskInfo_JsZip.url = self.generalTaskInfo.JsZipUrl;
            self.sonTaskInfo_JsZip.ratio = 0.2;
            NSString * jsSavePath = [NSString stringWithFormat:@"%@/js/%@",fileIdPath,self.sonTaskInfo_JsZip.fileName];
            self.sonTaskInfo_JsZip.filePath = jsSavePath;
            self.sonTaskInfo_JsZip.totalBytesExpectedToWrite = self.generalTaskInfo.pptZipSize * 0.8;
        }
        
        if ([PLVFdUtil checkStringUseable:self.generalTaskInfo.pptZipUrl]) {
            self.sonTaskInfo_pptZip = [self createDefaultTaskInfo];
            self.sonTaskInfo_pptZip.url = self.generalTaskInfo.pptZipUrl;
            self.sonTaskInfo_pptZip.ratio = 0.3;
            NSString * pptSavePath = [NSString stringWithFormat:@"%@/ppt/%@",fileIdPath,self.sonTaskInfo_pptZip.fileName];
            self.sonTaskInfo_pptZip.filePath = pptSavePath;
            self.sonTaskInfo_pptZip.totalBytesExpectedToWrite = self.generalTaskInfo.pptZipSize * 0.2;
        }
    }
    
    if ([PLVFdUtil checkArrayUseable:self.generalTaskInfo.subtitleList]) {;
        self.subtitleTaskInfoDic = [NSMutableDictionary dictionary];
        self.subtitleDataTaskerDic = [NSMutableDictionary dictionary];
        for (NSDictionary *dict in self.generalTaskInfo.subtitleList) {
            NSString *srtUrl = PLV_SafeStringForDictKey(dict, @"srtUrl");
            NSString *status = PLV_SafeStringForDictKey(dict, @"status");
            if ([PLVFdUtil checkStringUseable:srtUrl] && [PLVFdUtil checkStringUseable:status] && [status isEqualToString:@"publish"]) {
                PLVDownloadTaskInfo *subtitleTaskInfo = [self createDefaultTaskInfo];
                subtitleTaskInfo.url = srtUrl;
                subtitleTaskInfo.ratio = 0.02; // 每个字幕文件占2%的进度
                NSString *subtitleSavePath = [NSString stringWithFormat:@"%@/subtitle/%@", fileIdPath, subtitleTaskInfo.fileName];
                subtitleTaskInfo.filePath = subtitleSavePath;
                [self.subtitleTaskInfoDic setValue:subtitleTaskInfo forKey:subtitleTaskInfo.fileName];
            }
        }
    }
}

- (void)createPlaybackDataTasker{
    _dataTasker_video = [[PLVDownloadDataTasker alloc]initWithDetailTaskInfo:self.sonTaskInfo_videoFile];

    if ([self.generalTaskInfo.liveType isEqualToString:@"ppt"]) {
        // 三分屏回放
        if (self.sonTaskInfo_JsZip) {
            _dataTasker_JsZip = [[PLVDownloadDataTasker alloc]initWithDetailTaskInfo:self.sonTaskInfo_JsZip];
        }
        
        if (self.sonTaskInfo_pptZip) {
            _dataTasker_pptZip = [[PLVDownloadDataTasker alloc]initWithDetailTaskInfo:self.sonTaskInfo_pptZip];
        }
    }

    // 创建字幕下载任务器
    if ([PLVFdUtil checkDictionaryUseable:self.subtitleTaskInfoDic]) {
        for (NSString *languageFileName in self.subtitleTaskInfoDic.allKeys) {
            PLVDownloadTaskInfo *subtitleTaskInfo = self.subtitleTaskInfoDic[languageFileName];
            PLVDownloadDataTasker *subtitleDataTasker = [[PLVDownloadDataTasker alloc] initWithDetailTaskInfo:subtitleTaskInfo];
            self.subtitleDataTaskerDic[languageFileName] = subtitleDataTasker;
        }
    }
}

- (PLVDownloadTaskInfo *)createDefaultTaskInfo{
    PLVDownloadTaskInfo * taskInfo = [[PLVDownloadTaskInfo alloc]init];
    
    // 事件
    __weak typeof(self) weakSelf = self;
    /// 进度改变
    taskInfo.downloadProgressChangeBlock = ^(PLVDownloadTaskInfo * _Nullable taskInfo, unsigned long long receivedSize, unsigned long long expectedSize, float progress, float speedValue) {
        // 状态
        weakSelf.generalTaskInfo.state = PLVDownloadStateDownloading;
        
        unsigned long long pptWritten = weakSelf.sonTaskInfo_pptZip ? weakSelf.sonTaskInfo_pptZip.totalBytesWritten : 0;
        unsigned long long jsWritten = weakSelf.sonTaskInfo_JsZip ? weakSelf.sonTaskInfo_JsZip.totalBytesWritten : 0;
        unsigned long long subtitleWritten = 0;
        
        // 计算字幕文件下载进度
        for (PLVDownloadTaskInfo *subtitleTask in weakSelf.subtitleTaskInfoDic.allValues) {
            subtitleWritten += subtitleTask.totalBytesWritten;
        }
        
        unsigned long long totalBytesWritten = weakSelf.sonTaskInfo_videoFile.totalBytesWritten + pptWritten + jsWritten + subtitleWritten;
        weakSelf.generalTaskInfo.totalBytesWritten = totalBytesWritten;
        
        unsigned long long pptExpectedToWrite = weakSelf.sonTaskInfo_pptZip ? weakSelf.sonTaskInfo_pptZip.totalBytesExpectedToWrite : 0;
        unsigned long long jsExpectedToWrite = weakSelf.sonTaskInfo_JsZip ? weakSelf.sonTaskInfo_JsZip.totalBytesExpectedToWrite : 0;
        unsigned long long totalBytesExpectedToWrite = weakSelf.sonTaskInfo_videoFile.totalBytesExpectedToWrite + pptExpectedToWrite + jsExpectedToWrite;
        weakSelf.generalTaskInfo.totalBytesExpectedToWrite = totalBytesExpectedToWrite;
        
        float pptSpeed = weakSelf.sonTaskInfo_pptZip ? weakSelf.sonTaskInfo_pptZip.speedValue : 0;
        float jsSpeed = weakSelf.sonTaskInfo_JsZip ? weakSelf.sonTaskInfo_JsZip.speedValue : 0;
        float totalSpeedValue = weakSelf.sonTaskInfo_videoFile.speedValue + pptSpeed + jsSpeed;
        
        NSString * totalSpeedValueString = [[NSByteCountFormatter stringFromByteCount:totalSpeedValue * 1024 countStyle:NSByteCountFormatterCountStyleFile]stringByAppendingString:@"/s"];
        weakSelf.generalTaskInfo.speed = totalSpeedValueString;
        
//        float videoRatio = (float)weakSelf.sonTaskInfo_videoFile.totalBytesExpectedToWrite / (float)totalBytesExpectedToWrite;
//        float pptRatio = (float)pptExpectedToWrite / (float)totalBytesExpectedToWrite;
//        float jsRatio = (float)jsExpectedToWrite / (float)totalBytesExpectedToWrite;
//
//        float videoPercentInTotal = weakSelf.sonTaskInfo_videoFile.progress * videoRatio * 1.0;
//        float pptPercentInTotal = weakSelf.sonTaskInfo_pptZip ? weakSelf.sonTaskInfo_pptZip.progress * pptRatio * 1.0 : 0;
//        float jsPercentInTotal = weakSelf.sonTaskInfo_JsZip ? weakSelf.sonTaskInfo_JsZip.progress * jsRatio * 1.0 : 0;
//
//        float totalProgress = videoPercentInTotal + pptPercentInTotal + jsPercentInTotal;
//        weakSelf.generalTaskInfo.progress = totalProgress;
        
        float totalProgress = (float)totalBytesWritten / (float)totalBytesExpectedToWrite * 1.0;
        weakSelf.generalTaskInfo.progress = totalProgress;
    };
    
    /// 状态改变
    taskInfo.downloadStateChangeBlock = ^(PLVDownloadTaskInfo * _Nullable taskInfo, PLVDownloadState state) {
        if (state == PLVDownloadStateSuccess) {
            if (taskInfo == weakSelf.sonTaskInfo_JsZip) {
                PLV_LOG_INFO(PLVConsoleLogModuleTypeDownload, @"PLVOperation - js下载完成");
                if (!taskInfo.sourceZip) {
                    PLV_LOG_WARN(PLVConsoleLogModuleTypeDownload, @"PLVOperation - js非压缩包资源形式");
                }
                NSArray * jsFileArr = [[PLVDownloadPathManager shareManager] findFileAtFolderPath:taskInfo.filePath stringInFileName:@".js"];
                NSString * jsFilePath = jsFileArr.firstObject;
                weakSelf.generalTaskInfo.JsFileName = jsFilePath.lastPathComponent;
                
                NSArray * htmlFileArr = [[PLVDownloadPathManager shareManager] findFileAtFolderPath:taskInfo.filePath stringInFileName:@".html"];
                NSString * htmlFile = htmlFileArr.firstObject;
                weakSelf.generalTaskInfo.htmlFileName = htmlFile.lastPathComponent;
            }else if(taskInfo == weakSelf.sonTaskInfo_pptZip){
                PLV_LOG_INFO(PLVConsoleLogModuleTypeDownload, @"PLVOperation - ppt下载完成");
                if (!taskInfo.sourceZip) {
                    PLV_LOG_WARN(PLVConsoleLogModuleTypeDownload, @"PLVOperation - ppt非压缩包资源形式");
                }
                NSString * pptFileName = [taskInfo.fileName stringByDeletingPathExtension];
                weakSelf.generalTaskInfo.pptFileName = pptFileName;
            }else if (taskInfo == weakSelf.sonTaskInfo_videoFile){
                PLV_LOG_INFO(PLVConsoleLogModuleTypeDownload, @"PLVOperation - 视频下载完成");
                weakSelf.generalTaskInfo.videoFileNameDic = @{@"1":taskInfo.fileName};
            }

            // 检查是否是字幕文件下载完成
            NSString *completedLanguageFileName = nil;
            for (NSString *languageFileName in weakSelf.subtitleTaskInfoDic.allKeys) {
                if (weakSelf.subtitleTaskInfoDic[languageFileName] == taskInfo) {
                    completedLanguageFileName = languageFileName;
                    break;
                }
            }
            
            if (completedLanguageFileName) {
                PLV_LOG_INFO(PLVConsoleLogModuleTypeDownload, @"PLVOperation - 字幕下载完成: %@", completedLanguageFileName);
                // 更新字幕文件名
                NSMutableArray *subtitleFileNameArray = [NSMutableArray arrayWithArray:weakSelf.generalTaskInfo.subtitleFileNameArray];
                [subtitleFileNameArray addObject:taskInfo.fileName];
                weakSelf.generalTaskInfo.subtitleFileNameArray = subtitleFileNameArray;
            }
        }else if (state == PLVDownloadStateStopped){
            // 需判断三个状态
            weakSelf.generalTaskInfo.state = PLVDownloadStateStopped;
            
            [weakSelf cancelInternal];
            [weakSelf downloadDone];
        }else if (state == PLVDownloadStateFailed){
            if (taskInfo == weakSelf.sonTaskInfo_JsZip) {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVOperation - js下载失败  %@",weakSelf.sonTaskInfo_JsZip.url);
            }else if (taskInfo == weakSelf.sonTaskInfo_pptZip){
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVOperation - ppt下载失败 %@",weakSelf.sonTaskInfo_pptZip.url);
            }else if (taskInfo == weakSelf.sonTaskInfo_videoFile){
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVOperation - video下载失败  %@",weakSelf.sonTaskInfo_videoFile.url);
            } else {
                NSString *completedLanguageFileName = nil;
                for (NSString *languageFileName in weakSelf.subtitleTaskInfoDic.allKeys) {
                    if (weakSelf.subtitleTaskInfoDic[languageFileName] == taskInfo) {
                        completedLanguageFileName = languageFileName;
                        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVOperation - subtitle下载失败  %@",taskInfo.url);
                        break;
                    }
                }
            }
        }
    };
    
    /// 下载完成
    taskInfo.downloadCompletedBlock = ^(PLVDownloadTaskInfo * _Nullable taskInfo, NSError * _Nullable error) {
        // 继续下载
        if (error) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVOperation - 任务下载失败 URL:%@",taskInfo.url);
            weakSelf.generalTaskInfo.error = error;
            weakSelf.generalTaskInfo.state = PLVDownloadStateFailed; // auto callback
            
            [weakSelf cancelInternal];
            [weakSelf downloadDone];
        }else{
            [weakSelf startDataTaskOnThisTurn];
            
            // 检查
            if ([weakSelf checkAllTaskSuccess]) {
                
                // 删除所有zip文件
                [weakSelf deleteAllTaskZipFile];
                
                weakSelf.generalTaskInfo.state = PLVDownloadStateSuccess; // auto callback
                [weakSelf downloadDone];
            }
        }
    };
    
    return taskInfo;
}

- (void)deleteAllTaskZipFile {
    [[PLVDownloadPathManager shareManager] removeFileWithPath:self.sonTaskInfo_JsZip.filePath];
    self.sonTaskInfo_JsZip.filePath = [self.sonTaskInfo_JsZip.filePath stringByDeletingPathExtension];
    
    [[PLVDownloadPathManager shareManager] removeFileWithPath:self.sonTaskInfo_pptZip.filePath];
    self.sonTaskInfo_pptZip.filePath = [self.sonTaskInfo_pptZip.filePath stringByDeletingPathExtension];
}

/// 启动本轮数据任务
- (void)startDataTaskOnThisTurn{
    PLVDownloadDataTasker * dataTasker;
    
    // 检查是否有字幕需要下载
    for (NSString *language in self.subtitleTaskInfoDic.allKeys) {
        PLVDownloadTaskInfo *subtitleTask = self.subtitleTaskInfoDic[language];
        if ([self needDownload:subtitleTask]) {
            dataTasker = self.subtitleDataTaskerDic[language];
            break;
        }
    }
    
    if (!dataTasker) {
        if ([self needDownload:self.sonTaskInfo_JsZip]) {
            dataTasker = self.dataTasker_JsZip;
        } else if ([self needDownload:self.sonTaskInfo_pptZip]) {
            dataTasker = self.dataTasker_pptZip;
        } else if ([self needDownload:self.sonTaskInfo_videoFile]) {
            dataTasker = self.dataTasker_video;
        }
    }

    if (dataTasker) {
        [dataTasker dataTask_startDownload];
    }
}

/// 判断某个具体下载任务当前是否应该开始下载
- (BOOL)needDownload:(PLVDownloadTaskInfo *)detailTaskInfo{
    if (!detailTaskInfo) {
        return NO;
    }else if (detailTaskInfo.state == PLVDownloadStateSuccess ||
        detailTaskInfo.state == PLVDownloadStateDownloading) {
        return NO;
    }else{
        return YES;
    }
}

- (BOOL)checkAllTaskSuccess{
    // 检查视频、PPT、JS是否下载完成
    BOOL jsSuccess = self.sonTaskInfo_JsZip ? NO : YES;
    if (!jsSuccess) { jsSuccess = self.sonTaskInfo_JsZip.state == PLVDownloadStateSuccess; }
    
    BOOL pptSuccess = self.sonTaskInfo_pptZip ? NO : YES;
    if (!pptSuccess) { pptSuccess = self.sonTaskInfo_pptZip.state == PLVDownloadStateSuccess; }
    
    BOOL videoSuccess = NO;
    videoSuccess = self.sonTaskInfo_videoFile.state == PLVDownloadStateSuccess;
    
    // 检查所有字幕是否下载完成
    BOOL subtitleSuccess = YES;
    for (PLVDownloadTaskInfo *subtitleTask in self.subtitleTaskInfoDic.allValues) {
        if (subtitleTask.state != PLVDownloadStateSuccess) {
            subtitleSuccess = NO;
            break;
        }
    }
    
    return jsSuccess && pptSuccess && videoSuccess && subtitleSuccess;
}

- (void)cancelInternal{
    dispatch_semaphore_wait(self.sem_2, DISPATCH_TIME_FOREVER);
    if (self.isCancelled) {
        dispatch_semaphore_signal(self.sem_2);
        return;
    }
    
    [super cancel];
    if (self.dataTasker_JsZip) {
        [self.dataTasker_JsZip dataTask_cancelDownload];
    }
    if (self.dataTasker_pptZip) {
        [self.dataTasker_pptZip dataTask_cancelDownload];
    }
    [self.dataTasker_video dataTask_cancelDownload];
    dispatch_semaphore_signal(self.sem_2);
}

- (void)downloadDone{
    if (self.dataTasker_JsZip) {
        [self.dataTasker_JsZip destroyDataTask];
    }
    if (self.dataTasker_pptZip) {
        [self.dataTasker_pptZip destroyDataTask];
    }
    [self.dataTasker_video destroyDataTask];

    if (self.isExecuting) self.executing = NO;
    if (!self.isFinished) self.finished = YES;
}

#pragma mark Setter
- (void)setFinished:(BOOL)finished {
    [self willChangeValueForKey:@"isFinished"];
    _finished = finished;
    [self didChangeValueForKey:@"isFinished"];
}

- (void)setExecuting:(BOOL)executing {
    [self willChangeValueForKey:@"isExecuting"];
    _executing = executing;
    [self didChangeValueForKey:@"isExecuting"];
}

#pragma mark Getter
- (BOOL)isAsynchronous{
    return YES;
}

- (BOOL)isExecuting{
    return _executing;
}

- (BOOL)isFinished{
    return _finished;
}

#pragma mark Super Method
- (void)start{
    if (self.needCancel) {
        [self downloadDone];
        return;
    }
    
    dispatch_semaphore_wait(self.sem_1, DISPATCH_TIME_FOREVER);
    
    if (!self.isExecuting) self.executing = YES;
    
    [self startDataTaskOnThisTurn];
    
    dispatch_semaphore_signal(self.sem_1);
}

- (void)cancel{
    dispatch_semaphore_wait(self.sem_1, DISPATCH_TIME_FOREVER);

    self.needCancel = YES;
    
    [self cancelInternal];
    
    if (self.generalTaskInfo.state == PLVDownloadStateWaiting) {
        // 未启动的任务
        self.generalTaskInfo.state = PLVDownloadStateStopped;
        
        /// 销毁
        self.dataTasker_JsZip = nil;
        self.dataTasker_video = nil;
        self.dataTasker_pptZip = nil;
        
        self.sonTaskInfo_JsZip = nil;
        self.sonTaskInfo_pptZip = nil;
        self.sonTaskInfo_videoFile = nil;
        
        self.generalTaskInfo.relatedOperation = nil;
    }
    
    dispatch_semaphore_signal(self.sem_1);
}

@end
