//
//  PLVDownloadURLSession.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVDownloadURLSession.h"
#import "PLVConsoleLogger.h"

static NSString * const PLVDownloadURLSessionBgID = @"com.polyv.download.bgSessionId";

@interface PLVDownloadURLSession ()<NSURLSessionTaskDelegate, NSURLSessionDataDelegate>

@property (nonatomic, strong) NSURLSession *session;

/// 全局下载数据任务器记录字典
@property (nonatomic, strong) NSMutableDictionary <NSString *, PLVDownloadDataTasker *> * generalDataTaskerDic;

@end

@implementation PLVDownloadURLSession

#pragma mark - [ Life Cycle ]

static PLVDownloadURLSession * plvSession = nil;

+ (instancetype)allocWithZone:(struct _NSZone *)zone
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        if (plvSession == nil) plvSession = [super allocWithZone:zone];
    });
    return plvSession;
}

- (instancetype)init{
    if (self = [super init]) {
        [self baseSetup];
    }
    return self;
}


#pragma mark - [ Private Method ]
- (void)baseSetup{
    self.generalDataTaskerDic = [[NSMutableDictionary alloc]init];
    
    NSURLSessionConfiguration * sessionConfiguration = [NSURLSessionConfiguration backgroundSessionConfigurationWithIdentifier:PLVDownloadURLSessionBgID];
    sessionConfiguration.timeoutIntervalForRequest = 15.0;
    sessionConfiguration.HTTPMaximumConnectionsPerHost = 10.0;
    sessionConfiguration.allowsCellularAccess = YES;
    sessionConfiguration.sessionSendsLaunchEvents = YES;
    
    self.session = [NSURLSession sessionWithConfiguration:sessionConfiguration
                                                 delegate:self
                                            delegateQueue:nil];
}

- (PLVDownloadDataTasker *)findDataTasker:(NSURLSessionTask *)task{
    NSString * taskIdStr = [NSString stringWithFormat:@"%lu",(unsigned long)task.taskIdentifier];
    PLVDownloadDataTasker * dataTasker = self.generalDataTaskerDic[taskIdStr];
    return dataTasker;
}


#pragma mark - [ Public Method ]
+ (instancetype)shareManager{
    if (plvSession) { return plvSession; }
    else { return [[self alloc] init]; }
}

- (void)registerDataTaskerWith:(PLVDownloadDataTasker *)dataTasker{
    NSString * taskIdStr = [NSString stringWithFormat:@"%lu",(unsigned long)dataTasker.taskIdentifier];
    if ([self.generalDataTaskerDic.allKeys containsObject:taskIdStr]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadURLSession - 注册失败，已包含此数据任务器");
        return;
    }
    [self.generalDataTaskerDic setObject:dataTasker forKey:taskIdStr];
}

- (void)removeDataTaskerWith:(PLVDownloadDataTasker *)dataTasker{
    NSString * taskIdStr = [NSString stringWithFormat:@"%lu",(unsigned long)dataTasker.taskIdentifier];
    if (![self.generalDataTaskerDic.allKeys containsObject:taskIdStr]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadURLSession - 移除失败，已不包含此数据任务器");
        return;
    }
    [self.generalDataTaskerDic removeObjectForKey:taskIdStr];
}


#pragma mark - [ Delegate ]
#pragma NSURLSessionTaskDelegate
- (void)URLSession:(NSURLSession *)session
              task:(NSURLSessionTask *)task
didCompleteWithError:(NSError *)error {
    PLVDownloadDataTasker * dataTasker = [self findDataTasker:task];
    if (dataTasker) {
        [dataTasker URLSession:session task:task didCompleteWithError:error];
    }
}

#pragma NSURLSessionDataDelegate
- (void)URLSession:(NSURLSession *)session
          dataTask:(NSURLSessionDataTask *)dataTask
didReceiveResponse:(NSURLResponse *)response
 completionHandler:(void (^)(NSURLSessionResponseDisposition disposition))completionHandler {
    PLVDownloadDataTasker * dataTasker = [self findDataTasker:dataTask];
    if (dataTasker) {
        [dataTasker URLSession:session dataTask:dataTask didReceiveResponse:response completionHandler:completionHandler];
    }
}

- (void)URLSession:(NSURLSession *)session
          dataTask:(NSURLSessionDataTask *)dataTask
    didReceiveData:(NSData *)data {
    PLVDownloadDataTasker * dataTasker = [self findDataTasker:dataTask];
    if (dataTasker) {
        [dataTasker URLSession:session dataTask:dataTask didReceiveData:data];
    }
}

- (void)URLSession:(NSURLSession *)session
          dataTask:(NSURLSessionDataTask *)dataTask
 willCacheResponse:(NSCachedURLResponse *)proposedResponse
 completionHandler:(void (^)(NSCachedURLResponse *cachedResponse))completionHandler {
    PLVDownloadDataTasker * dataTasker = [self findDataTasker:dataTask];
    if (dataTasker) {
        [dataTasker URLSession:session dataTask:dataTask willCacheResponse:proposedResponse completionHandler:completionHandler];
    }
}

@end
