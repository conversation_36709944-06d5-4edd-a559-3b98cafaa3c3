//
//  PLVDownloadURLSession.h
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "PLVDownloadDataTasker.h"

NS_ASSUME_NONNULL_BEGIN

/// 下载会话器（单例类）
@interface PLVDownloadURLSession : NSObject

#pragma mark - [ 属性 ]

/// 下载会话
@property (nonatomic, strong, readonly) NSURLSession *session;

#pragma mark - [ 方法 ]

/// 获取单例
+ (instancetype)shareManager;

/// 注册一个下载数据任务器，让其接收数据回调（注册后将被持有）
- (void)registerDataTaskerWith:(PLVDownloadDataTasker *)dataTasker;

/// 移除一个下载数据任务器的注册，让其不接收数据回调，不再被持有
- (void)removeDataTaskerWith:(PLVDownloadDataTasker *)dataTasker;

@end

NS_ASSUME_NONNULL_END
