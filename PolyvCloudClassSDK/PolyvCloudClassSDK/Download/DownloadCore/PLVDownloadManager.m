//
//  PLVDownloadManager.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/23.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVDownloadManager.h"

#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVConsoleLogger.h"

#import "PLVDownloadURLSession.h"
#import "PLVDownloadPathManager.h"
#import "PLVDownloadPlaybackOperation.h"
#import "PLVDownloadCommonOperation.h"

#import "PLVDownloadTaskInfo+PrivateExtension.h"
#import "PLVDownloadDatabaseManager+Playback.h"

@interface PLVDownloadManager ()

/// 文件存放路径
@property (nonatomic, copy) NSString * fileCacheViewerPath;

/// 处理队列
@property (nonatomic, strong, nonnull) NSOperationQueue *downloadQueue;

@end

@implementation PLVDownloadManager

#pragma mark - [ Life Cycle ]

static PLVDownloadManager * manager = nil;

+ (instancetype)allocWithZone:(struct _NSZone *)zone
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        if (manager == nil) manager = [super allocWithZone:zone];
    });
    return manager;
}

- (instancetype)init{
    if ((self = [super init])) {
        [self setupData];
        [self setupDownload];
    }
    return self;
}

#pragma mark - [ Private Method ]
- (void)setupData{
    self.fileCacheViewerPath = [PLVDownloadPathManager shareManager].fileCacheViewerPath;
    
    __weak typeof(self) weakSelf = self;
    [[PLVDownloadPathManager shareManager] addViewerIdChangeBlock:^(PLVDownloadPathManager * _Nonnull manager, NSString * _Nonnull viewerId) {
        weakSelf.fileCacheViewerPath = nil;
        [weakSelf.downloadQueue cancelAllOperations];
        weakSelf.fileCacheViewerPath = [PLVDownloadManager shareManager].fileCacheViewerPath;
    } key:@"com.polyv.PLVDownloadManager"];
}

- (void)setupDownload{
    _downloadQueue = [[NSOperationQueue alloc]init];
    _downloadQueue.maxConcurrentOperationCount = 3;
    _downloadQueue.name = @"com.polyv.PLVDownloadManager";
}


#pragma mark - [ Public Method ]
+ (instancetype)shareManager{
    if (manager) { return manager; }
    else { return [[self alloc] init]; }
}

#pragma mark Setter
- (void)setMaxConcurrentDownloads:(NSInteger)maxConcurrentDownloads{
    if (maxConcurrentDownloads == 0) {
        PLV_LOG_WARN(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 设置最大下载并发数失败，不可为0");
        return;
    }
    _downloadQueue.maxConcurrentOperationCount = maxConcurrentDownloads;
}

#pragma mark Getter
- (NSInteger)maxConcurrentDownloads{
    return _downloadQueue.maxConcurrentOperationCount;
}

- (NSUInteger)currentQueueCount{
    return _downloadQueue.operationCount;
}

- (NSUInteger)currentDownloadCount{
    NSUInteger realCount = 0;
    NSArray * operationArr = _downloadQueue.operations;
    for (NSOperation * obj in operationArr) {
        if (obj.executing) { realCount ++; }
    }
    return realCount;
}

#pragma mark 任务控制
/// 添加一个下载任务（不会自动开始下载）
- (void)addDownloadTaskWith:(PLVDownloadTaskInfo *)taskInfo{
    if (!taskInfo || ![taskInfo isKindOfClass:PLVDownloadTaskInfo.class]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 添加下载任务失败，参数错误 taskInfo:%@",taskInfo);
        return;
    }
    
    if (!taskInfo.taskInfoId || ![taskInfo.taskInfoId isKindOfClass:NSString.class] || taskInfo.taskInfoId.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 添加下载任务失败，任务taskInfoId错误");
        return;
    }
    
    BOOL urlAvailable = [PLVFdUtil checkStringUseable:taskInfo.url];
    
    if ([taskInfo isKindOfClass:PLVDownloadPlaybackTaskInfo.class]) {
        PLVDownloadPlaybackTaskInfo * playbackTaskInfo = (PLVDownloadPlaybackTaskInfo *)taskInfo;
        if (!playbackTaskInfo.multiResource && !urlAvailable) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 添加下载任务失败，回放任务url错误");
            return;
        }else{
            // 把账号信息保存到数据库中，为了播放离线缓存的时候，可以无网使用viewerId
            playbackTaskInfo.viewerId = self.viewerId;
            playbackTaskInfo.viewerName = self.viewerName;
            playbackTaskInfo.viewerAvatar = self.viewerAvatar;
            [[PLVDownloadDatabaseManager shareManager] addPlaybackTaskInfo:playbackTaskInfo];
        }
    }else{
        if (!urlAvailable) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 添加下载任务失败，任务url错误");
            return;
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 添加下载任务失败，未支持PLVDownloadTaskInfo任务");
        }
    }
}

/// 删除一个下载任务（将删除对应下载文件）
- (void)deleteDownloadTaskWith:(PLVDownloadTaskInfo *)taskInfo{
    if (!taskInfo) { PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, PLVFDLocalizableString(@"PLVDownloadManager - 删除下载任务失败，参数错误")); }
    
    if (![PLVFdUtil checkStringUseable:taskInfo.taskInfoId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 开始下载失败，参数taskInfoId错误");
        return;
    }
    
    // 数据库删除一条记录
    if (taskInfo.relatedOperation) {
        if (taskInfo.state == PLVDownloadStateWaiting ||
            taskInfo.state == PLVDownloadStateDownloading) {
            [self stopDownloadWith:taskInfo];
        }else{
            //PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 删除任务失败，内部错误，状态错误：%@",NSStringFromPLVDownloadState(taskInfo.state));
            taskInfo.relatedOperation = nil;
        }
    }
    if ([taskInfo isKindOfClass:PLVDownloadPlaybackTaskInfo.class]) {
        [[PLVDownloadDatabaseManager shareManager] deletePlaybackTaskInfo:(PLVDownloadPlaybackTaskInfo *)taskInfo];
    }
}

#pragma mark 下载控制
/// 指定某个任务，开始下载
- (void)startDownloadWith:(PLVDownloadTaskInfo *)taskInfo{
    if (!taskInfo) { PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, PLVFDLocalizableString(@"PLVDownloadManager - 开始下载失败，参数错误")); }
    
    if (![PLVFdUtil checkStringUseable:taskInfo.taskInfoId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 开始下载失败，参数taskInfoId错误");
        return;
    }
    
    if (taskInfo.state == PLVDownloadStateDefault ||
        taskInfo.state == PLVDownloadStateStopped ||
        taskInfo.state == PLVDownloadStateFailed) {
        
        if ([taskInfo isKindOfClass:PLVDownloadPlaybackTaskInfo.class]) {
            if (!taskInfo.relatedOperation) {
                PLVDownloadPlaybackTaskInfo * playbackTaskInfo = (PLVDownloadPlaybackTaskInfo *)taskInfo;
                
                NSOperation * operation;
                if (playbackTaskInfo.multiResource) {
                    operation = [[PLVDownloadPlaybackOperation alloc]initWithTaskInfo:playbackTaskInfo];
                }else{
                    operation = [[PLVDownloadCommonOperation alloc]initWithTaskInfo:playbackTaskInfo];
                }
                playbackTaskInfo.relatedOperation = operation;
                playbackTaskInfo.state = PLVDownloadStateWaiting;

                [_downloadQueue addOperation:operation];
            }else{
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 该视频开始下载失败，内部错误，本身已存在下载器");
                return;
            }
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 对象类型非法:%@",taskInfo);
        }
        
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 该视频开始下载失败，状态错误:%@",NSStringFromPLVDownloadState(taskInfo.state));
        return;
    }
}

/// 指定一个下载任务，暂停下载
- (void)stopDownloadWith:(PLVDownloadTaskInfo *)taskInfo{
    if (!taskInfo) { PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, PLVFDLocalizableString(@"PLVDownloadManager - 暂停下载失败，参数错误")); }
    
    if (!taskInfo.taskInfoId || ![taskInfo.taskInfoId isKindOfClass:NSString.class] || taskInfo.taskInfoId.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 开始下载失败，参数taskInfoId错误");
        return;
    }
    
    if (taskInfo.state == PLVDownloadStateWaiting ||
        taskInfo.state == PLVDownloadStateDownloading)  {
        
        if (taskInfo.relatedOperation) {
            [taskInfo.relatedOperation cancel];
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 该视频暂停下载失败，未找到对应下载处理器");
            return;
        }
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"PLVDownloadManager - 该视频暂停下载失败，状态错误:%@",NSStringFromPLVDownloadState(taskInfo.state));
        return;
    }
}

@end
