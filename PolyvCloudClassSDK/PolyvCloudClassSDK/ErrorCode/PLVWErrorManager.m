//
//  PLVWErrorManager.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/2/11.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVWErrorManager.h"
#import <PLVFoundationSDK/PLVFPlayErrorCodeGenerator.h>
#import <PLVFoundationSDK/PLVFDownloadErrorCodeGenerator.h>
#import <PLVFoundationSDK/PLVFUploadErrorCodeGenerator.h>
#import <PLVFoundationSDK/PLVFRecordErrorCodeGenerator.h>
#import <PLVFoundationSDK/PLVFRtmpErrorCodeGenerator.h>
#import <PLVFoundationSDK/PLVFChatErrorCodeGenerator.h>
#import <PLVFoundationSDK/PLVFLinkErrorCodeGenerator.h>
#import <PLVFoundationSDK/PLVFPPTErrorCodeGenerator.h>
#import <PLVFoundationSDK/PLVFInitErrorCodeGenerator.h>
#import <PLVFoundationSDK/PLVFSocketErrorCodeGenerator.h>
#import <PLVFoundationSDK/PLVFHttpErrorCodeGenerator.h>
#import <PLVFoundationSDK/PLVFInteractionErrorCodeGenerator.h>
#import <PLVFoundationSDK/PLVFBeautyErrorCodeGenerator.h>

static NSString *PLVSErrorDomain = @"net.polyv.PLVCloudClassSDK";

@implementation PLVWErrorManager

#pragma mark - Life Cycle

- (instancetype)init {
    self = [super init];
    if (self) {
    }
    return self;
}

#pragma mark - Public

+ (instancetype)sharedManager {
    static PLVWErrorManager *mananger = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        mananger = [[PLVWErrorManager alloc] init];
    });
    return mananger;
}

- (NSError *)errorWithModul:(PLVFErrorCodeModul)modul code:(NSInteger)code {
    NSInteger errorCode = [self errorCodeWithModul:modul code:code];
    if (errorCode == -1) {
        return nil;
    }
    
    NSMutableDictionary *muUserInfo = [[NSMutableDictionary alloc] init];
    
    NSString *errorDescription = [self errorDescriptionWithModul:modul code:code];
    if (errorDescription) {
        muUserInfo[NSLocalizedDescriptionKey] = errorDescription;
    }
    
    NSString *helpAnchor = [self customErrorHelpWithModul:modul code:code];
    if (helpAnchor) {
        muUserInfo[NSHelpAnchorErrorKey] = helpAnchor;
    }
    
    NSError *error = [NSError errorWithDomain:PLVSErrorDomain code:errorCode userInfo:[muUserInfo copy]];
    return error;
}

#pragma mark - Private

- (Class)errorDefinClass:(PLVFErrorCodeModul)modul {
    if (modul == PLVFErrorCodeModulPlay) {
        return [PLVFPlayErrorCodeGenerator class];
    } else if (modul == PLVFErrorCodeModulDownload) {
        return [PLVFDownloadErrorCodeGenerator class];
    } else if (modul == PLVFErrorCodeModulUpload) {
        return [PLVFUploadErrorCodeGenerator class];
    } else if (modul == PLVFErrorCodeModulRecord) {
        return [PLVFRecordErrorCodeGenerator class];
    } else if (modul == PLVFErrorCodeModulRtmp) {
        return [PLVFRtmpErrorCodeGenerator class];
    } else if (modul == PLVFErrorCodeModulChat) {
        return [PLVFChatErrorCodeGenerator class];
    } else if (modul == PLVFErrorCodeModulLink) {
        return [PLVFLinkErrorCodeGenerator class];
    } else if (modul == PLVFErrorCodeModulPPT) {
        return [PLVFPPTErrorCodeGenerator class];
    } else if (modul == PLVFErrorCodeModulInit) {
        return [PLVFInitErrorCodeGenerator class];
    } else if (modul == PLVFErrorCodeModulSocket) {
        return [PLVFSocketErrorCodeGenerator class];
    } else if (modul == PLVFErrorCodeModulHttp) {
        return [PLVFHttpErrorCodeGenerator class];
    } else if (modul == PLVFErrorCodeModulInteraction) {
        return [PLVFInteractionErrorCodeGenerator class];
    } else if (modul == PLVFErrorCodeModulBeauty) {
        return [PLVFBeautyErrorCodeGenerator class];
    } else {
        return nil;
    }
}

- (NSInteger)errorCodeWithModul:(PLVFErrorCodeModul)modul code:(NSInteger)code {
    if (code < 0 || code >= pow(10, 4)) {
        return -1;
    }
    
    Class class = [self errorDefinClass:modul];
    if (class == nil) {
        return -1;
    }
    
    SEL selector = NSSelectorFromString(@"errorCode:");
    if ([class respondsToSelector:selector]) {
        NSMethodSignature *methodSignature = [class methodSignatureForSelector:selector];
        NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:methodSignature];
        [invocation setTarget:class];
        [invocation setSelector:selector];
        [invocation setArgument:&code atIndex:2];
        [invocation invoke];
        
        if (methodSignature.methodReturnLength) {
            NSInteger value;
            [invocation getReturnValue:&value];
            return value;
        }
    }
    return -1;
}

- (NSString *)errorDescriptionWithModul:(PLVFErrorCodeModul)modul code:(NSInteger)code {
    NSString *customDescription = [self customErrorDescriptionWithModul:modul code:code];
    if (customDescription) {
        return customDescription;
    }
    
    Class class = [self errorDefinClass:modul];
    SEL selector = NSSelectorFromString(@"errorDescription:");
    if ([class respondsToSelector:selector]) {
        NSMethodSignature *methodSignature = [class methodSignatureForSelector:selector];
        NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:methodSignature];
        [invocation setTarget:class];
        [invocation setSelector:selector];
        [invocation setArgument:&code atIndex:2];
        [invocation invoke];
        
        if (methodSignature.methodReturnLength) {
            void *tempValue;
            [invocation getReturnValue:&tempValue];
            NSString *resultVal = (__bridge NSString *)tempValue;
            return resultVal;
        }
    }
    return nil;
}

- (NSString *)customErrorDescriptionWithModul:(PLVFErrorCodeModul)modul code:(NSInteger)code {
    NSInteger errorCode = [self errorCodeWithModul:modul code:code];
    if (errorCode == -1) {
        return nil;
    }
    
    NSString *newDescription = nil;
    SEL selector = NSSelectorFromString(@"customLocalizedDescriptionWithModul:code:");
    // delegate 允许在 app 进行错误码描述的自定义，自定义方法跟本方法相同
    // 优先级: app对错误码描述的自定义 > SDK 对错误码描述的自定义 > Foundation 对错误码描述的定义
    if (self.delegate && [self.delegate respondsToSelector:selector]) {
        NSMethodSignature *methodSignature = [self.delegate methodSignatureForSelector:selector];
        NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:methodSignature];
        [invocation setTarget:self.delegate];
        [invocation setSelector:selector];
        [invocation setArgument:&modul atIndex:2];
        [invocation setArgument:&code atIndex:3];
        [invocation invoke];
        
        if (methodSignature.methodReturnLength) {
            void *tempValue;
            [invocation getReturnValue:&tempValue];
            newDescription = (__bridge NSString *)tempValue;
        }
    }
    if (newDescription) {
        return newDescription;
    }
    /*
     // 如果需要更改 Foundation 定下的错误码描述，请按如下方法修改
    if (modul == PLVFErrorCodeModulPPT) {
        if (code == PLVFPPTErrorCodeWebInitFail) {
            newDescription = PLVFDLocalizableString(@"需要更改的新的错误描述");
        }
    }
     */
    return newDescription;
}

- (NSString *)customErrorHelpWithModul:(PLVFErrorCodeModul)modul code:(NSInteger)code {
    NSString *errorHelp = nil;
    SEL selector = NSSelectorFromString(@"customHelpAnchorErrorWithModul:code:");
    // delegate 允许在 app 进行错误码帮助指引的自定义，自定义方法跟本方法相同
    // 优先级: app对错误码帮助指引的定义 > SDK 对错误码帮助指引的定义
    if (self.delegate && [self.delegate respondsToSelector:selector]) {
        NSMethodSignature *methodSignature = [self.delegate methodSignatureForSelector:selector];
        NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:methodSignature];
        [invocation setTarget:self.delegate];
        [invocation setSelector:selector];
        [invocation setArgument:&modul atIndex:2];
        [invocation setArgument:&code atIndex:3];
        [invocation invoke];
        
        if (methodSignature.methodReturnLength) {
            void *tempValue;
            [invocation getReturnValue:&tempValue];
            errorHelp = (__bridge NSString *)tempValue;
        }
    }
    if (errorHelp) {
        return errorHelp;
    }
    /*
     // 如果需要制定错误码的帮助指引，请按如下方法添加
    if (modul == PLVFErrorCodeModulPPT) {
        if (code == PLVFPPTErrorCodeWebInitFail) {
            errorHelp = PLVFDLocalizableString(@"错误帮助指引文字");
        }
    }
     */
    return errorHelp;
}

@end
