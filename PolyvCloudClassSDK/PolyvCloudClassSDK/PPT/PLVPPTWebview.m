//
//  PLVPPTWebview.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2020/9/16.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVPPTWebview.h"

#import "PLVLiveVideoConfig.h"
#import <PLVLiveScenesSDK/PLVLiveScenesSDK.h>

@interface PLVPPTWebview () <PLVJSBridgeDelegate>

@property (nonatomic, strong) PLVJSBridge *jsBridge;
@property (nonatomic, assign, readonly) BOOL webviewLoadFinish;
@property (nonatomic, assign) BOOL userInfoHadSeted;

@end

@implementation PLVPPTWebview

#pragma mark - [ Life Period ]
- (void)dealloc{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypePPT, @"%s",__FUNCTION__);
}

- (instancetype)initWithFrame:(CGRect)frame{
    if (self = [super initWithFrame:frame]) {
        [self setup];
    }
    return self;
}

#pragma mark - [ Public Methods ]
/// 页面加载
- (void)loadOnlinePPT{
    NSString * pptUrl = [NSString stringWithFormat:@"%@?ts=%@&useWKWebviewForIOS=true",PLVLiveConstantsDocumentPPTForMobileHTML, [PLVFdUtil curTimeStamp]];
    NSString * chatApiDomain = PLVLiveVideoConfig.sharedInstance.chatApiDomain;
    if ([PLVFdUtil checkStringUseable:chatApiDomain]) { pptUrl = [pptUrl stringByAppendingFormat:@"&domainName=%@",chatApiDomain]; }
    
    [self.jsBridge loadWebView:pptUrl inView:self];
    [self layoutWebviewFrame];
}

- (void)loadLocalPPTWithHTMLString:(NSString *)htmlString baseURL:(NSURL *)baseURL{
    [self.jsBridge loadHTMLString:htmlString baseURL:baseURL inView:self];
    [self layoutWebviewFrame];
}

- (void)loadLocalPPTWithFileURL:(NSURL *)URL allowingReadAccessToURL:(NSURL *)readAccessURL{
    [self.jsBridge loadFileURL:URL allowingReadAccessToURL:readAccessURL inView:self];
    [self layoutWebviewFrame];
}

/// 直播场景相关
- (void)refreshPPT:(NSString *)json {
    if ([PLVFdUtil checkStringUseable:json]) {
        [self.jsBridge call:@"refreshPPT" params:@[json]];
    }else{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePPT, @"PLVPPTView - refreshPPT failed, json illegal %@",json);
    }
}

- (void)refreshPPT:(NSString *)json delay:(NSUInteger)delay {
    if ([PLVFdUtil checkStringUseable:json]) {
        [self.jsBridge call:@"refreshPPT" params:@[json, @(delay)]];
    }else{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePPT, @"PLVPPTView - refreshPPT failed, json illegal %@",json);
    }
}

- (void)setSEIData:(NSString *)json {
    if ([PLVFdUtil checkStringUseable:json]) {
        [self.jsBridge call:@"setSeiDataForIOS" params:@[json]]; // 带‘ForIOS’后缀的方法是JS适配WKWebview的方法
    }else{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePPT, @"PLVPPTView - setSeiData failed, json illegal %@",json);
    }
}

/// 双向画笔相关
- (void)setPaintPermission:(BOOL)paint controlPPTPermission:(BOOL)control{
    self.jsBridge.webView.userInteractionEnabled = paint;
    [self.jsBridge call:@"setPaintPermissionForIOS" params:@[[NSString stringWithFormat:@"{\"canEditPaint\":\"%d\", \"canControlPPT\":\"%d\"}", paint ? 1 : 0, control ? 1 : 0]]];
}

- (void)setPaintStatus:(NSString *)status {
    [self.jsBridge call:@"setPaintStatus" params:@[[NSString stringWithFormat:@"{\"status\":\"%@\"}", status]]];
}

- (void)setBrushColor:(NSString *)color {
    if ([PLVFdUtil checkStringUseable:color]) {
        [self.jsBridge call:@"changeColor" params:@[color]];
    }else{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePPT, @"PLVPPTView - changeColor failed, color illegal %@",color);
    }
}

- (void)toDelete {
    [self.jsBridge call:@"toDelete" params:@[]];
}

/// 回放场景相关
- (void)pptStart:(NSString *)vid {
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    NSString *timestamp = [PLVFdUtil curTimeStamp];
    NSString *plain = [NSString stringWithFormat:@"%@appId%@timestamp%@vid%@%@", liveConfig.appSecret, liveConfig.appId, timestamp, vid, liveConfig.appSecret];
    NSString *sign = [[PLVDataUtil md5HexDigest:plain] uppercaseString];
    [self.jsBridge call:@"videoStart" params:@[[NSString stringWithFormat:@"{\"appId\":\"%@\",\"timestamp\":\"%@\",\"sign\":\"%@\",\"vid\":\"%@\"}", liveConfig.appId, timestamp, sign, vid]]];
}

- (void)pptStartWithVideoId:(NSString *)videoId channelId:(NSString *)channelId {
    if (![PLVFdUtil checkStringUseable:videoId] ||
        ![PLVFdUtil checkStringUseable:channelId]) {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePPT, @"PLVPPTView - videoStart failed, 【parma illegal】(videoId:%@, channelId:%@）", videoId, channelId);
        return;
    }
    
    NSString *type = @"playback";
    [self.jsBridge call:@"videoStart" params:@[[NSString stringWithFormat:@"{\"type\":\"%@\",\"roomId\":\"%@\",\"id\":\"%@\"}", type, channelId, videoId]]]; // roomId字段放入频道Id内容（前端叫法与移动端叫法不一样）

}

- (void)pptStartWithFileId:(NSString *)fileId channelId:(NSString *)channelId {
    if (![PLVFdUtil checkStringUseable:fileId] ||
        ![PLVFdUtil checkStringUseable:channelId]) {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePPT, @"PLVPPTView - videoStart failed, 【parma illegal】(videoId:%@, channelId:%@）", fileId, channelId);
        return;
    }
    
    NSString *type = @"record";
    [self.jsBridge call:@"videoStart" params:@[[NSString stringWithFormat:@"{\"type\":\"%@\",\"roomId\":\"%@\",\"id\":\"%@\"}", type, channelId, fileId]]]; // roomId字段放入频道Id内容（前端叫法与移动端叫法不一样）

}

- (void)pptPlay:(long)currentTime {
    [self.jsBridge call:@"pptPlay" params:@[@(currentTime)]];
}

- (void)pptPause:(long)currentTime {
    [self.jsBridge call:@"pptPause" params:@[@(currentTime)]];
}

- (void)pptSeek:(long)toTime {
    [self.jsBridge call:@"pptSeek" params:@[@(toTime)]];
}

#pragma mark Setter
- (void)setUserInfo:(NSDictionary *)userInfo {
    _userInfo = userInfo;
    if ([PLVFdUtil checkDictionaryUseable:userInfo]) {
        if (self.webviewLoadFinish) { [self setUserInfoToPPT]; }
    }else{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePPT, @"PLVPPTView - setUserInfo break off, userInfo illegal %@",userInfo);
    }
}

- (void)setCustomActivityIndicator:(BOOL)customActivityIndicator{
    _customActivityIndicator = customActivityIndicator;
    self.jsBridge.customActivityIndicator = customActivityIndicator;
}

- (void)setJsDebugMode:(BOOL)jsDebugMode{
    _jsDebugMode = jsDebugMode;
    self.jsBridge.debugMode = jsDebugMode;
}

#pragma mark - [ Private Methods ]
- (void)setup{
    self.jsBridge = [[PLVJSBridge alloc] init];
    self.jsBridge.delegate = self;
    [self.jsBridge addJsFunctionsReceiver:self];
    [self.jsBridge addObserveJsFunctions:@[@"pptPrepare", @"sendSocketEventForIOS", @"videoDuration", @"changePPTPosition"]];
}

- (void)layoutWebviewFrame{
    if (self.jsBridge.webviewLoadFinish) {
        self.jsBridge.webView.frame = self.bounds;
        self.jsBridge.webView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        self.jsBridge.webView.userInteractionEnabled = NO;
    }else if(!self.jsBridge.webviewLoadFaid){
        __weak typeof(self) weakSelf = self;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [weakSelf layoutWebviewFrame];
        });
    }
}

- (void)setUserInfoToPPT{
    if ([PLVFdUtil checkDictionaryUseable:_userInfo]) {
        if (!self.userInfoHadSeted) {
            NSError *error = nil;
            NSData *jsonData = [NSJSONSerialization dataWithJSONObject:_userInfo options:NSJSONWritingPrettyPrinted error:&error];
            if (error == nil && jsonData.length > 0) {
                NSString *userJSON = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
                [self.jsBridge call:@"setUserForIOS" params:@[userJSON]];
                self.userInfoHadSeted = YES;
            } else {
                PLV_LOG_DEBUG(PLVConsoleLogModuleTypePPT, @"PLVPPTView - setUserInfo failed, json error:%@, jsonData:%@",error,jsonData);
            }
        }
    }else{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePPT, @"PLVPPTView - setUserInfo failed, userInfo illegal %@",_userInfo);
    }
}

#pragma mark Getter
- (BOOL)webviewLoadFinish{
    return self.jsBridge.webviewLoadFinish;
}


#pragma mark - [ Delegate ]
#pragma mark JS Callback
/// 通用
- (void)pptPrepare:(id)sender {
    [self.jsBridge call:@"pptPrepareCallback" params:nil];
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvPPTWebviewHadPrepared:)]) {
            [weakSelf.delegate plvPPTWebviewHadPrepared:weakSelf];
        }
    });
}

/// 直播场景
- (void)sendSocketEventForIOS:(NSString *)jsonData {
    if ([PLVFdUtil checkStringUseable:jsonData]) {
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvPPTWebview:sendPaintInfo:)]) {
            [self.delegate plvPPTWebview:self sendPaintInfo:jsonData];
        }
    }else{
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypePPT, @"PLVPPTView - sendPaintInfo failed, json data illegal %@",jsonData);
    }
}

/// 回放场景
- (void)videoDuration:(id)sender{
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvPPTWebviewGetPlayerCurrentTime:)]) {
            NSTimeInterval curTime = [weakSelf.delegate plvPPTWebviewGetPlayerCurrentTime:weakSelf];
            [weakSelf.jsBridge call:@"videoDurationCallback" params:@[[NSString stringWithFormat:@"{\"time\":\"%f\"}", curTime]]];
        }
    });
}

- (void)changePPTPosition:(id)data {
    BOOL status = NO;
    if ([data isKindOfClass:[NSNumber class]]) {
        status = [data boolValue];
    }
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvPPTWebview:changePPTPosition:)]) {
        [self.delegate plvPPTWebview:self changePPTPosition:!status];
    }
}

#pragma mark PLVJSBridgeDelegate
- (void)plvJSBridgeWebviewDidFinishLoad:(PLVJSBridge *)jsBridge{
    [self setUserInfoToPPT];
    if (self.jsDelegate && [self.jsDelegate respondsToSelector:@selector(plvJSBridgeWebviewDidFinishLoad:)]) {
        [self.jsDelegate plvJSBridgeWebviewDidFinishLoad:jsBridge];
    }
}

- (void)plvJSBridgeWebviewDidFailLoad:(PLVJSBridge *)jsBridge withError:(NSError *)error{
    if (self.jsDelegate && [self.jsDelegate respondsToSelector:@selector(plvJSBridgeWebviewDidFailLoad:withError:)]) {
        [self.jsDelegate plvJSBridgeWebviewDidFailLoad:jsBridge withError:error];
    }
}

- (void)plvJSBridge:(PLVJSBridge *)jsBridge webviewLodingShow:(BOOL)loadingShow{
    if (self.jsDelegate && [self.jsDelegate respondsToSelector:@selector(plvJSBridge:webviewLodingShow:)]) {
        [self.jsDelegate plvJSBridge:jsBridge webviewLodingShow:loadingShow];
    }
}

- (void)plvJSBridge:(PLVJSBridge *)jsBridge showConfirmPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(BOOL))completionHandler{
    if (self.jsDelegate && [self.jsDelegate respondsToSelector:@selector(plvJSBridge:showConfirmPanelWithMessage:initiatedByFrame:completionHandler:)]) {
        [self.jsDelegate plvJSBridge:jsBridge showConfirmPanelWithMessage:message initiatedByFrame:frame completionHandler:completionHandler];
    }
}

@end

