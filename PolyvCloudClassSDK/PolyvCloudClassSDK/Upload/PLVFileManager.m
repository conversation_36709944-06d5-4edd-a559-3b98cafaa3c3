//
//  PLVFileManager.m
//  PLVCloudClassStreamerSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/2.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVFileManager.h"

@implementation PLVFileManager

#pragma mark - Public

+ (NSString *)copyFile:(NSURL *)sourceFileURL channelId:(NSString *)channelId {
    NSString *fileName = sourceFileURL.lastPathComponent;
    NSString *desDirPath = [self destinateDirPath:channelId];
    if (desDirPath == nil) {
        return nil;
    }
    NSString *desFilePath = [desDirPath stringByAppendingFormat:@"/%@", fileName];
    if ([self isFileExist:desFilePath]) {
        if ([self isFileSame:desFilePath sourcePath:sourceFileURL.path]) {
            return desFilePath;
        } else {
            desFilePath = [self destinateFilePath:sourceFileURL desDirPath:desDirPath count:1];
        }
    }
    BOOL success = [[NSFileManager defaultManager] copyItemAtPath:sourceFileURL.path toPath:desFilePath error:nil];
    return success ? desFilePath : nil;
}

+ (void)deleteFile:(NSString *)fileName channelId:(NSString *)channelId {
    NSString *desDirPath = [self destinateDirPath:channelId];
    if (desDirPath) {
        NSString *desFilePath = [desDirPath stringByAppendingFormat:@"/%@", fileName];
        if ([self isFileExist:desFilePath]) {
            [[NSFileManager defaultManager] removeItemAtPath:desFilePath error:nil];
        }
    }
}

+ (NSString *)filePathWithFileName:(NSString *)fileName channelId:(NSString *)channelId {
    NSString *desDirPath = [self destinateDirPath:channelId];
    NSString *desFilePath = nil;
    if (desDirPath) {
         desFilePath = [desDirPath stringByAppendingFormat:@"/%@", fileName];
    }
    return desFilePath;
}

#pragma mark - Private

/// 返回文档路径 Library/Caches/doc_{channelId}/'，该路径文件夹不存在则创建
+ (NSString *)destinateDirPath:(NSString *)channelId {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *cachesPath = paths[0];
    NSString *dirPath = [NSString stringWithFormat:@"%@doc_%@", cachesPath, channelId];
    BOOL isDirectory = NO;
    BOOL exist = [[NSFileManager defaultManager] fileExistsAtPath:dirPath isDirectory:&isDirectory];
    if (exist && isDirectory) {
        return dirPath;;
    }
    BOOL success = [[NSFileManager defaultManager] createDirectoryAtPath:dirPath withIntermediateDirectories:YES attributes:nil error:nil];
    return success ? dirPath : nil;
}

/// 目标路径下是否已存在同名文档
+ (BOOL)isFileExist:(NSString *)filePath {
    BOOL isDirectory = NO;
    BOOL exist = [[NSFileManager defaultManager] fileExistsAtPath:filePath isDirectory:&isDirectory];
    return (exist && isDirectory == NO);
}

/// 比对文档是否相同
+ (BOOL)isFileSame:(NSString *)desPath sourcePath:(NSString *)sourcePath {
    NSData *desData = [NSData dataWithContentsOfFile:desPath];
    NSData *sourceData = [NSData dataWithContentsOfFile:sourcePath];
    return [desData isEqualToData:sourceData];
}

/// desDirPath 路径下若存在与 sourceFileURL 同名文件，文件名加上（count），递归调用直到没有重名文件为止
+ (NSString *)destinateFilePath:(NSURL *)sourceFileURL desDirPath:(NSString *)desDirPath count:(NSInteger)count {
    NSString *fileName = sourceFileURL.lastPathComponent;
    NSString *fileExtension = sourceFileURL.pathExtension;
    NSString *fileNameFirstPath = [fileName substringToIndex:(fileName.length - fileExtension.length - 1)];
    NSString *desFilePath = [desDirPath stringByAppendingFormat:@"/%@(%zd).%@", fileNameFirstPath, count, fileExtension];
    if ([self isFileExist:desFilePath]) {
        if ([self isFileSame:desFilePath sourcePath:sourceFileURL.path]) {
            return desFilePath;
        } else {
            return [self destinateFilePath:sourceFileURL desDirPath:desDirPath count:++count];
        }
    } else {
        return desFilePath;
    }
}

@end
