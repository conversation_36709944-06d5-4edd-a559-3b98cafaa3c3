//
//  PLVDocumentTokenModel.h
//  PLVCloudClassStreamerSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/6.
//  Copyright © 2020 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 文档上传 token 模型
@interface PLVDocumentTokenModel : NSObject

/// 频道号
@property (nonatomic, copy) NSString *channelId;

/// 服务器生成的文件自增 ID
@property (nonatomic, copy, readonly) NSString *autoId;

/// 文件 ID
@property (nonatomic, copy, readonly) NSString *fileId;

/// PPT 状态：
/// normal - 正常，不需要重复上传文件，已经有上传成功的相同PPT，可以直接进入轮询
/// waitConvert - 转换PPT中，已经有相同的文件正在转换中，直接进入轮询
/// waitUpload - 等待上传状态，进入上传阿里云步骤
@property (nonatomic, copy, readonly) NSString *convertStatus;

/// oss 上传OSSAccessId（上传所需 tAccessKey）
@property (nonatomic, copy, readonly) NSString *accessId;

/// oss 上传OSSAccessKey（上传所需 tSecretKey）
@property (nonatomic, copy, readonly) NSString *accessKey;

/// 上传所需 tToken
@property (nonatomic, copy, readonly) NSString *token;

/// 加密串过期时间，如 "2019-07-17T04:36:26Z"
@property (nonatomic, copy, readonly) NSString *expiration;

/// oss 上传的 endpoint
@property (nonatomic, copy, readonly) NSString *endpoint;

/// oss 上传的 host
@property (nonatomic, copy, readonly) NSString *host;

/// oss 上传的桶
@property (nonatomic, copy, readonly) NSString *bucket;

/// 上传到 oss 的文件夹
@property (nonatomic, copy, readonly) NSString *dir;

/// 文档的存放目录 key
@property (nonatomic, copy, readonly) NSString *object;

/// oss 上传的回调参数（必须带上这个参数，用于回调后端告知上传状态）
@property (nonatomic, copy) NSDictionary * __nullable callback;

@end

NS_ASSUME_NONNULL_END
