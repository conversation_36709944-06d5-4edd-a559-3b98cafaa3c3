//
//  PLVDocumentUploadModel.m
//  PLVCloudClassStreamerSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/3.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVDocumentUploadModel.h"

@implementation PLVDocumentUploadModel

- (void)setProgress:(float)progress {
    if(progress <= _progress) {
        return;
    }
    _progress = progress;
    if (_uploadProgress) {
        self.uploadProgress(progress);
    }
}

- (void)setupUploadStatusWithStatusStirng:(NSString *)statusString {
    if (!statusString ||
        ![statusString isKindOfClass:[NSString class]] ||
        statusString.length == 0) {
        return;
    }
    if ([statusString isEqualToString:@"waitUpload"]) { // 等待上传
        self.status = PLVDocumentUploadStatusUploading;
    } else if ([statusString isEqualToString:@"failUpload"]) { // 上传失败
        self.status = PLVDocumentUploadStatusFailure;
    } else if ([statusString isEqualToString:@"waitConvert"]) { // 等待转码
        self.status = PLVDocumentUploadStatusSuccess;
    } else if ([statusString isEqualToString:@"failConvert"]) { // 转码失败
        self.status = PLVDocumentUploadStatusConvertFailure;
    }
}

@end
