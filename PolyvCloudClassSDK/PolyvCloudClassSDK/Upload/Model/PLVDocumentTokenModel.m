//
//  PLVDocumentTokenModel.m
//  PLVCloudClassStreamerSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/6.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVDocumentTokenModel.h"

@interface PLVDocumentTokenModel ()

@property (nonatomic, copy) NSString *autoId;

@property (nonatomic, copy) NSString *fileId;

@property (nonatomic, copy) NSString *convertStatus;

@property (nonatomic, copy) NSString *accessId;

@property (nonatomic, copy) NSString *accessKey;

@property (nonatomic, copy) NSString *token;

@property (nonatomic, copy) NSString *expiration;

@property (nonatomic, copy) NSString *endpoint;

@property (nonatomic, copy) NSString *host;

@property (nonatomic, copy) NSString *bucket;

@property (nonatomic, copy) NSString *dir;

@property (nonatomic, copy) NSString *object;

@end

@implementation PLVDocumentTokenModel

@end
