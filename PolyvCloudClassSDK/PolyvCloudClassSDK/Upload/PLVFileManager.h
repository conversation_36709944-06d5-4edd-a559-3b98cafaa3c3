//
//  PLVFileManager.h
//  PLVCloudClassStreamerSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/2.
//  Copyright © 2020 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 在沙盒中操作上传文件的文件管理器
@interface PLVFileManager : NSObject

/// 拷贝文档到目标文件夹
/// @param sourceFileURL 文档源路径
/// @param channelId  上传文档时登录频道号
+ (NSString *)copyFile:(NSURL *)sourceFileURL channelId:(NSString *)channelId;

/// 清理指定文档
/// @param fileName 待清理文档名称
/// @param channelId  清理文档时登录频道号
+ (void)deleteFile:(NSString *)fileName channelId:(NSString *)channelId;


/// 获取文档在沙盒的路径
/// @param fileName 文档名称
/// @param channelId 上传文档时登录频道号
+ (NSString * _Nullable)filePathWithFileName:(NSString *)fileName channelId:(NSString *)channelId;

@end

NS_ASSUME_NONNULL_END
