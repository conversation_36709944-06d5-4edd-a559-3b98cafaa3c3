//
//  PLVDocumentUploader.m
//  PLVCloudClassStreamerSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/6.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVDocumentUploader.h"
#import "PLVDocumentUploadModel.h"
#import "PLVDocumentTokenModel.h"
#import "PLVLivePrivateAPI.h"
#import "PLVWErrorManager.h"
#import "PLVWLogReporterManager.h"
#import "PLVWELogEventDefine.h"
#import <PLVModel/PLVModel.h>
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import <AliyunOSSiOS/OSSService.h>
#import "PLVLiveVClassAPI.h"

@interface PLVSDocumentAliModel : NSObject

@property (nonatomic, strong) OSSClient *ossClient;

@property (nonatomic, strong) OSSResumableUploadRequest *uploadRequest;

@end

@implementation PLVSDocumentAliModel

- (instancetype)initWithClient:(OSSClient *)client request:(OSSResumableUploadRequest *)request {
    self = [super init];
    if (self) {
        _ossClient = client;
        _uploadRequest = request;
    }
    return self;
}

@end

@interface PLVDocumentUploader ()

@property (nonatomic, strong) NSMutableDictionary <NSString *, PLVSDocumentAliModel *> *aliDictionary;

@end

@implementation PLVDocumentUploader

#pragma mark - Life Cycle

- (instancetype)init {
    self = [super init];
    if (self) {
        _aliDictionary = [[NSMutableDictionary alloc] init];
    }
    return self;
}

#pragma mark - Public

+ (instancetype)sharedUploader {
    static PLVDocumentUploader *uploader = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        uploader = [[PLVDocumentUploader alloc] init];
    });
    return uploader;
}

- (void)uploadModel:(PLVDocumentUploadModel *)model channelId:(NSString *)channelId {
    __weak typeof(self) weakSelf = self;
    [self getTokenWithModel:model channelId:channelId success:^(PLVDocumentTokenModel *token) {
        if (weakSelf.delegate) {
            [weakSelf.delegate uploader:weakSelf startSuccess:YES model:model];
        }
        [weakSelf uploadWithModel:model token:token];
    } failure:^(NSError *error) {
        if (weakSelf.delegate) {
            [weakSelf.delegate uploader:weakSelf startSuccess:NO model:model];
        }
    }];
}

- (void)uploadModel:(PLVDocumentUploadModel *)model channelId:(NSString *)channelId retry:(BOOL)retry {
    __weak typeof(self) weakSelf = self;
    [self getTokenWithModel:model channelId:channelId success:^(PLVDocumentTokenModel *token) {
        if (weakSelf.delegate) {
            if (retry) {
                [weakSelf.delegate uploader:weakSelf retrySuccess:YES model:model];
            } else {
                [weakSelf.delegate uploader:weakSelf restartSuccess:YES model:model];
            }
        }
        [weakSelf uploadWithModel:model token:token];
    } failure:^(NSError *error) {
        if (weakSelf.delegate) {
            if (retry) {
                [weakSelf.delegate uploader:weakSelf retrySuccess:NO model:model];
            } else {
                [weakSelf.delegate uploader:weakSelf restartSuccess:NO model:model];
            }
        }
    }];
}

/// 上传文档
/// @note 用于互动学堂场景
- (void)uploadModel:(PLVDocumentUploadModel *)model lessionId:(NSString *)lessionId {
    if (!model ||
        ![PLVFdUtil checkStringUseable:lessionId]) {
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(uploader:startSuccess:model:)]) {
            [self.delegate uploader:self startSuccess:NO model:model];
        }
        return;
    }
    __weak typeof(self) weakSelf = self;
    
    if (self.isTeacher) {
        [PLVLiveVClassAPI teacherGetLiveAPIChannelTokenWithLessonId:lessionId success:^(NSDictionary * _Nonnull responseDict) {
            [weakSelf dealGetTokenSuccessWithModel:model responseDict:responseDict weakSelf:weakSelf];
        } failure:^(NSError * _Nonnull error) {
            [weakSelf dealGetTokenFailureWithModel:model];
        }];
    } else {
        [PLVLiveVClassAPI watcherGetLiveAPIChannelTokenWithLessonId:lessionId courseCode:self.courseCode success:^(NSDictionary * _Nonnull responseDict) {
            [weakSelf dealGetTokenSuccessWithModel:model responseDict:responseDict weakSelf:weakSelf];
        } failure:^(NSError * _Nonnull error) {
            [weakSelf dealGetTokenFailureWithModel:model];
        }];
    }
    
    
}

/// 用于中断任务的恢复及失败重试
/// @note 用于互动学堂场景
- (void)uploadModel:(PLVDocumentUploadModel *)model
          lessionId:(NSString *)lessionId
              retry:(BOOL)retry {
    
    if (!model ||
        ![PLVFdUtil checkStringUseable:lessionId]) {
        [self notifyListenerUploadModel:model success:NO retry:retry];
        return;
    }
    
    __weak typeof(self) weakSelf = self;
    if (self.isTeacher) {
        [PLVLiveVClassAPI teacherGetLiveAPIChannelTokenWithLessonId:lessionId
                                                   success:^(NSDictionary * _Nonnull responseDict) {
            [weakSelf dealRetryUploadSuccessWithModel:model responseDict:responseDict retry:retry weakSelf:weakSelf];
        } failure:^(NSError * _Nonnull error) {
            [weakSelf notifyListenerUploadModel:model success:NO retry:retry];
        }];
    } else {
        [PLVLiveVClassAPI watcherGetLiveAPIChannelTokenWithLessonId:lessionId courseCode:self.courseCode success:^(NSDictionary * _Nonnull responseDict) {
            [weakSelf dealRetryUploadSuccessWithModel:model responseDict:responseDict retry:retry weakSelf:weakSelf];
        } failure:^(NSError * _Nonnull error) {
            [weakSelf notifyListenerUploadModel:model success:NO retry:retry];
        } ];
    }
}

- (void)stopAllUpload {
    for (NSString * key in [self.aliDictionary allKeys]) {
        PLVSDocumentAliModel *aliModel = self.aliDictionary[key];
        [aliModel.uploadRequest cancel];
        [aliModel.ossClient abortResumableMultipartUpload:aliModel.uploadRequest];
    }
    
    [self.aliDictionary removeAllObjects];
}

#pragma mark - Private

- (void)getTokenWithModel:(PLVDocumentUploadModel *)model
                channelId:(NSString *)channelId
                  success:(void (^)(PLVDocumentTokenModel *token))success
                  failure:(void (^)(NSError *error))failure {
    __weak typeof(self) weakSelf = self;

    [PLVLivePrivateAPI getUploadTokenWithChannelId:channelId fileId:model.fileId fileName:model.fileName convertType:model.convertType success:^(NSDictionary * _Nonnull responseObject, NSDictionary * _Nonnull callback) {
        PLVDocumentTokenModel *token = [PLVDocumentTokenModel plv_modelWithJSON:responseObject];
        if ([token.convertStatus isEqualToString:@"normal"] ||
            [token.convertStatus isEqualToString:@"waitConvert"]) {
            // 错误码：文档已存在服务端
            NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulUpload
                                                                             code:PLVFUploadErrorCodeDocumentUploadedExist];
            NSString *errorMessage = [NSString stringWithFormat:PLVFDLocalizableString(@"文档已存在，convertStatus为%@"), token.convertStatus];
            NSDictionary *userInfo = [NSDictionary dictionaryWithObject:errorMessage forKey:NSLocalizedDescriptionKey];
            NSError *userError = [NSError errorWithDomain:failError.domain code:failError.code userInfo:userInfo];
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:userError.code information:userError.localizedDescription];

            if (weakSelf.delegate) {
                [weakSelf.delegate uploader:weakSelf error:userError model:model];
            }
            failure(userError);
        } else {
            token.channelId = channelId;
            token.callback = callback;
            success(token);
        }
    } failure:^(NSError * _Nonnull error) { // 错误码：获取 token 失败
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:error.code information:error.localizedDescription];
        if (weakSelf.delegate) {
            [weakSelf.delegate uploader:weakSelf error:error model:model];
        }
        failure(error);
    }];
}

- (void)getTokenWithModel:(PLVDocumentUploadModel *)model
                channelId:(NSString *)channelId
             channelToken:(NSString *)channelToken
                    appId:(NSString *)appId
                  success:(void (^)(PLVDocumentTokenModel *token))success
                  failure:(void (^)(NSError *error))failure {
    __weak typeof(self) weakSelf = self;
    
    [PLVLivePrivateAPI getUploadTokenWithChannelId:channelId channelToken:channelToken appId:appId fileId:model.fileId fileName:model.fileName convertType:model.convertType success:^(NSDictionary * _Nonnull responseObject, NSDictionary * _Nullable callback) {
        PLVDocumentTokenModel *token = [PLVDocumentTokenModel plv_modelWithJSON:responseObject];
        if ([token.convertStatus isEqualToString:@"normal"] ||
            [token.convertStatus isEqualToString:@"waitConvert"]) {
            // 错误码：文档已存在服务端
            NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulUpload
                                                                             code:PLVFUploadErrorCodeDocumentUploadedExist];
            NSString *errorMessage = [NSString stringWithFormat:PLVFDLocalizableString(@"文档已存在，convertStatus为%@"), token.convertStatus];
            NSDictionary *userInfo = [NSDictionary dictionaryWithObject:errorMessage forKey:NSLocalizedDescriptionKey];
            NSError *userError = [NSError errorWithDomain:failError.domain code:failError.code userInfo:userInfo];
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:userError.code information:userError.localizedDescription];
            
            if (weakSelf.delegate) {
                [weakSelf.delegate uploader:weakSelf error:userError model:model];
            }
            failure(userError);
        } else {
            token.channelId = channelId;
            token.callback = callback;
            success(token);
        }
    } failure:^(NSError * _Nonnull error) { // 错误码：获取 token 失败
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:error.code information:error.localizedDescription];
        if (weakSelf.delegate) {
            [weakSelf.delegate uploader:weakSelf error:error model:model];
        }
        failure(error);
    }];
}

- (PLVDocumentTokenModel *)synGetTokenWithModel:(PLVDocumentUploadModel *)model channelId:(NSString *)channelId error:(NSError **)error {
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    
    __block PLVDocumentTokenModel *token = nil;
    __block NSDictionary *errorInfo = nil;
    [PLVLivePrivateAPI getUploadTokenWithChannelId:channelId fileId:model.fileId fileName:model.fileName convertType:model.convertType success:^(NSDictionary * _Nonnull responseObject, NSDictionary * _Nonnull callback) {
        PLVDocumentTokenModel *token = [PLVDocumentTokenModel plv_modelWithJSON:responseObject];
        token.channelId = channelId;
        token.callback = callback;
        
        dispatch_semaphore_signal(semaphore);
    } failure:^(NSError * _Nonnull error) {
        errorInfo = @{NSLocalizedDescriptionKey:error.localizedDescription};
        dispatch_semaphore_signal(semaphore);
    }];
    
    dispatch_time_t timeoutInterval = dispatch_time(DISPATCH_TIME_NOW, 12 * NSEC_PER_SEC);
    dispatch_semaphore_wait(semaphore, timeoutInterval);
    
    if (token == nil) {
        errorInfo = @{NSLocalizedDescriptionKey:PLVFDLocalizableString(@"请求超时")};
    }
    if (error && errorInfo) {
        NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulUpload
                                                                         code:PLVFUploadErrorCodeDocumentOSSTokenRefreshError];
        NSDictionary *userInfo = [NSDictionary dictionaryWithObject:errorInfo forKey:NSLocalizedDescriptionKey];
        NSError *userError = [NSError errorWithDomain:failError.domain code:failError.code userInfo:userInfo];
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:userError.code information:userError.localizedDescription];
        *error = userError;
    }
    
    return token;
}

- (void)uploadWithModel:(PLVDocumentUploadModel *)model token:(PLVDocumentTokenModel *)token {
    OSSClient *ossClient = [self getOSSClientWithUploadModel:model token:token];
    OSSResumableUploadRequest *uploadRequest = [self getUploadRequestWithUploadModel:model token:token];
    PLVSDocumentAliModel *aliModel = [[PLVSDocumentAliModel alloc] initWithClient:ossClient request:uploadRequest];
    [self.aliDictionary setObject:aliModel forKey:model.fileId];
    
    OSSTask *osTask = [ossClient resumableUpload:uploadRequest];
    __weak typeof(self) weakSelf = self;
    [osTask continueWithBlock:^id(OSSTask *task) {
        if (task.error) {
            // 错误码：上传失败
            NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulUpload
                                                                             code:PLVFUploadErrorCodeDocumentOSSTaskError];
            NSString *description = [NSString stringWithFormat:@"%@(#%zd)", task.error.localizedDescription, task.error.code];
            NSDictionary *userInfo = [NSDictionary dictionaryWithObject:description forKey:NSLocalizedDescriptionKey];
            NSError *userError = [NSError errorWithDomain:failError.domain code:failError.code userInfo:userInfo];
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:userError.code information:userError.localizedDescription];
            
            if (weakSelf.delegate) {
                [weakSelf.delegate uploader:weakSelf success:NO model:model];
                [weakSelf.delegate uploader:weakSelf error:userError model:model];
            }
            [weakSelf.aliDictionary removeObjectForKey:model.fileId];
        } else {
            if (weakSelf.delegate) {
                [weakSelf.delegate uploader:weakSelf success:YES model:model];
            }
            [weakSelf.aliDictionary removeObjectForKey:model.fileId];
//            [[PLVWLogReporterManager sharedManager] reportWithEvent:PLVSELogUploadEventUploadSuccess
//                                                              modul:PLVSELogModulUpload
//                                                        information:model.fileId];
        }
        return nil;
    }];
}

- (OSSClient *)getOSSClientWithUploadModel:(PLVDocumentUploadModel *)model
                                     token:(PLVDocumentTokenModel *)token {
    __block BOOL first = YES;
    __weak typeof(self) weakSelf = self;
    id<OSSCredentialProvider> credential = [[OSSFederationCredentialProvider alloc] initWithFederationTokenGetter:^OSSFederationToken * _Nullable{
        if (first) {
//            [[PLVWLogReporterManager sharedManager] reportWithEvent:PLVSELogUploadEventSetSTSToken
//                                                              modul:PLVSELogModulUpload
//                                                        information:model.fileId];
            first = NO;
            OSSFederationToken *ossToken = [[OSSFederationToken alloc] init];
            ossToken.tAccessKey = token.accessId;
            ossToken.tSecretKey = token.accessKey;
            ossToken.tToken = token.token;
            ossToken.expirationTimeInGMTFormat = token.expiration;
            return ossToken;
        } else {
//            [[PLVWLogReporterManager sharedManager] reportWithEvent:PLVSELogUploadEventRefreshSTSToken
//                                                              modul:PLVSELogModulUpload
//                                                        information:model.fileId];
            NSError *error = nil;
            PLVDocumentTokenModel *aToken = [weakSelf synGetTokenWithModel:model channelId:token.channelId error:&error];
            if (aToken == nil) {
                if (weakSelf.delegate && error) { // 错误码：上传失败 - 刷新 STS token 失败
                    [weakSelf.delegate uploader:weakSelf error:error model:model];
                }
                return nil;
            }
            
            OSSFederationToken *ossToken = [[OSSFederationToken alloc] init];
            ossToken.tAccessKey = aToken.accessId;
            ossToken.tSecretKey = aToken.accessKey;
            ossToken.tToken = aToken.token;
            ossToken.expirationTimeInGMTFormat = aToken.expiration;
            return ossToken;
        }
    }];
    OSSClient *ossClient = [[OSSClient alloc] initWithEndpoint:token.endpoint credentialProvider:credential];
    return ossClient;
}

- (OSSResumableUploadRequest *)getUploadRequestWithUploadModel:(PLVDocumentUploadModel *)model
                                                         token:(PLVDocumentTokenModel *)token {
    __weak typeof(self) weakSelf = self;
    void (^myUploadProgress)(int64_t bytesSent, int64_t totalByteSent, int64_t totalBytesExpectedToSend) =
    ^(int64_t bytesSent, int64_t totalByteSent, int64_t totalBytesExpectedToSend) {
        float progress = totalByteSent * 1.0 / totalBytesExpectedToSend;
        progress = MAX(MIN(progress, 1), 0);
        model.progress = progress;
        
//        PLVS_LOG_DEBUG(PLVSConsoleLogModuleTypeUpload, @"UPLOADER-Process changed: %.1f", progress);
        if (weakSelf.delegate) {
            [weakSelf.delegate uploader:weakSelf progressChanged:progress model:model];
        }
    };
    
    OSSResumableUploadRequest *resumableUpload = [OSSResumableUploadRequest new];
    resumableUpload.bucketName = token.bucket;
    resumableUpload.objectKey = token.object;
    resumableUpload.callbackParam = token.callback;
    resumableUpload.uploadingFileURL = [NSURL fileURLWithPath:model.filePath];
    resumableUpload.uploadProgress = myUploadProgress;
    resumableUpload.deleteUploadIdOnCancelling = NO;
    resumableUpload.recordDirectoryPath = [self checkPointCacheDirectory];
    return resumableUpload;
}

- (NSString *)checkPointCacheDirectory {
    NSString *cacheDir = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES)[0];
    NSString *dirString = [NSString stringWithFormat:@"%@/PLVSDocumentCheckPoint", cacheDir];
    BOOL isDirectory = NO;
    BOOL exist = [[NSFileManager defaultManager] fileExistsAtPath:dirString isDirectory:&isDirectory];
    if (!exist || !isDirectory) {
        [[NSFileManager defaultManager] createDirectoryAtPath:dirString withIntermediateDirectories:YES attributes:nil error:nil];
    }
    return dirString;
}

- (void)dealGetTokenSuccessWithModel:(PLVDocumentUploadModel * _Nonnull)model responseDict:(NSDictionary * _Nonnull)responseDict weakSelf:(PLVDocumentUploader *const __weak)weakSelf {
    if ([PLVFdUtil checkDictionaryUseable:responseDict]) {
        NSString *token = PLV_SafeStringForDictKey(responseDict, @"token");
        NSString *channelId = PLV_SafeStringForDictKey(responseDict, @"channelId");
        NSString *appId = PLV_SafeStringForDictKey(responseDict, @"appId");
        
        [weakSelf getTokenWithModel:model channelId:channelId channelToken:token appId:appId  success:^(PLVDocumentTokenModel *token) {
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(uploader:startSuccess:model:)]) {
                [weakSelf.delegate uploader:weakSelf startSuccess:YES model:model];
            }
            
            [weakSelf uploadWithModel:model token:token];
        } failure:^(NSError *error) {
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(uploader:startSuccess:model:)]) {
                [weakSelf.delegate uploader:weakSelf startSuccess:NO model:model];
            }
        }];
        
    } else {
        if (weakSelf.delegate &&
            [weakSelf.delegate respondsToSelector:@selector(uploader:startSuccess:model:)]) {
            [weakSelf.delegate uploader:weakSelf startSuccess:NO model:model];
        }
    }
}

- (void)dealGetTokenFailureWithModel:(PLVDocumentUploadModel * _Nonnull)model {
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(uploader:startSuccess:model:)]) {
        [self.delegate uploader:self startSuccess:NO model:model];
    }
}

- (void)dealRetryUploadSuccessWithModel:(PLVDocumentUploadModel * _Nonnull)model responseDict:(NSDictionary * _Nonnull)responseDict retry:(BOOL)retry weakSelf:(PLVDocumentUploader *const __weak)weakSelf {
    if ([PLVFdUtil checkDictionaryUseable:responseDict]) {
        NSString *token = PLV_SafeStringForDictKey(responseDict, @"token");
        NSString *channelId = PLV_SafeStringForDictKey(responseDict, @"channelId");
        NSString *appId = PLV_SafeStringForDictKey(responseDict, @"appId");
        
        [weakSelf getTokenWithModel:model
                          channelId:channelId
                       channelToken:token
                              appId:appId
                            success:^(PLVDocumentTokenModel *token) {
            [weakSelf notifyListenerUploadModel:model success:YES retry:retry];
            [weakSelf uploadWithModel:model token:token];
        } failure:^(NSError *error) {
            [weakSelf notifyListenerUploadModel:model success:NO retry:retry];
        }];
        
    } else {
        [weakSelf notifyListenerUploadModel:model success:NO retry:retry];
    }
}

#pragma mark Listener

- (void)notifyListenerUploadModel:(PLVDocumentUploadModel *)model
                  success:(BOOL)success
                    retry:(BOOL)retry {
    if (retry) {
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(uploader:retrySuccess:model:)]) {
            [self.delegate uploader:self retrySuccess:success model:model];
        }
    } else {
        if (self.delegate &&
            [self.delegate respondsToSelector:@selector(uploader:restartSuccess:model:)]) {
            [self.delegate uploader:self restartSuccess:success model:model];
        }
    }
}

@end
