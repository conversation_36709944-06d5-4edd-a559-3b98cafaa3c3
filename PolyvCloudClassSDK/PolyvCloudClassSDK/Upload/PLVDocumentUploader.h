//
//  PLVDocumentUploader.h
//  PLVCloudClassStreamerSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/6.
//  Copyright © 2020 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>

@class PLVDocumentUploadModel;
@class PLVDocumentUploader;

NS_ASSUME_NONNULL_BEGIN

@protocol PLVDocumentUploaderDelegate <NSObject>

/// 初次添加上传任务启动成功/失败时回调
/// @note 获取OSS Token
- (void)uploader:(PLVDocumentUploader *)uploader startSuccess:(BOOL)success model:(PLVDocumentUploadModel *)model;

/// 上传进度发生变化时回调
/// @note 上传OSS
- (void)uploader:(PLVDocumentUploader *)uploader progressChanged:(float)progress model:(PLVDocumentUploadModel *)model;

/// 上传成功/失败时回调
/// @note 上传OSS
- (void)uploader:(PLVDocumentUploader *)uploader success:(BOOL)success model:(PLVDocumentUploadModel *)model;

/// 恢复上传任务启动成功/失败时回调
/// @note 获取OSS Token
- (void)uploader:(PLVDocumentUploader *)uploader restartSuccess:(BOOL)success model:(PLVDocumentUploadModel *)model;

/// 失败重试任务启动成功/失败时回调
- (void)uploader:(PLVDocumentUploader *)uploader retrySuccess:(BOOL)success model:(PLVDocumentUploadModel *)model;

/// 错误统一回调
- (void)uploader:(PLVDocumentUploader *)uploader error:(NSError *)error model:(PLVDocumentUploadModel *)model;

@end

/// 文档上传核心类
@interface PLVDocumentUploader : NSObject

@property (nonatomic, weak) id<PLVDocumentUploaderDelegate> delegate;

@property (nonatomic, assign, getter=isTeacher) BOOL teacher; // 是否为讲师

@property (nonatomic, copy) NSString *courseCode; // 课程号

+ (instancetype)sharedUploader;

- (void)stopAllUpload;

#pragma mark 三分屏开播场景
/// 上传文档
/// @param model 文档模型
- (void)uploadModel:(PLVDocumentUploadModel *)model channelId:(NSString *)channelId;

/// 用于中断任务的恢复及失败重试
/// @param model 上传模型
/// @param channelId 频道号
/// @param retry YES: 失败重试；NO:中断任务恢复
- (void)uploadModel:(PLVDocumentUploadModel *)model channelId:(NSString *)channelId retry:(BOOL)retry;

#pragma mark 互动学堂场景
/// 上传文档
/// @note 用于互动学堂场景
/// @param lessionId 课节Id
- (void)uploadModel:(PLVDocumentUploadModel *)model lessionId:(NSString *)lessionId;

/// 用于中断任务的恢复及失败重试
/// @note 用于互动学堂场景
/// @param model 上传模型
/// @param lessionId 课节Id
/// @param retry YES: 失败重试；NO:中断任务恢复
- (void)uploadModel:(PLVDocumentUploadModel *)model lessionId:(NSString *)lessionId retry:(BOOL)retry;

@end

NS_ASSUME_NONNULL_END
