//
//  PLVDocumentUploadClient.m
//  PLVCloudClassStreamerSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/3.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVDocumentUploadClient.h"
#import "PLVFileManager.h"
#import "PLVDocumentUploader.h"
#import "PLVWErrorManager.h"
#import "PLVWLogReporterManager.h"
#import "PLVWELogEventDefine.h"
#import <PLVModel/PLVModel.h>
#import <CommonCrypto/CommonDigest.h>
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVLiveVideoAPI.h"

#define PLVSDocumentUploadCacheKey(channelId) [NSString stringWithFormat:@"plvs_document_upload_%@", channelId]

@interface PLVDocumentUploadClient ()<
PLVDocumentUploaderDelegate
>

@property (nonatomic, assign) BOOL pptAnimationEnabled;

@property (nonatomic, strong) NSString *channelId;

@property (nonatomic, strong) NSString *lessionId;

@property (nonatomic, assign) BOOL teacher;

@property (nonatomic, copy) NSMutableArray <PLVDocumentUploadModel *> *uploadArray;

@property (nonatomic, copy) NSMutableArray <PLVDocumentUploadModel *> *uploadingArray;

@end

@implementation PLVDocumentUploadClient

#pragma mark - Life Cycle

- (instancetype)init {
    self = [super init];
    if (self) {
        _uploadArray = [[NSMutableArray alloc] init];
        _uploadingArray = [[NSMutableArray alloc] init];
        [PLVDocumentUploader sharedUploader].delegate = self;
    }
    return self;
}

#pragma mark - Public

+ (instancetype)sharedClient {
    static PLVDocumentUploadClient *client = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        client = [[PLVDocumentUploadClient alloc] init];
    });
    return client;
}

- (void)setupWithChannelId:(NSString *)channelId pptAnimationEnable:(BOOL)pptAnimationEnabled {
    [self setupWithChannelId:channelId lessionId:@"" courseCode:nil pptAnimationEnable:NO teacher:YES];
}

- (void)setupWithChannelId:(NSString *)channelId lessionId:(NSString *)lessionId pptAnimationEnable:(BOOL)pptAnimationEnabled {
    [self setupWithChannelId:channelId lessionId:lessionId courseCode:nil pptAnimationEnable:pptAnimationEnabled teacher:YES];
}

- (void)setupWithChannelId:(NSString *)channelId lessionId:(NSString *)lessionId courseCode:(NSString *)courseCode pptAnimationEnable:(BOOL)pptAnimationEnabled teacher:(BOOL)teacher {
    self.channelId = channelId;
    self.lessionId = lessionId;
    self.pptAnimationEnabled = pptAnimationEnabled;
    self.teacher = teacher;
    [PLVDocumentUploader sharedUploader].teacher = teacher;
    [PLVDocumentUploader sharedUploader].courseCode = courseCode;
    
    [self getCache];
}

- (void)uploadDocumentWithFileURL:(NSURL *)fileURL convertType:(NSString *)convertType {
//    [[PLVWLogReporterManager sharedManager] reportWithEvent:PLVSELogUploadEventUploadBegin
//                                                      modul:PLVSELogModulUpload
//                                                information:fileURL.path];
    
    // 拷贝文档到沙盒
    NSString *desFilePath = [PLVFileManager copyFile:fileURL channelId:self.channelId];
    if (desFilePath == nil) {
//        PLVS_LOG_ERROR(PLVSConsoleLogModuleTypeUpload, @"UPLOAD_CLIENT-copy file AT %@ failure", fileURL);
        if (self.errorDelegate) {
            NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulUpload
                                                                             code:PLVFUploadErrorCodeDocumentCopyError];
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:failError.localizedDescription];
            
            [self.errorDelegate uploadError:failError];
        }
        return;
    }
    
    NSURL *desFileURL = [NSURL fileURLWithPath:desFilePath];
    NSString *fileId = [self getFileIdWithFileURL:desFileURL convertType:convertType];
    BOOL exist = [self isModelExist:fileId model:nil];
    if (exist) {
//        PLVS_LOG_ERROR(PLVSConsoleLogModuleTypeUpload, @"UPLOAD_CLIENT-file AT %@ upload already exist", fileURL);
        if (self.errorDelegate) {
            NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulUpload
                                                                             code:PLVFUploadErrorCodeDocumentUploadingExist];
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:failError.localizedDescription];
            
            [self.errorDelegate uploadError:failError];
        }
        return;
    }
    
    // 更新缓存、更新上传队列
    PLVDocumentUploadModel *model = [self generateModelWithFileId:fileId fileURL:desFileURL convertType:convertType];
    [self.uploadArray addObject:model];
    [self.uploadingArray addObject:model];
    [self addCacheWithModel:model];
    
    //上传文档
    if ([PLVFdUtil checkStringUseable:self.lessionId]) {
        [[PLVDocumentUploader sharedUploader] uploadModel:model lessionId:self.lessionId];
    } else {
        [[PLVDocumentUploader sharedUploader] uploadModel:model channelId:self.channelId];
    }
}

- (void)removeUploadWithFileId:(NSString *)fileId {
    PLVDocumentUploadModel *model = nil;
    [self isModelExist:fileId model:&model];
    if (model == nil) {
        return;
    }
    
    // 清理沙盒文档
    [PLVFileManager deleteFile:model.fileName channelId:self.channelId];
    // 更新缓存
    [self removeCacheWithModel:model];
    // 更新上传队列
    [self.uploadArray removeObject:model];
    [self.uploadingArray removeObject:model];
}

- (void)deleteFileWithFileName:(NSString *)fileName {
    [PLVFileManager deleteFile:fileName channelId:self.channelId];
}

- (void)retryUploadWithFileId:(NSString *)fileId {
    PLVDocumentUploadModel *model = nil;
    [self isModelExist:fileId model:&model];
    if (model == nil) {
        return;
    }
    [[PLVDocumentUploader sharedUploader] uploadModel:model channelId:self.channelId retry:YES];
}

- (void)retryUploadWithModel:(PLVDocumentUploadModel *)model {
    if (!model ||
        ![PLVFdUtil checkStringUseable:model.fileId] ||
        ![PLVFdUtil checkStringUseable:model.fileName] ||
        ![PLVFdUtil checkStringUseable:model.convertType]) {
        return;
    }
    
    NSString *filePath = [PLVFileManager filePathWithFileName:model.fileName channelId:self.channelId];
    if ([PLVFdUtil checkStringUseable:filePath]) {
        model.filePath = filePath;
    }
    
    [[PLVDocumentUploader sharedUploader] uploadModel:model lessionId:self.lessionId retry:YES];
}

- (void)retryUploadConvertFailureDocumentWithModel:(PLVDocumentUploadModel *)model {
    if (!model ||
        ![PLVFdUtil checkStringUseable:model.fileId] ||
        ![PLVFdUtil checkStringUseable:model.fileName] ||
        ![PLVFdUtil checkStringUseable:model.convertType]) {
        return;
    }
  
    NSString *filePath = [PLVFileManager filePathWithFileName:model.fileName channelId:self.channelId];
    if ([PLVFdUtil checkStringUseable:filePath]) {
        model.filePath = filePath;
    }
    model.status = PLVDocumentUploadStatusUploading;
    
   [[PLVDocumentUploader sharedUploader] uploadModel:model lessionId:self.lessionId];
}

- (void)continueAllUpload {
    for (PLVDocumentUploadModel *model in self.uploadingArray) {
        if ([PLVFdUtil checkStringUseable:self.lessionId]) {
            [[PLVDocumentUploader sharedUploader] uploadModel:model lessionId:self.lessionId retry:YES];
        } else {
            [[PLVDocumentUploader sharedUploader] uploadModel:model channelId:self.channelId retry:YES];
        }
    }
}

- (void)clearAllUpload {
    [self.uploadArray removeAllObjects];
    [self.uploadingArray removeAllObjects];
    [self removeAllUploadingModelFromCache];
    [self getCache];
    if (self.resultDelegate) {
        __weak typeof(self) weakSelf = self;
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf.resultDelegate updateUploadData];
        });
    }
}

- (void)stopAllUpload {
    BOOL uploading = ([[PLVDocumentUploadClient sharedClient].uploadingArray count] > 0);
    if (uploading) { // 停止所有上传
        [[PLVDocumentUploader sharedUploader] stopAllUpload];
    }
    
    [self.uploadArray removeAllObjects];
    [self.uploadingArray removeAllObjects];
}

#pragma mark - Private

/// 指定 fileId 任务恢复“上传中”状态
- (void)uploadRebootSuccessWithFileId:(NSString *)fileId {
    PLVDocumentUploadModel *model = nil;
    [self isModelExist:fileId model:&model];
    if (model == nil) {
        return;
    }
    
    model.status = PLVDocumentUploadStatusUploading;
    [self updateCacheWithModel:model];
}

/// 指定 fileId 任务上传失败
- (void)uploadFailWithFileId:(NSString *)fileId {
    PLVDocumentUploadModel *model = nil;
    [self isModelExist:fileId model:&model];
    if (model == nil) {
        return;
    }
    
    model.progress = 0;
    model.status = PLVDocumentUploadStatusFailure;
    [self updateCacheWithModel:model];
}

/// 指定 fileId 任务上传进度变更
- (void)uploadProgressChanged:(float)progress field:(NSString *)fileId {
    PLVDocumentUploadModel *model = nil;
    [self isModelExist:fileId model:&model];
    if (model == nil) {
        return;
    }
    
    if (progress <= model.progress) {
        return;
    }
    
    model.progress = progress;
    if (model.uploadProgress) {
        dispatch_async(dispatch_get_main_queue(), ^{
            model.uploadProgress(progress);
        });
    }
}

#pragma mark Private - Cache Related

/// 读取缓存数据
- (NSArray *)getCacheArray {
    return [[NSUserDefaults standardUserDefaults] objectForKey:PLVSDocumentUploadCacheKey(self.channelId)];
}

///  写入缓存数据
- (void)saveCacheArray:(NSArray *)array {
    [[NSUserDefaults standardUserDefaults] setObject:array forKey:PLVSDocumentUploadCacheKey(self.channelId)];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

/// 根据频道号读取上传队列、上传中队列到内存数组
- (void)getCache {
    NSArray *cacheArray = [self getCacheArray];
    if (cacheArray == nil || [cacheArray count] == 0) {
        return;
    }
    
    for (NSDictionary *cacheDict in cacheArray) {
        PLVDocumentUploadModel *model = [PLVDocumentUploadModel plv_modelWithJSON:cacheDict];
        NSString *filePath = [PLVFileManager filePathWithFileName:model.fileName channelId:self.channelId];
        if ([PLVFdUtil checkStringUseable:filePath]) {
            model.filePath = filePath;
        }
        [_uploadArray addObject:model];
        if (model.status == PLVDocumentUploadStatusUploading) {
            [_uploadingArray addObject:model];
        }
    }
}

/// 缓存数据新增模型
- (void)addCacheWithModel:(PLVDocumentUploadModel *)model {
    NSMutableArray *muArray = [[NSMutableArray alloc] init];
    
    NSArray *cacheArray = [self getCacheArray];
    if (cacheArray) {
        [muArray addObjectsFromArray:cacheArray];
    }
    
    NSDictionary *dict = [model plv_modelToJSONObject];
    [muArray addObject:dict];
    [self saveCacheArray:[muArray copy]];
}

/// 从缓存移除模型
- (void)removeCacheWithModel:(PLVDocumentUploadModel *)model {
    NSMutableArray *muArray = [[NSMutableArray alloc] init];
    
    NSArray *cacheArray = [self getCacheArray];
    if (cacheArray) {
        [muArray addObjectsFromArray:cacheArray];
    }
    
    for (NSDictionary *dict in cacheArray) {
        NSString *dictFileId = dict[@"fileId"];
        if ([model.fileId isEqualToString:dictFileId]) {
            [muArray removeObject:dict];
            break;
        }
    }
    [self saveCacheArray:[muArray copy]];
}

/// 从缓存中更新指定 fileId 的数据
- (void)updateCacheWithModel:(PLVDocumentUploadModel *)model {
    NSMutableArray *muArray = [[NSMutableArray alloc] init];
    
    NSArray *cacheArray = [self getCacheArray];
    if (cacheArray == nil) {
        return;
    }
    
    [muArray addObjectsFromArray:cacheArray];
    for (NSDictionary *dict in cacheArray) {
        NSString *cacheFildId = dict[@"fileId"];
        if ([cacheFildId isEqualToString:model.fileId]) {
            NSDictionary *updateDict = [model plv_modelToJSONObject];
            [muArray removeObject:dict];
            [muArray addObject:updateDict];
            break;
        }
    }
    [self saveCacheArray:[muArray copy]];
}

/// 从缓存、沙盒、服务器中删除所有 status 为 PLVDocumentUploadStatusUploading 的数据
- (void)removeAllUploadingModelFromCache {
    NSMutableArray *muArray = [[NSMutableArray alloc] init];
    
    NSArray *cacheArray = [self getCacheArray];
    if (cacheArray == nil) {
        return;
    }
    
    [muArray addObjectsFromArray:cacheArray];
    for (NSDictionary *dict in cacheArray) {
        NSInteger status = PLV_SafeIntegerForDictKey(dict, @"status");
        NSString *fileName = PLV_SafeStringForDictKey(dict, @"fileName");
        NSString *fileId = PLV_SafeStringForDictKey(dict, @"fileId");
        
        if (status == PLVDocumentUploadStatusUploading) {
            // 从缓存中删除
            [muArray removeObject:dict];
            
            // 从沙盒中删除文档
            if ([PLVFdUtil checkStringUseable:fileName]) {
                [self deleteFileWithFileName:fileName];
            }
            
            // 从服务器中删除文档
            if ([PLVFdUtil checkStringUseable:fileId]) {
                [PLVLiveVideoAPI deleteDocumentWithChannelId:self.channelId fileId:fileId completion:^{} failure:^(NSError * _Nonnull error) {}];
            }
        }
    }
    [self saveCacheArray:[muArray copy]];
}

/// 删除上传缓存
/// @param fileId 文档Id
- (void)removeUploadCacheWithFileId:(NSString *)fileId {
    PLVDocumentUploadModel *model = nil;
    [self isModelExist:fileId model:&model];
    if (model == nil) {
        return;
    }
    // 更新缓存
    [self removeCacheWithModel:model];
    // 更新上传队列
    [self.uploadArray removeObject:model];
    [self.uploadingArray removeObject:model];
}

#pragma mark Private - Utils

/// 生成 fileId：文件的MD5值+ 频道号+转码类型convertType
- (NSString *)getFileIdWithFileURL:(NSURL *)fileURL convertType:(NSString *)convertType {
    NSString *md5Str = [self md5WithFilePath:fileURL.path];
    NSString *result = [NSString stringWithFormat:@"%@%@%@", md5Str, self.channelId, convertType];
    return result;
}

/// 生成文件的 MD5 值
- (NSString *)md5WithFilePath:(NSString *)filePath {
    NSFileHandle *handle = [NSFileHandle fileHandleForReadingAtPath:filePath];
    if (handle == nil) {
        return nil;
    }

    CC_MD5_CTX md5;
    CC_MD5_Init(&md5);
    BOOL done = NO;
    while (done == NO) {
        NSData* fileData = [handle readDataOfLength: 256 ];
        CC_MD5_Update(&md5, [fileData bytes], (CC_LONG)[fileData length]);
        if( [fileData length] == 0 ) {
            done = YES;
        }
    }
    
    unsigned char digest[CC_MD5_DIGEST_LENGTH];
    CC_MD5_Final(digest, &md5);
    NSString *s = [NSString stringWithFormat: @"%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x",
                   digest[0], digest[1], digest[2], digest[3], digest[4], digest[5],
                   digest[6], digest[7], digest[8], digest[9], digest[10], digest[11],
                   digest[12], digest[13], digest[14], digest[15]];
    return s;
}

/// 生成数据模型
- (PLVDocumentUploadModel *)generateModelWithFileId:(NSString *)fileId fileURL:(NSURL *)fileURL convertType:(NSString *)convertType {
    PLVDocumentUploadModel *model = [[PLVDocumentUploadModel alloc] init];
    model.fileId = fileId;
    model.fileName = fileURL.lastPathComponent;
    model.filePath = fileURL.path;
    model.convertType = convertType;
    return model;
}

/// 指定 fileId 模型是否存在队列中
- (BOOL)isModelExist:(NSString *)fileId model:(PLVDocumentUploadModel **)aModel {
    for (PLVDocumentUploadModel *model in self.uploadArray) {
        if ([model.fileId isEqualToString:fileId]) {
            if (aModel) {
                *aModel = model;
            }
            return YES;;
        }
    }
    return NO;
}

#pragma mark - PLVDocumentUploaderDelegate

- (void)uploader:(PLVDocumentUploader *)uploader startSuccess:(BOOL)success model:(PLVDocumentUploadModel *)model {
    if (success) {
        if (self.resultDelegate) {
            __weak typeof(self) weakSelf = self;
            dispatch_async(dispatch_get_main_queue(), ^{
                [weakSelf.resultDelegate uploadStartWithModel:model];
            });
        }
    } else {
        // 更新缓存
        [self removeCacheWithModel:model];
        // 更新上传队列
        [self.uploadArray removeObject:model];
        [self.uploadingArray removeObject:model];
        // 删除沙盒文件
        [self deleteFileWithFileName:model.fileName];
    }
}

- (void)uploader:(PLVDocumentUploader *)uploader progressChanged:(float)progress model:(PLVDocumentUploadModel *)model {
    [self uploadProgressChanged:progress field:model.fileId];
}

- (void)uploader:(PLVDocumentUploader *)uploader success:(BOOL)success model:(PLVDocumentUploadModel *)model {
    if (success) {
        // 互动学堂场景，上传成功不自动删除沙盒文件, 转码成功才自动删除沙盒文件
        if ([PLVFdUtil checkStringUseable:self.lessionId]) {
            [self removeUploadCacheWithFileId:model.fileId];
        } else {
            [self removeUploadWithFileId:model.fileId];
        }
    } else {
        [self uploadFailWithFileId:model.fileId];
    }

    if (self.resultDelegate) {
        __weak typeof(self) weakSelf = self;
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf.resultDelegate uploadSuccess:success model:model];
        });
    }
}

- (void)uploader:(PLVDocumentUploader *)uploader restartSuccess:(BOOL)success model:(PLVDocumentUploadModel *)model {
    if (!success) { // 成功恢复上传中任务无需修改数据状态
        [self uploadFailWithFileId:model.fileId];
    }
    
    if (self.resultDelegate) {
        __weak typeof(self) weakSelf = self;
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf.resultDelegate uploadRestartSuccess:success model:model];
        });
    }
}

- (void)uploader:(PLVDocumentUploader *)uploader retrySuccess:(BOOL)success model:(PLVDocumentUploadModel *)model {
    if (success) { //失败任务重试失败无需修改数据状态
        [self uploadRebootSuccessWithFileId:model.fileId];
    }
    
    if (self.resultDelegate) {
        __weak typeof(self) weakSelf = self;
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf.resultDelegate uploadRetrySuccess:success model:model];
        });
    }
}

- (void)uploader:(PLVDocumentUploader *)uploader error:(NSError *)error model:(PLVDocumentUploadModel *)model {
//    PLVS_LOG_ERROR(PLVSConsoleLogModuleTypeUpload, @"UPLOADER-Failure\rfileId: %@\rerror: %@", model.fileId, error);
    if (self.errorDelegate) {
        [self.errorDelegate uploadError:error];
    }
}

@end
