//
//  PLVStreamerCommodityWebViewBridge.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2022/10/10.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVStreamerCommodityWebViewBridge.h"

@implementation PLVStreamerCommodityWebViewBridge

#pragma mark - [ Life Cycle ]

- (instancetype)initBridgeWithWebView:(WKWebView *)webView webViewDelegate:(id<WKNavigationDelegate>)webViewDelegate {
    self = [super initBridgeWithWebView:webView webViewDelegate:webViewDelegate];
    if (self) {
        [self registerFunctions];
    }
    return self;
}

#pragma mark - [ Private Method ]

- (void)registerFunctions {
    [self registerGetParamsInfoFunction];
}

#pragma mark - Register JS Method

#pragma mark 数据交互

- (void)registerGetParamsInfoFunction {
    NSString *jsEvent = @"getNativeAppParamsInfo";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(getAPPInfoInStreamerCommodityWebViewBridge:)]) {
            NSDictionary *dict = [weakSelf.delegate getAPPInfoInStreamerCommodityWebViewBridge:weakSelf];
            NSString *jsonString = [weakSelf jsonStringFromDictionary:dict];
            responseCallback(jsonString);
        } else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"getAPPInfoInStreamerCommodityWebViewBridge - delegate not implement method:[getAPPInfoInStreamerCommodityWebViewBridge:]");
            responseCallback(nil);
        }
    }];
}

@end
