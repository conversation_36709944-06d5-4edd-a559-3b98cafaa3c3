//
//  PLVWebViewBridge.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/02/04.
//  Copyright © 2021 PLV. All rights reserved.
//

#import "PLVWebViewBridge.h"
#import "PLVWErrorManager.h"
#import "PLVWLogReporterManager.h"
#import "PLVLiveVideoConfig.h"
#import "PLVConsoleLogger.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>

@interface PLVWebViewBridge ()

@property (nonatomic, strong) PLVFWKWebViewJavascriptBridge *bridge;

@property (nonatomic, strong) WKWebView *webView;

@property (nonatomic, weak) id<WKNavigationDelegate> webViewDelegate;

@end

@implementation PLVWebViewBridge

#pragma mark - Initialize

+ (instancetype)bridgeWithWebview:(WKWebView *)webView webviewDelegate:(id<WKNavigationDelegate>)webViewDelegate {
    return [[self alloc] initBridgeWithWebview:webView webviewDelegate:webViewDelegate];
}

+ (instancetype)bridgeWithWebview:(WKWebView *)webView {
    return [[self alloc] initBridgeWithWebview:webView webviewDelegate:nil];
}

- (instancetype)initBridgeWithWebview:(WKWebView *)webView webviewDelegate:(id<WKNavigationDelegate>)webViewDelegate {
    self = [super init];
    if (self) {
        _webView = webView;
        if (webViewDelegate) {
            _webViewDelegate = webViewDelegate;
        }
        
        _bridge = [PLVFWKWebViewJavascriptBridge bridgeForWebView:_webView];
        [_bridge setWebViewDelegate:webViewDelegate];
        
        [self registerGetAppInfoFunction];
    }
    return self;
}

#pragma mark - Public

- (void)setWebViewDelegate:(id<WKNavigationDelegate>)webViewDelegate {
    [_bridge setWebViewDelegate:webViewDelegate];
}

#pragma mark - Call JS Method

- (void)setUserInfo:(NSDictionary *)userDict {
    if (![PLVFdUtil checkDictionaryUseable:userDict]) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":@"setUser",@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s set user info failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:userDict options:0 error:0];
    NSString *dataStr = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    [self.bridge callHandler:@"setUser" data:dataStr responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller setUser with (paramer: %@, response: %@)", jsonData, responseData);
    }];
}

- (void)changePPTPageWithType:(PLVChangePPTPageType)type {
    NSString *typeString = @"";
    NSString *method = @"changePPTPage";
    switch (type) {
        case PLVChangePPTPageTypePreviousStep:
            typeString = @"gotoPreviousStep";
            break;
        case PLVChangePPTPageTypeNextStep:
            typeString = @"gotoNextStep";
            break;
        case PLVChangePPTPageTypePPTBtnBack:
            typeString = @"pptBtnBack";
            break;
    }
    if (![PLVFdUtil checkStringUseable:typeString]) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":method,@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s %@ failed with 【%@】", __FUNCTION__ , myError, method);
        return;
    }
    
    NSDictionary *dict = @{@"type":typeString};
    [self.bridge callHandler:method data:dict responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller %@ with (parma:%@, response:%@)", method, dict, responseData);
    }];
}

#pragma mark 观看专用方法

- (void)refreshPPTWithJsonObject:(NSDictionary *)jsonObject delay:(NSUInteger)delay  {
    if (![PLVFdUtil checkDictionaryUseable:jsonObject]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s refreshPPT with 【param illegal】(param:%@)", __FUNCTION__, jsonObject);
        return;
    }
    NSMutableDictionary *muDict = [[NSMutableDictionary alloc] initWithDictionary:jsonObject];
    if (delay >= 0) {
        muDict[@"delayTime"] = @(delay);
    }
    [self.bridge callHandler:@"refreshPPT" data:[muDict copy] responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller refreshPPT with (parma:%@)", muDict);
    }];
}

- (void)pptSetLocalPath:(NSString *)path {
    if (![PLVFdUtil checkStringUseable:path]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s ppt setLocalPath failed with 【parma illegal】(path:%@）", __FUNCTION__, path);
        return;
    }
    [self.bridge callHandler:@"setOfflinePath" data:@{@"path" : path} responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller set OffLinePath data with (parma:%@, response:%@)", path, responseData);
    }];
}

- (void)pptLocalStartWithVideoId:(NSString *)videoId vid:(NSString *)vid {
    if (![PLVFdUtil checkStringUseable:videoId] ||
        ![PLVFdUtil checkStringUseable:vid]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s ppt localstart failed with 【parma illegal】(videoId:%@, vid:%@）", __FUNCTION__, videoId, vid);
        return;
    }
    [self.bridge callHandler:@"videoStart" data:@{@"videoId" : videoId, @"vid" : vid} responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller ppt start with (response:%@)", responseData);
    }];
}

- (void)setSEIData:(long)newTimeStamp {
    if (newTimeStamp < 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s set SEI data failed with 【parma illegal】(paramer:%ld）", __FUNCTION__, newTimeStamp);
        return;
    }
    NSDictionary *param = @{@"time":@(newTimeStamp)};
    [self.bridge callHandler:@"setSeiData" data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller set SEI data with (parma:%@, response:%@)", param, responseData);
    }];
}

- (void)pptStartWithVid:(NSString *)vid {
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s ppt start failed with 【parma illegal】(vid:%@）", __FUNCTION__, vid);
        return;
    }
    NSString *timestamp = [PLVFdUtil curTimeStamp];
    NSString *plain = [NSString stringWithFormat:@"%@appId%@timestamp%@vid%@%@", appSecret, appId, timestamp, vid, appSecret];
    NSString *sign = [[PLVDataUtil md5HexDigest:plain] uppercaseString];
    if (![PLVFdUtil checkStringUseable:timestamp] ||
        ![PLVFdUtil checkStringUseable:sign] ||
        ![PLVFdUtil checkStringUseable:vid]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s ppt start failed with 【parma illegal】(timestamp:%ld, sign:%@, vid:%@）", __FUNCTION__, timestamp, sign, vid);
        return;
    }
    NSDictionary *param = @{@"appId":appId, @"timestamp":timestamp, @"sign":sign, @"vid":vid};
    [self.bridge callHandler:@"videoStart" data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller ppt start with (parma:%@, response:%@)", param, responseData);
    }];
}

- (void)pptStartWithVideoId:(NSString *)videoId channelId:(NSString *)channelId {
    if (![PLVFdUtil checkStringUseable:videoId] ||
        ![PLVFdUtil checkStringUseable:channelId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s ppt start failed with 【parma illegal】(videoId:%@, channelId:%@）", __FUNCTION__, videoId, channelId);
        return;
    }
    NSDictionary *param = @{@"id":videoId, @"type":@"playback", @"roomId":channelId}; // roomId字段放入频道Id内容（前端叫法与移动端叫法不一样）
    [self.bridge callHandler:@"videoStart" data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller ppt start with (parma:%@, response:%@)", param, responseData);
    }];
}

- (void)pptStartWithFileId:(NSString *)fileId channelId:(NSString *)channelId {
    if (![PLVFdUtil checkStringUseable:fileId] ||
        ![PLVFdUtil checkStringUseable:channelId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s ppt start failed with 【parma illegal】(fileId:%@, channelId:%@）", __FUNCTION__, fileId, channelId);
        return;
    }
    NSDictionary *param = @{@"id":fileId, @"type":@"record", @"roomId":channelId}; // roomId字段放入频道Id内容（前端叫法与移动端叫法不一样）
    [self.bridge callHandler:@"videoStart" data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller ppt start with (parma:%@, response:%@)", param, responseData);
    }];
}

#pragma mark 推流专用方法

- (void)setPaintPermission:(NSString *)userType {
    if (![PLVFdUtil checkStringUseable:userType]) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":@"setPaintPermission",@"param":@"param is invail"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        return;
    }
    NSDictionary *param = @{@"userType":userType};
    [self.bridge callHandler:@"setPaintPermission" data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - setPaintPermission with (param:%@,  response:%@)", param, responseData);
    }];
}

- (void)setPaintStatus:(BOOL)open {
    NSDictionary *param = @{@"status":(open ? @"open" : @"close")};
    [self.bridge callHandler:@"setPaintStatus" data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - setPaintStatus with (param:%@,  response:%@)", param, responseData);
    }];
}

- (void)setSliceStart:(id)jsonDict {
    if (jsonDict == nil) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":@"sliceStart",@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        return;
    }
    [self.bridge callHandler:@"sliceStart" data:jsonDict responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - sliceStart with (jsonDict:%@,  response:%@)", jsonDict, responseData);
    }];
}

- (void)changePPTWithAutoId:(NSUInteger)autoId pageNumber:(NSInteger)pageNumber {
    if (autoId < 0 || pageNumber < 0) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":@"changePPT",@"autoId":@(autoId),@"pageNumber":@(pageNumber)};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        return;
    }
    __weak typeof(self) weakSelf = self;
    NSDictionary *param = @{@"autoId":@(autoId), @"pageId":@(pageNumber), @"docType":@(1), @"isCamClosed":@(0)};
    [self.bridge callHandler:@"changePPT" data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - changePPT with (param:%@)", param);
        if (autoId != 0) {
            if (responseData == nil ||
                responseData[@"autoId"] == nil ||
                responseData[@"smallImages"] == nil ||
                ![responseData[@"smallImages"] isKindOfClass:[NSArray class]]) {
                NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
                NSDictionary *errorInformation = @{@"method":@"changePPT",@"responseData":(responseData ?: @"")};
                [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            } else {
                if (weakSelf.delegate &&
                    [weakSelf.delegate respondsToSelector:@selector(jsbridge_documentChangeWithAutoId:imageUrls:fileName:)]) {
                    NSInteger autoId = PLV_SafeIntegerForDictKey(responseData, @"autoId");
                    NSArray *smallImages = PLV_SafeArraryForDictKey(responseData, @"smallImages");
                    [weakSelf.delegate jsbridge_documentChangeWithAutoId:autoId imageUrls:smallImages fileName:nil];
                }
            }
        }
    }];
}

- (void)setDrawType:(PLVWebViewBrushPenType)type {
    NSString *typeString = nil;
    if (type == PLVWebViewBrushPenTypeText) {
        typeString = @"text";
    } else if (type == PLVWebViewBrushPenTypeArrow) {
        typeString = @"arrowLine";
    } else if (type == PLVWebViewBrushPenTypeFreePen) {
        typeString = @"line";
    } else if (type == PLVWebViewBrushPenTypeRect) {
        typeString = @"rect";
    }
    
    if (typeString == nil) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":@"setDrawType",@"type":@(type)};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        return;
    }
    NSDictionary *param = @{@"type":typeString};
    [self.bridge callHandler:@"setDrawType" data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - setDrawType with (param:%@,  response:%@)", param, responseData);
    }];
}

- (void)changeColor:(NSString *)hexString {
    if (![PLVFdUtil checkStringUseable:hexString]) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":@"changeColor",@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s change color failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    [self.bridge callHandler:@"changeColor" data:hexString responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - changeColor with (param:%@,  response:%@)", hexString, responseData);
    }];
}

- (void)doUndo {
    [self.bridge callHandler:@"undo" data:nil responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - undo with (response:%@)", responseData);
    }];
}

- (void)toDelete {
    [self.bridge callHandler:@"toDelete" data:nil responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - toDelete with (response:%@)", responseData);
    }];
}

- (void)deleteAllPaint {
    [self.bridge callHandler:@"deleteAllPaint" data:nil responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - deleteAllPaint with (response:%@)", responseData);
    }];
}

- (void)changeTextContent:(NSString *)content {
    NSString *requestContent = content ?: @"";
    [self.bridge callHandler:@"changeTextContent" data:requestContent responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - changeTextContent with (param:%@, response:%@)", requestContent, responseData);
    }];
}

- (void)resetWhiteboardPPTZoomRatio {
    [self.bridge callHandler:@"toZoomReset" data:nil responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - toZoomReset with (response:%@)", responseData);
    }];
}

#pragma mark - Register JS Method

- (void)registerSocketEventFunction {
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:@"sendSocketEvent" handler:^(NSDictionary *data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - sendSocketEvent with (response:%@)", data);
        
        if (data == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":@"sendSocketEvent",@"responseData":@"responseData is nil"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - sendSocketEvent failed with 【%@】", __FUNCTION__ ,myError);
        } else {
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(jsbridge_sendSocketEventWithJson:)]) {
                [weakSelf.delegate jsbridge_sendSocketEventWithJson:data];
            }
        }
        responseCallback(nil);
    }];
}

#pragma mark 观看专用方法

- (void)registerGetAppInfoFunction {
    NSString *jsEvent = @"getNativeAppParamsInfo";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(NSDictionary *data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@", jsEvent);
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        if (liveConfig.appId && liveConfig.appSecret) {
            NSDictionary *dict = @{
                @"appId" : liveConfig.appId,
                @"appSecret" : liveConfig.appSecret,
                @"sm2Key" : @{
                    @"platformPublicKey" : [NSString stringWithFormat:@"%@", [PLVFSignConfig sharedInstance].serverSM2PublicKey], // 平台公钥(接口提交参数加密用)
                    @"userPrivateKey" : [NSString stringWithFormat:@"%@", [PLVFSignConfig sharedInstance].clientSM2PrivateKey] // 用户私钥(接口返回内容解密用)
                }
            };
            NSString *jsonString = [weakSelf jsonStringFromDictionary:dict];
            responseCallback(jsonString);
        } else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ failed with 【appId\appSecret null】", jsEvent);
            responseCallback(nil);
        }
    }];
}

- (void)registerPPTPrepareFunction {
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:@"pptPrepare" handler:^(NSDictionary *data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - pptPrepare with (response:%@)", data);
        if (weakSelf.delegate &&
            [weakSelf.delegate respondsToSelector:@selector(jsbridge_PPTHadPrepare)]) {
            [weakSelf.delegate jsbridge_PPTHadPrepare];
        }
        responseCallback(@"function is success");
    }];
}

- (void)registerVideoDurationFunction {
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:@"videoDuration" handler:^(NSDictionary *data, WVJBResponseCallback responseCallback) {
//        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - videoDuration with (response:%@)", data);
        
        if (weakSelf.delegate &&
            [weakSelf.delegate respondsToSelector:@selector(jsbridge_getCurrentPlaybackTime)]) {
            NSTimeInterval currentTime = [weakSelf.delegate jsbridge_getCurrentPlaybackTime];
            NSDictionary *param = @{@"time": [NSNumber numberWithDouble:currentTime]};
            responseCallback(param);
        }
    }];
}

- (void)registerChangePPTPositionFunction {
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:@"changePPTPosition" handler:^(id data, WVJBResponseCallback responseCallback) {
        
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - changePPTPosition with (response:%@)", data);
        
        BOOL status = NO;
        if ([data isKindOfClass:[NSNumber class]]) {
            status = [data boolValue];
        }
        if (weakSelf.delegate &&
            [weakSelf.delegate respondsToSelector:@selector(jsbridge_changePPTPosition:)]) {
            [weakSelf.delegate jsbridge_changePPTPosition:status];
        }
    }];
}

- (void)registerWatchPPTStatusChangeFunction {
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:@"pptStatusChange" handler:^(NSDictionary *data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - pptStatusChange with (response:%@)", data);
        
        if (data == nil ||
            data[@"autoId"] == nil ||
            data[@"pageId"] == nil ||
            data[@"total"] == nil ||
            data[@"step"] == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":@"pptStatusChange",@"responseData":(data ?: @"responseData is nil")};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - pptStatusChange failed with 【%@】", __FUNCTION__ ,myError);
        } else {
            NSUInteger autoId = PLV_SafeIntegerForDictKey(data, @"autoId");
            NSUInteger pageNumber = PLV_SafeIntegerForDictKey(data, @"pageId");
            NSUInteger totalPage = PLV_SafeIntegerForDictKey(data, @"total");
            NSUInteger step = PLV_SafeIntegerForDictKey(data, @"step");
            
            NSDictionary *maxTeacherOp = PLV_SafeDictionaryForDictKey(data, @"maxTeacherOp");
            NSUInteger maxNextNumber = 0;
            if ([PLVFdUtil checkDictionaryUseable:maxTeacherOp]) {
                maxNextNumber = PLV_SafeIntegerForDictKey(maxTeacherOp, @"pageId");
            }
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(jsbridge_pageStatusChangeWithAutoId:pageNumber:totalPage:pptStep:maxNextNumber:)]) {
                [weakSelf.delegate jsbridge_pageStatusChangeWithAutoId:autoId pageNumber:pageNumber totalPage:totalPage pptStep:step maxNextNumber:maxNextNumber];
            }
        }
        responseCallback(nil);
    }];
}

#pragma mark 推流专用方法

- (void)registerPPTStatusChangeFunction {
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:@"pptStatusChange" handler:^(NSDictionary *data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - pptStatusChange");
        
        if (data == nil ||
            data[@"autoId"] == nil ||
            data[@"pageId"] == nil ||
            data[@"total"] == nil ||
            data[@"step"] == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":@"pptStatusChange",@"responseData":(data ?: @"responseData is nil")};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - pptStatusChange failed with 【%@】", __FUNCTION__ ,myError);
        } else {
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(jsbridge_pageStatusChangeWithAutoId:pageNumber:totalPage:pptStep:)]) {
                NSUInteger autoId = PLV_SafeIntegerForDictKey(data, @"autoId");
                NSUInteger pageNumber = PLV_SafeIntegerForDictKey(data, @"pageId");
                NSUInteger totalPage = PLV_SafeIntegerForDictKey(data, @"total");
                NSUInteger step = PLV_SafeIntegerForDictKey(data, @"step");
                [weakSelf.delegate jsbridge_pageStatusChangeWithAutoId:autoId pageNumber:pageNumber totalPage:totalPage pptStep:step];
            }
        }
        responseCallback(nil);
    }];
}

- (void)registerPPTInputFunction {
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:@"toEditText" handler:^(NSDictionary *data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - toEditText with (response:%@)", data);
        
        if (data == nil ||
            data[@"color"] == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":@"toEditText",@"responseData":(data ?: @"responseData is invaild")};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - toEditText failed with 【%@】", __FUNCTION__ ,myError);
        } else {
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(jsbridge_documentInputWithText:textColor:)]) {
                NSString *inputTextString = PLV_SafeStringForDictKey(data, @"content");
                NSString *textColorString = PLV_SafeStringForDictKey(data, @"color");
                [weakSelf.delegate jsbridge_documentInputWithText:inputTextString textColor:textColorString];
            }
        }
    }];
}

- (void)registerWhiteImagesFunction {
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:@"whiteImages" handler:^(NSDictionary *data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - whiteImages with (response:%@)", data);
        
        BOOL success = NO;
        if (data && [data isKindOfClass:[NSString class]]) {
            NSString *jsonString = (NSString *)data;
            NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
            NSError *error;
            NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                                options:NSJSONReadingMutableContainers
                                                                  error:&error];
            if (!error && dic &&
                [dic isKindOfClass:[NSDictionary class]] && dic[@"base64"]) {
                NSString *originBase64String = PLV_SafeStringForDictKey(dic, @"base64");
                //进行首尾空字符串的处理
                originBase64String = [originBase64String stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
                //进行空字符串的处理
                originBase64String = [originBase64String stringByReplacingOccurrencesOfString:@"\r" withString:@""];
                //进行换行字符串的处理
                originBase64String = [originBase64String stringByReplacingOccurrencesOfString:@"\n" withString:@""];
                NSString *prefix = @"data:image/png;base64,";
                if ([originBase64String hasPrefix:prefix]) {
                    success = YES;
                    if (weakSelf.delegate &&
                        [weakSelf.delegate respondsToSelector:@selector(jsbridge_updateWhiteboardImageData:pageNumber:)]) {
                        NSString *imageString = [originBase64String substringFromIndex:prefix.length];
                        NSData *imageData = [[NSData alloc] initWithBase64EncodedString:imageString
                                                                                     options:NSDataBase64DecodingIgnoreUnknownCharacters];
                        NSInteger pageId = PLV_SafeIntegerForDictKey(dic, @"pageId");
                        [weakSelf.delegate jsbridge_updateWhiteboardImageData:imageData pageNumber:pageId];
                    }
                }
            }
        }
        
        if (!success) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":@"whiteImagesForIOS",@"responseData":(data ?: @"responseData is invaild")};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - whiteImages failed with 【%@】", __FUNCTION__ ,myError);
        }
    }];
}

- (void)registerPPTThumbnailFunction {
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:@"pptThumbnail" handler:^(NSDictionary *data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - PPTThumbnail");
        
        if (!data || data[@"autoId"] == 0) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":@"PPTThumbnail",@"responseData":(data ?: @"responseData is invaild")};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - PPTThumbnail failed with 【%@】", __FUNCTION__ ,myError);
        } else {
            if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(jsbridge_documentChangeWithAutoId:imageUrls:fileName:)]) {
                NSString *fileName = PLV_SafeStringForDictKey(data, @"fileName");
                NSArray *smallImages = PLV_SafeArraryForDictKey(data, @"smallImages");
                NSInteger autoId = PLV_SafeIntegerForDictKey(data, @"autoId");
                [weakSelf.delegate jsbridge_documentChangeWithAutoId:autoId imageUrls:smallImages fileName:fileName];
            }
        }
    }];
}

- (void)registerWhiteboardPPTZoomChangeFunction {
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:@"zoomChange" handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - zoomChange with (response:%@)", data);
        NSInteger zoomRatio = PLV_SafeIntegerForValue(data);
        if (!data || zoomRatio == 0) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":@"zoomChange",@"responseData":(data ?: @"responseData is invaild")};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - zoomChange failed with 【%@】", __FUNCTION__ ,myError);
        } else {
            if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(jsbridge_updateWhiteboardPPTZoomRatio:)]) {
                [weakSelf.delegate jsbridge_updateWhiteboardPPTZoomRatio:zoomRatio];
            }
        }
    }];
}

- (void)removePPTThumbnailFunction {
    [self.bridge removeHandler:@"pptThumbnail"];
}

#pragma mark Utils

// 将字典转化为 JSON 字符串
- (NSString *)jsonStringFromDictionary:(NSDictionary *)dict {
    if (![PLVFdUtil checkDictionaryUseable:dict]) {
        return nil;
    }
    
    if (![NSJSONSerialization isValidJSONObject:dict]) {
        return nil;
    }
    
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dict options:NSJSONWritingPrettyPrinted error:nil];
    return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
}

@end
