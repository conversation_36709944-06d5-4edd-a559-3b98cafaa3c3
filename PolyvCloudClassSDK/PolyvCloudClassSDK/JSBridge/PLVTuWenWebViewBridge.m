//
//  PLVTuWenWebViewBridge.m
//  PLVLiveScenesSDK
//
//  Created by Saky<PERSON> on 2023/6/5.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVTuWenWebViewBridge.h"

@implementation PLVTuWenWebViewBridge

#pragma mark - [ Life Cycle ]

- (instancetype)initBridgeWithWebView:(WKWebView *)webView webViewDelegate:(id<WKNavigationDelegate>)webViewDelegate {
    self = [super initBridgeWithWebView:webView webViewDelegate:webViewDelegate];
    if (self) {
        [self registerFunctions];
    }
    return self;
}

#pragma mark - [ Private Method ]

- (void)registerFunctions {
    [self registerGetParamsInfoFunction];
    [self registerCallAppEventFunction];
}

#pragma mark - Register JS Method
// 注册前端调用App的事件
- (void)registerCallAppEventFunction {
    NSString *jsEvent = @"callAppEvent";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (![PLVFdUtil checkDictionaryUseable:data]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed", __FUNCTION__, jsEvent);
        } else {
            if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvTuWenWebViewBridge:callAppEvent:)]) {
                [weakSelf.delegate plvTuWenWebViewBridge:weakSelf callAppEvent:data];
            }
        }
    }];
}

#pragma mark 数据交互
- (void)registerGetParamsInfoFunction {
    NSString *jsEvent = @"getNativeAppParamsInfo";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(getAPPInfoInTuWenWebViewBridge:)]) {
            NSDictionary *dict = [weakSelf.delegate getAPPInfoInTuWenWebViewBridge:weakSelf];
            NSString *jsonString = [weakSelf jsonStringFromDictionary:dict];
            responseCallback(jsonString);
        } else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVTuWenWebViewBridge - delegate not implement method:[getAPPInfoInTuWenWebViewBridge:]");
            responseCallback(nil);
        }
    }];
}

@end
