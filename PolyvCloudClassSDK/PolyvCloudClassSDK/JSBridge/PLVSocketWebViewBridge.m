//
//  PLVSocketWebViewBridge.m
//  PLVLiveScenesSDK
//
//  Created by Sakya on 2021/12/22.
//  Copyright © 2021 PLV. All rights reserved.
//

#import "PLVSocketWebViewBridge.h"
// 模块
#import "PLVWErrorManager.h"
#import "PLVConsoleLogger.h"
#import "PLVWLogReporterManager.h"
#import "PLVSocketManager.h"

@interface PLVSocketWebViewBridge ()<PLVSocketManagerProtocol>

@property (nonatomic, strong) WKWebView *webView;

/// 数据
@property (nonatomic, assign) BOOL socketLoginSuccessed; //socket 是否已经登录成功
@property (nonatomic, strong) PLVFWKWebViewJavascriptBridge *bridge;
@property (nonatomic, strong) NSMutableArray *socketEvents; //需要socket添加监听的event
@property (nonatomic, assign) float triviaCardTimeoutSec; //socket提交超时秒数
@property (nonatomic, assign) NSInteger triviaCardMaxRetryCount; //socket最大重试提交次数

@end

@implementation PLVSocketWebViewBridge {
    /// PLVSocketManager回调的执行队列
    dispatch_queue_t socketDelegateQueue;
}

#pragma mark - [ Life Cycle ]

- (instancetype)initBridgeWithWebView:(WKWebView *)webView webViewDelegate:(id<WKNavigationDelegate>)webViewDelegate {
    self = [super init];
    if (self) {
        _webView = webView;
        _socketLoginSuccessed = NO;
        
        _bridge = [PLVFWKWebViewJavascriptBridge bridgeForWebView:webView];
        [_bridge setWebViewDelegate:webViewDelegate];
        
        [self setup];
        [self registerContainerWebViewSendSocketEvent];
        [self registerInteractWebViewSendSocketEvent];
        [self registerAddSocketEvents];
    }
    return self;
}

#pragma mark - [ Public Method ]

- (void)setWebViewDelegate:(id<WKNavigationDelegate>)webViewDelegate {
    [_bridge setWebViewDelegate:webViewDelegate];
}

#pragma mark - Call JS Method

- (void)updateNativeAppParamsInfo:(NSDictionary *)appInfo {
    NSString *jsEvent = @"updateNativeAppParamsInfo";
    if (![PLVFdUtil checkDictionaryUseable:appInfo]) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent ,@"param":@"param is error"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVInteractWebViewBridge caller - %@:%@", jsEvent, appInfo);
        return;
    }
    
    NSString *jsonString = [self jsonStringFromDictionary:appInfo];
    [self.bridge callHandler:jsEvent data:jsonString responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, responseData);
    }];
}

- (void)updateChannelConfigInfo:(NSDictionary *)configInfo {
    NSString *jsEvent = @"updateChannelConfig";
    if (![PLVFdUtil checkDictionaryUseable:configInfo]) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent ,@"param":@"param is error"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVInteractWebViewBridge caller - %@:%@", jsEvent, configInfo);
        return;
    }
    
    NSString *jsonString = [self jsonStringFromDictionary:configInfo];
    [self.bridge callHandler:jsEvent data:jsonString responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, responseData);
    }];
}

- (void)callWebViewEvent:(NSDictionary *)event {
    NSString *jsEvent = @"appCallWebViewEvent";
    if (![PLVFdUtil checkDictionaryUseable:event]) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent ,@"param":@"param is error"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVInteractWebViewBridge caller - %@:%@", jsEvent, event);
        return;
    }
    
    NSString *jsonString = [self jsonStringFromDictionary:event];
    [self.bridge callHandler:jsEvent data:jsonString responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, responseData);
    }];
}

#pragma mark - [ Private Method ]

- (void)setup {
    self.triviaCardTimeoutSec = 5.0;
    self.triviaCardMaxRetryCount = 3;
    
    /// 添加 socket 事件监听
    socketDelegateQueue = dispatch_get_main_queue();
    [[PLVSocketManager sharedManager] addDelegate:self delegateQueue:socketDelegateQueue];
}

- (void)addObserveSocketEvents:(NSArray *)events {
    if (![PLVFdUtil checkArrayUseable:events]) {
        return;
    }
    
    if (!_socketLoginSuccessed) {
        self.socketEvents = [NSMutableArray arrayWithArray:events];
        return;
    }
    
    NSMutableArray *addSocketEvents = [NSMutableArray arrayWithCapacity:events.count];
    for (NSInteger index = 0; index < events.count; index ++) {
        NSString *event = events[index];
        if (![PLVFdUtil checkStringUseable:event] || [addSocketEvents containsObject:event]) {
            continue;
        }
        [[PLVSocketManager sharedManager] addObserveSocketEvent:event];
        [addSocketEvents addObject:event];
    }
    [self.socketEvents removeAllObjects];
}

#pragma mark Getter

- (NSMutableArray *)socketEvents {
    if (!_socketEvents) {
        _socketEvents = [NSMutableArray array];
    }
    return _socketEvents;
}

#pragma mark Call JS Method

- (void)registerContainerWebViewSendSocketEvent {
    NSString *jsEvent = @"sendSocketData";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (data == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is nil"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__ , jsEvent, myError);
        } else {
            [weakSelf sendSocketEventWithJsonObject:data];
        }
    }];
}

- (void)registerInteractWebViewSendSocketEvent {
    NSString *jsEvent = @"webViewSendSocketEvent";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        NSDictionary *dict = [weakSelf dictionaryFromJSONObject:data];
        if (![PLVFdUtil checkDictionaryUseable:dict]) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is error"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__, jsEvent, myError);
            [weakSelf submitSocketExceptionCompletionHandler:responseCallback];
        } else {
            NSString *event = PLV_SafeStringForDictKey(dict, @"event");
            NSString *content = PLV_SafeStringForDictKey(dict, @"value");
            [weakSelf emitSocketEvent:event content:content retryCount:0 completionHandler:responseCallback];
        }
    }];
}

- (void)registerAddSocketEvents {
    NSString *jsEvent = @"addObserveSocketEvent";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        NSDictionary *dict = [weakSelf dictionaryFromJSONObject:data];
        NSArray *events = PLV_SafeArraryForDictKey(dict, @"events");
        if (![PLVFdUtil checkDictionaryUseable:dict] ||
            ![PLVFdUtil checkArrayUseable:events]) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is error"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__, jsEvent, myError);
        } else {
            [weakSelf addObserveSocketEvents:events];
        }
    }];
}

#pragma mark Send Socket

- (void)sendSocketEventWithJsonObject:(id)jsonObject {
    NSDictionary *tempDict = [self dictionaryFromJSONObject:jsonObject];
    if ([PLVFdUtil checkDictionaryUseable:tempDict]) {
        NSString *socketDataString = PLV_SafeStringForDictKey(tempDict, @"socketData");
        NSDictionary *socketDataDict = [self dictionaryFromJSONObject:socketDataString];
        if (socketDataDict) {
            [[PLVSocketManager sharedManager] emitMessage:socketDataDict];
        }
    }
}

- (void)emitSocketEvent:(NSString *)event content:(NSString *)content retryCount:(NSInteger)retryCount completionHandler:(void (^)(id _Nonnull))completionHandler {
    __weak typeof(self) weakSelf = self;
    [[PLVSocketManager sharedManager] emitEvent:event content:content timeout:5 callback:^(NSArray * _Nonnull ackArray) {
        NSString *ack = ackArray.firstObject;
        if ([PLVFdUtil checkStringUseable:ack] && [ack isEqualToString:@"NO ACK"]) {
            __block NSInteger count = (retryCount > 0 ? retryCount : 1);
            if (count <= weakSelf.triviaCardMaxRetryCount) {
                count ++;
                [weakSelf emitSocketEvent:event content:content retryCount:retryCount completionHandler:completionHandler];
            } else {
                [weakSelf submitSocketExceptionCompletionHandler:completionHandler];
            }
        } else {
            completionHandler ? completionHandler(ackArray.firstObject) : nil;
        }
    }];
}

- (void)submitSocketExceptionCompletionHandler:(void (^)(id _Nonnull))completionHandler {
    completionHandler ? completionHandler(@"{\"code\":400}") : nil;
}

#pragma mark Forward Socket To JS

- (void)forwardSocketEvent:(NSString *)event content:(NSString *)content {
    NSString *jsEvent = @"forwardSocketEvent";
    if (![PLVFdUtil checkStringUseable:event] ||
        ![PLVFdUtil checkStringUseable:content]) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent, @"param":@"param is error"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVInteractWebViewBridge caller - %@:%@ content:%@", jsEvent, event, content);
        return;
    }
    
    NSDictionary *dict = @{@"event" : event,
                           @"value" : @"SOCKENTCONTENT"};
    NSString *jsonString = [self jsonStringFromDictionary:dict];
    // 格式化传给JS的socket消息数据结构
    NSString *socketJSONString = [jsonString stringByReplacingOccurrencesOfString:@"\"SOCKENTCONTENT\"" withString:content];
    [self.bridge callHandler:jsEvent data:socketJSONString responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, responseData);
    }];
}

- (void)joinSocketData:(NSString *)socketData {
    NSString *jsEvent = @"joinSocketData";
    if (!socketData ||
        ![socketData isKindOfClass:[NSString class]] ||
        socketData.length ==0) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent,@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    NSDictionary *param = @{@"socketData":socketData};
    [self.bridge callHandler:jsEvent data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (parma:%@, response:%@)", jsEvent, param, responseData);
    }];
}

#pragma mark PLVSocketManagerProtocol

- (void)socketMananger_didLoginSuccess:(NSString *)ackString {
    _socketLoginSuccessed = YES;
    if ([PLVFdUtil checkArrayUseable:self.socketEvents]) {
        [self addObserveSocketEvents:self.socketEvents];
    }
}

/// socket 接收到 "message" 事件
- (void)socketMananger_didReceiveMessage:(NSString *)subEvent
                                    json:(NSString *)jsonString
                              jsonObject:(id)object {
    
    NSDictionary *jsonDict = (NSDictionary *)object;
    if (![PLVFdUtil checkDictionaryUseable:jsonDict]) {
        return;
    }
    
    // 过滤前端不需要的事件
    if ([subEvent isEqualToString:@"LOGIN"] ||
       [subEvent isEqualToString:@"LOGOUT"] ||
       [subEvent isEqualToString:@"SPEAK"] ||
       [subEvent isEqualToString:@"CHAT_IMG"] ||
       [subEvent isEqualToString:@"REWARD"] ||
       [subEvent isEqualToString:@"customMessage"]) {
       return;
    }
    
    // webview 关心的事件需要转发给webview
    if ([subEvent isEqualToString:@"onSliceID"] ||
        [subEvent isEqualToString:@"onSliceDraw"] ||
        [subEvent isEqualToString:@"onSliceOpen"] ||
        [subEvent isEqualToString:@"onSliceClose"] ||
        [subEvent isEqualToString:@"onSliceControl"]) {
        [self joinSocketData:jsonString];
    }
    [self forwardSocketEvent:@"message" content:jsonString];
}

/// socket 接收到其它的事件
- (void)socketMananger_didReceiveEvent:(NSString *)event subEvent:(NSString *)subEvent json:(NSString *)jsonString jsonObject:(id)object {
    // 过滤前端不需要的事件
    if ([event isEqualToString:@"emotion"] ||
        [event isEqualToString:@"customMessage"]) {
        return;
    }
    [self forwardSocketEvent:event content:jsonString];
}

#pragma mark Utils

// 将字典转化为 JSON 字符串
- (NSString *)jsonStringFromDictionary:(NSDictionary *)dict {
    if (![PLVFdUtil checkDictionaryUseable:dict]) {
        return nil;
    }
    
    if (![NSJSONSerialization isValidJSONObject:dict]) {
        return nil;
    }
    
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dict options:NSJSONWritingPrettyPrinted error:nil];
    return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
}

// 将 JSON 数据转化为字典
- (NSDictionary *)dictionaryFromJSONObject:(id)jsonObject {
    if (!jsonObject) {
        return nil;
    }
    
    if ([PLVFdUtil checkDictionaryUseable:jsonObject]) {
        return (NSDictionary *)jsonObject;
    }
    
    NSData *jsonData = [jsonObject dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *dataDict = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
    return dataDict;
}

@end
