//
//  PLVStreamerInteractWebViewBridge.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2024/5/15.
//  Copyright © 2024 PLV. All rights reserved.
//

#import "PLVStreamerInteractWebViewBridge.h"

@implementation PLVStreamerInteractWebViewBridge

#pragma mark - [ Life Cycle ]

- (instancetype)initBridgeWithWebView:(WKWebView *)webView webViewDelegate:(id<WKNavigationDelegate>)webViewDelegate {
    self = [super initBridgeWithWebView:webView webViewDelegate:webViewDelegate];
    if (self) {
        [self registerFunctions];
    }
    return self;
}

#pragma mark - [ Private Method ]

- (void)registerFunctions {
    [self registerGetParamsInfoFunction];
    [self registerShowWebViewFunction];
    [self registerCloseWebViewFunction];
    [self registerCallAppEventFunction];
}

#pragma mark 原生视图控制

#pragma mark 数据交互
- (void)registerGetParamsInfoFunction {
    NSString *jsEvent = @"getNativeAppParamsInfo";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(getAPPInfoInStreamerInteractBridge:)]) {
            NSDictionary *dict = [weakSelf.delegate getAPPInfoInStreamerInteractBridge:weakSelf];
            NSString *jsonString = [weakSelf jsonStringFromDictionary:dict];
            responseCallback(jsonString);
        } else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVStreamerInteractWebViewBridge - delegate not implement method:[getAPPInfoInTuWenWebViewBridge:]");
            responseCallback(nil);
        }
    }];
}

- (void)registerShowWebViewFunction {
    NSString *jsEvent = @"showWebView";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvStreamerInteractBridgeShowWebView:)]) {
            [weakSelf.delegate plvStreamerInteractBridgeShowWebView:weakSelf];
        }
    }];
}

- (void)registerCloseWebViewFunction {
    NSString *jsEvent = @"closeWebView";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvStreamerInteractBridgeCloseWebView:)]) {
            [weakSelf.delegate plvStreamerInteractBridgeCloseWebView:weakSelf];
        }
    }];
}

// 注册前端调用App的事件
- (void)registerCallAppEventFunction {
    NSString *jsEvent = @"callAppEvent";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (![PLVFdUtil checkDictionaryUseable:data]) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is error"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__, jsEvent, myError);
        } else {
            if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvStreamerInteractBridge:callAppEvent:)]) {
                [weakSelf.delegate plvStreamerInteractBridge:weakSelf callAppEvent:data];
            }
        }
    }];
}

@end
