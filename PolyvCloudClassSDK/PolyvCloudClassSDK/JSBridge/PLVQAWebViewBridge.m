//
//  PLVQAWebViewBridge.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2024/2/27.
//  Copyright © 2024 PLV. All rights reserved.
//

#import "PLVQAWebViewBridge.h"

@implementation PLVQAWebViewBridge

#pragma mark - [ Life Cycle ]

- (instancetype)initBridgeWithWebView:(WKWebView *)webView webViewDelegate:(id<WKNavigationDelegate>)webViewDelegate {
    self = [super initBridgeWithWebView:webView webViewDelegate:webViewDelegate];
    if (self) {
        [self registerFunctions];
    }
    return self;
}

#pragma mark - [ Private Method ]

- (void)registerFunctions {
    [self registerGetParamsInfoFunction];
}

#pragma mark - Register JS Method
#pragma mark 数据交互
- (void)registerGetParamsInfoFunction {
    NSString *jsEvent = @"getNativeAppParamsInfo";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(getAPPInfoInQAWebViewBridge:)]) {
            NSDictionary *dict = [weakSelf.delegate getAPPInfoInQAWebViewBridge:weakSelf];
            NSString *jsonString = [weakSelf jsonStringFromDictionary:dict];
            responseCallback(jsonString);
        } else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVQAWebViewBridge - delegate not implement method:[getAPPInfoInQAWebViewBridge:]");
            responseCallback(nil);
        }
    }];
}

@end
