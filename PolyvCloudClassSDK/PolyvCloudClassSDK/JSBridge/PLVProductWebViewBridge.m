//
//  PLVProductWebViewBridge.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON> on 2022/5/16.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVProductWebViewBridge.h"

@implementation PLVProductWebViewBridge

#pragma mark - [ Life Cycle ]

- (instancetype)initBridgeWithWebView:(WKWebView *)webView webViewDelegate:(id<WKNavigationDelegate>)webViewDelegate {
    self = [super initBridgeWithWebView:webView webViewDelegate:webViewDelegate];
    if (self) {
        [self registerFunctions];
    }
    return self;
}

#pragma mark - [ Private Method ]

- (void)registerFunctions {
    [self registerGetParamsInfoFunction];
    [self registerClickProductButtonFunction];
    [self registerShowJobDetailFunction];
}

#pragma mark - Register JS Method

#pragma mark 数据交互

- (void)registerGetParamsInfoFunction {
    NSString *jsEvent = @"getNativeAppParamsInfo";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(getAPPInfoInProductWebViewBridge:)]) {
            NSDictionary *dict = [weakSelf.delegate getAPPInfoInProductWebViewBridge:weakSelf];
            NSString *jsonString = [weakSelf jsonStringFromDictionary:dict];
            responseCallback(jsonString);
        } else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"getAPPInfoInProductWebViewBridge - delegate not implement method:[getAPPInfoInWebViewBridge:]");
            responseCallback(nil);
        }
    }];
}

- (void)registerClickProductButtonFunction {
    NSString *jsEvent = @"clickProductButton";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvProductWebViewBridge:clickProductButtonWithJSONObject:)]) {
            [self.delegate plvProductWebViewBridge:self clickProductButtonWithJSONObject:data];
        }
    }];
}

- (void)registerShowJobDetailFunction {
    NSString *jsEvent = @"showJobDetail";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvProductWebViewBridge:showJobDetailWithJSONObject:)]) {
            [weakSelf.delegate plvProductWebViewBridge:weakSelf showJobDetailWithJSONObject:data];
        }
    }];
}

@end
