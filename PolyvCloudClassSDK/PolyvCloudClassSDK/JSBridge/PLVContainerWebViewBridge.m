//
//  PLVContainerWebViewBridge.m
//  PLVLiveScenesSDK
//
//  Created by lijingtong on 2021/7/13.
//  Copyright © 2021 PLV. All rights reserved.
//

#import "PLVContainerWebViewBridge.h"

// 模块
#import "PLVWErrorManager.h"
#import "PLVWLogReporterManager.h"
#import "PLVConsoleLogger.h"
#import "PLVWErrorManager.h"
#import "PLVSocketManager.h"

static NSString *kContainerApplianceTypeFreeLine = @"freeLine";
static NSString *kContainerApplianceTypeArrow = @"arrow";
static NSString *kContainerApplianceTypeChoice = @"choice";
static NSString *kContainerApplianceTypeEraser = @"eraser";
static NSString *kContainerApplianceTypeText = @"text";
static NSString *kContainerApplianceTypeMove = @"move";

@interface PLVContainerWebViewBridge()

@end

@implementation PLVContainerWebViewBridge

#pragma mark - [ Life Cycle ]
- (instancetype)initBridgeWithWebview:(WKWebView *)webView webviewDelegate:(id<WKNavigationDelegate>)webViewDelegate {
    return [self initBridgeWithWebView:webView webViewDelegate:webViewDelegate];
}

#pragma mark - [ Public ]

#pragma mark Call JS Method (native -> js)

- (void)changeApplianceType:(PLVContainerApplianceType)type {
    NSString *jsEvent = @"changeAppliance";
    NSString *appliance = [self applianceTypeStringWithType:type];
    NSDictionary *param = @{@"appliance":appliance};
    [self.bridge callHandler:jsEvent data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (parma:%@, response:%@)", jsEvent, param, responseData);
    }];
}

- (void)changeFontSize:(NSUInteger)fontSize {
    NSString *jsEvent = @"changeFontSize";
    if (fontSize < 0) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent,@"fontSize":@(fontSize)};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    
    NSDictionary *param = @{@"fontSize":@(fontSize)};
    [self.bridge callHandler:jsEvent data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (parma:%@, response:%@)", jsEvent, param, responseData);
    }];
}

- (void)changeLineWidth:(NSUInteger)width {
    NSString *jsEvent = @"changeLineWidth";
    if (width < 0) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent,@"width":@(width)};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    
    NSDictionary *param = @{@"lineWidth":@(width)};
    [self.bridge callHandler:jsEvent data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (parma:%@, response:%@)", jsEvent, param, responseData);
    }];
}

- (void)changeStrokeHexColor:(NSString *)hexColor {
    NSString *jsEvent = @"changeStrokeStyle";
    if (!hexColor ||
        ![hexColor isKindOfClass:[NSString class]] ||
        hexColor.length == 0) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent,@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    
    NSDictionary *param = @{@"strokeStyle":hexColor};
    [self.bridge callHandler:jsEvent data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (parma:%@, response:%@)", jsEvent, param, responseData);
    }];
}

- (void)closePptWithAutoId:(NSUInteger)autoId {
    NSString *jsEvent = @"closePpt";
    if (autoId < 0) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent,@"autoId":@(autoId)};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    
    NSDictionary *param = @{@"autoId":@(autoId)};
    [self.bridge callHandler:jsEvent data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (parma:%@, response:%@)", jsEvent, param, responseData);
    }];
}

- (void)doClear {
    NSString *jsEvent = @"doClear";
    [self.bridge callHandler:jsEvent data:nil responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (response:%@)", jsEvent, responseData);
    }];
}

- (void)doRedo {
    NSString *jsEvent = @"doRedo";
    [self.bridge callHandler:jsEvent data:nil responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (response:%@)", jsEvent, responseData);
    }];
}

- (void)doUndo {
    NSString *jsEvent = @"doUndo";
    [self.bridge callHandler:jsEvent data:nil responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (response:%@)", jsEvent, responseData);
    }];
}

- (void)doDelete {
    NSString *jsEvent = @"doDelete";
    [self.bridge callHandler:jsEvent data:nil responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (response:%@)", jsEvent, responseData);
    }];
}

- (void)finishEditText:(NSString *)text {
    NSString *jsEvent = @"finishEditText";
    if (!text ||
        ![text isKindOfClass:[NSString class]] ||
        text.length ==0) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent,@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    
    NSDictionary *param = @{@"content":text};
    [self.bridge callHandler:jsEvent data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (parma:%@, response:%@)", jsEvent, param, responseData);
    }];
}

- (void)cancelEditText {
    NSString *jsEvent = @"cancelEditText";
    [self.bridge callHandler:jsEvent data:nil responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (response:%@)", jsEvent, responseData);
    }];
}

- (void)givePaintBrushAuth:(PLVContainerResponseCallback)callback {
    NSString *jsEvent = @"givePaintBrushAuth";
    [self.bridge callHandler:jsEvent data:nil responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (response:%@)", jsEvent, responseData);
        if (callback) {
            callback(responseData);
        }
    }];
}

- (void)joinSocketData:(NSString *)socketData {
    NSString *jsEvent = @"joinSocketData";
    if (!socketData ||
        ![socketData isKindOfClass:[NSString class]] ||
        socketData.length ==0) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent,@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    NSDictionary *param = @{@"socketData":socketData};
    [self.bridge callHandler:jsEvent data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (parma:%@, response:%@)", jsEvent, param, responseData);
    }];
}

- (void)openPptWithAutoId:(NSUInteger)autoId {
    NSString *jsEvent = @"openPpt";
    if (autoId < 0) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent,@"autoId":@(autoId)};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    
    NSDictionary *param = @{@"autoId":@(autoId)};
    [self.bridge callHandler:jsEvent data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (parma:%@, response:%@)", jsEvent, param, responseData);
    }];
}

- (void)operateContainerWithContainerId:(NSString *)containerId close:(BOOL)close {
    NSString *jsEvent = @"operateContainer";
    if (!containerId ||
        ![containerId isKindOfClass:[NSString class]] ||
        containerId.length == 0) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent,@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    
    NSString *operateType = close ? @"close" : @"open";
    NSDictionary *param = @{@"containerId":containerId, @"operateType":operateType};
    [self.bridge callHandler:jsEvent data:param responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (parma:%@, response:%@)", jsEvent, param, responseData);
    }];
}

- (void)removePaintBrushAuth:(PLVContainerResponseCallback)callback {
    NSString *jsEvent = @"removePaintBrushAuth";
    [self.bridge callHandler:jsEvent data:nil responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (response:%@)", jsEvent, responseData);
        if (callback) {
            callback(responseData);
        }
    }];
}

- (void)resetZoom {
    NSString *jsEvent = @"resetZoom";
    [self.bridge callHandler:jsEvent data:nil responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (response:%@)", jsEvent, responseData);
    }];
}

- (void)setOrRemoveGroupLeader:(BOOL)isLeader callback:(PLVContainerResponseCallback)callback {
    NSString *jsEvent = @"setGroupLeader";
    [self.bridge callHandler:jsEvent data:@{@"isLeader":@(isLeader)} responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (response:%@)", jsEvent, responseData);
        if (callback) {
            callback(responseData);
        }
    }];
}

- (void)switchRoomWithAckData:(NSDictionary *)ackData datacallback:(PLVContainerResponseCallback)callback {
    NSString *jsEvent = @"switchRoom";
    
    NSMutableDictionary *dicM = [NSMutableDictionary dictionary];
    NSString *roomId = PLV_SafeStringForDictKey(ackData, @"id");
  
    if (![PLVFdUtil checkDictionaryUseable:ackData] ||
        ![PLVFdUtil checkStringUseable:roomId]) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent,@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    
    
    NSString *sessiondId = PLV_SafeStringForDictKey(ackData, @"sessionId");
    NSString *fullScreenPPT = PLV_SafeStringForDictKey(ackData, @"fullScreenPPT");
    
    NSArray *pptListArray = PLV_SafeArraryForDictKey(ackData, @"pptList");
    NSMutableArray *pptListM = [NSMutableArray array];
    if ([PLVFdUtil checkArrayUseable:pptListArray]) {
        for (NSDictionary *dict in pptListArray) {
            if ([PLVFdUtil checkDictionaryUseable:dict]) {
                    [pptListM addObject:dict];
            }
        }
    }
    
    NSArray *pptStorageListArray = PLV_SafeArraryForDictKey(ackData, @"pptStorageList");
    NSMutableArray *pptStorageListArrayM = [NSMutableArray array];
    if ([PLVFdUtil checkArrayUseable:pptStorageListArray]) {
        for (NSDictionary *dict in pptStorageListArray) {
            if ([PLVFdUtil checkDictionaryUseable:dict]) {
                    [pptStorageListArrayM addObject:dict];
            }
        }
    }
    // 设置数据，只设置数值有效的字段。
    
    plv_dict_set(dicM, @"roomId", roomId);
    
    if ([PLVFdUtil checkStringUseable:sessiondId]) {
        plv_dict_set(dicM, @"sessionId", sessiondId);
    }
    
    if ([PLVFdUtil checkArrayUseable:pptListM]) {
        plv_dict_set(dicM, @"pptList", pptListM);
    }
    
    if ([PLVFdUtil checkArrayUseable:pptStorageListArrayM]) {
        plv_dict_set(dicM, @"pptStorageList", pptStorageListArrayM);
    }
    
    if ([PLVFdUtil checkStringUseable:fullScreenPPT]) {
        plv_dict_set(dicM, @"fullScreenPPT", fullScreenPPT);
    }
    
    if ([ackData.allKeys containsObject:@"zoom"]) {
        plv_dict_set(dicM, @"zoom", @(PLV_SafeFloatForDictKey(ackData, @"zoom")));
    }
    
    if ([ackData.allKeys containsObject:@"top"]) {
        plv_dict_set(dicM, @"top", @(PLV_SafeFloatForDictKey(ackData, @"top")));
    }
    
    if ([ackData.allKeys containsObject:@"left"]) {
        plv_dict_set(dicM, @"left", @(PLV_SafeFloatForDictKey(ackData, @"left")));
    }
    
    if ([ackData.allKeys containsObject:@"width"]) {
        plv_dict_set(dicM, @"width", @(PLV_SafeFloatForDictKey(ackData, @"width")));
    }
    
    if ([ackData.allKeys containsObject:@"height"]) {
        plv_dict_set(dicM, @"height", @(PLV_SafeFloatForDictKey(ackData, @"height")));
    }
    
    if (![PLVFdUtil checkDictionaryUseable:dicM]) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":jsEvent,@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    
    [self.bridge callHandler:jsEvent data:dicM responseCallback:^(id responseData) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller '%@' with (response:%@)", jsEvent, responseData);
        if (callback) {
            callback(responseData);
        }
    }];
    
}


#pragma mark Register JS Method (js -> native)

- (void)registerRefreshMinimizeContainerData {
    __weak typeof(self) weakSelf = self;
    NSString *jsEvent = @"refreshMinimizeContainerData";
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        if (data == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is nil"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__ , jsEvent, myError);
        } else {
            PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
            
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(containerWebViewBridge:didRefreshMinimizeContainerDataWithJsonObject:)]) {
                [weakSelf.delegate containerWebViewBridge:weakSelf didRefreshMinimizeContainerDataWithJsonObject:data];
            } else {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVContainerWebViewBridge - delegate not implement method:[containerWebViewBridge:didRefreshMinimizeContainerDataWithJsonObject:]");
            }
        }
    }];
}

- (void)registerRefreshPptContainerTotal {
    NSString *jsEvent = @"refreshPptContainerTotal";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        if (data == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is nil"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__ , jsEvent, myError);
        } else {
            PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(containerWebViewBridge:didRefreshPptContainerTotalWithJsonObject:)]) {
                [weakSelf.delegate containerWebViewBridge:weakSelf didRefreshPptContainerTotalWithJsonObject:data];
            } else {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVContainerWebViewBridge - delegate not implement method:[containerWebViewBridge:didRefreshPptContainerTotalWithJsonObject:]");
            }
        }
    }];
}

- (void)registerSendSocketData {
    NSString *jsEvent = @"sendSocketData";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        if (data == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is nil"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__ , jsEvent, myError);
        } else {
            PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(containerWebViewBridge:didSendSocketEventWithJsonObject:)]) {
                [weakSelf.delegate containerWebViewBridge:weakSelf didSendSocketEventWithJsonObject:data];
            } else {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVContainerWebViewBridge - delegate not implement method:[containerWebViewBridge:didSendSocketEventWithJsonObject:]");
            }
        }
    }];
}

- (void)registerStartEditText {
    NSString *jsEvent = @"startEditText";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        if (data == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is nil"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__ , jsEvent, myError);
        } else {
            PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(containerWebViewBridge:willStartEditTextWithJsonObject:)]) {
                [weakSelf.delegate containerWebViewBridge:weakSelf willStartEditTextWithJsonObject:data];
            } else {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVContainerWebViewBridge - delegate not implement method:[containerWebViewBridge:willStartEditTextWithJsonObject:]");
            }
        }
    }];
}

- (void)registerToggleOperationStatus {
    NSString *jsEvent = @"toggleOperationStatus";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        if (data == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is nil"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__ , jsEvent, myError);
        } else {
            PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(containerWebViewBridge:didRefreshBrushToolStatusWithJsonObject:)]) {
                [weakSelf.delegate containerWebViewBridge:weakSelf didRefreshBrushToolStatusWithJsonObject:data];
            } else {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVContainerWebViewBridge - delegate not implement method:[containerWebViewBridge:didSendSocketEventWithJsonObject:]");
            }
        }
    }];
}

- (void)registerChangeApplianceType {
    NSString *jsEvent = @"changeAppliance";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        if (data == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is nil"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__ , jsEvent, myError);
        } else {
            PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
            
            NSDictionary *dict = [weakSelf jsonDictWithJsonObject:data];
            NSString *appliance = PLV_SafeStringForDictKey(dict, @"appliance");
            PLVContainerApplianceType type = [weakSelf applianceTypeWithTypeString:appliance];
            
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(containerWebViewBridge:didChangeApplianceType:)]) {
                [weakSelf.delegate containerWebViewBridge:weakSelf didChangeApplianceType:type];
            } else {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVContainerWebViewBridge - delegate not implement method:[containerWebViewBridge:didChangeApplianceType:]");
            }
        }
    }];
}

- (void)registerChangeStrokeHexColor {
    NSString *jsEvent = @"changeStrokeStyle";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        if (data == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is nil"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__ , jsEvent, myError);
        } else {
            PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
            
            NSDictionary *dict = [weakSelf jsonDictWithJsonObject:data];
            NSString *strokeStyle = PLV_SafeStringForDictKey(dict, @"strokeStyle");
          
            if (![PLVFdUtil checkStringUseable:strokeStyle]) {
                NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
                NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is nil"};
                [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
                
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__ , jsEvent, myError);
                return;
            } else {
                if (weakSelf.delegate &&
                    [weakSelf.delegate respondsToSelector:@selector(containerWebViewBridge:didChangeStrokeHexColor:)]) {
                    [weakSelf.delegate containerWebViewBridge:weakSelf didChangeStrokeHexColor:strokeStyle];
                } else {
                    PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVContainerWebViewBridge - delegate not implement method:[containerWebViewBridge:didChangeStrokeHexColor:]");
                }
            }
        }
    }];
}

- (void)registerChangeFontSize {
    NSString *jsEvent = @"changeFontSize";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        if (data == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is nil"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__ , jsEvent, myError);
        } else {
            PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
            
            NSDictionary *dict = [weakSelf jsonDictWithJsonObject:data];
            CGFloat fontSize = PLV_SafeFloatForDictKey(dict, @"fontSize");
            
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(containerWebViewBridge:didChangeFontSize:)]) {
                [weakSelf.delegate containerWebViewBridge:weakSelf didChangeFontSize:fontSize];
            } else {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVContainerWebViewBridge - delegate not implement method:[containerWebViewBridge:didChangeFontSize:]");
            }
        }
    }];
}

- (void)registerChangeLineWidth {
    NSString *jsEvent = @"changeLineWidth";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        if (data == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is nil"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__ , jsEvent, myError);
        } else {
            PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
            
            NSDictionary *dict = [weakSelf jsonDictWithJsonObject:data];
            CGFloat lineWidth = PLV_SafeFloatForDictKey(dict, @"lineWidth");
            
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(containerWebViewBridge:didChangeLineWidth:)]) {
                [weakSelf.delegate containerWebViewBridge:weakSelf didChangeLineWidth:lineWidth];
            } else {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVContainerWebViewBridge - delegate not implement method:[containerWebViewBridge:didChangeLineWidth:]");
            }
        }
    }];
}

- (void)registerZoomPercenChange {
    NSString *jsEvent = @"zoomPercenChange";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        if (data == nil) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is nil"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__ , jsEvent, myError);
        } else {
            PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
            
            NSDictionary *dict = [weakSelf jsonDictWithJsonObject:data];
            CGFloat zoomPercen = PLV_SafeFloatForDictKey(dict, @"zoomPercen");
            
            if (weakSelf.delegate &&
                [weakSelf.delegate respondsToSelector:@selector(containerWebViewBridge:didChangeZoomPercent:)]) {
                [weakSelf.delegate containerWebViewBridge:weakSelf didChangeZoomPercent:zoomPercen];
            } else {
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVContainerWebViewBridge - delegate not implement method:[containerWebViewBridge:didChangeZoomPercent:]");
            }
        }
    }];
}
#pragma mark - [ native -> socket]
#pragma mark 讲师专属方法，画笔权限授权、取消授权，需要老师发送socket消息

- (void)setPaintBrushAuthWithUserId:(NSString *)userId sessionId:(NSString *)sessionId{
    NSString *EVENT = @"TEACHER_SET_PERMISSION";
    if (![PLVFdUtil checkStringUseable:userId] ||
        ![PLVFdUtil checkStringUseable:sessionId]) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":EVENT,@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    
    [[PLVSocketManager sharedManager] emitPermissionMessageWithUserId:userId type:PLVSocketPermissionTypePaint status:YES ];
}

- (void)removePaintBrushAuthWithUserId:(NSString *)userId {
    NSString *EVENT = @"TEACHER_SET_PERMISSION";
    if (![PLVFdUtil checkStringUseable:userId]) {
        NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeJS_ParameterError];
        NSDictionary *errorInformation = @{@"method":EVENT,@"param":@"param is nil"};
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s failed with 【%@】", __FUNCTION__ ,myError);
        return;
    }
    
    [[PLVSocketManager sharedManager] emitPermissionMessageWithUserId:userId type:PLVSocketPermissionTypePaint status:NO];
}

#pragma mark - [ Private Method ]

- (PLVContainerApplianceType)applianceTypeWithTypeString:(NSString *)typeString {
    if (![PLVFdUtil checkStringUseable:typeString]) {
        return PLVContainerApplianceTypeUnknown;
    }
    if ([typeString isEqualToString:kContainerApplianceTypeFreeLine]) {
        return PLVContainerApplianceTypeFreeLine;
    } else if ([typeString isEqualToString:kContainerApplianceTypeArrow]) {
        return PLVContainerApplianceTypeArrow;
    } else if ([typeString isEqualToString:kContainerApplianceTypeChoice]) {
        return PLVContainerApplianceTypeChoice;
    } else if ([typeString isEqualToString:kContainerApplianceTypeEraser]){
        return PLVContainerApplianceTypeEraser;
    } else if ([typeString isEqualToString:kContainerApplianceTypeText]) {
        return PLVContainerApplianceTypeText;
    } else if ([typeString isEqualToString:kContainerApplianceTypeMove]) {
        return PLVContainerApplianceTypeMove;
    }else {
        return PLVContainerApplianceTypeUnknown;
    }
}

- (NSString *)applianceTypeStringWithType:(PLVContainerApplianceType)type {
    NSString *applianceTypeString;
    switch (type) {
        case PLVContainerApplianceTypeFreeLine:
            applianceTypeString = @"freeLine";
            break;
        case PLVContainerApplianceTypeArrow:
            applianceTypeString = @"arrow";
            break;
        case PLVContainerApplianceTypeChoice:
            applianceTypeString = @"choice";
            break;
        case PLVContainerApplianceTypeEraser:
            applianceTypeString = @"eraser";
            break;
        case PLVContainerApplianceTypeText:
            applianceTypeString = @"text";
            break;
        case PLVContainerApplianceTypeMove:
            applianceTypeString = @"move";
            break;
        default:
            applianceTypeString = @"";
            break;
    }
    return applianceTypeString;
}

- (NSDictionary *)jsonDictWithJsonObject:(id)jsonObject {
    NSDictionary *jsonDict = nil;
    if (jsonObject) {
        if ([jsonObject isKindOfClass:[NSString class]]) {
            NSData *data = [jsonObject dataUsingEncoding:NSUTF8StringEncoding];
            jsonDict = [NSJSONSerialization JSONObjectWithData:data options:0 error:nil];
        } else if ([jsonObject isKindOfClass:[NSDictionary class]]) {
            jsonDict = (NSDictionary *)jsonObject;
        }
    }
    return jsonDict;
}

@end
