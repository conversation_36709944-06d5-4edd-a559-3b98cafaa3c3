//
//  PLVInteractWebViewBridge.m
//  PLVLiveScenesSDK
//
//  Created by Saky<PERSON> on 2021/12/14.
//  Copyright © 2021 PLV. All rights reserved.
//

#import "PLVInteractWebViewBridge.h"
#import "PLVWErrorManager.h"
#import "PLVConsoleLogger.h"
#import "PLVWLogReporterManager.h"

@implementation PLVInteractWebViewBridge

#pragma mark - [ Life Cycle ]

- (instancetype)initBridgeWithWebView:(WKWebView *)webView webViewDelegate:(id<WKNavigationDelegate>)webViewDelegate {
    self = [super initBridgeWithWebView:webView webViewDelegate:webViewDelegate];
    if (self) {
        [self registerFunctions];
    }
    return self;
}

#pragma mark - [ Private Method ]

- (void)registerFunctions {
    [self registerShowWebViewFunction];
    [self registerInitWebViewFinishFunction];
    [self registerCloseWebViewFunction];
    [self registerLockToPortraitFunction];
    [self registerLinkClickFunction];
    [self registerGetParamsInfoFunction];
    [self registerUpdateAppStatusFunction];
    [self registerCallAppEventFunction];
    [self registerGetInteractInfoFunction];
    [self registerCardClickProductButtonFunction];
    [self registerWelfareLotteryCommentSuccessFunction];
    [self registerWelfareLotteryEntranceDataChangeFunction];
    [self registerNotCheckInTime];
}

#pragma mark - Register JS Method

#pragma mark 原生视图控制

- (void)registerShowWebViewFunction {
    NSString *jsEvent = @"showWebView";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvInteractWebViewBridgeShowWebView:)]) {
            [weakSelf.delegate plvInteractWebViewBridgeShowWebView:weakSelf];
        }
    }];
}

// 注册webView加载完毕的方法
- (void)registerInitWebViewFinishFunction {
    NSString *jsEvent = @"initWebView";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvInteractWebViewBridgeWebViewDidFinish:)]) {
            [weakSelf.delegate plvInteractWebViewBridgeWebViewDidFinish:weakSelf];
        }
    }];
}

- (void)registerCloseWebViewFunction {
    NSString *jsEvent = @"closeWebView";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvInteractWebViewBridgeCloseWebView:)]) {
            [weakSelf.delegate plvInteractWebViewBridgeCloseWebView:weakSelf];
        }
    }];
}

// 屏幕锁定
- (void)registerLockToPortraitFunction {
    NSString *jsEvent = @"lockToPortrait";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller- %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvInteractWebViewBridgeLockPortraitScreen:)]) {
            [weakSelf.delegate plvInteractWebViewBridgeLockPortraitScreen:weakSelf];
        }
    }];
}

// 打开外链
- (void)registerLinkClickFunction {
    NSString *jsEvent = @"linkClick";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (![PLVFdUtil checkStringUseable:data]) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is error"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__, jsEvent, myError);
        } else {
            if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvInteractWebViewBridge:openLink:)]) {
                [weakSelf.delegate plvInteractWebViewBridge:weakSelf openLink:data];
            }
        }
    }];
}

// 通知APP状态更新
- (void)registerUpdateAppStatusFunction {
    NSString *jsEvent = @"webViewUpdateAppStatus";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (!data) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is error"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__, jsEvent, myError);
        } else {
            if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvInteractWebViewBridge:updateAppStatuWithJSONObject:)]) {
                [weakSelf.delegate plvInteractWebViewBridge:weakSelf updateAppStatuWithJSONObject:data];
            }
        }
    }];
}

// 注册前端调用App的事件
- (void)registerCallAppEventFunction {
    NSString *jsEvent = @"callAppEvent";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (![PLVFdUtil checkDictionaryUseable:data]) {
            NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodeJS_DataError];
            NSDictionary *errorInformation = @{@"method":jsEvent,@"responseData":@"responseData is error"};
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:errorInformation];
            
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"%s JSBridge caller - %@ failed with 【%@】", __FUNCTION__, jsEvent, myError);
        } else {
            if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvInteractWebViewBridge:callAppEvent:)]) {
                [weakSelf.delegate plvInteractWebViewBridge:weakSelf callAppEvent:data];
            }
        }
    }];
}

- (void)registerCardClickProductButtonFunction {
    NSString *jsEvent = @"clickProductButton";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvInteractWebViewBridge:bigCardClickProductButtonWithJSONObject:)]) {
            [weakSelf.delegate plvInteractWebViewBridge:weakSelf bigCardClickProductButtonWithJSONObject:data];
        }
    }];
}

- (void)registerWelfareLotteryEntranceDataChangeFunction {
    NSString *jsEvent = @"welfareLotteryEntranceDataChange";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvInteractWebViewBridge:welfareLotteryEntranceDataChangeWithJSONObject:)]) {
            [weakSelf.delegate plvInteractWebViewBridge:weakSelf welfareLotteryEntranceDataChangeWithJSONObject:data];
        }
    }];
}

/// 强制签到：未在规定时间内签到，退出直播间
- (void)registerNotCheckInTime{
    NSString *jsEvent = @"notCheckIn";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvInteractWebViewBridge:notCheckInWithJSONObject:)]) {
            [weakSelf.delegate plvInteractWebViewBridge:weakSelf notCheckInWithJSONObject:data];
        }
    }];
}

- (void)registerWelfareLotteryCommentSuccessFunction {
    NSString *jsEvent = @"welfareLotteryCommentSuccess";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(plvInteractWebViewBridge:welfareLotteryCommentSuccessWithJSONObject:)]) {
            [weakSelf.delegate plvInteractWebViewBridge:weakSelf welfareLotteryCommentSuccessWithJSONObject:data];
        }
    }];
}

#pragma mark 数据交互

// 注册JS获取APP字段方法
- (void)registerGetParamsInfoFunction {
    NSString *jsEvent = @"getNativeAppParamsInfo";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(getAPPInfoInInteractWebViewBridge:)]) {
            NSDictionary *dict = [weakSelf.delegate getAPPInfoInInteractWebViewBridge:weakSelf];
            NSString *jsonString = [weakSelf jsonStringFromDictionary:dict];
            responseCallback(jsonString);
        } else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVInteractWebViewBridge - delegate not implement method:[getAPPInfoInWebViewBridge:]");
            responseCallback(nil);
        }
    }];
}

- (void)registerGetInteractInfoFunction {
    NSString *jsEvent = @"getInteractInfo";
    __weak typeof(self) weakSelf = self;
    [self.bridge registerHandler:jsEvent handler:^(id data, WVJBResponseCallback responseCallback) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeJSBridge, @"JSBridge caller - %@ with (response:%@)", jsEvent, data);
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(getInteractInfoInInteractWebViewBridge:)]) {
            NSDictionary *dict = [weakSelf.delegate getInteractInfoInInteractWebViewBridge:weakSelf];
            NSString *jsonString = [weakSelf jsonStringFromDictionary:dict];
            responseCallback(jsonString);
        } else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeJSBridge, @"PLVInteractWebViewBridge - delegate not implement method:[getInteractInfoInInteractWebViewBridge:]");
            responseCallback(nil);
        }
    }];
}

@end
