//
//  PLVPlaybackCacheManager.m
//  PLVLiveScenesSDK
//
//  Created by junotang on 2022/5/24.
//  Copyright © 2022 PLV. All rights reserved.
//

#import "PLVPlaybackCacheManager.h"
#import "PLVLivePrivateAPI.h"
#import "PLVDownloadDatabaseManager+Playback.h"
#import "PLVDownloadPathManager.h"
#import "PLVDownloadManager.h"
#import "PLVConsoleLogger.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVWErrorManager.h"
#import "PLVWLogReporterManager.h"
#import "PLVDownloadTaskInfo+PrivateExtension.h"

@implementation PLVPlaybackCacheManager

+ (void)asynGetPlaybackPlayerCacheState:(NSString *)fileId completion:(void (^)(PLVPlaybackVideoCacheState))completion {
    if (!completion) {
        return;
    }
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        completion([self stateOfPlaybackPlayerCacheState:fileId]);
    });
}

+ (PLVPlaybackVideoCacheState)stateOfPlaybackPlayerCacheState:(NSString *)fileId {
    if (![PLVFdUtil checkStringUseable:fileId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"fileId is invalid.");
        return PLVPlaybackVideoCacheStateUnknonwn;
    }
    
    PLVDownloadPlaybackTaskInfo *playbackTaskInfo = [[PLVDownloadDatabaseManager shareManager] checkAndGetPlaybackTaskInfoWithFileId:fileId];
    if (playbackTaskInfo) {
        if (playbackTaskInfo.state == PLVDownloadStateSuccess) {
            return PLVPlaybackVideoCacheStateLocal;
        } else {
            return PLVPlaybackVideoCacheStateDownloadQueue;
        }
    } else {
        return PLVPlaybackVideoCacheStateNone;
    }
}

+ (void)enqueueDownloadQueueWithVideoPoolId:(NSString *)videoPoolId channelId:(NSString *)channelId completion:(void (^)(NSError *))completion {
    if (![PLVFdUtil checkStringUseable:videoPoolId]) {
        [self reportErrorCompletion:completion withErrorCode:PLVFDownloadErrorCodeGetVideoInfo_videoIdError description:@"videoPoolId is empty."];
        return;
    }
    
    PLVDownloadPlaybackTaskInfo *playbackTaskInfo = [[PLVDownloadDatabaseManager shareManager] checkAndGetPlaybackTaskInfoWithVideoPoolId:videoPoolId];
    if (playbackTaskInfo) {
        if (playbackTaskInfo.state == PLVDownloadStateSuccess) {
            [self reportErrorCompletion:completion withErrorCode:PLVFDownloadErrorCodeDownloadFile_LocalExist description:@"fileId already exists in the local."];
        } else {
            [self reportErrorCompletion:completion withErrorCode:PLVFDownloadErrorCodeDownloadFile_QueueExist description:@"fileId already exist in the download queue."];
        }
    }
    else {
        [PLVLivePrivateAPI loadVideoInfoWithVid:videoPoolId channelId:channelId completion:^(NSDictionary * _Nonnull data) {
            PLVPlaybackVideoInfoModel *videoInfoModel = [PLVPlaybackVideoInfoModel playbackVideoInfoModelWithJsonDict:data];
            
            if (videoInfoModel.playbackCacheEnabled) {
                PLVDownloadPlaybackTaskInfo *taskInfo = [self toDownloadPlaybackTaskInfo:videoInfoModel];
                [[PLVDownloadManager shareManager] addDownloadTaskWith:taskInfo];
                [[PLVDownloadManager shareManager] startDownloadWith:taskInfo];
                completion(nil);
            }
            else {
                NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulDownload code:PLVFDownloadErrorCodeGetVideoInfo_videoInfoError];
                completion(failError);
            }
        } failure:^(NSError * _Nonnull error) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"%s %@",__FUNCTION__,error);
            [self reportErrorCompletion:completion withErrorCode:PLVFDownloadErrorCodeGetVideoInfo_videoInfoError description:error.localizedDescription];
        }];
    }
}

+ (void)enqueueDownloadQueueWithRecordFileId:(NSString *)fileId
                                   channelId:(NSString *)channelId
                                  completion:(void (^)(NSError *error))completion {
    if (![PLVFdUtil checkStringUseable:fileId]) {
        [self reportErrorCompletion:completion withErrorCode:PLVFDownloadErrorCodeGetVideoInfo_videoIdError description:@"fileId is empty."];
        return;
    }
    
    PLVDownloadPlaybackTaskInfo *playbackTaskInfo = [[PLVDownloadDatabaseManager shareManager] checkAndGetPlaybackTaskInfoWithFileId:fileId];
    if (playbackTaskInfo) {
        if (playbackTaskInfo.state == PLVDownloadStateSuccess) {
            [self reportErrorCompletion:completion withErrorCode:PLVFDownloadErrorCodeDownloadFile_LocalExist description:@"fileId already exists in the local."];
        } else {
            [self reportErrorCompletion:completion withErrorCode:PLVFDownloadErrorCodeDownloadFile_QueueExist description:@"fileId already exist in the download queue."];
        }
    }
    else {
        [PLVLivePrivateAPI loadRecordVideoInfoWithFileId:fileId channelId:channelId completion:^(NSDictionary * _Nonnull data) {
            PLVPlaybackVideoInfoModel *videoInfoModel = [PLVPlaybackVideoInfoModel playbackVideoInfoModelWithRecordJsonDict:data];
            if (videoInfoModel.playbackCacheEnabled) {
                PLVDownloadPlaybackTaskInfo *taskInfo = [self toDownloadPlaybackTaskInfo:videoInfoModel];
                [[PLVDownloadManager shareManager] addDownloadTaskWith:taskInfo];
                [[PLVDownloadManager shareManager] startDownloadWith:taskInfo];
                completion(nil);
            }
            else {
                NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulDownload code:PLVFDownloadErrorCodeGetVideoInfo_videoInfoError];
                completion(failError);
            }
        } failure:^(NSError * _Nonnull error) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"%s %@",__FUNCTION__,error);
            [self reportErrorCompletion:completion withErrorCode:PLVFDownloadErrorCodeGetVideoInfo_videoInfoError description:error.localizedDescription];
        }];
    }
}

+ (void)enqueueDownloadQueueWithPlaybackPlayerModel:(PLVPlaybackVideoInfoModel *)playerModel completion:(void (^)(NSError *))completion {
    if (!playerModel && [playerModel isKindOfClass:[PLVPlaybackLocalVideoInfoModel class]]) {
        [self reportErrorCompletion:completion withErrorCode:PLVFDownloadErrorCodeGetVideoInfo_videoIdError description:@"The playerModel isn't online model."];
        return;
    }
    
    if (!playerModel.playbackCacheEnabled) {
        NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulDownload code:PLVFDownloadErrorCodeGetVideoInfo_videoInfoError];
        completion(failError);
    }
    
    PLVDownloadPlaybackTaskInfo * playbackTaskInfo = [[PLVDownloadDatabaseManager shareManager] checkAndGetPlaybackTaskInfoWithFileId:playerModel.fileId];
    if (playbackTaskInfo) {
        if (playbackTaskInfo.state == PLVDownloadStateSuccess) {
            [self reportErrorCompletion:completion withErrorCode:PLVFDownloadErrorCodeDownloadFile_LocalExist description:@"The playerModel already exists in the local."];
        } else {
            [self reportErrorCompletion:completion withErrorCode:PLVFDownloadErrorCodeDownloadFile_QueueExist description:@"The playerModel already exist in the download queue."];
        }
    } else {
        PLVDownloadPlaybackTaskInfo *taskInfo = [self toDownloadPlaybackTaskInfo:playerModel];
        [[PLVDownloadManager shareManager] addDownloadTaskWith:taskInfo];
        [[PLVDownloadManager shareManager] startDownloadWith:taskInfo];
        completion(nil);
    }
}

#pragma mark - private
+ (void)reportErrorCompletion:(void (^)(NSError *))completion withErrorCode:(NSInteger)errCode description:(NSString *)description {
    if (completion) {
        if (errCode == 0) {
            completion(nil);
        } else {
            NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulDownload code:errCode];
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:description];
            completion(failError);
        }
    }
}

@end

@implementation PLVPlaybackCacheManager (Builder)

/// 通过本地/在线数据生成回放列表/点播列表中的视频信息模型 (将优先判断是否存在本地数据)
+ (void)playbackVideoInfoModelWithVid:(NSString *)vid
                         channelId:(NSString *)channelId
                          listType:(NSString *)listType
                        completion:(void (^)(PLVPlaybackVideoInfoModel *model, NSError *error))completion {
    NSAssert(completion != nil, @"completion can,t be nil.");
    
    if (![PLVFdUtil checkStringUseable:vid]) {
        completion(nil, [self reportErrorWithCode:PLVFDownloadErrorCodeGetVideoInfo_videoIdError description:@"vid is empty."]);
        return;
    }
    
    NSString *videopoodid = [vid componentsSeparatedByString:@"_"].firstObject;
    PLVDownloadPlaybackTaskInfo *playbackTaskInfo = [[PLVDownloadDatabaseManager shareManager] checkAndGetPlaybackTaskInfoWithVideoPoolId:videopoodid];
    if (playbackTaskInfo && playbackTaskInfo.state == PLVDownloadStateSuccess) {
        PLVPlaybackLocalVideoInfoModel * model = [self toPlaybackPlayerModel:playbackTaskInfo];
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            if (model) {
                completion(model, nil);
            }
        });
    }else {
        [self loadOnlinePlaybackVideoInfoModelWithVid:vid channelId:channelId listType:listType completion:completion];
    }
}

/// 通过在线数据生成回放列表/点播列表中的视频信息模型
+ (void)loadOnlinePlaybackVideoInfoModelWithVid:(NSString *)vid
                                   channelId:(NSString *)channelId
                                    listType:(NSString *)listType
                                  completion:(void (^)(PLVPlaybackVideoInfoModel *model, NSError *error))completion {
    NSAssert(completion != nil, @"completion can,t be nil.");
    
    if (![PLVFdUtil checkStringUseable:vid]) {
        completion(nil, [self reportErrorWithCode:PLVFDownloadErrorCodeGetVideoInfo_videoIdError description:@"vid is empty."]);
        return;
    }
    
    [PLVLivePrivateAPI loadVideoInfoWithVid:vid channelId:channelId listType:listType completion:^(NSDictionary * _Nonnull data) {
        PLVPlaybackVideoInfoModel *model = [PLVPlaybackVideoInfoModel playbackVideoInfoModelWithJsonDict:data];
        model.playbackCacheEnabled = NO;
        model.vid = vid;
        completion(model, nil);
    } failure:^(NSError * _Nonnull error) {
        completion(nil, error);
    }];
}

/// 通过本地/在线数据生成暂存列表中的视频信息模型 (将优先判断是否存在本地数据)
+ (void)recordPlaybackVideoInfoModelWithFileId:(NSString *)fileId
                                     channelId:(NSString *)channelId
                                    completion:(void (^)(PLVPlaybackVideoInfoModel *model, NSError *error))completion {
    NSAssert(completion != nil, @"completion can,t be nil.");
    
    if (![PLVFdUtil checkStringUseable:fileId]) {
        completion(nil, [self reportErrorWithCode:PLVFDownloadErrorCodeGetVideoInfo_videoIdError description:@"fileId is empty."]);
        return;
    }
    
    PLVDownloadPlaybackTaskInfo *playbackTaskInfo = [[PLVDownloadDatabaseManager shareManager] checkAndGetPlaybackTaskInfoWithFileId:fileId];
    if (playbackTaskInfo && playbackTaskInfo.state == PLVDownloadStateSuccess) {
        PLVPlaybackLocalVideoInfoModel * model = [self toPlaybackPlayerModel:playbackTaskInfo];
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            if (model) {
                completion(model, nil);
            }
        });
    }else {
        [self loadOnlineRecordPlaybackVideoInfoModelWithFileId:fileId channelId:channelId completion:completion];
    }
}

/// 通过在线数据生成暂存列表中的视频信息模型
+ (void)loadOnlineRecordPlaybackVideoInfoModelWithFileId:(NSString *)fileId
                                               channelId:(NSString *)channelId
                                              completion:(void (^)(PLVPlaybackVideoInfoModel *model, NSError *error))completion {
    NSAssert(completion != nil, @"completion can,t be nil.");
    
    if (![PLVFdUtil checkStringUseable:fileId]) {
        completion(nil, [self reportErrorWithCode:PLVFDownloadErrorCodeGetVideoInfo_videoIdError description:@"fileId is empty."]);
        return;
    }
    
    [PLVLivePrivateAPI loadRecordVideoInfoWithFileId:fileId channelId:channelId completion:^(NSDictionary * _Nonnull data) {
        PLVPlaybackVideoInfoModel *model = [PLVPlaybackVideoInfoModel playbackVideoInfoModelWithRecordJsonDict:data];
        completion(model, nil);
    } failure:^(NSError * _Nonnull error) {
        completion(nil, error);
    }];
}

#pragma mark - private
+ (NSError *)reportErrorWithCode:(NSInteger)code description:(NSString *)description {
    NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulDownload code:code];
    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:description];
    return failError;
}

@end

@implementation PLVPlaybackCacheManager (Adapter)

/// 下载回放模型 -> 本地播放回放模型
+ (PLVPlaybackLocalVideoInfoModel *)toPlaybackPlayerModel:(PLVDownloadPlaybackTaskInfo *)taskInfo{
    if (taskInfo.state != PLVDownloadStateSuccess) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeDownload, @"模型转换失败 - 任务未下载完成");
        return nil;
    }
    
    PLVPlaybackLocalVideoInfoModel * model = [[PLVPlaybackLocalVideoInfoModel alloc]init];
    
    model.liveType = taskInfo.liveType;
    model.listType = taskInfo.listType;
    
    model.title = taskInfo.title;
    model.duration = taskInfo.duration;
    model.firstImage = taskInfo.coverUrl;
    model.fileUrl = taskInfo.videoUrl;
    model.subtitleList = taskInfo.subtitleList;
    
    model.vid = taskInfo.vid;
    model.videoId = taskInfo.videoId;
    model.videoPoolId = taskInfo.videoPoolId;
    model.fileId = taskInfo.fileId;
    model.channelId = taskInfo.channelId;
    model.channelSessionId = taskInfo.channelSessionId;
    model.originSessionId = taskInfo.channelSessionId;
    model.playbackCacheEnabled = taskInfo.playbackCacheEnabled;
    
    model.videoSize = taskInfo.videoSize;
    model.zipSize = taskInfo.pptZipSize;
    model.totalBytesExpectedToWrite = taskInfo.totalBytesExpectedToWrite;
    model.videoUrl = taskInfo.videoUrl;
    model.zipUrl = taskInfo.pptZipUrl;
    model.pptJsonUrl = taskInfo.JsZipUrl;
    
    model.viewerId = taskInfo.viewerId;
    model.viewerName = taskInfo.viewerName;
    model.viewerAvatar = taskInfo.viewerAvatar;
    
    // 配置本地的路径信息
    NSString *fileIdPath = [[PLVDownloadPathManager shareManager].viewerPlaybackPath stringByAppendingPathComponent:taskInfo.fileId];
    model.fileIdPath = fileIdPath;
    
    return model;
}

/// 在线播放回放模型 -> 下载回放模型
+ (PLVDownloadPlaybackTaskInfo *)toDownloadPlaybackTaskInfo:(PLVPlaybackVideoInfoModel *)model{
    PLVDownloadPlaybackTaskInfo * taskInfo = [[PLVDownloadPlaybackTaskInfo alloc]init];
    
    taskInfo.liveType = model.liveType;
    taskInfo.listType = model.listType;
    
    taskInfo.title = model.title;
    taskInfo.duration = model.duration;
    taskInfo.coverUrl = model.firstImage;
    
    taskInfo.vid = model.vid;
    taskInfo.videoId = model.videoId;
    taskInfo.videoPoolId = model.videoPoolId;
    taskInfo.fileId = model.fileId;
    taskInfo.channelId = model.channelId;
    taskInfo.channelSessionId = model.channelSessionId;
    taskInfo.originSessionId = model.channelSessionId;
    taskInfo.playbackCacheEnabled = model.playbackCacheEnabled;
    taskInfo.videoSize = model.videoSize;
    
    taskInfo.viewerId = model.viewerId;
    taskInfo.viewerName = model.viewerName;
    taskInfo.viewerAvatar = model.viewerAvatar;
    
    if ([model.liveType isEqualToString:@"ppt"]) {
        // 三分屏回放
        taskInfo.videoUrl = model.videoUrl;
        taskInfo.JsZipUrl = model.pptJsonUrl;
        taskInfo.pptZipUrl = model.zipUrl;
        taskInfo.pptZipSize = model.zipSize;
        taskInfo.totalBytesExpectedToWrite = model.zipSize + model.videoSize;
    }
    else {
        // 纯视频回放
        taskInfo.url = model.videoUrl;
        taskInfo.videoUrl = model.videoUrl;
        taskInfo.totalBytesExpectedToWrite = model.videoSize;

        // 设定保存路径
        NSString *fileIdPath = [[PLVDownloadPathManager shareManager].viewerPlaybackPath stringByAppendingPathComponent:model.fileId];
        taskInfo.filePath = [NSString stringWithFormat:@"%@/video/1/%@",fileIdPath, taskInfo.fileName];
    }
    
    return taskInfo;
}

@end
