<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>FMWK</string>
	<key>CFBundleShortVersionString</key>
	<string>1.26.0</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>NSPrincipalClass</key>
	<string></string>
	<key>PLVLiveScenesBasePlayerSDK</key>
	<array>
		<string>PLVPlayer</string>
		<string>PLVQosLoadingTracerModel</string>
		<string>PLVLiveSampleBufferDisplayView</string>
	</array>
	<key>PLVLiveScenesBaseRTCSDK</key>
	<array>
		<string>PLVLinkMicGetTokenModel</string>
		<string>PLVRTCStatistics</string>
	</array>
	<key>PLVLiveScenesBaseSDK</key>
	<array>
		<string>PLVLiveConstants</string>
		<string>PLVLiveVideoConfig</string>
	</array>
	<key>PLVLiveScenesBeautySDK</key>
	<array>
		<string>PLVBeautyManager</string>
		<string>PLVBeautyResourceManager</string>
	</array>
	<key>PLVLiveScenesChatroomSDK</key>
	<array>
		<string>PLVChatroomManager</string>
		<string>PLVSpeakMessage</string>
		<string>PLVQuoteMessage</string>
		<string>PLVImageMessage</string>
		<string>PLVImageEmotionMessage</string>
		<string>PLVCustomMessage</string>
		<string>PLVRewardMessage</string>
		<string>PLVFileMessage</string>
		<string>PLVRedpackMessage</string>
		<string>PLVSpeakTopMessage</string>
	</array>
	<key>PLVLiveScenesClassSDK</key>
	<array>
		<string>PLVChannelClassManager</string>
	</array>
	<key>PLVLiveScenesConsoleLoggerSDK</key>
	<array>
		<string>PLVConsoleLogger</string>
	</array>
	<key>PLVLiveScenesDownloadSDK</key>
	<array>
		<string>PLVDownloadCommonOperation</string>
		<string>PLVDownloadPlaybackTaskInfo</string>
		<string>PLVDownloadDataTasker</string>
		<string>PLVPlaybackCacheManager</string>
		<string>PLVDownloadDatabaseManager</string>
		<string>PLVDownloadURLSession</string>
		<string>PLVDownloadTaskInfo</string>
		<string>PLVDownloadPlaybackOperation</string>
		<string>PLVDownloadManager</string>
		<string>PLVDownloadPathManager</string>
	</array>
	<key>PLVLiveScenesErrorManagerSDK</key>
	<array>
		<string>PLVWErrorManager</string>
	</array>
	<key>PLVLiveScenesHiClassSDK</key>
	<array>
		<string>PLVHiClassManager</string>
	</array>
	<key>PLVLiveScenesInteractionSDK</key>
	<array>
		<string>PLVInteractWebview</string>
		<string>PLVInteractBaseApp</string>
	</array>
	<key>PLVLiveScenesLinkMicSDK</key>
	<array>
		<string>PLVLinkMicManager</string>
	</array>
	<key>PLVLiveScenesLivePlaybackPlayerSDK</key>
	<array>
		<string>PLVLivePlaybackPlayer</string>
		<string>PLVPlaybackVideoInfoModel</string>
	</array>
	<key>PLVLiveScenesLivePlayerSDK</key>
	<array>
		<string>PLVLivePlayer</string>
	</array>
	<key>PLVLiveScenesLogReporterSDK</key>
	<array>
		<string>PLVViewLogCustomParam</string>
		<string>PLVWELogEventDefine</string>
		<string>PLVWLogReporterManager</string>
	</array>
	<key>PLVLiveScenesLoganSDK</key>
	<array>
		<string>PLVLoganUploadManager</string>
		<string>PLVLoganUploadModel</string>
		<string>PLVLoganUploadTokenModel</string>
		<string>PLVLoganManager</string>
	</array>
	<key>PLVLiveScenesNetworkSDK</key>
	<array>
		<string>PLVCommodityModel</string>
		<string>PLVLiveVideoChannelMenuInfo</string>
		<string>PLVPlaybackListModel</string>
		<string>PLVLivePlaybackSectionModel</string>
		<string>PLVChannelPlaybackInfoModel</string>
		<string>PLVChannelInfoModel</string>
		<string>PLVLiveVClassAPI</string>
		<string>PLVLiveVideoAPI</string>
		<string>PLVLivePrivateAPI</string>
		<string>PLVLiveAPIUtils</string>
		<string>PLVImageUpload</string>
		<string>PLVLiveHttpDnsManager</string>
	</array>
	<key>PLVLiveScenesPPTWebviewSDK</key>
	<array>
		<string>PLVPPTWebview</string>
	</array>
	<key>PLVLiveScenesPictureInPictureSDK</key>
	<array>
		<string>PLVLivePictureInPictureManager</string>
	</array>
	<key>PLVLiveScenesPlaybackMessageSDK</key>
	<array>
		<string>PLVPlaybackMessageManager</string>
		<string>PLVPlaybackMsgUser</string>
		<string>PLVPlaybackMessage</string>
		<string>PLVPlaybackMsgIndex</string>
	</array>
	<key>PLVLiveScenesPublicStreamPlayerSDK</key>
	<array>
		<string>PLVPublicStreamGetInfoModel</string>
		<string>PLVPublicStreamPlayer</string>
	</array>
	<key>PLVLiveScenesSDKSubModulesList</key>
	<array>
		<string>PLVLiveScenesLinkMicSDK</string>
		<string>PLVLiveScenesStreamerSDK</string>
		<string>PLVLiveScenesBaseRTCSDK</string>
		<string>PLVLiveScenesBasePlayerSDK</string>
		<string>PLVLiveScenesLivePlayerSDK</string>
		<string>PLVLiveScenesLivePlaybackPlayerSDK</string>
		<string>PLVLiveScenesBaseSDK</string>
		<string>PLVLiveScenesClassSDK</string>
		<string>PLVLiveScenesHiClassSDK</string>
		<string>PLVLiveScenesPPTWebviewSDK</string>
		<string>PLVLiveScenesWebviewBridgeSDK</string>
		<string>PLVLiveScenesUploadSDK</string>
		<string>PLVLiveScenesChatroomSDK</string>
		<string>PLVLiveScenesSocketSDK</string>
		<string>PLVLiveScenesInteractionSDK</string>
		<string>PLVLiveScenesErrorManagerSDK</string>
		<string>PLVLiveScenesConsoleLoggerSDK</string>
		<string>PLVLiveScenesLogReporterSDK</string>
		<string>PLVLiveScenesNetworkSDK</string>
		<string>PLVLiveScenesPictureInPictureSDK</string>
		<string>PLVLiveScenesDownloadSDK</string>
		<string>PLVLiveScenesBeautySDK</string>
		<string>PLVLiveScenesPlaybackMessageSDK</string>
		<string>PLVLiveScenesPublicStreamPlayerSDK</string>
		<string>PLVLiveScenesLoganSDK</string>
	</array>
	<key>PLVLiveScenesSocketSDK</key>
	<array>
		<string>PLVSocketManager</string>
		<string>PLVSocketEventDefine</string>
	</array>
	<key>PLVLiveScenesStreamerSDK</key>
	<array>
		<string>PLVRTCStreamerManager</string>
		<string>PLVRTCStreamerMixUser</string>
	</array>
	<key>PLVLiveScenesUploadSDK</key>
	<array>
		<string>PLVDocumentTokenModel</string>
		<string>PLVDocumentUploadModel</string>
		<string>PLVDocumentUploadClient</string>
		<string>PLVDocumentUploader</string>
		<string>PLVFileManager</string>
	</array>
	<key>PLVLiveScenesWebviewBridgeSDK</key>
	<array>
		<string>PLVWebViewBridge</string>
		<string>PLVContainerWebViewBridge</string>
		<string>PLVInteractWebViewBridge</string>
		<string>PLVSocketWebViewBridge</string>
		<string>PLVProductWebViewBridge</string>
		<string>PLVStreamerCommodityWebViewBridge</string>
		<string>PLVTuWenWebViewBridge</string>
		<string>PLVStreamerInteractWebViewBridge</string>
		<string>PLVQAWebViewBridge</string>
	</array>
</dict>
</plist>
