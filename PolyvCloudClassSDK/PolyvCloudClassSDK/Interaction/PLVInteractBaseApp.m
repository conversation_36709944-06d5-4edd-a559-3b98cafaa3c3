//
//  PLVInteractBaseApp.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2020/9/10.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVInteractBaseApp.h"
#import "PLVConsoleLogger.h"

@implementation PLVInteractBaseApp

- (instancetype)initWithJsBridge:(PLVJSBridge *)jsBridge{
    if (self = [super init]) {
        _jsBridge = jsBridge;
        
        _triviaCardTimeoutSec = 5.0;
        _triviaCardMaxRetryCount = 3;
    }
    return self;
}

- (void)callRequirePortraitScreen{
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvInteractAppRequirePortraitScreen:)]) {
        [self.delegate plvInteractAppRequirePortraitScreen:self];
    }
}

- (void)callWebviewShow{
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvInteractApp:webviewShow:)]) {
        [self.delegate plvInteractApp:self webviewShow:YES];
    }
}

- (void)submitResultCallback:(NSString *)json event:(NSString *)event{
    if ([PLVFdUtil checkStringUseable:json] && [PLVFdUtil checkStringUseable:event]) {
        NSData *jsonData = [json dataUsingEncoding:NSUTF8StringEncoding];
        NSError *jsonError = nil;
        NSDictionary *jsonDict = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:&jsonError];
        if (jsonError == nil && jsonDict != nil) {
            NSString *code = [NSString stringWithFormat:@"%@",jsonDict[@"code"]];
            NSString *callback = [NSString stringWithFormat:@"{\"EVENT\":\"%@\",\"code\":%@}",event,code];
            [self.jsBridge call:@"interactiveEventCallback" params:@[callback]];
            [self callWebviewShow];
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeInteract, @"PLVInteractBaseApp - [js call] submit result callback failed %@ %@",jsonError,jsonDict);
        }
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeInteract, @"PLVInteractBaseApp - [js call] submit result callback failed, param illegal %@ %@",json,event);
    }
}

- (void)submitResultTimeoutCallback:(NSString *)event{
    [self submitResultCallback:@"{\"code\":400}" event:event];
}

@end
