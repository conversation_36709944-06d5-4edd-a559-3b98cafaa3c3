//
//  PLVInteractWebview.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON> on 2020/9/10.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVInteractWebview.h"
#import "PLVSocketManager.h"
#import "PLVConsoleLogger.h"

@interface PLVInteractWebview () <PLVSocketManagerProtocol>

@property (nonatomic, strong) PLVJSBridge * jsBridge;

@end

@implementation PLVInteractWebview {
    /// PLVSocketManager回调的执行队列
    dispatch_queue_t socketDelegateQueue;
}

#pragma mark - [ Life Period ]
- (void)dealloc{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeInteract, @"%s",__FUNCTION__);
}

- (instancetype)init{
    if (self = [super init]) {
        [self setup];
    }
    return self;
}

#pragma mark - [ Public Methods ]
#pragma mark Getter
- (WKWebView *)webview{
    return self.jsBridge.webView;
}


#pragma mark - [ Private Methods ]
- (void)setup{
    self.jsBridge = [[PLVJSBridge alloc] init];
    
    /// 添加 socket 事件监听
    socketDelegateQueue = dispatch_get_main_queue();
    [[PLVSocketManager sharedManager] addDelegate:self delegateQueue:socketDelegateQueue];
}


#pragma mark - [ Delegate ]
#pragma mark PLVSocketManagerProtocol
/// socket 接收到 "message" 事件
- (void)socketMananger_didReceiveMessage:(NSString *)subEvent
                                    json:(NSString *)jsonString
                              jsonObject:(id)object {
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvInteractWebview:didReceiveInteractMessageString:jsonDict:)]) {
        [self.delegate plvInteractWebview:self didReceiveInteractMessageString:jsonString jsonDict:object];
    }
}

@end
