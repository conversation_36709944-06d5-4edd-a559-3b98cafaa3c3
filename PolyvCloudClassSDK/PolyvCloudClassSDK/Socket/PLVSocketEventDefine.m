//
//  PLVSocketEventDefine.m
//  PLVBusinessSDK
//
//  Created by ftao on 10/11/2017.
//  Copyright © 2017 PLV. All rights reserved.
//

#import "PLVSocketEventDefine.h"

#pragma mark - 聊天室事件键集合

/*!
 @constant    PLVSocketIOChatRoomUserKey
 @abstract    聊天室用户信息
 */
 NSString *const PLVSocketIOChatRoomUserKey = @"user";   /* NSDictionary */
     NSString *const PLVSocketIOChatRoomUserClientIpKey = @"clientIp";   /* 用户IP */
     NSString *const PLVSocketIOChatRoomUserNickKey = @"nick";   /* 用户昵称 */
     NSString *const PLVSocketIOChatRoomUserPicKey = @"pic";  /* 用户头像 */
     NSString *const PLVSocketIOChatRoomUserRoomIdKey = @"roomId";  /* 房间号 */
     NSString *const PLVSocketIOChatRoomUserUidKey = @"uid";  /* socket 分配的id */
     NSString *const PLVSocketIOChatRoomUserUserIdKey = @"userId";  /* 用户唯一标识 */
     NSString *const PLVSocketIOChatRoomUserUserTypeKey = @"userType";  /* 用户类型，目前有teacher(老师)、assistant(助教)、manager(管理员)、slice(云课堂学员) */
     NSString *const PLVSocketIOChatRoomUserActorKey = @"actor";  /* optional, 头衔 */
     NSString *const PLVSocketIOChatRoomUserCanAnswerKey = @"canAnswer";  /* optional, 是否有回答提问的权限 */

#pragma mark 聊天室基本消息

/*!
 @constant    PLVSocketIOChatRoom_LOGIN_EVENT
 @abstract    登录消息，登录房间的时候服务器会广播这一消息
 */
 NSString *const PLVSocketIOChatRoom_LOGIN_EVENT = @"LOGIN";
     NSString *const PLVSocketIOChatRoom_LOGIN_onlineUserNumber = @"onlineUserNumber";  /* NSNumber, 当前房间总在线人数 */
     NSString *const PLVSocketIOChatRoom_LOGIN_userKey = PLVSocketIOChatRoomUserKey;  /* NSDictionary，等同 PLVSocketIOChatRoomUserKey */

/*!
 @constant    PLVSocketIOChatRoom_LOGOUT_EVENT
 @abstract    退出消息，断开连接服务器广播的消息
 */
 NSString *const PLVSocketIOChatRoom_LOGOUT_EVENT = @"LOGOUT";
     NSString *const PLVSocketIOChatRoom_LOGOUT_onlineUserNumber = @"onlineUserNumber";  /* NSNumber, 当前在线总人数 */
     NSString *const PLVSocketIOChatRoom_LOGOUT_uid = @"uid";  /* 离开人的uid */

/*!
 @constant    PLVSocketIOChatRoom_RELOGIN_EVENT
 @abstract    当前用户id在别处重新登录
 */
NSString *const PLVSocketIOChatRoom_RELOGIN_EVENT = @"RELOGIN";
    NSString *const PLVSocketIOChatRoom_RELOGIN_channelId = @"channelId";  /* 频道号 */

/*!
 @constant    PLVSocketIOChatRoom_SET_NICK_EVENT
 @abstract    昵称设置，设置的昵称存在时间为5个小时
 */
NSString *const PLVSocketIOChatRoom_SET_NICK_EVENT = @"SET_NICK";
    NSString *const PLVSocketIOChatRoom_SET_NICK_message = @"message"; /* 消息内容 */
    NSString *const PLVSocketIOChatRoom_SET_NICK_status = @"status"; /* 设置状态 */
    NSString *const PLVSocketIOChatRoom_SET_NICK_nick = @"nick"; /* 设置昵称 */
    NSString *const PLVSocketIOChatRoom_SET_NICK_userId = @"userId"; /* 用户Id */

/*!
 @constant    PLVSocketIOChatRoom_GONGGAO_EVENT
 @abstract    公告消息，如有公告，登录房间的时候服务器会广播这一消息，公告即为管理员的发言信息
 */
 NSString *const PLVSocketIOChatRoom_GONGGAO_EVENT = @"GONGGAO";
     NSString *const PLVSocketIOChatRoom_GONGGAO_content = @"content";  /* 公告内容 */

/*!
 @constant    PLVSocketIOChatRoom_BULLETIN_EVENT
 @abstract    公告消息，POLYV 后台聊天室管理中，发布公告的内容
 */
NSString *const PLVSocketIOChatRoom_BULLETIN_EVENT = @"BULLETIN";
    NSString *const PLVSocketIOChatRoom_BULLETIN_RemoveBulletin = @"REMOVE_BULLETIN";
    NSString *const PLVSocketIOChatRoom_BULLETIN_content = @"content"; /* 公告内容 */
    //NSString *const PLVSocketIOChatRoom_BULLETIN_roomId = @"roomId"; /* NSNumber, 房间号 */

/*!
 @constant    PLVSocketIOChatRoom_SPEAK_EVENT
 @abstract    发言,接收别人的发言消息(不包括自己)
 */
 NSString *const PLVSocketIOChatRoom_SPEAK_EVENT = @"SPEAK";
     NSString *const PLVSocketIOChatRoom_SPEAK_id = @"id";  /* 消息唯一标志 */
     NSString *const PLVSocketIOChatRoom_SPEAK_time = @"time";  /* NSNumber,时间戳 */
     NSString *const PLVSocketIOChatRoom_SPEAK_userKey = PLVSocketIOChatRoomUserKey;  /* NSDictionary，等同 PLVSocketIOChatRoomUserKey，发言人 */
     NSString *const PLVSocketIOChatRoom_SPEAK_values = @"values";  /* NSArray, 消息内容 */

/*!
 @constant    PLVSocketIOChatRoom_EMOTION_EVENT
 @abstract    图片表情,接收别人的图片表情消息(不包括自己)
 */
NSString *const PLVSocketIOChatRoom_EMOTION_EVENT = @"emotion";
    NSString *const PLVSocketIOChatRoom_EMOTION_id = @"id";  /* 图片id */
    NSString *const PLVSocketIOChatRoom_EMOTION_messageId = @"messageId";  /* 消息唯一标志 */
    NSString *const PLVSocketIOChatRoom_EMOTION_source = @"source";  /* 是否为特殊身份聊天,extend表示为特殊身份聊天 */

#pragma mark 聊天室赞赏消息

/*!
 @constant    PLVSocketIOChatRoom_FLOWERS_EVENT
 @abstract    送花事件
 */
 NSString *const PLVSocketIOChatRoom_FLOWERS_EVENT = @"FLOWERS";
     NSString *const PLVSocketIOChatRoom_FLOWERS_nick = @"nick";  /* 送花人昵称 */
     NSString *const PLVSocketIOChatRoom_FLOWERS_uimg = @"uimg";  /* 送花人头像 */

/*!
 @constant    PLVSocketIOChatRoom_LIKES_EVENT
 @abstract    点赞事件
 */
 NSString *const PLVSocketIOChatRoom_LIKES_EVENT = @"LIKES";
     NSString *const PLVSocketIOChatRoom_LIKES_count = @"count";  /* NSNumber, 当前点赞总数，若发生消息的时候没传此属性，则不返回这个属性 */
     NSString *const PLVSocketIOChatRoom_LIKES_nick = @"nick";  /* 点赞人昵称 */

/*!
 @constant    PLVSocketIOChatRoom_REWARD_EVENT
 @abstract    打赏事件，目前需要通过HTTP接口进行支付后，由后台通知服务器
 */
 NSString *const PLVSocketIOChatRoom_REWARD_EVENT = @"REWARD";
     NSString *const PLVSocketIOChatRoom_REWARD_roomId = @"roomId";  /* NSNumber, 房间号 */
     NSString *const PLVSocketIOChatRoom_REWARD_contentKey = @"content";  /* NSDictionary, 打赏的内容 */
         NSString *const PLVSocketIOChatRoom_REWARD_content_gimg = @"gimg";  /* 礼物的图片，若为空，则代表现金打赏 */
         NSString *const PLVSocketIOChatRoom_REWARD_content_rewardContent = @"rewardContent";  /* 礼物的名称,若gimg为空,此字段为打赏的金额 */
         NSString *const PLVSocketIOChatRoom_REWARD_content_uimg = @"uimg";  /* 打赏人的头像 */
         NSString *const PLVSocketIOChatRoom_REWARD_content_unick = @"unick";  /* 打赏人的昵称 */

/*!
 @constant    PLVSocketIOChatRoom_REDPAPER_EVENT
 @abstract    发红包，和打赏的情况类似，也是通过HTTP接口支付后由后台通知服务器
 */
 NSString *const PLVSocketIOChatRoom_REDPAPER_EVENT = @"REDPAPER";
     NSString *const PLVSocketIOChatRoom_REDPAPER_msgSource = @"msgSource";  /* 消息类型 */
     NSString *const PLVSocketIOChatRoom_REDPAPER_totalAmount = @"totalAmount";  /* 红包总金额 */
     NSString *const PLVSocketIOChatRoom_REDPAPER_number = @"number";  /* 红包个数 */
     NSString *const PLVSocketIOChatRoom_REDPAPER_content = @"content";  /* 祝福语 */
     NSString *const PLVSocketIOChatRoom_REDPAPER_redpackId = @"redpackId";  /* 红包ID */
     NSString *const PLVSocketIOChatRoom_REDPAPER_redCacheId = @"redCacheId";  /* 红包缓存ID */
     NSString *const PLVSocketIOChatRoom_REDPAPER_userKey = @"user";  /* NSDictionary, 发红包用户 */
         NSString *const PLVSocketIOChatRoom_REDPAPER_user_roomId = @"roomId";  /* 房间号 */
         NSString *const PLVSocketIOChatRoom_REDPAPER_user_openId = @"openId";  /* 微信用户openId */
         NSString *const PLVSocketIOChatRoom_REDPAPER_user_nick = @"nick";  /* 用户昵称 */
         NSString *const PLVSocketIOChatRoom_REDPAPER_user_pic = @"pic";  /* 用户头像 */
         NSString *const PLVSocketIOChatRoom_REDPAPER_user_userId = @"userId";  /* optional, 用户Id */

#pragma mark 聊天室管理及控制

/*!
 @constant    PLVSocketIOChatRoom_REMOVE_CONTENT_EVENT
 @abstract    删除某条聊天记录，目前需要通过HTTP接口调用
 */
 NSString *const PLVSocketIOChatRoom_REMOVE_CONTENT_EVENT = @"REMOVE_CONTENT";
     NSString *const PLVSocketIOChatRoom_REMOVE_CONTENT_roomId = @"roomId";  /* NSNumber, 房间号 */
     NSString *const PLVSocketIOChatRoom_REMOVE_CONTENT_id = @"id";  /* 删除内容对应的id */
     NSString *const PLVSocketIOChatRoom_REMOVE_CONTENT_content = @"content";  /* optional, 删除的内容 */

/*!
 @constant    PLVSocketIOChatRoom_REMOVE_HISTORY_EVENT
 @abstract    清空聊天记录
 */
 NSString *const PLVSocketIOChatRoom_REMOVE_HISTORY_EVENT = @"REMOVE_HISTORY";

/*!
 @constant    PLVSocketIOChatRoom_CLOSEROOM_EVENT
 @abstract    关闭聊天室
 */
 NSString *const PLVSocketIOChatRoom_CLOSEROOM_EVENT = @"CLOSEROOM";
     NSString *const PLVSocketIOChatRoom_CLOSEROOM_valueKey = @"value";  /* NSDictionary */
         NSString *const PLVSocketIOChatRoom_CLOSEROOM_value_roomId = @"roomId";  /* NSNumber, 房间号 */
         NSString *const PLVSocketIOChatRoom_CLOSEROOM_value_closed = @"closed";  /* NSNumber(BOOL)，true为关闭；false为开启 */

/*!
 @constant    PLVSocketIOChatRoom_CLOSE_DANMU_EVENT
 @abstract    关闭弹幕
 */
 NSString *const PLVSocketIOChatRoom_CLOSE_DANMU_EVENT = @"CLOSE_DANMU";
     NSString *const PLVSocketIOChatRoom_CLOSE_DANMU_isClose = @"isClose";  /* NSNumber(BOOL)，true为关；false为开启 */


/*!
 @constant    PLVSocketIOChatRoom_BANIP_EVENT
 @abstract    禁言事件
 */
 NSString *const PLVSocketIOChatRoom_BANIP_EVENT = @"BANIP";
     NSString *const PLVSocketIOChatRoom_BANIP_userIds = @"userIds";  /* NSArray<NSDictionary，内容可使用PLVSocketIOChatRoomUserKey内Key值>, 被禁用户 */

/*!
 @constant    PLVSocketIOChatRoom_UNSHIELD_EVENT
 @abstract    解禁言事件
 */
 NSString *const PLVSocketIOChatRoom_UNSHIELD_EVENT = @"UNSHIELD";
     NSString *const PLVSocketIOChatRoom_UNSHIELD_userIds = @"userIds";  /* NSArray<NSDictionary，内容可使用PLVSocketIOChatRoomUserKey内Key值>, 解禁用户 */

/*!
 @constant    PLVSocketIOChatRoom_KICK_EVENT
 @abstract    踢人事件
 */
 NSString *const PLVSocketIOChatRoom_KICK_EVENT = @"KICK";
     NSString *const PLVSocketIOChatRoom_KICK_userKey = PLVSocketIOChatRoomUserKey;  /* NSDictionary，等同 PLVSocketIOChatRoomUserKey，被踢用户对象 */


/*!
 @constant    PLVSocketIOChatRoom_DEBUG_EVENT
 @abstract    调试模式，调试模式的开关在助教页面
 */
 NSString *const PLVSocketIOChatRoom_DEBUG_EVENT = @"DEBUG";
     NSString *const PLVSocketIOChatRoom_DEBUG_roomId = @"roomId";  /* NSNumber, 房间号 */
     NSString *const PLVSocketIOChatRoom_DEBUG_isdebug = @"isdebug";  /* NSNumber(BOOL)，true为开启调试模式；false为关闭 */

/*!
 @constant    PLVSocketIOChatRoom_CHATROOM_CONTROL_EVENT
 @abstract    聊天室管理操作事件（关闭欢迎语、关闭点赞语）
 */
 NSString *const PLVSocketIOChatRoom_CHATROOM_CONTROL_EVENT = @"CHATROOM_CONTROL";
     NSString *const PLVSocketIOChatRoom_CHATROOM_CONTROL_roomId = @"roomId";  /* NSNumber, 房间号 */
     NSString *const PLVSocketIOChatRoom_CHATROOM_CONTROL_type = @"type";  /* 控制类型：closeWelcome(关闭欢迎语)、closeLikes(关闭点赞语)等 */
     NSString *const PLVSocketIOChatRoom_CHATROOM_CONTROL_value = @"value";  /* NSNumber(BOOL)，YES 为开启；NO 为关闭 */

#pragma mark 聊天室问答消息

/*!
 @constant    PLVSocketIOChatRoom_QUESTION_EVENT
 @abstract    老师提问，在客户端聊天室发起，可以了解下https://dev.polyv.net/2016/08/clientchat/ 2.3问答功能
 */
 NSString *const PLVSocketIOChatRoom_QUESTION_EVENT = @"QUESTION";
     NSString *const PLVSocketIOChatRoom_QUESTION_roomId = @"roomId";  /* NSNumber, 房间号 */
     NSString *const PLVSocketIOChatRoom_QUESTION_question = @"question";  /* 问题选项(单选题/判断题) */

/*!
 @constant    PLVSocketIOChatRoom_CLOSE_QUESTION_EVENT
 @abstract    关闭问答，老师发起问答后关闭问答
 */
 NSString *const PLVSocketIOChatRoom_CLOSE_QUESTION_EVENT = @"CLOSE_QUESTION";
     NSString *const PLVSocketIOChatRoom_CLOSE_QUESTION_roomId = @"roomId";  /* NSNumber, 房间号 */

/*!
 @constant    PLVSocketIOChatRoom_ANSWER_EVENT
 @abstract    学员在观看端回答老师提出的问题
 */
 NSString *const PLVSocketIOChatRoom_ANSWER_EVENT = @"ANSWER";
     NSString *const PLVSocketIOChatRoom_ANSWER_roomId = @"roomId";  /* NSNumber, 房间号 */
     NSString *const PLVSocketIOChatRoom_ANSWER_answer = @"answer";  /* 学员选择的答案 */
     NSString *const PLVSocketIOChatRoom_ANSWER_nick = @"nick";  /* 回答问题学员昵称 */

#pragma mark 聊天室自定义消息

/*!
 @constant    PLVSocketIOChatRoom_CUSTOMER_MESSAGE_EVENT
 @abstract    自定义消息接收事件，目前只能通过后台管理员调用HTTP接口发送消息，可参考https://dev.polyv.net/2016/12/send-chat/
 */
 NSString *const PLVSocketIOChatRoom_CUSTOMER_MESSAGE_EVENT = @"CUSTOMER_MESSAGE";
     NSString *const PLVSocketIOChatRoom_CUSTOMER_MESSAGE_roomId = @"roomId";  /* NSNumber, 房间号 */
     NSString *const PLVSocketIOChatRoom_CUSTOMER_MESSAGE_image = @"image";  /* 消息图片(根据发送的消息是否有图片决定) */
     NSString *const PLVSocketIOChatRoom_CUSTOMER_MESSAGE_content = @"content";  /* 消息内容 */

#pragma mark 聊天室私聊消息
/*!
 @constant    PLVSocketIOChatRoom_S_QUESTION_EVENT
 @abstract    学生在观看端发起提问
 */
NSString *const PLVSocketIOChatRoom_S_QUESTION_EVENT = @"S_QUESTION";
    NSString *const PLVSocketIOChatRoom_S_QUESTION_roomId = @"roomId";  /* NSNumber, 房间号 */
    NSString *const PLVSocketIOChatRoom_S_QUESTION_content = @"content";  /* 提问内容 */
    NSString *const PLVSocketIOChatRoom_S_QUESTION_userKey = PLVSocketIOChatRoomUserKey;  /* NSDictionary，提问学生相关信息，可以参看 PLVSocketIOChatRoomUserKey 字典属性内容*/

/*!
 @constant    PLVSocketIOChatRoom_T_ANSWER_EVENT
 @abstract    老师回答学生的提问
 */
NSString *const PLVSocketIOChatRoom_T_ANSWER_EVENT = @"T_ANSWER";
    NSString *const PLVSocketIOChatRoom_T_ANSWER_roomId = @"roomId";  /* NSNumber, 房间号 */
    NSString *const PLVSocketIOChatRoom_T_ANSWER_content = @"content";  /* 回答内容 */
    NSString *const PLVSocketIOChatRoom_T_ANSWER_sUserId = @"s_userId";  /* 提问学生userId */
    NSString *const PLVSocketIOChatRoom_T_ANSWER_userKey = PLVSocketIOChatRoomUserKey;  /* NSDictionary，讲师信息，可以参看 PLVSocketIOChatRoomUserKey 字典属性内容 */

#pragma mark 奖杯消息
/*!
 @constant    PLVSocketIOChatRoom_SEND_CUP_EVENT
 @abstract    奖杯事件
 */
NSString *const PLVSocketIOChatRoom_SEND_CUP_EVENT = @"SEND_CUP";

#pragma mark - 连麦事件键集合

/*
 @constant    PLVSocketIOLinkMicUserkey
 @abstract    举手学员信息
 */
 NSString *const PLVSocketIOLinkMicUserkey = @"user";
     NSString *const PLVSocketIOLinkMicUserNickkey = @"nick";  /* 用户昵称 */
     NSString *const PLVSocketIOLinkMicUserPickey = @"pic";  /* 用户头像 */
     NSString *const PLVSocketIOLinkMicUserUserIdkey = @"userId";  /* 用户userId */
     NSString *const PLVSocketIOLinkMicUserUserTypekey = @"userType";  /* 用户类型，默认为空，老师为teacher，ppt学员为slice */
     NSString *const PLVSocketIOLinkMicUserRoomIdkey = @"roomId";  /* NSNumber, optional, 用户所在房间/频道号 */
     NSString *const PLVSocketIOLinkMicUserStatuskey = @"status";  /* optional, 用户当前连麦状态 */
     NSString *const PLVSocketIOLinkMicUserUidkey = @"uid";  /* optional, 用户链接聊天室服务器uid，由服务器生成 */

/*!
 @constant    PLVSocketIOLinkMic_OPEN_MICROPHONE_EVENT
 @abstract    老师开启关闭连麦事件消息
 */
 NSString *const PLVSocketIOLinkMic_OPEN_MICROPHONE_EVENT = @"OPEN_MICROPHONE";
     NSString *const PLVSocketIOLinkMic_OPEN_MICROPHONE_roomId = @"roomId";  /* NSNumber, 房间号 */
     NSString *const PLVSocketIOLinkMic_OPEN_MICROPHONE_type = @"type";  /* 连麦类型：audio(语音通话)；video(视频通话) */
     NSString *const PLVSocketIOLinkMic_OPEN_MICROPHONE_status = @"status";  /* 状态：open(打开)、close(关闭) */
     NSString *const PLVSocketIOLinkMic_OPEN_MICROPHONE_teacherId = @"teacherId";  /* optional, 教师 id */
     NSString *const PLVSocketIOLinkMic_OPEN_MICROPHONE_userId = @"userId"; /* optional, 用户id，存在此字段时非关闭所有连麦 */

/*!
 @constant    PLVSocketIOLinkMic_JOIN_REQUEST_key
 @abstract    举手事件
 */
 NSString *const PLVSocketIOLinkMic_JOIN_REQUEST_key = @"joinRequest";
     NSString *const PLVSocketIOLinkMic_JOIN_REQUEST_roomId = @"roomId";  /* NSNumber, 房间号 */
     NSString *const PLVSocketIOLinkMic_JOIN_REQUEST_userKey = PLVSocketIOLinkMicUserkey;  /* NSDictionary, 等同 PLVSocketIOLinkMicUserkey，学员信息 */

/*!
 @constant    PLVSocketIOLinkMic_JOIN_RESPONSE_key
 @abstract    老师同意通话事件
 */
 NSString *const PLVSocketIOLinkMic_JOIN_RESPONSE_key = @"joinResponse";
     NSString *const PLVSocketIOLinkMic_JOIN_RESPONSE_roomId = @"roomId";  /* NSNumber, 房间号 */
     NSString *const PLVSocketIOLinkMic_JOIN_RESPONSE_userKey = PLVSocketIOLinkMicUserkey;  /* NSDictionary, 等同 PLVSocketIOLinkMicUserkey，学员信息 */
     NSString *const PLVSocketIOLinkMic_JOIN_RESPONSE_value = @"value";

/*!
 @constant    PLVSocketIOLinkMic_JOIN_SUCCESS_key
 @abstract    加入连麦频道成功事件消息
 */
 NSString *const PLVSocketIOLinkMic_JOIN_SUCCESS_key = @"joinSuccess";
     NSString *const PLVSocketIOLinkMic_JOIN_SUCCESS_roomId = @"roomId";  /* NSNumber, 房间号 */
     NSString *const PLVSocketIOLinkMic_JOIN_SUCCESS_userKey = PLVSocketIOLinkMicUserkey;  /* NSDictionary, 等同 PLVSocketIOLinkMicUserkey，学员信息 */
     NSString *const PLVSocketIOLinkMic_JOIN_SUCCESS_value = @"value";

/*!
 @constant    PLVSocketIOLinkMic_JOIN_ANSWERTTL_key
 @abstract    观众查询邀请连麦接受时限
 */
 NSString *const PLVSocketIOLinkMic_JOIN_ANSWERTTL_key = @"joinAnswerTTL";

/*!
 @constant    PLVSocketIOLinkMic_JOIN_LEAVE_key
 @abstract    结束发言事件消息
 */
 NSString *const PLVSocketIOLinkMic_JOIN_LEAVE_key = @"joinLeave";
     NSString *const PLVSocketIOLinkMic_JOIN_LEAVE_roomId = @"roomId";  /* NSNumber, 房间号 */
     NSString *const PLVSocketIOLinkMic_JOIN_LEAVE_userKey = PLVSocketIOLinkMicUserkey;  /* NSDictionary, 等同 PLVSocketIOLinkMicUserkey，学员信息 */

NSString *const PLVSocketLinkMicEventType_TEACHER_INFO_key = @"O_TEACHER_INFO";

NSString *const PLVSocketLinkMicEventType_MuteUserMedia_key = @"MuteUserMedia";

NSString *const PLVSocketLinkMicEventType_SwitchView_key = @"switchView";

NSString *const PLVSocketLinkMicEventType_TEACHER_SET_PERMISSION_key = @"TEACHER_SET_PERMISSION";

/*!
 @constant    PLVSocketLinkMicEventType_changeVideoAndPPTPosition_key
 @abstract    讲师主动切换PPT和播放器的位置
 */
NSString *const PLVSocketLinkMicEventType_changeVideoAndPPTPosition_key = @"changeVideoAndPPTPosition";

/*!
 @constant    PLVSocketLinkMicEventType_switchJoinVoice_key
 @abstract    允许某人上麦
 */
NSString *const PLVSocketLinkMicEventType_switchJoinVoice_key = @"switchJoinVoice";

#pragma mark - 电话入会事件键集合

/*!
 @constant    PLVSocketIOSipLinkMic_EVENT
 @abstract    电话入会事件
 */
NSString *const PLVSocketIOSipLinkMic_EVENT = @"sip";
    NSString *const PLVSocketIOSipLinkMic_type = @"type";  /* NSString, 电话入会事件类型 */
    NSString *const PLVSocketIOSipLinkMic_message = @"messsage";  /* NSString, 相关信息说明 */
    NSString *const PLVSocketIOSipLinkMic_name = @"name";  /* NSString, 用户昵称 */
    NSString *const PLVSocketIOSipLinkMic_phoneNumber = @"phoneNumber";  /*  NSString, 电话号码 */
    NSString *const PLVSocketIOSipLinkMic_isAll = @"isAll"; /* 状态, Y(操作所有)、N(操作指定用户) */
    NSString *const PLVSocketIOSipLinkMic_id = @"id";  /* NSString, 拨号id */
/*!
 @constant    PLVSocketIOSipLinkMic_call_key
 @abstract    呼叫
 */
NSString *const PLVSocketIOSipLinkMic_call_key = @"call";

/*!
 @constant    PLVSocketIOSipLinkMic_cancelCall_key
 @abstract    取消呼叫
 */
NSString *const PLVSocketIOSipLinkMic_cancelCall_key = @"cancelCall";

/*!
 @constant    PLVSocketIOSipLinkMic_recall_key
 @abstract    重新呼叫
 */
NSString *const PLVSocketIOSipLinkMic_recall_key = @"recall";

/*!
 @constant    PLVSocketIOSipLinkMic_callTimeout_key
 @abstract    呼叫超时
 */
NSString *const PLVSocketIOSipLinkMic_callTimeout_key = @"callTimeout";

/*!
 @constant    PLVSocketIOSipLinkMic_callReject_key
 @abstract    拒绝呼叫
 */
NSString *const PLVSocketIOSipLinkMic_callReject_key = @"callReject";

/*!
 @constant    PLVSocketIOSipLinkMic_disconnectCall_key
 @abstract    断开呼叫
 */
NSString *const PLVSocketIOSipLinkMic_disconnectCall_key = @"disconnectCall";

/*!
 @constant    PLVSocketIOSipLinkMic_callFail_key
 @abstract    呼叫失败
 */
NSString *const PLVSocketIOSipLinkMic_callFail_key = @"callFail";

/*!
 @constant    PLVSocketIOSipLinkMic_callSuccess_key
 @abstract    呼叫成功
 */
NSString *const PLVSocketIOSipLinkMic_callSuccess_key = @"callSuccess";

/*!
 @constant    PLVSocketIOSipLinkMic_hangUp_key
 @abstract    挂断
 */
NSString *const PLVSocketIOSipLinkMic_hangUp_key = @"hangUp";

/*!
 @constant    PLVSocketIOSipLinkMic_callIn_key
 @abstract    呼入
 */
NSString *const PLVSocketIOSipLinkMic_callIn_key = @"callIn";

/*!
 @constant    PLVSocketIOSipLinkMic_mute_key
 @abstract    静音
 */
NSString *const PLVSocketIOSipLinkMic_mute_key = @"mute";

/*!
 @constant    PLVSocketIOSipLinkMic_unMute_key
 @abstract    取消静音
 */
NSString *const PLVSocketIOSipLinkMic_unMute_key = @"unMute";

#pragma mark - 云课堂事件键集合

/*!
 @constant    PLVSocketIOPPT_onSliceID_key
 @abstract    登录Socket后服务器传值
 */
NSString *const PLVSocketIOPPT_onSliceID_key = @"onSliceID";
    NSString *const PLVSocketIOPPT_onSliceID_data = @"data"; /* NSDictionary */

/*!
 @constant    PLVSocketIOPPT_onSliceOpen_key
 @abstract    客户端打开PPT操作事件
 */
NSString *const PLVSocketIOPPT_onSliceOpen_key = @"onSliceOpen";
    NSString *const PLVSocketIOPPT_onSliceOpen_data = @"data"; /* NSDictionary */

/*!
 @constant    PLVSocketIOPPT_onSliceStart_key
 @abstract    客户端点击上课推流事件
 */
NSString *const PLVSocketIOPPT_onSliceStart_key = @"onSliceStart";
    NSString *const PLVSocketIOPPT_onSliceStart_data = @"data"; /* NSDictionary */
    NSString *const PLVSocketIOPPT_onSliceStart_isNoCount = @"isNoCount"; /* BOOL, 合并ppt的数据用，也区分开新旧数据 */
    NSString *const PLVSocketIOPPT_onSliceStart_pushTime = @"pushtime"; /* NSNumber */
    NSString *const PLVSocketIOPPT_onSliceStart_timeStamp = @"timeStamp"; /* NSNumber */

/*!
 @constant    PLVSocketIOPPT_onSliceDraw_key
 @abstract    画笔格式事件
 */
NSString *const PLVSocketIOPPT_onSliceDraw_key = @"onSliceDraw";
    NSString *const PLVSocketIOPPT_onSliceDraw_data = @"data"; /* NSDictionary */

/*!
 @constant    PLVSocketIOPPT_onSliceControl_key
 @abstract    PPT控制命令
 */
NSString *const PLVSocketIOPPT_onSliceControl_key = @"onSliceControl";
    NSString *const PLVSocketIOPPT_onSliceControl_data = @"data";  /* NSDictionary */

#pragma mark - 图文直播事件键集合

/*!
 @constant    PLVSocketIOTuwen_Create_key
 @abstract    聊天室消息，收到新的图文触发
 */
NSString *const PLVSocketIOTuwen_Create_key = @"CREATE_IMAGE_TEXT";
NSString *const PLVSocketIOTuwen_Create_data = @"data"; /* NSDictionary */

/*!
 @constant    PLVSocketIOTuwen_Delete_key
 @abstract    聊天室消息，删除图文
 */
NSString *const PLVSocketIOTuwen_Delete_key = @"DELETE_IMAGE_TEXT";
NSString *const PLVSocketIOTuwen_Delete_data = @"data"; /* NSDictionary */

/*!
 @constant    PLVSocketIOTuwen_Settop_key
 @abstract    聊天室消息，置顶图文
 */
NSString *const PLVSocketIOTuwen_Settop_key = @"SET_TOP_IMAGE_TEXT";
NSString *const PLVSocketIOTuwen_Settop_data = @"data"; /* NSDictionary */

/*!
 @constant    PLVSocketIOTuwen_Edit_key
 @abstract    聊天室消息，编辑现有图文
 */
NSString *const PLVSocketIOTuwen_Edit_key = @"SET_IMAGE_TEXT_MSG";
NSString *const PLVSocketIOTuwen_Edit_data = @"data"; /* NSDictionary */

#pragma mark - 问答功能事件键集合

/*!
 @constant    PLVSocketIOQA_LAUNCH_QUESTION_EVENT
 @abstract    发出问答事件
 */
NSString *const PLVSocketIOQA_LAUNCH_QUESTION_EVENT = @"LAUNCH_Q";
    NSString *const PLVSocketIOQA_LAUNCH_QUESTION_content = @"content";

/*!
 @constant    PLVSocketIOQA_LAUNCH_ANSWER_EVENT
 @abstract    回复问答事件
 */
NSString *const PLVSocketIOQA_LAUNCH_ANSWER_EVENT = @"LAUNCH_A";
    NSString *const PLVSocketIOQA_LAUNCH_ANSWER_content = @"content";
    NSString *const PLVSocketIOQA_LAUNCH_ANSWER_id = @"id";
    NSString *const PLVSocketIOQA_LAUNCH_ANSWER_user = @"user";
    NSString *const PLVSocketIOQA_LAUNCH_ANSWER_sessionId = @"sessionId";
    NSString *const PLVSocketIOQA_LAUNCH_ANSWER_roomId = @"roomId";
    NSString *const PLVSocketIOQA_LAUNCH_ANSWER_questionerId = @"questionerId";
    NSString *const PLVSocketIOQA_LAUNCH_ANSWER_questionId = @"questionId";

/*!
 @constant    PLVSocketIOQA_DELETE_ANSWER_EVENT
 @abstract    删除问答事件
 */
NSString *const PLVSocketIOQA_DELETE_ANSWER_EVENT = @"DELETE_QA_ANSWER";
    NSString *const PLVSocketIOQA_DELETE_ANSWER_id = @"id";
    NSString *const PLVSocketIOQA_DELETE_ANSWER_questionId = @"questionId";
    NSString *const PLVSocketIOQA_DELETE_ANSWER_roomId = @"roomId";
    NSString *const PLVSocketIOQA_DELETE_ANSWER_sessionId = @"sessionId";

#pragma mark - 互动课堂事件键集合

/*!
 @constant    PLVSocketIOClass_onClassStart_key
 @abstract    开始上课事件
 */
NSString *const PLVSocketIOClass_onClassStart_key = @"onClassStart";
    NSString *const PLVSocketIOClass_onClassStart_roomId = @"roomId";
    NSString *const PLVSocketIOClass_onClassStart_sessionId = @"sessionId";
    NSString *const PLVSocketIOClass_onClassStart_timeStamp = @"timeStamp"; /* NSNumber */
    NSString *const PLVSocketIOClass_onClassStart_isNoCount = @"isNoCount"; /* BOOL */
    NSString *const PLVSocketIOClass_onClassStart_data = @"data"; /* NSDictionary */
    NSString *const PLVSocketIOClass_onClassStart_pushtime = @"pushtime"; /* NSNumber */
    NSString *const PLVSocketIOClass_onClassStart_teacherId = @"teacherId";
/*!
 @constant    PLVSocketIOClass_onClassEnd_key
 @abstract    结束上课事件
 */
NSString *const PLVSocketIOClass_onClassEnd_key = @"onClassEnd";
    NSString *const PLVSocketIOClass_onClassEnd_roomId = @"roomId";
    NSString *const PLVSocketIOClass_onClassEnd_timeStamp = @"timeStamp";

/*!
 @constant    PLVSocketIOClass_onClassControl_key
 @abstract    课堂控制事件
 */
NSString *const PLVSocketIOClass_onClassControl_key = @"onClassControl";
    NSString *const PLVSocketIOClass_onClassControl_roomId = @"roomId";
    NSString *const PLVSocketIOClass_onClassControl_timeStamp = @"timeStamp"; /* NSNumber */
    NSString *const PLVSocketIOClass_onClassControl_type = @"type"; /* muteAllAudio(全员静音)、muteAllVideo(全员下麦)、muteUserAudio(静音某个学员)、muteUserVideo(下麦某个学员) */
NSString *const PLVSocketIOClass_onClassControl_isMuted = @"isMuted"; /* BOOL: true(静音/下麦)、false(恢复静音/上麦) */
    NSString *const PLVSocketIOClass_onClassControl_userId = @"userId"; /* Optional, 学员id */

#pragma mark - H5互动消息事件键集合

#pragma mark 答题卡事件键集合
/*!
 @constant    PLVSocketInteraction_onTriviaCard_key
 @abstract    答题卡事件
 */
NSString *const PLVSocketInteraction_onTriviaCard_key = @"onTriviaCard";
    NSString *const PLVSocketInteraction_onTriviaCard_questionContent = @"GET_TEST_QUESTION_CONTENT";
    NSString *const PLVSocketInteraction_onTriviaCard_questionResult = @"GET_TEST_QUESTION_RESULT";
    NSString *const PLVSocketIOClass_onTriviaCard_stop = @"STOP_TEST_QUESTION";
    NSString *const PLVSocketInteraction_onTriviaCard_about = @"TEST_QUESTION";

#pragma mark 问卷事件键集合
/*!
 @constant    PLVSocketInteraction_onQuestionnaire_key
 @abstract    问卷事件
 */
NSString *const PLVSocketInteraction_onQuestionnaire_key = @"onQuestionnaire";
    NSString *const PLVSocketInteraction_onQuestionnaire_start = @"START_QUESTIONNAIRE";
    NSString *const PLVSocketInteraction_onQuestionnaire_stop = @"STOP_QUESTIONNAIRE";
    NSString *const PLVSocketInteraction_onQuestionnaire_sendResult = @"SEND_QUESTIONNAIRE_RESULT";
    NSString *const PLVSocketInteraction_onQuestionnaire_achievement = @"QUESTIONNAIRE_ACHIEVEMENT";
    NSString *const PLVSocketInteraction_onQuestionnaire_about = @"QUESTIONNAIRE";

#pragma mark 抽奖事件键集合
/*!
 @constant    PLVSocketInteraction_onLottery_key
 @abstract    抽奖事件
 */
NSString *const PLVSocketInteraction_onLottery_key = @"onLottery";
    NSString *const PLVSocketInteraction_onLottery_start = @"LotteryStart";
    NSString *const PLVSocketInteraction_onLottery_stop = @"LotteryEnd";
    NSString *const PLVSocketInteraction_onLottery = @"ON_LOTTERY";
    NSString *const PLVSocketInteraction_onLottery_winner = @"LotteryWinner";
    NSString *const PLVSocketInteraction_onLottery_about = @"Lottery";

#pragma mark 签到事件键集合
/*!
 @constant    PLVSocketInteraction_onSignIn_key
 @abstract    签到事件
 */
NSString *const PLVSocketInteraction_onSignIn_key = @"onSignIn";
    NSString *const PLVSocketInteraction_onSignIn_start = @"SIGN_IN";
    NSString *const PLVSocketInteraction_onSignIn_stop = @"STOP_SIGN_IN";
    NSString *const PLVSocketInteraction_onSignIn_about = @"SIGN_IN";

#pragma mark 中奖消息记录事件键集合
/*!
 @constant    PLVSocketInteraction_lotteryWinRecord_key
 @abstract    签到事件
 */
NSString *const PLVSocketInteraction_lotteryWinRecord_key = @"onLotteryWinRecord";
    NSString *const PLVSocketInteraction_lotteryWinRecord_about = @"Lottery_Win_Record";

#pragma mark 卡片推送事件键集合
/*!
 @constant    PLVSocketCardPush_newsPush_key
 @abstract    营销 - 卡片推送事件
 */
NSString *const PLVSocketCardPush_newsPush_key = @"newsPush";
    NSString *const PLVSocketCardPush_newsPush_EVENT = @"EVENT"; /* start开启卡片推送 cancel取消卡片推送*/
    NSString *const PLVSocketCardPush_newsPush_roomId = @"roomId";
    NSString *const PLVSocketCardPush_newsPush_entrance = @"entrance";/* 是否开启卡片入口 */
    NSString *const PLVSocketCardPush_newsPush_lookTime = @"lookTime"; /* 观看时长后弹出 (毫秒) */
    NSString *const PLVSocketCardPush_newsPush_time = @"total";  /* 卡片倒计时 */

/*!
 @constant    PLVSocketProduct_product_key
 @abstract    营销 - 热卖商品推送事件
 */
NSString *const PLVSocketProduct_product_key = @"product";
    NSString *const PLVSocketProduct_productClick_EVENT = @"EVENT"; /* 商品点击事件 */
    NSString *const PLVSocketProduct_productClick_nickName = @"nickName"; /* 昵称 */
    NSString *const PLVSocketProduct_productClick_type = @"type";/* 商品类型 */
    NSString *const PLVSocketProduct_productClick_positionName = @"positionName"; /* 商品信息 */

