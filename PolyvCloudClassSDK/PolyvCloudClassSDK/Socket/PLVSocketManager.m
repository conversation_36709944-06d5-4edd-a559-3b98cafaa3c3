//
//  PLVSocketManager.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/13.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVSocketManager.h"
#import "PLVSocketManager+Private.h"
#import "PLVLiveVideoAPI.h"
#import "PLVLiveVClassAPI.h"
#import "PLVLiveVideoConfig.h"
#import "PLVWErrorManager.h"
#import "PLVWELogEventDefine.h"
#import "PLVWLogReporterManager.h"
#import "PLVConsoleLogger.h"
#import "PLVLoganManager+Private.h"

#import <PLVBusinessSDK/PLVBSocketClient.h>
#import <PLVBusinessSDK/PLVBSocketClientConfiguration.h>
#import <PLVFoundationSDK/PLVFoundationSDK.h>

static NSInteger const kPLVSocketDefaultChatMessageMaxLength = 50;

@interface PLVSocketManager ()<
PLVBSocketClientProtocol
>

#pragma mark 外部只读属性
@property (nonatomic, assign) BOOL childRoomEnable;
@property (nonatomic, strong) NSString *roomId;
@property (nonatomic, copy) NSString *loginSuccessAckString;

#pragma mark 外部不可见属性

@property (nonatomic, assign) BOOL gettingToken; /// 是否正在获取token
@property (nonatomic, assign) NSInteger getTokenFailCount; /// 获取token失败次数，超过getTokenMaxCount不再重试
@property (nonatomic, strong) PLVBSocketClient *socket;
@property (nonatomic, strong) PLVBSocketClientConfiguration *socketConfiguration;
@property (nonatomic, strong) NSMutableArray *loginMessageList;
@property (nonatomic, strong) NSMutableArray *speakMessageList;
@property (nonatomic, strong) NSTimer *chatMessageOnceControlTimer;
@property (nonatomic, assign) NSInteger messagesReceivedIn500ms; // 500ms接收socket消息总数量
@property (nonatomic, assign) NSInteger oncePeakCount; // 500ms接收socket消息最大数量
@property (nonatomic, assign) NSInteger onceCount; // 每分钟接收socket消息总数量
@property (nonatomic, assign) NSInteger totalPeakCount; // 每分钟接收socket消息最大数量
@property (nonatomic, assign) NSInteger totalCount; // 接收socket消息总数量
@property (nonatomic, assign) NSTimeInterval lastReportTime; // 上一次上报的时间

@end

@implementation PLVSocketManager {
    dispatch_queue_t multicastQueue;
    PLVMulticastDelegate<PLVSocketManagerProtocol> *multicastDelegate;
}

#pragma mark - Getter

- (PLVSocketConnectStatus)status {
    return (PLVSocketConnectStatus)self.socket.status;
}

- (BOOL)login {
    return self.socket.login;
}

- (NSString *)socketId {
    return self.socket.socketId;
}

- (PLVSocketUserType)userType {
    return (PLVSocketUserType)self.socketConfiguration.user.userType;
}

- (NSString *)userTypeString {
    return self.socketConfiguration.user.userTypeString;
}

- (NSString *)linkMicId {
    return self.socketConfiguration.user.linkMicId;
}

- (NSString *)viewerId {
    return self.socketConfiguration.user.viewerId;
}

- (NSString *)viewerName {
    return self.socketConfiguration.user.viewerName;
}

- (NSString *)avatarUrl {
    return self.socketConfiguration.user.avatarUrl;
}

- (NSString *)actor {
    return self.socketConfiguration.user.actor;
}

- (NSString *)liveParam4 {
    return self.socketConfiguration.user.liveParam4;
}

- (NSString *)chatToken {
    return self.token;
}

#pragma mark - 生命周期

+ (instancetype)sharedManager {
    static dispatch_once_t onceToken;
    static PLVSocketManager *mananger = nil;
    dispatch_once(&onceToken, ^{
        mananger = [[self alloc] init];
    });
    return mananger;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        multicastQueue = dispatch_queue_create("com.PLVLiveScenesSDK.PLVSocketManager", DISPATCH_QUEUE_CONCURRENT);
        multicastDelegate = (PLVMulticastDelegate <PLVSocketManagerProtocol> *)[[PLVMulticastDelegate alloc] init];
        _loginMessageList = [NSMutableArray array];
        _speakMessageList = [NSMutableArray array];
    }
    return self;
}

- (void)dealloc {
    [self stopChatMessageOnceControlTimer];
}

#pragma mark - 登入

- (BOOL)emitLoginEvent {
    if (self.socket.status != PLVBSocketConnectStatusConnected ||
        !self.socket.login) {
        return NO;
    }
    
    PLVBSocketClientConfiguration *configuration = self.socket.configuration;
    PLVBSocketAccount *account = configuration.account;
    PLVBSocketUser *user = configuration.user;
    
    if (!configuration ||
        !user) {
        return NO;
    }
    
    if (![PLVFdUtil checkStringUseable:self.roomId]) {
        return NO;
    }
    
    NSMutableDictionary *loginDict = [NSMutableDictionary dictionary];
    plv_dict_set(loginDict, @"EVENT", @"LOGIN");
    plv_dict_set(loginDict, @"getCup", @(configuration.getCup ? 1 : 0));
    
    plv_dict_set(loginDict, @"roomId", self.roomId);
    plv_dict_set(loginDict, @"channelId", account.channelId);
    plv_dict_set(loginDict, @"sessionId", configuration.sessionId);
    if (account.userId) {
        plv_dict_set(loginDict, @"accountId", account.userId);
    }
    
    NSString *userTypeString = user.userTypeString ?: @"";
    NSString *viewerId = user.viewerId ?: @"";
    NSString *viewerName = user.viewerName ?: @"";
    NSString *viewerAvatar = user.avatarUrl ?: @"";
    plv_dict_set(loginDict, @"type", userTypeString);
    plv_dict_set(loginDict, @"actor", user.actor);
    plv_dict_set(loginDict, @"micId", user.linkMicId);
    plv_dict_set(loginDict, @"values", @[viewerName, viewerAvatar, viewerId]);
    
    __weak typeof(self) weakSelf = self;
    BOOL success = [[PLVSocketManager sharedManager] emitMessage:loginDict timeout:12.0 callback:^(NSArray * _Nonnull ackArray) {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypeSocket, @"ackArray %@", ackArray);
        BOOL success = NO;
        
        NSString *ackStr = nil;
        if (ackArray && [ackArray count] > 0) {
            ackStr = [NSString stringWithFormat:@"%@", ackArray.firstObject];
            if (ackStr && [ackStr isKindOfClass:[NSString class]] && ackStr.length > 4) {
                int status = [[ackStr substringToIndex:1] intValue];
                success = (status == 2);
                if (status == 2) {
                    success = YES;
                }
            }
        }
        
        if (!success) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"socket emit login message failed with ack【%@】(roomId:%@, channelId:%@)", ackArray, weakSelf.roomId, account.channelId);
        }
    }];
    return success;
}

- (BOOL)loginWithChannelId:(NSString *)channelId
                  viewerId:(NSString *)viewerId
                viewerName:(NSString *)viewerName
                 avatarUrl:(NSString *)avatarUrl
                     actor:(NSString * _Nullable)actor
                  userType:(PLVSocketUserType)userType {
    return [self loginWithChannelId:channelId viewerId:viewerId viewerName:viewerName avatarUrl:avatarUrl extraParam:nil actor:actor userType:userType];
}
- (BOOL)loginWithChannelId:(NSString *)channelId
                  viewerId:(NSString *)viewerId
                viewerName:(NSString *)viewerName
                 avatarUrl:(NSString *)avatarUrl
                extraParam:(NSDictionary * _Nullable)extraParam
                     actor:(NSString * _Nullable)actor
                  userType:(PLVSocketUserType)userType {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeSocket, @"%s", __FUNCTION__);
    NSString *userId = [PLVLiveVideoConfig sharedInstance].userId;
    if (!self.vclassDomain) {
        if (!userId || ![userId isKindOfClass:[NSString class]] || userId.length == 0) {
            NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulSocket code:PLVFSocketErrorCodeGetToken_ParameterError];
            [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:nil];
            [self notifyDelegatesDidStartFailure:failError];
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s, login failed, %@, userId:%@", __FUNCTION__, failError, userId);
            return NO;
        }
    }
    if (!channelId || ![channelId isKindOfClass:[NSString class]] || channelId.length == 0) {
        NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulSocket code:PLVFSocketErrorCodeGetToken_ParameterError];
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:nil];
        [self notifyDelegatesDidStartFailure:failError];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s, login failed, %@, channelId:%@", __FUNCTION__, failError, channelId);
        return NO;
    }
    if (!viewerId || ![viewerId isKindOfClass:[NSString class]] || viewerId.length == 0 ||
        !viewerName || ![viewerName isKindOfClass:[NSString class]] || viewerName.length == 0 ||
        !avatarUrl || ![avatarUrl isKindOfClass:[NSString class]] || avatarUrl.length == 0 ||
        userType == PLVSocketUserTypeUnknown || userType > PLVSocketUserTypeSCStudent) {
        NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulSocket code:PLVFSocketErrorCodeParameterInvalid];
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:nil];
        [self notifyDelegatesDidStartFailure:failError];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s, login failed, %@, viewerId:%@, viewerName:%@, avatarUrl:%@, userType:%lu", __FUNCTION__, failError, viewerId, viewerName, avatarUrl, userType);
        return NO;
    }
    
    if (self.socket) { // 如果之前已处于登录状态，需要先退出登录
        [self.socket logout];
    }
    
    // 初始化socketConfiguration对象
    PLVBSocketClientConfiguration *configuration = [[PLVBSocketClientConfiguration alloc] init];
    
    // 配置socketConfiguration对象
    configuration.getCup = self.getCup;
    configuration.sessionId = self.lessonId;
    [configuration configureDomain:self.chatDomain];
    [configuration setAccountWithChannelId:channelId userId:userId];
    
    PLVBSocketUserType bUserType = (PLVBSocketUserType)userType;
    [configuration setUserWithViewerId:viewerId viewerName:viewerName avatarUrl:avatarUrl extraParam:extraParam actor:actor userType:bUserType];
    PLV_KEY_INFO(@"viewerIds", viewerId);
    
    NSSet *listenEvents = [NSSet setWithArray:[self getFinalListenEvents]];
    [configuration configureListeningEvents:listenEvents];
    
    self.socketConfiguration = configuration;
    self.socket = [PLVBSocketClient clientWithConfiguration:self.socketConfiguration];
    self.socket.delegate = self;
    
    // 获取房间号和token
    BOOL success = [self getToken];
    return success;
}

- (NSArray *)getFinalListenEvents {
    NSArray *defaultEvents = @[@"customMessage",
                              @"joinRequest",
                              @"joinResponse",
                              @"joinSuccess",
                              @"joinLeave",
                              @"joinAnswer",
                              @"MuteUserMedia",
                              @"switchView",
                              @"assistantSliceControl",
                              @"emotion",
                              @"seminar",
                              @"changeMicSite",
                              @"timer",
                              @"newsPush",
                              @"focus",
                              @"transmit",
                              @"sip",
                              @"product",
                              @"speak"];
    
    if (!self.listeningEvents || ![self.listeningEvents isKindOfClass:[NSSet class]] || self.listeningEvents.count == 0) {
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypeSocket, @"custom listening events is nil");
        return defaultEvents;
    }
    
    NSMutableArray *eventArray = [[NSMutableArray alloc] initWithArray:defaultEvents];
    for (NSString *event in self.listeningEvents) {
        if (![event isKindOfClass:[NSString class]] || event.length == 0) {
            continue;
        }
        [eventArray addObject:event];
    }
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeSocket, @"listening events with (parameters:%@)", eventArray);
    return [eventArray copy];
}

- (BOOL)getToken {
    NSString *channelId = self.socketConfiguration.account.channelId;
    NSString *viewerId = self.viewerId;
    if (!channelId || ![channelId isKindOfClass:[NSString class]] || channelId.length == 0 ||
        !viewerId || ![viewerId isKindOfClass:[NSString class]] || viewerId.length == 0) {
        NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulSocket code:PLVFSocketErrorCodeGetToken_ParameterError];
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:nil];
        [self notifyDelegatesDidStartFailure:failError];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s, getToken failed with 【%@】(channelId:%@, viewerId；%@)", __FUNCTION__, failError, channelId, viewerId);
        return NO;
    }
    
    if (self.getTokenFailCount == 3) {// 最多重试3次
        NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulSocket code:PLVFSocketErrorCodeGetTokenRetryError];
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:nil];
        [self notifyDelegatesDidStartFailure:failError];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s, ReGetToken failed with 【%@】(channelId:%@, viewerId；%@)", __FUNCTION__, failError, channelId, viewerId);
        return NO;
    }
    
    if (self.gettingToken) {
        return NO;
    }
    self.gettingToken = YES;
    
    __weak typeof(self) weakSelf = self;
    
    void(^successBlock)(NSString * _Nonnull roomId, NSString * _Nonnull token) =
    ^(NSString *roomId, NSString *token) {
        PLV_LOG_INFO(PLVConsoleLogModuleTypeSocket, @"get chat token success with (roomId:%@, token:%@", weakSelf.roomId, token);
        weakSelf.gettingToken = NO;
        weakSelf.getTokenFailCount = 0;
        
        weakSelf.roomId = roomId;
        weakSelf.token = token;
        [weakSelf loginSocketWithRoomId:weakSelf.roomId token:token];
    };
    
    void(^failureBlock)(NSError * _Nonnull error) = ^(NSError * _Nonnull error) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s, getToken failed with 【%@】(channelId:%@, viewerId:%@)", __FUNCTION__, error, channelId, viewerId);
        
        weakSelf.gettingToken = NO;
        weakSelf.getTokenFailCount++;
        [weakSelf performSelector:@selector(getToken) withObject:nil afterDelay:3.0]; // 3秒后重试
    };
    
    if (self.vclassDomain) {
        PLVBSocketUser *user = self.socketConfiguration.user;
        if (user.userType == PLVBSocketUserTypeTeacher) { // 互动学堂讲师端
            [PLVLiveVClassAPI teacherChatTokenWithLessonId:self.lessonId success:^(NSDictionary * _Nonnull responseDict) {
                NSString *token = PLV_SafeStringForDictKey(responseDict, @"chatToken");
                successBlock(channelId, token);
            } failure:failureBlock];
        } else if (user.userType == PLVBSocketUserTypeSCStudent) { // 互动学堂学生端
            [PLVLiveVClassAPI watcherChatTokenWithCourseCode:self.courseCode lessonId:self.lessonId success:^(NSDictionary * _Nonnull responseDict) {
                NSString *token = PLV_SafeStringForDictKey(responseDict, @"chatToken");
                successBlock(channelId, token);
            } failure:failureBlock];
        } else {
            return NO;
        }
    } else { // 非互动学堂场景
        NSString *userTypeString = self.userTypeString;
        [PLVLiveVideoAPI getChatTokenWithChannelId:channelId userId:viewerId role:userTypeString groupId:self.liveParam4 completion:^(NSDictionary * _Nonnull data) {
            NSString *childRoomEnabled = PLV_SafeStringForDictKey(data, @"childRoomEnabled");
            weakSelf.childRoomEnable = childRoomEnabled.boolValue;
            
            NSString *roomId = nil;
            if (weakSelf.allowChildRoom && weakSelf.childRoomEnable) { // allowChildRoom与childRoomEnable均为YES才开启分房间
                NSString *roomIdValue = PLV_SafeStringForDictKey(data, @"roomId");
                if (roomIdValue) {
                    roomId = roomIdValue;
                }
            } else {
                roomId = channelId;
            }
            
            NSString *token = PLV_SafeStringForDictKey(data, @"token");
            successBlock(roomId, token);
        } failure:failureBlock];
    }
    
    return YES;
}

- (void)loginSocketWithRoomId:(NSString *)roomId token:(NSString *)token {
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0 ||
        !token || ![token isKindOfClass:[NSString class]] || token.length == 0) {
        NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulSocket code:PLVFSocketErrorCodeGetToken_DataError];
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:nil];
        [self notifyDelegatesDidStartFailure:failError];
        
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s, login socket failed 【%@】(roomId:%@, token:%@)", __FUNCTION__, failError, roomId, token);
        return;
    }
    
    [self.socket loginWithRoomId:roomId token:token];
}

#pragma mark - 登出

- (void)logout {
    [[PLVLoganManager sharedManager] checkAndReportCurrentLogan];
    PLV_LOG_INFO(PLVConsoleLogModuleTypeSocket, @"call socket logout");
    // 移除所有监听
    [self removeAllDelegates];
    
    // 关键资源释放，关键属性置空
    if (self.socket) {
        [self.socket logout];
        self.socket = nil;
    }
    self.socketConfiguration = nil;
    
    // 外部读写属性重置
    self.allowChildRoom = NO;
    self.getCup = NO;
    self.chatDomain = nil;
    self.listeningEvents = nil;
    self.vclassDomain = NO;
    self.courseCode = nil;
    self.lessonId = nil;
    self.chatMessageOnceControlEnabled = NO;
    self.chatMessageMaxLengthOnce = 0;
    self.messagesReceivedIn500ms = 0;
    self.oncePeakCount = 0;
    self.onceCount = 0;
    self.totalCount = 0;
    self.totalPeakCount = 0;
    self.lastReportTime = 0;
    
    // 外部不可见/只读属性重置
    self.childRoomEnable = NO;
    self.roomId = nil;
    self.loginSuccessAckString = nil;
    self.gettingToken = NO;
    self.getTokenFailCount = 0;
}

#pragma mark - Add Socket Event

- (void)addObserveSocketEvent:(NSString *)event {
    if (self.login && ![[self getFinalListenEvents] containsObject:event]) {
        [self.socket addObserveSocketEvent:event];
    }
}

#pragma mark -  聊天消息阈值管理

- (void)setChatMessageOnceControlEnabled:(BOOL)chatMessageOnceControlEnabled {
    _chatMessageOnceControlEnabled = chatMessageOnceControlEnabled;
    if (!chatMessageOnceControlEnabled) {
        [self stopChatMessageOnceControlTimer];
        [self chatMessageOnceControlTimerAction];
    } else {
        [self startChatMessageOnceControlTimer];
    }
}

- (NSInteger)chatMessageMaxLength {
    if (self.chatMessageMaxLengthOnce <= 0) {
        return kPLVSocketDefaultChatMessageMaxLength;
    }
    return self.chatMessageMaxLengthOnce;
}

- (void)startChatMessageOnceControlTimer {
    if (_chatMessageOnceControlTimer) {
        [self stopChatMessageOnceControlTimer];
    }
    if (!self.chatMessageOnceControlEnabled) {
        return;
    }
    _chatMessageOnceControlTimer = [NSTimer scheduledTimerWithTimeInterval:0.5 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(chatMessageOnceControlTimerAction) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:_chatMessageOnceControlTimer forMode:NSRunLoopCommonModes];
    self.messagesReceivedIn500ms = 0;
    self.oncePeakCount = 0;
    self.onceCount = 0;
    self.totalCount = 0;
    self.totalPeakCount = 0;
    self.lastReportTime = [[NSDate date] timeIntervalSince1970];
}

- (void)stopChatMessageOnceControlTimer {
    [_chatMessageOnceControlTimer invalidate];
    _chatMessageOnceControlTimer = nil;
}

- (void)chatMessageOnceControlTimerAction {
    if (self.chatMessageOnceControlEnabled) {
        NSInteger currentMessagesReceivedIn500ms = self.messagesReceivedIn500ms;
        self.messagesReceivedIn500ms = 0;
        self.onceCount += currentMessagesReceivedIn500ms;
        if (currentMessagesReceivedIn500ms > self.oncePeakCount) {
            self.oncePeakCount = currentMessagesReceivedIn500ms;
        }
        NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
        if (currentTime - self.lastReportTime >= 60) { // 每分钟上报socket数量统计
            self.lastReportTime = currentTime;
            if (self.onceCount > self.totalPeakCount) {
                self.totalPeakCount = self.onceCount;
            }
            NSString *info = [NSString stringWithFormat:@"totalCount:%ld, totalPeakCount:%ld, onceCount:%ld, oncePeakCount:%ld, reportTime:%lld", self.totalCount, self.totalPeakCount, self.onceCount, self.oncePeakCount, (long long)currentTime * 1000];
            self.onceCount = 0;
            [[PLVWLogReporterManager sharedManager] reportWithEvent:@"socketMessageCount" modul:PLVWELogModulSocket information:info patch:YES];
        }
    }
    
    if (self.loginMessageList.count > 0) {
        NSArray *loginMessageArray = [self.loginMessageList subarrayWithRange:NSMakeRange(0, MIN(self.chatMessageMaxLength, self.loginMessageList.count))];
        [self.loginMessageList removeAllObjects];
        for (NSDictionary *loginMessage in loginMessageArray) {
            NSString *subEvent = PLV_SafeStringForDictKey(loginMessage, @"subEvent");
            NSString *jsonString = PLV_SafeStringForDictKey(loginMessage, @"jsonString");
            id object = loginMessage[@"jsonObject"];
            [self notifyDelegatesDidReceiveMessage:subEvent json:jsonString jsonObject:object];
        }
    }
    if (self.speakMessageList.count > 0) {
        NSArray *speakMessageArray = [self.speakMessageList subarrayWithRange:NSMakeRange(0, MIN(self.chatMessageMaxLength, self.speakMessageList.count))];
        [self.speakMessageList removeAllObjects];
        for (NSDictionary *speakMessage in speakMessageArray) {
            NSString *subEvent = PLV_SafeStringForDictKey(speakMessage, @"subEvent");
            NSString *jsonString = PLV_SafeStringForDictKey(speakMessage, @"jsonString");
            id object = speakMessage[@"jsonObject"];
            [self notifyDelegatesDidReceiveMessage:subEvent json:jsonString jsonObject:object];
        }
    }
}

#pragma mark - PLVBSocketClient Protocol

- (void)socketClient:(PLVBSocketClient *)socketClient didConnectStatusChange:(PLVBSocketConnectStatus)connectStatus {
    PLV_LOG_INFO(PLVConsoleLogModuleTypeSocket, @"socket connect  with (state:%lu)", connectStatus);

    NSInteger errorCode = -1;
    if (connectStatus == PLVBSocketConnectStatusError) {
        errorCode = PLVFSocketErrorCodeConnectError;
    } else if (connectStatus == PLVBSocketConnectStatusReconnect) {
        errorCode = PLVFSocketErrorCodeReconnect;
    } else if (connectStatus == PLVBSocketConnectStatusDisconnect) {
        errorCode = PLVFSocketErrorCodeDisconnect;
    }
    if (errorCode != -1) {
        NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulSocket code:errorCode];
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:nil];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s, socket connect failed with【%@】", __FUNCTION__, failError);
    }
    
    [self notifyDelegatesDidConnectStatusChange:(PLVSocketConnectStatus)connectStatus];
}

- (void)socketClient:(PLVBSocketClient *)socketClient didLoginSuccess:(NSArray <NSString *> *)ackArray {
    PLV_LOG_INFO(PLVConsoleLogModuleTypeSocket, @"socket login success with (ackString:%@)", ackArray);
    self.loginSuccessAckString = ackArray.firstObject;
    [self notifyDelegatesDidLoginSuccess:self.loginSuccessAckString];
    
    if ([ackArray count] > 1 &&
        [ackArray[1] isKindOfClass:[NSDictionary class]]) {
        NSDictionary *extraInfo = (NSDictionary *)ackArray[1];
        for (NSString *key in extraInfo.allKeys) {
            if ([key isKindOfClass:[NSString class]] &&
                [key isEqualToString:@"transmitDoubleMode"]) {
                NSString *event = @"transmit";
                NSString *subEvent = @"transmitDoubleMode";
                NSString *jsonString = extraInfo[key];
                [self notifyDelegatesDidReceiveEvent:event subEvent:subEvent json:jsonString jsonObject:nil];
            }
        }
    }
}

- (void)socketClient:(PLVBSocketClient *)socketClient didLoginFailure:(NSError *)error {
    PLV_LOG_INFO(PLVConsoleLogModuleTypeSocket, @"socket login failed with 【%@】", error);
    
    NSInteger errorCode = -1;
    if (error.code == PLVSocketLoginErrorCodeAckError) {
        errorCode = PLVFSocketErrorCodeLoginAckDataError;
    } else if (error.code == PLVSocketLoginErrorCodeTokenExpired) {
        errorCode = PLVFSocketErrorCodeTokenInvail;
    } else if (error.code == PLVSocketLoginErrorCodeLoginRefuse) {
        errorCode = PLVFSocketErrorCodeLoginRefuse;
    } else if (error.code == PLVSocketLoginErrorCodeRelogin) {
        errorCode = PLVFSocketErrorCodeLoginRelogin;
    } else if (error.code == PLVSocketLoginErrorCodeKick) {
        errorCode = PLVFSocketErrorCodeLoginAckBeKicked;
    }
    if (errorCode != -1) {
        NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulSocket code:errorCode];
        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:nil];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s, socket connect failed with 【%@】", __FUNCTION__, failError);
    }
    
    [self notifyDelegatesDidLoginFailure:error];
}

- (void)socketClient:(PLVBSocketClient *)socketClient
   didReceiveMessage:(NSString *)subEvent
                json:(NSString *)jsonString
          jsonObject:(id)object {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeSocket, @"[message evenet] new message with (subEvent:%@, jsonString:%@)", subEvent, jsonString);
    
    if (self.chatMessageOnceControlEnabled) {
        self.messagesReceivedIn500ms++;
        self.totalCount++;
    }
    
    if ([subEvent isEqualToString:@"LOGIN"] && self.chatMessageOnceControlEnabled) {
        NSMutableDictionary *loginDict = [NSMutableDictionary dictionary];
        plv_dict_set(loginDict, @"subEvent", subEvent);
        plv_dict_set(loginDict, @"jsonString", jsonString);
        plv_dict_set(loginDict, @"jsonObject", object);
        [self.loginMessageList addObject:loginDict];
    } else if ([subEvent isEqualToString:@"SPEAK"] && self.chatMessageOnceControlEnabled) {
        NSMutableDictionary *speakDict = [NSMutableDictionary dictionary];
        plv_dict_set(speakDict, @"subEvent", subEvent);
        plv_dict_set(speakDict, @"jsonString", jsonString);
        plv_dict_set(speakDict, @"jsonObject", object);
        [self.speakMessageList addObject:speakDict];
    } else {
        [self notifyDelegatesDidReceiveMessage:subEvent json:jsonString jsonObject:object];
    }
}

- (void)socketClient:(PLVBSocketClient *)socketClient
     didReceiveEvent:(NSString *)event
            subEvent:(NSString * _Nullable)subEvent
                json:(NSString *)jsonString
          jsonObject:(id)object {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeSocket, @"[other evenet] new message with (event:%@, subEvent:%@, jsonString:%@)", event, subEvent, jsonString);
    
    if (self.chatMessageOnceControlEnabled) {
        self.messagesReceivedIn500ms++;
        self.totalCount++;
    }
    
    [self notifyDelegatesDidReceiveEvent:event subEvent:subEvent json:jsonString jsonObject:object];
}

#pragma mark - Multicase

- (void)addDelegate:(id<PLVSocketManagerProtocol>)delegate delegateQueue:(dispatch_queue_t)delegateQueue {
    dispatch_barrier_async(multicastQueue, ^{
        [self->multicastDelegate addDelegate:delegate delegateQueue:delegateQueue];
    });
}

- (void)removeDelegate:(id<PLVSocketManagerProtocol>)delegate {
    dispatch_barrier_async(multicastQueue, ^{
        [self->multicastDelegate removeDelegate:delegate];
    });
}

- (void)removeAllDelegates {
    dispatch_barrier_async(multicastQueue, ^{
        [self->multicastDelegate removeAllDelegates];
    });
}

- (void)notifyDelegatesDidStartFailure:(NSError *)error {
    dispatch_async(multicastQueue, ^{
        [self->multicastDelegate socketManager_didStartFailure:error];
    });
}

- (void)notifyDelegatesDidConnectStatusChange:(PLVSocketConnectStatus)status {
    dispatch_async(multicastQueue, ^{
        [self->multicastDelegate socketMananger_didConnectStatusChange:status];
    });
}

- (void)notifyDelegatesDidLoginSuccess:(NSString *)ackString {
    dispatch_async(multicastQueue, ^{
        [self->multicastDelegate socketMananger_didLoginSuccess:ackString];
    });
}

- (void)notifyDelegatesDidLoginFailure:(NSError *)error {
    dispatch_async(multicastQueue, ^{
        [self->multicastDelegate socketMananger_didLoginFailure:error];
    });
}

- (void)notifyDelegatesDidReceiveMessage:(NSString *)subEvent
                                   json:(NSString *)jsonString
                             jsonObject:(id)object {
    dispatch_async(multicastQueue, ^{
        [self->multicastDelegate socketMananger_didReceiveMessage:subEvent json:jsonString jsonObject:object];
    });
}

- (void)notifyDelegatesDidReceiveEvent:(NSString *)event
                             subEvent:(NSString *)subEvent
                                 json:(NSString *)jsonString
                           jsonObject:(id)object {
    dispatch_async(multicastQueue, ^{
        [self->multicastDelegate socketMananger_didReceiveEvent:event subEvent:subEvent json:jsonString jsonObject:object];
    });
}

#pragma mark - Message Emit

- (BOOL)emitMessage:(id)content {
    BOOL success = [self p_emitEvent:@"message" content:content timeout:0 callback:nil];
    return success;
}

- (BOOL)emitMessage:(id)content timeout:(double)timeout callback:(void (^)(NSArray *ackArray))callback {
    BOOL success = [self p_emitEvent:@"message" content:content timeout:timeout callback:callback];
    return success;
}

- (BOOL)emitEvent:(NSString *)event content:(id)content {
    BOOL success = [self p_emitEvent:event content:content timeout:0 callback:nil];
    return success;
}

- (BOOL)emitEvent:(NSString *)event content:(id)content timeout:(double)timeout callback:(void (^)(NSArray *ackArray))callback {
    BOOL success = [self p_emitEvent:event content:content timeout:timeout callback:callback];
    return success;
}

- (BOOL)emitPermissionMessageWithUserId:(NSString *)userId type:(PLVSocketPermissionType)type status:(BOOL)status {
    return [self emitPermissionMessageWithUserId:userId type:type status:status timeout:0 callback:nil];
}

- (BOOL)emitPermissionMessageWithUserId:(NSString *)userId type:(PLVSocketPermissionType)type status:(BOOL)status timeout:(double)timeout callback:(void (^ _Nullable)(NSArray * _Nonnull))callback {
    NSString *sessionId = self.socketConfiguration.sessionId;
    NSString *roomId = self.roomId;
    NSString *typeString = [self permissionTypeStirngWithType:type];
    BOOL paramInvalid = (![PLVFdUtil checkStringUseable:userId] ||
                         ![PLVFdUtil checkStringUseable:roomId] ||
                         ![PLVFdUtil checkStringUseable:typeString]);
    
    /// 互动学堂场景下 需要 sessionId 参数
    if (self.vclassDomain) {
        if (paramInvalid || ![PLVFdUtil checkStringUseable:sessionId]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s emitPermissionMessage failed with 【param illegal】(userId:%@, sessionId:%@, roomId:%@, type:%@)", __FUNCTION__, userId, sessionId, roomId, typeString);
            return NO;
        }
    } else {
        if (paramInvalid) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s emitPermissionMessage failed with 【param illegal】(userId:%@, roomId:%@, type:%@)", __FUNCTION__, userId, roomId, typeString);
            return NO;
        }
    }
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    plv_dict_set(params, @"EVENT", @"TEACHER_SET_PERMISSION");
    plv_dict_set(params, @"roomId", roomId);
    plv_dict_set(params, @"sessionId", sessionId);
    plv_dict_set(params, @"userId", userId);
    plv_dict_set(params, @"status", status?@"1":@"0");
    plv_dict_set(params, @"emitMode", @"0");
    plv_dict_set(params, @"type", typeString);
    if(type == PLVSocketPermissionTypeRaiseHand){
        plv_dict_set(params, @"raiseHandTime", @"10000"); // 举手毫秒时间，< 120000，待此时间过后系统自动发送取消举手事件
    }
    
    NSString *sign = [self permissionSignWithParams:params];
    plv_dict_set(params, @"sign", sign);
    
    BOOL success = [self p_emitEvent:@"message" content:params timeout:timeout callback:callback];
    return success;
}

- (BOOL)emitPermissionMessageForCloseAllLinkMicWithTimeout:(double)timeout callback:(void (^ _Nullable)(NSArray * _Nonnull))callback {
    NSString *sessionId = self.socketConfiguration.sessionId;
    NSString *roomId = self.roomId;
    
    /// 互动学堂场景下才需要用到sessionId参数
    BOOL check = self.vclassDomain ? ([PLVFdUtil checkStringUseable:roomId] && [PLVFdUtil checkStringUseable:sessionId]) : [PLVFdUtil checkStringUseable:roomId];
    if (!check) {
        if (self.vclassDomain) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s closeAllLinkMic emitPermissionMessage failed with 【param illegal】(sessionId:%@, roomId:%@)", __FUNCTION__, sessionId, roomId);
        } else {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"%s closeAllLinkMic emitPermissionMessage failed with 【param illegal】(roomId:%@)", __FUNCTION__, roomId);
        }
        return NO;
    }
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    plv_dict_set(params, @"EVENT", @"TEACHER_SET_PERMISSION");
    plv_dict_set(params, @"roomId", roomId);
    plv_dict_set(params, @"sessionId", sessionId);
    plv_dict_set(params, @"userId", @"all");
    plv_dict_set(params, @"toAll", @(YES));
    plv_dict_set(params, @"status", @"0");
    plv_dict_set(params, @"emitMode", @"0");
    plv_dict_set(params, @"type", @"voice");
    
    NSString *sign = [self permissionSignWithParams:params];
    plv_dict_set(params, @"sign", sign);
    
    BOOL success = [self p_emitEvent:@"message" content:params timeout:timeout callback:callback];
    return success;
}

/// 发送消息私有方法
- (BOOL)p_emitEvent:(NSString *)event
            content:(id)content
            timeout:(double)timeout
           callback:(void (^ _Nullable )(NSArray *ackArray))callback {
    if (self.status != PLVSocketConnectStatusConnected) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"send emit event failed with 【socket not connected】(socket state:%lu)", self.status);
        return NO;
    }
    
    BOOL success = [self.socket emitEvent:event content:content timeout:timeout callback:callback];
    if (!success) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeSocket, @"send emit event failed with (event:%@, content:%@)", event, content);
        return NO;
    } else {
        return YES;
    }
}

#pragma mark TEACHER_SET_PERMISSION事件 参数处理

/// TEACHER_SET_PERMISSION sign参数
/// @param params 参数
- (NSString *)permissionSignWithParams:(NSDictionary *)params {
    NSString *sign = nil;
    NSArray *keys = [params.allKeys sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) { // NSLiteralSearch ：区分大小写
        return [obj1 compare:obj2 options:NSLiteralSearch];
    }];
    NSMutableString *paramStr = [NSMutableString string];
    for (NSString *key in keys) {
        [paramStr appendFormat:@"%@%@", key, params[key]];
    }
    
    NSString *plain = [NSString stringWithFormat:@"polyvChatSign%@polyvChatSign",paramStr];
    sign = [[PLVDataUtil md5HexDigest:plain] uppercaseString];
    
    return sign;
}

/// PLVSocketPermissionType 转字符串
/// @param type 类型
- (NSString *)permissionTypeStirngWithType:(PLVSocketPermissionType)type {
    NSString *typeString = nil;
    switch (type) {
        case PLVSocketPermissionTypeSpeaker:
            typeString = @"speaker";
            break;
        case PLVSocketPermissionTypePaint:
            typeString = @"paint";
            break;
        case PLVSocketPermissionTypeCup:
            typeString = @"cup";
            break;
        case PLVSocketPermissionTypeRaiseHand:
            typeString = @"raiseHand";
            break;
        case PLVSocketPermissionTypeCamera:
            typeString = @"video";
            break;
        case PLVSocketPermissionTypeMicrophone:
            typeString = @"audio";
            break;
        case PLVSocketPermissionTypeScreenShare:
            typeString = @"screenShare";
            break;
        default:
            break;
    }
    return typeString;
}

@end
