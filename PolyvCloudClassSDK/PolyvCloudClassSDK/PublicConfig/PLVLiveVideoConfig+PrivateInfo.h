//
//  PLVLiveVideoConfig+PrivateInfo.h
//  PLVLiveScenesSDK
//
//  Created by 黄佳玮 on 2022/4/14.
//  Copyright © 2022 PLV. All rights reserved.
//

#import <PLVLiveScenesSDK/PLVLiveScenesSDK.h>

/// 仅面向内部的属性和方法
@interface PLVLiveVideoConfig ()

/// 推流参数  (简化命名作保护，原名clientParams，不允许公开，请勿截图暴露)
@property (nonatomic, copy) NSDictionary *cp;
/// 推流模板开关
@property (nonatomic, assign) BOOL cpstEnabled;
/// 推流模板
@property (nonatomic, copy) NSDictionary *pstJson;
/// sdk接口安全设置，YES为开启
@property (nonatomic, assign) BOOL sdkSafetyEnabled;
/// 接口签名是否启用 sha256 加密，响应 sdkSafetyEnabled
@property (nonatomic, assign) BOOL realEnableSha256;
/// 接口是否启用防重放，响应 sdkSafetyEnabled
@property (nonatomic, assign) BOOL realEnableSignatureNonce;
/// 接口是否启用响应加密，响应 sdkSafetyEnabled
@property (nonatomic, assign) BOOL realEnableResponseEncrypt;
/// 接口是否启用请求加密，响应 sdkSafetyEnabled
@property (nonatomic, assign) BOOL realEnableRequestEncrypt;
/// 接口是否启用安全接口，响应 sdkSafetyEnabled
@property (nonatomic, assign) BOOL realEnableSecureApi;
/// 接口是否启用资源鉴权，响应 sdkSafetyEnabled
@property (nonatomic, assign) BOOL realEnableResourceAuth;
/// 接口加密类型，响应 sdkSafetyEnabled
@property (nonatomic, assign, readonly) PLVEncryptType realEncryptType;
/// 直播 观众连麦时设置第一画面时推流视频清晰度 N、360p、720p、1080p
@property (nonatomic, copy) NSString *appPushStreamParams;
/// 美颜类型
@property (nonatomic, copy) NSString *beautyType;


@end

