//
//  PLVLiveVideoConfig.m
//  PLVLiveScenesSDK
//
//  Created by ftao on 24/10/2017.
//  Copyright © 2017 PLV. All rights reserved.
//

#import "PLVLiveVideoConfig.h"
#import <UIKit/UIDevice.h>
#import "PLVLiveAPIUtils.h"
#import "PLVLiveVideoAPI.h"
#import "PLVLiveHttpDnsManager.h"
#import <PLVFoundationSDK/PLVFUserAgentBuilder.h>
#import <PLVFoundationSDK/PLVFdUtil.h>
#import <PLVFoundationSDK/PLVKeyUtil.h>
#import "PLVLiveVideoConfig+PrivateInfo.h"
#import "PLVLoganManager+Private.h"

/*
 history:
 0.6.0-20181228、0.6.1-20190124、0.6.4-20190320
 0.7.0-20190429
 0.8.0-20190621
 0.9.0-20190905-beta2、0.9.0-20191009
 0.10.0-20191119
 0.10.1-20191211
 0.11.0-20191227
 0.11.1-20200109
 0.11.2-20200117
 0.12.0-20200313
 0.12.1-20200319
 0.13.0-20200506
 0.13.1-20200515
 0.14.0-20200624
 0.14.1-20200808
 0.15.0-20200818
 0.15.1-20200824
 1.0.0-20201029
 1.0.1-20201207
 1.1.0-innerTest
 1.1.0-20210107
 1.2.0-20210201
 1.2.1-20210305
 1.2.2-20210319
 1.2.3-20210402
 1.2.4-20210421
 1.2.5-20210430
 1.3.0-20210422
 1.4.1-20210602
 1.5.0-20210623
 1.5.1-20210720
 1.5.2-20210810
 1.6.0-20210914
 1.6.2-20211015
 1.7.0-20211028
 1.7.2-20211115
 1.7.3-20211207
 1.8.0-20211220
 1.8.1-20220107
 1.8.2-20220228
 1.8.3-20220318
 1.9.1-20220513
 1.9.1.1-20220520
 1.9.2-abn-20220610
 1.9.3-20220620
 1.9.4-20220713
 1.9.5-20220730
 1.10.0-20220830
 1.10.1-20220909
 1.10.2-20221010
 1.10.3-20221026
 1.10.4-20221129
 1.10.5-20230111
 1.10.6-20230210
 1.10.7-20230308
 1.10.8-20230406
 1.11.2-20230424
 1.11.3-20230519
 1.12.2-20230621
 1.13.0-20230728
 1.13.2-abn-20230802
 1.14.0-20230831
 1.15.0-20231030
 1.15.1-20231102
 1.16.0-20231208
 1.16.1-20231219
 1.16.2-20240105
 1.16.3-20240131
 1.16.4-********
 1.17.0-********
 1.17.1-********
 1.18.0-********
 1.18.1-********
 1.19.0-********
 1.19.1-********
 1.20.0-********
 1.21.0-********
 1.22.0-********
 1.22.1-********
 1.23.0-********
 1.24.0-********
 1.25.0-********
 1.25.1-********
 */
// PLAYER 版本，组成：<主版本号>.<次版本号>.<子版本号>-<版本日期>
#define PCCSDK_VERSION @"1.26.0-********"
#define PCCSDK_PLAYER_NAME @"polyv-iOS-live-sdk"

@interface PLVLiveVideoConfig ()

@property (nonatomic, assign) BOOL configAccount;

@property (nonatomic, strong) NSString *chatApiDomain;

@end

@implementation PLVLiveVideoConfig

@synthesize playerName, playerVersion, userAgent, userId, appId, appSecret, playerId, logLevel;

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static PLVLiveVideoConfig *liveConfig = nil;
    dispatch_once(&onceToken, ^{
        liveConfig = [[self alloc] init];
    });
    return liveConfig;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.playerName = @"polyv-iOS-live-sdk";
        self.playerVersion = PCCSDK_VERSION;
        self.enableHttpDNS = YES;
        self.enableIPV6 = NO;
        [PLVKeyUtil setUp];
        [self createUserAgent];
        
        [[PLVFUserAgentBuilder sharedBuilder]
            configWithProductType:PLVFProductTypeLiveScene sdkVersion:PCCSDK_VERSION];
    
        self.logLevel = PLVLogLevelNoLog;
        
    }
    return self;
}

- (BOOL)configWithUserId:(NSString *)userId appId:(NSString *)appId appSecret:(NSString *)appSecret {
    if (![PLVFdUtil checkStringUseable:userId] ||
        ![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        self.configAccount = NO;
        return NO;
    }
    self.userId = userId;
    self.appId = appId;
    self.appSecret = appSecret;
    self.configAccount = YES;
    PLV_KEY_INFO(@"userIds", userId);
    [[PLVLoganManager sharedManager] checkAndReportIfNeed];
    return YES;
}

+ (void)setPrivateDomainWithData:(NSDictionary *)data {
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    liveConfig.chatApiDomain = nil;
    if (data != nil && [data isKindOfClass:[NSDictionary class]] && data.count > 0) {
        NSString *chatDomain = data[@"chatDomain"];
        Class PLVSocketManagerClass = NSClassFromString(@"PLVSocketManager");
        if (PLVSocketManagerClass && [PLVSocketManagerClass respondsToSelector:@selector(sharedManager)]) {
            [[PLVSocketManagerClass performSelector:@selector(sharedManager)] setValue:chatDomain forKey:@"chatDomain"];
        }
        
        NSString *chatApiDomain = data[@"chatApiDomain"];
        if ([chatApiDomain isKindOfClass:NSString.class] && ![PLVApichatDomain containsString:chatApiDomain]) {
            liveConfig.chatApiDomain = chatApiDomain;
            [PLVLiveVideoAPI setPrivateApichatDomainName:chatApiDomain];
        }
    }
    
    if (!liveConfig.chatApiDomain) {
        [PLVLiveVideoAPI setPrivateApichatDomainName:@""];
    }
}

/// 是否启用 HttpDNS
- (void)setEnableHttpDNS:(BOOL)enableHttpDNS{
    _enableHttpDNS = enableHttpDNS;
    if (_enableHttpDNS) {
        [[PLVLiveHttpDnsManager sharedManager] setupHttpDNSWithSecretKey:nil];
    }
}

- (void)setLocalLogDisable:(BOOL)localLogDisable {
    _localLogDisable = localLogDisable;
    [PLVLoganManager sharedManager].logDisable = localLogDisable;
}

- (void)setLocalLogUploadDisable:(BOOL)localLogUploadDisable {
    _localLogUploadDisable = localLogUploadDisable;
    [PLVLoganManager sharedManager].logUploadDisable = localLogUploadDisable;
}

#pragma mark - <PLVConfig>

+ (void)setPlayerVersion:(NSString *)playerVersion {
    [PLVLiveVideoConfig sharedInstance].playerVersion = playerVersion;
}

+ (void)setLogLevel:(PLVLogLevel)logLevel {
    [PLVLiveVideoConfig sharedInstance].logLevel = logLevel;
}

+ (NSString *)playerVersion {
    return [PLVLiveVideoConfig sharedInstance].playerVersion;
}

+ (NSString *)playerName {
    return [PLVLiveVideoConfig sharedInstance].playerName;
}

+ (NSString *)userAgent {
    return [PLVLiveVideoConfig sharedInstance].userAgent;
}

+ (PLVLogLevel)logLevel {
    return [PLVLiveVideoConfig sharedInstance].logLevel;
}


+ (NSString *)createPlayerId {
    long long ts =(long long)([[NSDate date] timeIntervalSince1970] * 1000.0);
    long r = arc4random_uniform(1000000) + 1000000;
    NSString *playerId = [NSString stringWithFormat:@"%lldX%ld", ts, r];
    PLVLiveVideoConfig.sharedInstance.playerId = playerId;
    /// 当播放器开始播放时，userAgent中的playerId需要动态变化
    [[PLVLiveVideoConfig sharedInstance] createUserAgent];
    return playerId;
}

- (BOOL)realEnableSha256 {
    return [PLVLiveVideoConfig sharedInstance].sdkSafetyEnabled && [PLVLiveVideoConfig sharedInstance].enableSha256;
}

- (BOOL)realEnableSignatureNonce {
    return [PLVLiveVideoConfig sharedInstance].sdkSafetyEnabled && [PLVLiveVideoConfig sharedInstance].enableSignatureNonce;
}

- (BOOL)realEnableResponseEncrypt {
    return [PLVLiveVideoConfig sharedInstance].sdkSafetyEnabled && [PLVLiveVideoConfig sharedInstance].enableResponseEncrypt;
}

- (BOOL)realEnableRequestEncrypt {
    return [PLVLiveVideoConfig sharedInstance].sdkSafetyEnabled && [PLVLiveVideoConfig sharedInstance].enableRequestEncrypt;
}

- (BOOL)realEnableSecureApi {
    return [PLVLiveVideoConfig sharedInstance].sdkSafetyEnabled && [PLVLiveVideoConfig sharedInstance].enableSecureApi;
}

- (BOOL)realEnableResourceAuth {
    return [PLVLiveVideoConfig sharedInstance].sdkSafetyEnabled && [PLVLiveVideoConfig sharedInstance].enableResourceAuth;
}

- (PLVEncryptType)realEncryptType {
    if ([PLVLiveVideoConfig sharedInstance].sdkSafetyEnabled) {
        return [PLVFSignConfig sharedInstance].encryptType;
    }
    return PLVEncryptType_None;
}

#pragma mark - Private

- (void)createUserAgent {
    NSString *prefix = [NSString stringWithFormat:@"%@-%@/%@", self.playerName, self.playerVersion, self.playerId];
    UIDevice *device = [UIDevice currentDevice];
    self.userAgent = [NSString stringWithFormat:@"%@ %@(%@)", prefix, device.model, [PLVFUserAgentBuilder getDeviceAllInfo]];
    [PLVFUserAgentBuilder sharedBuilder].userAgent = self.userAgent;
}

#pragma mark - Public

#pragma mark Getter

- (NSDictionary *)clientParams {
    return _cp;
}

- (BOOL)clientPushStreamTemplateEnabled {
    return _cpstEnabled && [PLVFdUtil checkDictionaryUseable:_pstJson];
}

- (PLVClientPushStreamTemplateAudioParams *)audioParams {
    NSDictionary *audioParamsDict = PLV_SafeDictionaryForDictKey(_pstJson, @"AudioParams");
    return [[PLVClientPushStreamTemplateAudioParams alloc]initWithDictionary:audioParamsDict];
}

- (NSArray<PLVClientPushStreamTemplateVideoParams *> *)videoParams {
    NSArray *videoParamsArray = PLV_SafeArraryForDictKey(_pstJson, @"VideoParams");
    NSMutableArray *videoParamsMuArray = [NSMutableArray array];
    if ([PLVFdUtil checkArrayUseable:videoParamsArray]){
        for (int i = 0; i < videoParamsArray.count; i++) {
            NSDictionary *dict = videoParamsArray[i];
            PLVClientPushStreamTemplateVideoParams *videoParamsDict = [[PLVClientPushStreamTemplateVideoParams alloc]initWithDictionary:dict];
            [videoParamsMuArray addObject:videoParamsDict];
        }
    }
    return videoParamsMuArray;
}

- (NSString *)teacherDefaultQualityLevel {
    return PLV_SafeStringForDictKey(_pstJson, @"teacherDefaultQualityLevel");
}

- (NSString *)guestDefaultQualityLevel {
    return PLV_SafeStringForDictKey(_pstJson, @"guestDefaultQualityLevel");
}

- (NSString *)audienceFirstScreenQualityLevel {
    return _appPushStreamParams;
}

@end

@interface  PLVClientPushStreamTemplateAudioParams ()
/// 音频采样率
@property (nonatomic, assign) NSInteger audioSamplerate;
/// 音频声道
@property (nonatomic, assign) NSInteger audioChannels;
/// 音频码率
@property (nonatomic, assign) NSInteger audioBitrate;

@end

@implementation PLVClientPushStreamTemplateAudioParams

- (instancetype)initWithDictionary:(NSDictionary *)dictionary {
    self = [super init];
    if (self) {
        if ([PLVFdUtil checkDictionaryUseable:dictionary]) {
            self.audioSamplerate = PLV_SafeIntegerForDictKey(dictionary, @"AudioSamplerate");
            self.audioChannels = PLV_SafeIntegerForDictKey(dictionary, @"AudioChannels");
            self.audioBitrate = PLV_SafeIntegerForDictKey(dictionary, @"AudioBitrate");
        }
    }
    return self;
}

@end

@interface  PLVClientPushStreamTemplateVideoParams ()

/// 推流质量等级，唯一标识
@property (nonatomic, copy) NSString * qualityLevel;
/// 推流质量名称
@property (nonatomic, copy) NSString * qualityName;
/// 推流质量名称（英文名字段）
@property (nonatomic, copy) NSString * qualityEnName;
/// 视频分辨率
@property (nonatomic, assign) CGSize videoResolution;
/// 视频码率
@property (nonatomic, assign) NSInteger videoBitrate;
/// 视频采集帧率
@property (nonatomic, assign) NSInteger videoFrameRate;
/// 视频关键帧帧率
@property (nonatomic, assign) NSInteger videoGop;
/// 屏幕共享分辨率
@property (nonatomic, assign) CGSize screenResolution;
/// 屏幕共享码率
@property (nonatomic, assign) NSInteger screenBitrate;
/// 屏幕共享采集帧率
@property (nonatomic, assign) NSInteger screenFrameRate;
/// 屏幕共享关键帧帧率
@property (nonatomic, assign) NSInteger screenGop;

@end

@implementation PLVClientPushStreamTemplateVideoParams

- (instancetype)initWithDictionary:(NSDictionary *)dictionary {
    self = [super init];
    if (self) {
        if ([PLVFdUtil checkDictionaryUseable:dictionary]) {
            self.qualityLevel = PLV_SafeStringForDictKey(dictionary, @"QualityLevel");
            self.qualityName = PLV_SafeStringForDictKey(dictionary, @"QualityName");
            self.qualityEnName = PLV_SafeStringForDictKey(dictionary, @"QualityEnName");
            self.videoBitrate = PLV_SafeIntegerForDictKey(dictionary, @"VideoBitrate");
            self.videoFrameRate = PLV_SafeIntegerForDictKey(dictionary, @"VideoFps");
            self.videoGop = PLV_SafeIntegerForDictKey(dictionary, @"VideoGop");
            NSInteger videoWidth = PLV_SafeIntegerForDictKey(dictionary,@"VideoWidth");
            NSInteger videoHeight = PLV_SafeIntegerForDictKey(dictionary,@"VideoHeight");
            self.videoResolution = CGSizeMake(videoWidth, videoHeight);
            self.screenBitrate = PLV_SafeIntegerForDictKey(dictionary, @"ScreenBitrate");
            self.screenFrameRate = PLV_SafeIntegerForDictKey(dictionary, @"ScreenFps");
            self.screenGop = PLV_SafeIntegerForDictKey(dictionary, @"ScreenGop");
            NSInteger screenWidth = PLV_SafeIntegerForDictKey(dictionary,@"ScreenWidth");
            NSInteger screenHeight = PLV_SafeIntegerForDictKey(dictionary,@"ScreenHeight");
            self.screenResolution = CGSizeMake(screenWidth, screenHeight);
        }
    }
    return self;
}

- (BOOL)isSupportVideoParams {
    // SDK 暂不支持此参数配置
    if (self.videoResolution.height == 480 && self.videoResolution.width >= 840) {
        return NO;
    }
    
    return YES;
}

@end
