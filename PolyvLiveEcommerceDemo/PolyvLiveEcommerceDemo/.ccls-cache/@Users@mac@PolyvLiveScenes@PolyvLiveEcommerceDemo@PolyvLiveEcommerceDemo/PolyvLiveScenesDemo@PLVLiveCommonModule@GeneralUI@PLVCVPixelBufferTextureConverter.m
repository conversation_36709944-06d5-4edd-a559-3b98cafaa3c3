//
//  PLVCVPixelBufferTextureConverter.m
//  PolyvLiveScenesDemo
//
//  Created by Assistant on 2025/1/11.
//  Copyright © 2025 PLV. All rights reserved.
//

#import "PLVCVPixelBufferTextureConverter.h"

@interface PLVCVPixelBufferTextureConverter()

@property (nonatomic, strong) EAGLContext *glContext;
@property (nonatomic, assign) CVOpenGLESTextureCacheRef textureCache;
@property (nonatomic, assign) GLuint frameBuffer;
@property (nonatomic, assign) GLuint renderBuffer;

@end

@implementation PLVCVPixelBufferTextureConverter

#pragma mark - Singleton

+ (instancetype)sharedInstance {
    static PLVCVPixelBufferTextureConverter *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[PLVCVPixelBufferTextureConverter alloc] init];
    });
    return instance;
}

#pragma mark - Lifecycle

- (instancetype)init {
    self = [super init];
    if (self) {
        _textureCache = NULL;
        _frameBuffer = 0;
        _renderBuffer = 0;
    }
    return self;
}

- (void)dealloc {
    [self cleanup];
}

#pragma mark - Setup and Cleanup

- (BOOL)setupWithContext:(EAGLContext *)context {
    self.glContext = context;
    
    if (!context) {
        NSLog(@"PLVCVPixelBufferTextureConverter: Invalid OpenGL ES context");
        return NO;
    }
    
    [EAGLContext setCurrentContext:context];
    
    // 创建纹理缓存
    CVReturn result = CVOpenGLESTextureCacheCreate(kCFAllocatorDefault,
                                                   NULL,
                                                   context,
                                                   NULL,
                                                   &_textureCache);
    
    if (result != kCVReturnSuccess) {
        NSLog(@"PLVCVPixelBufferTextureConverter: Failed to create texture cache: %d", result);
        return NO;
    }
    
    return YES;
}

- (void)cleanup {
    if (_textureCache) {
        CVOpenGLESTextureCacheFlush(_textureCache, 0);
        CFRelease(_textureCache);
        _textureCache = NULL;
    }
    
    if (_frameBuffer) {
        glDeleteFramebuffers(1, &_frameBuffer);
        _frameBuffer = 0;
    }
    
    if (_renderBuffer) {
        glDeleteRenderbuffers(1, &_renderBuffer);
        _renderBuffer = 0;
    }
}

#pragma mark - CVPixelBuffer to OpenGL Texture

- (BOOL)convertPixelBuffer:(CVPixelBufferRef)pixelBuffer toTexture:(GLuint *)textureOut {
    if (!pixelBuffer || !textureOut || !_textureCache) {
        return NO;
    }
    
    CVOpenGLESTextureRef texture = NULL;
    BOOL success = [self convertPixelBufferToTextureRef:pixelBuffer textureRef:&texture];
    
    if (success && texture) {
        *textureOut = CVOpenGLESTextureGetName(texture);
        CFRelease(texture);
        return YES;
    }
    
    return NO;
}

- (BOOL)convertPixelBufferToTextureRef:(CVPixelBufferRef)pixelBuffer 
                            textureRef:(CVOpenGLESTextureRef *)textureRef {
    if (!pixelBuffer || !textureRef || !_textureCache) {
        return NO;
    }
    
    size_t width = CVPixelBufferGetWidth(pixelBuffer);
    size_t height = CVPixelBufferGetHeight(pixelBuffer);
    OSType pixelFormat = CVPixelBufferGetPixelFormatType(pixelBuffer);
    
    GLenum format;
    GLenum type;
    
    // 根据像素格式确定 OpenGL 格式
    switch (pixelFormat) {
        case kCVPixelFormatType_32BGRA:
            format = GL_BGRA;
            type = GL_UNSIGNED_BYTE;
            break;
        case kCVPixelFormatType_32RGBA:
            format = GL_RGBA;
            type = GL_UNSIGNED_BYTE;
            break;
        case kCVPixelFormatType_24RGB:
            format = GL_RGB;
            type = GL_UNSIGNED_BYTE;
            break;
        case kCVPixelFormatType_420YpCbCr8BiPlanarFullRange:
        case kCVPixelFormatType_420YpCbCr8BiPlanarVideoRange:
            // 对于 YUV 格式，这里只处理 Y 平面
            format = GL_LUMINANCE;
            type = GL_UNSIGNED_BYTE;
            break;
        default:
            NSLog(@"PLVCVPixelBufferTextureConverter: Unsupported pixel format: %u", (unsigned int)pixelFormat);
            return NO;
    }
    
    CVReturn result = CVOpenGLESTextureCacheCreateTextureFromImage(
        kCFAllocatorDefault,
        _textureCache,
        pixelBuffer,
        NULL,
        GL_TEXTURE_2D,
        format,
        (GLsizei)width,
        (GLsizei)height,
        format,
        type,
        0,
        textureRef
    );
    
    if (result != kCVReturnSuccess) {
        NSLog(@"PLVCVPixelBufferTextureConverter: Failed to create texture from pixel buffer: %d", result);
        return NO;
    }
    
    // 设置纹理参数
    GLuint textureName = CVOpenGLESTextureGetName(*textureRef);
    glBindTexture(GL_TEXTURE_2D, textureName);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    
    return YES;
}

#pragma mark - OpenGL Texture to CVPixelBuffer

- (BOOL)convertTexture:(GLuint)textureID 
                 width:(int)width 
                height:(int)height 
        toPixelBuffer:(CVPixelBufferRef *)pixelBuffer {
    if (!textureID || width <= 0 || height <= 0 || !pixelBuffer) {
        return NO;
    }
    
    // 创建帧缓冲
    GLuint framebuffer;
    glGenFramebuffers(1, &framebuffer);
    glBindFramebuffer(GL_FRAMEBUFFER, framebuffer);
    
    // 附加纹理到帧缓冲
    glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, textureID, 0);
    
    GLenum status = glCheckFramebufferStatus(GL_FRAMEBUFFER);
    if (status != GL_FRAMEBUFFER_COMPLETE) {
        NSLog(@"PLVCVPixelBufferTextureConverter: Framebuffer not complete: %x", status);
        glDeleteFramebuffers(1, &framebuffer);
        return NO;
    }
    
    BOOL result = [self readFramebuffer:framebuffer width:width height:height toPixelBuffer:pixelBuffer];
    
    glDeleteFramebuffers(1, &framebuffer);
    return result;
}

- (BOOL)readFramebuffer:(GLuint)framebuffer 
                  width:(int)width 
                 height:(int)height 
         toPixelBuffer:(CVPixelBufferRef *)pixelBuffer {
    if (!framebuffer || width <= 0 || height <= 0 || !pixelBuffer) {
        return NO;
    }
    
    // 创建 CVPixelBuffer
    CVPixelBufferRef buffer = [self createPixelBufferWithWidth:width 
                                                        height:height 
                                                   pixelFormat:kCVPixelFormatType_32BGRA];
    if (!buffer) {
        return NO;
    }
    
    // 绑定帧缓冲
    glBindFramebuffer(GL_FRAMEBUFFER, framebuffer);
    
    // 锁定 CVPixelBuffer
    CVReturn lockResult = CVPixelBufferLockBaseAddress(buffer, 0);
    if (lockResult != kCVReturnSuccess) {
        NSLog(@"PLVCVPixelBufferTextureConverter: Failed to lock pixel buffer: %d", lockResult);
        CVPixelBufferRelease(buffer);
        return NO;
    }
    
    // 读取像素数据
    void *baseAddress = CVPixelBufferGetBaseAddress(buffer);
    size_t bytesPerRow = CVPixelBufferGetBytesPerRow(buffer);
    
    // 分配临时缓冲区用于翻转图像
    size_t dataSize = bytesPerRow * height;
    uint8_t *tempBuffer = malloc(dataSize);
    
    glReadPixels(0, 0, width, height, GL_BGRA, GL_UNSIGNED_BYTE, tempBuffer);
    
    // 翻转图像（OpenGL 的原点在左下角，Core Video 的原点在左上角）
    uint8_t *destPtr = (uint8_t *)baseAddress;
    for (int y = 0; y < height; y++) {
        uint8_t *srcRow = tempBuffer + (height - 1 - y) * bytesPerRow;
        uint8_t *destRow = destPtr + y * bytesPerRow;
        memcpy(destRow, srcRow, bytesPerRow);
    }
    
    free(tempBuffer);
    
    // 解锁 CVPixelBuffer
    CVPixelBufferUnlockBaseAddress(buffer, 0);
    
    *pixelBuffer = buffer;
    return YES;
}

#pragma mark - Utility Methods

- (CVPixelBufferRef)createPixelBufferWithWidth:(int)width 
                                        height:(int)height 
                                   pixelFormat:(OSType)pixelFormat {
    if (width <= 0 || height <= 0) {
        return NULL;
    }
    
    NSDictionary *options = @{
        (id)kCVPixelBufferCGImageCompatibilityKey: @(YES),
        (id)kCVPixelBufferCGBitmapContextCompatibilityKey: @(YES),
        (id)kCVPixelBufferIOSurfacePropertiesKey: @{}
    };
    
    CVPixelBufferRef pixelBuffer = NULL;
    CVReturn result = CVPixelBufferCreate(kCFAllocatorDefault,
                                        width,
                                        height,
                                        pixelFormat,
                                        (__bridge CFDictionaryRef)options,
                                        &pixelBuffer);
    
    if (result != kCVReturnSuccess) {
        NSLog(@"PLVCVPixelBufferTextureConverter: Failed to create pixel buffer: %d", result);
        return NULL;
    }
    
    return pixelBuffer;
}

- (GLuint)createTextureWithWidth:(int)width height:(int)height {
    if (width <= 0 || height <= 0) {
        return 0;
    }
    
    GLuint texture;
    glGenTextures(1, &texture);
    glBindTexture(GL_TEXTURE_2D, texture);
    
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, width, height, 0, GL_RGBA, GL_UNSIGNED_BYTE, NULL);
    
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    
    return texture;
}

- (GLenum)getGLFormatFromPixelBuffer:(CVPixelBufferRef)pixelBuffer {
    if (!pixelBuffer) {
        return GL_RGBA;
    }
    
    OSType pixelFormat = CVPixelBufferGetPixelFormatType(pixelBuffer);
    
    switch (pixelFormat) {
        case kCVPixelFormatType_32BGRA:
            return GL_BGRA;
        case kCVPixelFormatType_32RGBA:
            return GL_RGBA;
        case kCVPixelFormatType_24RGB:
            return GL_RGB;
        case kCVPixelFormatType_420YpCbCr8BiPlanarFullRange:
        case kCVPixelFormatType_420YpCbCr8BiPlanarVideoRange:
            return GL_LUMINANCE;
        default:
            return GL_RGBA;
    }
}

@end 