//
//  PLVCVPixelBufferTextureConverter.h
//  PolyvLiveScenesDemo
//
//  Created by Assistant on 2025/1/11.
//  Copyright © 2025 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <OpenGLES/ES2/gl.h>
#import <OpenGLES/ES2/glext.h>
#import <CoreVideo/CoreVideo.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * CVPixelBuffer 和 OpenGL texture 相互转换工具类
 * 支持高效的零拷贝转换和内存管理
 */
@interface PLVCVPixelBufferTextureConverter : NSObject

/// 单例对象
+ (instancetype)sharedInstance;

/// 初始化 OpenGL ES 上下文和纹理缓存
- (BOOL)setupWithContext:(EAGLContext *)context;

/// 清理资源
- (void)cleanup;

#pragma mark - CVPixelBuffer to OpenGL Texture

/**
 * 将 CVPixelBuffer 转换为 OpenGL 纹理
 * @param pixelBuffer 输入的 CVPixelBuffer
 * @param textureOut 输出的纹理 ID
 * @return 是否转换成功
 */
- (BOOL)convertPixelBuffer:(CVPixelBufferRef)pixelBuffer 
                 toTexture:(GLuint *)textureOut;

/**
 * 将 CVPixelBuffer 转换为 OpenGL 纹理（零拷贝方式）
 * @param pixelBuffer 输入的 CVPixelBuffer
 * @param textureRef 输出的 CVOpenGLESTextureRef
 * @return 是否转换成功
 */
- (BOOL)convertPixelBufferToTextureRef:(CVPixelBufferRef)pixelBuffer 
                            textureRef:(CVOpenGLESTextureRef *)textureRef;

#pragma mark - OpenGL Texture to CVPixelBuffer

/**
 * 将 OpenGL 纹理转换为 CVPixelBuffer
 * @param textureID 输入的纹理 ID
 * @param width 纹理宽度
 * @param height 纹理高度
 * @param pixelBuffer 输出的 CVPixelBuffer
 * @return 是否转换成功
 */
- (BOOL)convertTexture:(GLuint)textureID 
                 width:(int)width 
                height:(int)height 
        toPixelBuffer:(CVPixelBufferRef *)pixelBuffer;

/**
 * 从帧缓冲读取像素数据到 CVPixelBuffer
 * @param framebuffer 帧缓冲 ID
 * @param width 宽度
 * @param height 高度
 * @param pixelBuffer 输出的 CVPixelBuffer
 * @return 是否转换成功
 */
- (BOOL)readFramebuffer:(GLuint)framebuffer 
                  width:(int)width 
                 height:(int)height 
         toPixelBuffer:(CVPixelBufferRef *)pixelBuffer;

#pragma mark - Utility Methods

/**
 * 创建指定格式的 CVPixelBuffer
 * @param width 宽度
 * @param height 高度
 * @param pixelFormat 像素格式
 * @return CVPixelBufferRef
 */
- (CVPixelBufferRef)createPixelBufferWithWidth:(int)width 
                                        height:(int)height 
                                   pixelFormat:(OSType)pixelFormat;

/**
 * 创建 OpenGL 纹理
 * @param width 纹理宽度
 * @param height 纹理高度
 * @return 纹理 ID
 */
- (GLuint)createTextureWithWidth:(int)width height:(int)height;

/**
 * 获取 CVPixelBuffer 的像素格式
 * @param pixelBuffer CVPixelBuffer
 * @return OpenGL 内部格式
 */
- (GLenum)getGLFormatFromPixelBuffer:(CVPixelBufferRef)pixelBuffer;

@end

NS_ASSUME_NONNULL_END 