//
//  PLVCVPixelBufferTextureConverterExample.m
//  PolyvLiveScenesDemo
//
//  Created by Assistant on 2025/1/11.
//  Copyright © 2025 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <OpenGLES/ES2/gl.h>
#import <OpenGLES/ES2/glext.h>
#import "PLVCVPixelBufferTextureConverter.h"

@interface PLVCVPixelBufferTextureConverterExample : NSObject

/// 示例：将 UIImage 转换为 CVPixelBuffer，再转换为 OpenGL 纹理
+ (void)exampleUIImageToTexture;

/// 示例：从纹理创建 CVPixelBuffer，再转换为 UIImage
+ (void)exampleTextureToUIImage;

/// 示例：视频处理流程
+ (void)exampleVideoProcessing;

/// 工具方法：从 UIImage 创建 CVPixelBuffer
+ (CVPixelBufferRef)createPixelBufferFromImage:(UIImage *)image;

/// 工具方法：从 CVPixelBuffer 创建 UIImage
+ (UIImage *)createImageFromPixelBuffer:(CVPixelBufferRef)pixelBuffer;

@end

@implementation PLVCVPixelBufferTextureConverterExample

+ (void)exampleUIImageToTexture {
    NSLog(@"=== 示例：UIImage -> CVPixelBuffer -> OpenGL Texture ===");
    
    // 1. 准备 OpenGL 上下文
    EAGLContext *context = [[EAGLContext alloc] initWithAPI:kEAGLRenderingAPIOpenGLES2];
    [EAGLContext setCurrentContext:context];
    
    // 2. 初始化转换器
    PLVCVPixelBufferTextureConverter *converter = [PLVCVPixelBufferTextureConverter sharedInstance];
    BOOL setupSuccess = [converter setupWithContext:context];
    
    if (!setupSuccess) {
        NSLog(@"❌ 转换器初始化失败");
        return;
    }
    
    // 3. 创建测试图像
    UIImage *testImage = [UIImage imageNamed:@"test_image"]; // 替换为实际图像
    if (!testImage) {
        // 创建一个简单的测试图像
        UIGraphicsBeginImageContextWithOptions(CGSizeMake(512, 512), NO, 1.0);
        CGContextRef context2 = UIGraphicsGetCurrentContext();
        CGContextSetRGBFillColor(context2, 1.0, 0.0, 0.0, 1.0); // 红色
        CGContextFillRect(context2, CGRectMake(0, 0, 512, 512));
        testImage = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
    }
    
    // 4. 将 UIImage 转换为 CVPixelBuffer
    CVPixelBufferRef pixelBuffer = [self createPixelBufferFromImage:testImage];
    if (!pixelBuffer) {
        NSLog(@"❌ 创建 CVPixelBuffer 失败");
        return;
    }
    
    NSLog(@"✅ 创建 CVPixelBuffer 成功: %dx%d", 
          (int)CVPixelBufferGetWidth(pixelBuffer),
          (int)CVPixelBufferGetHeight(pixelBuffer));
    
    // 5. 将 CVPixelBuffer 转换为 OpenGL 纹理
    CVOpenGLESTextureRef textureRef;
    BOOL convertSuccess = [converter convertPixelBufferToTextureRef:pixelBuffer textureRef:&textureRef];
    
    if (convertSuccess) {
        GLuint textureID = CVOpenGLESTextureGetName(textureRef);
        NSLog(@"✅ 转换为 OpenGL 纹理成功，纹理 ID: %u", textureID);
        
        // 6. 在这里可以使用纹理进行渲染
        glBindTexture(GL_TEXTURE_2D, textureID);
        // ... OpenGL 渲染代码 ...
        
        // 7. 清理资源
        CFRelease(textureRef);
    } else {
        NSLog(@"❌ 转换为 OpenGL 纹理失败");
    }
    
    CVPixelBufferRelease(pixelBuffer);
}

+ (void)exampleTextureToUIImage {
    NSLog(@"=== 示例：OpenGL Texture -> CVPixelBuffer -> UIImage ===");
    
    // 1. 准备 OpenGL 上下文
    EAGLContext *context = [[EAGLContext alloc] initWithAPI:kEAGLRenderingAPIOpenGLES2];
    [EAGLContext setCurrentContext:context];
    
    // 2. 初始化转换器
    PLVCVPixelBufferTextureConverter *converter = [PLVCVPixelBufferTextureConverter sharedInstance];
    [converter setupWithContext:context];
    
    // 3. 创建一个测试纹理
    int width = 512;
    int height = 512;
    GLuint textureID = [converter createTextureWithWidth:width height:height];
    
    if (textureID == 0) {
        NSLog(@"❌ 创建测试纹理失败");
        return;
    }
    
    // 4. 填充纹理数据（创建一个渐变图案）
    uint8_t *textureData = malloc(width * height * 4);
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int index = (y * width + x) * 4;
            textureData[index + 0] = (uint8_t)(255 * x / width);     // R
            textureData[index + 1] = (uint8_t)(255 * y / height);    // G
            textureData[index + 2] = 128;                            // B
            textureData[index + 3] = 255;                            // A
        }
    }
    
    glBindTexture(GL_TEXTURE_2D, textureID);
    glTexSubImage2D(GL_TEXTURE_2D, 0, 0, 0, width, height, GL_RGBA, GL_UNSIGNED_BYTE, textureData);
    free(textureData);
    
    NSLog(@"✅ 创建测试纹理成功，纹理 ID: %u", textureID);
    
    // 5. 将纹理转换为 CVPixelBuffer
    CVPixelBufferRef pixelBuffer;
    BOOL convertSuccess = [converter convertTexture:textureID 
                                               width:width 
                                              height:height 
                                      toPixelBuffer:&pixelBuffer];
    
    if (convertSuccess) {
        NSLog(@"✅ 转换为 CVPixelBuffer 成功");
        
        // 6. 将 CVPixelBuffer 转换为 UIImage
        UIImage *resultImage = [self createImageFromPixelBuffer:pixelBuffer];
        if (resultImage) {
            NSLog(@"✅ 转换为 UIImage 成功，图像尺寸: %.0fx%.0f", 
                  resultImage.size.width, resultImage.size.height);
            
            // 在这里可以保存图像或进行其他处理
            // UIImageWriteToSavedPhotosAlbum(resultImage, nil, nil, nil);
        } else {
            NSLog(@"❌ 转换为 UIImage 失败");
        }
        
        CVPixelBufferRelease(pixelBuffer);
    } else {
        NSLog(@"❌ 转换为 CVPixelBuffer 失败");
    }
    
    glDeleteTextures(1, &textureID);
}

+ (void)exampleVideoProcessing {
    NSLog(@"=== 示例：视频处理流程 ===");
    
    // 这是一个模拟的视频处理流程示例
    // 实际使用中，pixelBuffer 会来自 AVCaptureVideoDataOutput 或 AVPlayerItemVideoOutput
    
    // 1. 准备 OpenGL 上下文
    EAGLContext *context = [[EAGLContext alloc] initWithAPI:kEAGLRenderingAPIOpenGLES2];
    [EAGLContext setCurrentContext:context];
    
    // 2. 初始化转换器
    PLVCVPixelBufferTextureConverter *converter = [PLVCVPixelBufferTextureConverter sharedInstance];
    [converter setupWithContext:context];
    
    // 3. 模拟输入 CVPixelBuffer（实际中来自相机或视频）
    UIImage *inputImage = [UIImage imageNamed:@"video_frame"]; // 替换为实际图像
    if (!inputImage) {
        // 创建测试图像
        UIGraphicsBeginImageContextWithOptions(CGSizeMake(1920, 1080), NO, 1.0);
        CGContextRef ctx = UIGraphicsGetCurrentContext();
        CGContextSetRGBFillColor(ctx, 0.0, 1.0, 0.0, 1.0); // 绿色背景
        CGContextFillRect(ctx, CGRectMake(0, 0, 1920, 1080));
        
        // 添加一些图案
        CGContextSetRGBFillColor(ctx, 1.0, 1.0, 0.0, 1.0); // 黄色
        CGContextFillEllipseInRect(ctx, CGRectMake(860, 490, 200, 100));
        
        inputImage = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
    }
    
    CVPixelBufferRef inputPixelBuffer = [self createPixelBufferFromImage:inputImage];
    if (!inputPixelBuffer) {
        NSLog(@"❌ 创建输入 CVPixelBuffer 失败");
        return;
    }
    
    NSLog(@"✅ 输入视频帧: %dx%d", 
          (int)CVPixelBufferGetWidth(inputPixelBuffer),
          (int)CVPixelBufferGetHeight(inputPixelBuffer));
    
    // 4. 转换为 OpenGL 纹理
    CVOpenGLESTextureRef inputTexture;
    BOOL success = [converter convertPixelBufferToTextureRef:inputPixelBuffer textureRef:&inputTexture];
    
    if (!success) {
        NSLog(@"❌ 转换为纹理失败");
        CVPixelBufferRelease(inputPixelBuffer);
        return;
    }
    
    GLuint inputTextureID = CVOpenGLESTextureGetName(inputTexture);
    NSLog(@"✅ 转换为纹理成功");
    
    // 5. 模拟 OpenGL 处理（滤镜、美颜等）
    // 这里只是简单地创建一个输出纹理，实际应用中会有复杂的着色器处理
    int width = (int)CVPixelBufferGetWidth(inputPixelBuffer);
    int height = (int)CVPixelBufferGetHeight(inputPixelBuffer);
    GLuint outputTextureID = [converter createTextureWithWidth:width height:height];
    
    // 模拟处理：简单地复制输入纹理
    // 实际应用中会使用帧缓冲和着色器进行复杂处理
    glBindTexture(GL_TEXTURE_2D, outputTextureID);
    // ... 实际的 OpenGL 处理代码 ...
    
    NSLog(@"✅ OpenGL 处理完成");
    
    // 6. 转换回 CVPixelBuffer
    CVPixelBufferRef outputPixelBuffer;
    success = [converter convertTexture:outputTextureID 
                                  width:width 
                                 height:height 
                         toPixelBuffer:&outputPixelBuffer];
    
    if (success) {
        NSLog(@"✅ 处理后转换为 CVPixelBuffer 成功");
        
        // 7. 输出处理后的视频帧
        // 实际应用中，这里会将 outputPixelBuffer 传递给编码器或显示器
        NSLog(@"📤 输出视频帧: %dx%d", 
              (int)CVPixelBufferGetWidth(outputPixelBuffer),
              (int)CVPixelBufferGetHeight(outputPixelBuffer));
        
        CVPixelBufferRelease(outputPixelBuffer);
    } else {
        NSLog(@"❌ 转换回 CVPixelBuffer 失败");
    }
    
    // 8. 清理资源
    CFRelease(inputTexture);
    glDeleteTextures(1, &outputTextureID);
    CVPixelBufferRelease(inputPixelBuffer);
}

#pragma mark - 工具方法

+ (CVPixelBufferRef)createPixelBufferFromImage:(UIImage *)image {
    if (!image) return NULL;
    
    CGSize imageSize = image.size;
    NSDictionary *options = @{
        (id)kCVPixelBufferCGImageCompatibilityKey: @(YES),
        (id)kCVPixelBufferCGBitmapContextCompatibilityKey: @(YES)
    };
    
    CVPixelBufferRef pixelBuffer = NULL;
    CVReturn status = CVPixelBufferCreate(kCFAllocatorDefault,
                                        imageSize.width,
                                        imageSize.height,
                                        kCVPixelFormatType_32BGRA,
                                        (__bridge CFDictionaryRef)options,
                                        &pixelBuffer);
    
    if (status != kCVReturnSuccess) {
        return NULL;
    }
    
    CVPixelBufferLockBaseAddress(pixelBuffer, 0);
    void *pixelData = CVPixelBufferGetBaseAddress(pixelBuffer);
    
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    CGContextRef context = CGBitmapContextCreate(pixelData,
                                               imageSize.width,
                                               imageSize.height,
                                               8,
                                               CVPixelBufferGetBytesPerRow(pixelBuffer),
                                               colorSpace,
                                               kCGImageAlphaPremultipliedFirst | kCGBitmapByteOrder32Little);
    
    CGContextDrawImage(context, CGRectMake(0, 0, imageSize.width, imageSize.height), image.CGImage);
    
    CGContextRelease(context);
    CGColorSpaceRelease(colorSpace);
    CVPixelBufferUnlockBaseAddress(pixelBuffer, 0);
    
    return pixelBuffer;
}

+ (UIImage *)createImageFromPixelBuffer:(CVPixelBufferRef)pixelBuffer {
    if (!pixelBuffer) return nil;
    
    CIImage *ciImage = [CIImage imageWithCVPixelBuffer:pixelBuffer];
    CIContext *context = [CIContext contextWithOptions:nil];
    CGImageRef cgImage = [context createCGImage:ciImage fromRect:ciImage.extent];
    
    if (!cgImage) return nil;
    
    UIImage *image = [UIImage imageWithCGImage:cgImage];
    CGImageRelease(cgImage);
    
    return image;
}

@end 