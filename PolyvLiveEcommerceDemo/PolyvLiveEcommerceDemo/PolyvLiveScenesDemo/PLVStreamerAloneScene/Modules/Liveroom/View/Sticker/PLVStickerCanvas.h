//
//  PLVStickerCanvas.h
//  PolyvLiveScenesDemo
//
//  Created by polyv on 2025/3/17.
//  Copyright © 2025 PLV. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PLVStickerTextModel.h"
#import "PLVStickerTextView.h"

NS_ASSUME_NONNULL_BEGIN

@class PLVStickerCanvas;

@protocol PLVStickerCanvasDelegate <NSObject>

/// 退出编辑模式
- (void)stickerCanvasExitEditMode:(PLVStickerCanvas *)stickerCanvas;

/// 进入编辑模式
- (void)stickerCanvasEnterEditMode:(PLVStickerCanvas *)stickerCanvas;

/// 开始编辑文本
- (void)stickerCanvasBeginEditingText:(PLVStickerCanvas *)stickerCanvas;

/// 结束编辑文本
- (void)stickerCanvasEndEditingText:(PLVStickerCanvas *)stickerCanvas;

/// 回调文字贴纸状态变化
- (void)stickerCanvasTextEditStateChanged:(PLVStickerCanvas *)stickerCanvas textView:(PLVStickerTextView *)textView;

/// 请求处理模版界面状态（当需要自动保存并关闭模版界面时）
- (void)stickerCanvasRequestHandleTemplateViewState:(PLVStickerCanvas *)stickerCanvas;

@end

@interface PLVStickerCanvas : UIView

@property (nonatomic, weak) id<PLVStickerCanvasDelegate> delegate;

@property (nonatomic, strong, readonly) UIView *contentView;
@property (nonatomic, assign) BOOL enableEdit;
@property (nonatomic, strong) PLVStickerTextView *currentEditingTextView;

@property (nonatomic, readonly) NSInteger curImageCount;

@property (nonatomic, readonly) NSInteger curTextCount;


/// 展示贴图画布
- (void)showCanvasWithImages:(NSArray<UIImage *> *)images;

/// 添加文本贴图
- (void)addTextStickerWithModel:(PLVStickerTextModel *)textModel;

/// 生成带透明通道的图片，子控件不透明
- (UIImage *)generateImageWithTransparentBackground;

/// 更新文本贴图
- (void)updateTextStickerWithModel:(PLVStickerTextModel *)textModel;

/// done 操作 保存贴纸
- (void)executeDone;

/// cancel 操作 取消贴纸编辑、新增
- (void)executeCancel;

@end

NS_ASSUME_NONNULL_END
