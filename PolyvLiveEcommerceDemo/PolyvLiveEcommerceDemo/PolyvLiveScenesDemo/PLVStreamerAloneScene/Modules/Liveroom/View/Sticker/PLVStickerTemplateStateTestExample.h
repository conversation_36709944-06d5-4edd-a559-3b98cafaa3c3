//
//  PLVStickerTemplateStateTestExample.h
//  PolyvLiveScenesDemo
//
//  Created by AI Assistant on 2025/1/8.
//  Copyright © 2025 PLV. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 贴纸模版界面状态管理测试示例
 * 
 * 这个类用于测试和验证在贴图编辑模式下，从文本贴纸切换到图片贴纸时
 * 模版界面的状态管理是否正确工作，包括：
 * 1. 自动执行done操作保存修改
 * 2. 模版界面正确隐藏
 * 3. 文本贴纸状态正确回到normal
 * 4. 图片贴纸正常被选中
 */
@interface PLVStickerTemplateStateTestExample : NSObject

/// 初始化方法
/// @param parentView 父视图，用于显示测试用的贴纸画布
- (instancetype)initWithParentView:(UIView *)parentView;

/// 测试基本的模版界面状态管理
- (void)testTemplateStateManagementBasic;

/// 测试文本编辑状态下的模版界面管理
- (void)testTemplateStateManagementWithTextEdit;

/// 测试无模版界面时的正常切换
- (void)testNormalSwitchingWithoutTemplate;

/// 测试多个文本贴纸的情况
- (void)testMultipleTextStickers;

/// 运行所有测试
- (void)runAllTests;

@end

NS_ASSUME_NONNULL_END
