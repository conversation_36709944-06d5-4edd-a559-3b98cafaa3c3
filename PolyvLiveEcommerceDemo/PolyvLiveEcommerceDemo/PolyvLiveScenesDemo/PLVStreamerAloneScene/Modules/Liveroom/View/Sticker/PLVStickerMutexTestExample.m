//
//  PLVStickerMutexTestExample.m
//  PolyvLiveScenesDemo
//
//  Created by AI Assistant on 2025/1/8.
//  Copyright © 2025 PLV. All rights reserved.
//

#import "PLVStickerMutexTestExample.h"
#import "PLVStickerCanvas.h"
#import "PLVStickerTextModel.h"

@interface PLVStickerMutexTestExample () <PLVStickerCanvasDelegate>

@property (nonatomic, strong) PLVStickerCanvas *stickerCanvas;
@property (nonatomic, weak) UIView *parentView;

@end

@implementation PLVStickerMutexTestExample

- (instancetype)initWithParentView:(UIView *)parentView {
    self = [super init];
    if (self) {
        _parentView = parentView;
        [self setupStickerCanvas];
        [self addTestStickers];
    }
    return self;
}

- (void)setupStickerCanvas {
    self.stickerCanvas = [[PLVStickerCanvas alloc] init];
    self.stickerCanvas.delegate = self;
    self.stickerCanvas.frame = self.parentView.bounds;
    [self.parentView addSubview:self.stickerCanvas];
}

- (void)addTestStickers {
    // 添加测试用的图片贴纸
    UIImage *testImage1 = [UIImage imageNamed:@"test_sticker_1"] ?: [self createTestImageWithColor:[UIColor redColor] text:@"图片1"];
    UIImage *testImage2 = [UIImage imageNamed:@"test_sticker_2"] ?: [self createTestImageWithColor:[UIColor blueColor] text:@"图片2"];
    [self.stickerCanvas showCanvasWithImages:@[testImage1, testImage2]];
    
    // 添加测试用的文本贴纸
    PLVStickerTextModel *textModel1 = [PLVStickerTextModel defaultTextModelWithText:@"文本贴纸1" templateType:PLVStickerTextTemplateType0];
    textModel1.position = CGPointMake(100, 200);
    [self.stickerCanvas addTextStickerWithModel:textModel1];
    
    PLVStickerTextModel *textModel2 = [PLVStickerTextModel defaultTextModelWithText:@"文本贴纸2" templateType:PLVStickerTextTemplateType1];
    textModel2.position = CGPointMake(250, 200);
    [self.stickerCanvas addTextStickerWithModel:textModel2];
}

- (UIImage *)createTestImageWithColor:(UIColor *)color text:(NSString *)text {
    CGSize size = CGSizeMake(100, 100);
    UIGraphicsBeginImageContextWithOptions(size, NO, 0);
    
    // 绘制背景色
    [color setFill];
    UIRectFill(CGRectMake(0, 0, size.width, size.height));
    
    // 绘制文字
    NSDictionary *attributes = @{
        NSFontAttributeName: [UIFont systemFontOfSize:16],
        NSForegroundColorAttributeName: [UIColor whiteColor]
    };
    CGSize textSize = [text sizeWithAttributes:attributes];
    CGPoint textPoint = CGPointMake((size.width - textSize.width) / 2, (size.height - textSize.height) / 2);
    [text drawAtPoint:textPoint withAttributes:attributes];
    
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return image;
}

#pragma mark - Test Methods

- (void)testImageStickerMutex {
    NSLog(@"=== 测试图片贴纸互斥逻辑 ===");
    NSLog(@"请依次点击不同的图片贴纸，观察边框显示情况");
    NSLog(@"预期结果：同时只有一个图片贴纸显示边框");
}

- (void)testTextStickerMutex {
    NSLog(@"=== 测试文本贴纸互斥逻辑 ===");
    NSLog(@"请依次点击不同的文本贴纸，观察编辑状态变化");
    NSLog(@"预期结果：同时只有一个文本贴纸处于actionshow状态");
}

- (void)testCrossTypeMutex {
    NSLog(@"=== 测试图片与文本贴纸互斥逻辑 ===");
    NSLog(@"请交替点击图片贴纸和文本贴纸");
    NSLog(@"预期结果：点击图片贴纸时文本贴纸回到normal状态，反之亦然");
}

- (void)testBlankAreaReset {
    NSLog(@"=== 测试空白区域重置逻辑 ===");
    NSLog(@"先选中任意贴纸，然后点击画布空白区域");
    NSLog(@"预期结果：所有贴纸都回到normal状态，退出编辑模式");
}

#pragma mark - PLVStickerCanvasDelegate

- (void)stickerCanvasEnterEditMode:(PLVStickerCanvas *)stickerCanvas {
    NSLog(@"✅ 进入编辑模式");
}

- (void)stickerCanvasExitEditMode:(PLVStickerCanvas *)stickerCanvas {
    NSLog(@"❌ 退出编辑模式");
}

- (void)stickerCanvasBeginEditingText:(PLVStickerCanvas *)stickerCanvas {
    NSLog(@"📝 开始编辑文本");
}

- (void)stickerCanvasEndEditingText:(PLVStickerCanvas *)stickerCanvas {
    NSLog(@"✏️ 结束编辑文本");
}

- (void)stickerCanvasTextEditStateChanged:(PLVStickerCanvas *)stickerCanvas textView:(PLVStickerTextView *)textView {
    NSString *stateString = @"";
    switch (textView.editState) {
        case PLVStickerTextEditStateNormal:
            stateString = @"Normal";
            break;
        case PLVStickerTextEditStateSelected:
            stateString = @"Selected";
            break;
        case PLVStickerTextEditStateActionVisible:
            stateString = @"ActionVisible";
            break;
        case PLVStickerTextEditStateTextEditing:
            stateString = @"TextEditing";
            break;
    }
    NSLog(@"📱 文本贴纸状态变化: %@ -> %@", textView.textModel.text, stateString);
}

@end

/*
 使用示例：
 
 // 在视图控制器中创建测试实例
 PLVStickerMutexTestExample *mutexTest = [[PLVStickerMutexTestExample alloc] initWithParentView:self.view];
 
 // 运行各种测试
 [mutexTest testImageStickerMutex];
 [mutexTest testTextStickerMutex];
 [mutexTest testCrossTypeMutex];
 [mutexTest testBlankAreaReset];
 
 // 观察控制台日志输出，验证互斥逻辑是否正确工作
 */
