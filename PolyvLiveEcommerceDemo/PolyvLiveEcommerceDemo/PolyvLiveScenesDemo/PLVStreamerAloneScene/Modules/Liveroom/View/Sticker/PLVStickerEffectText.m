//
//  PLVStickerEffectText.m
//  PolyvLiveScenesDemo
//
//  Created by polyv on 2025/7/9.
//  Copyright © 2025 PLV. All rights reserved.
//

#import "PLVStickerEffectText.h"
#import "PLVStickerEffectLable.h"

@interface PLVStickerEffectText ()

@property (nonatomic, strong) PLVStickerEffectLable *effectLable;
@property (nonatomic, strong) UIImageView *bgImageView;
@property (nonatomic, strong) UIImageView *tipsIcon;

@property (nonatomic, strong) NSString *text;
@property (nonatomic, assign) PLVStickerTextTemplateType templateType;

@end

@implementation PLVStickerEffectText

- (instancetype)initWithText:(NSString *)text templateType:(PLVStickerTextTemplateType)templateType {
    self = [super initWithFrame:CGRectZero];
    if (self) {
        self.text = text;
        self.templateType = templateType;

        [self setupUI];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];

    switch (self.templateType) {
        case PLVStickerTextTemplateType0:
            break;
        case PLVStickerTextTemplateType1:
            break;
        case PLVStickerTextTemplateType2:
            break;
        case PLVStickerTextTemplateType3:
            break;
        case PLVStickerTextTemplateType4:
            break;
        case PLVStickerTextTemplateType5:
            break;
        case PLVStickerTextTemplateType6:
            break;
        case PLVStickerTextTemplateType7:
            break;
        default:
            break;
    }

    self.bgImageView.frame = self.bounds;
    self.tipsIcon.frame = CGRectMake(0, 0, 20, 20);
    self.effectLable.frame = self.bounds;
}

- (void)setupUI {
    self.bgImageView = [[UIImageView alloc] init];
    self.bgImageView.image = [UIImage imageNamed:@"plv_sticker_effect_text_bg"];
    [self addSubview:self.bgImageView];

    self.tipsIcon = [[UIImageView alloc] init];
    self.tipsIcon.image = [UIImage imageNamed:@"plv_sticker_effect_text_tips"];
    [self addSubview:self.tipsIcon];

    self.effectLable = [[PLVStickerEffectLable alloc] init];
    self.effectLable.text = self.text;
    [self configEffectLable:self.templateType];
    
    [self addSubview:self.effectLable];
}

- (void)updateText:(NSString *)text templateType:(PLVStickerTextTemplateType)templateType {
    self.text = text;
    self.effectLable.text = self.text;

    self.templateType = templateType;

    [self configEffectLable:templateType];
}

- (void)updateText:(NSString *)text{
    self.text = text;
    self.effectLable.text = self.text;
}

- (void)configEffectLable:(PLVStickerTextTemplateType )templateType{
    PLVStickerTextTemplate *template = [PLVStickerTextTemplate defaultTextTemplateWithTemplateType:templateType];
    self.effectLable.textColor = template.textColor;
    self.effectLable.backgroundColor = template.backgroundColor;
    self.effectLable.font = [UIFont fontWithName:template.fontName size:template.fontSize];
    self.effectLable.textAlignment = template.textAlignment;
    self.effectLable.strokeWidth = template.strokeWidth;
    self.effectLable.strokeColor = template.strokeColor;
    self.effectLable.textInsets = template.textInsets;
    self.effectLable.customShadowOffset = template.customShadowOffset;
    self.effectLable.customShadowBlurRadius = template.customShadowBlurRadius;
    self.effectLable.customShadowColor = template.customShadowColor;
}


@end
