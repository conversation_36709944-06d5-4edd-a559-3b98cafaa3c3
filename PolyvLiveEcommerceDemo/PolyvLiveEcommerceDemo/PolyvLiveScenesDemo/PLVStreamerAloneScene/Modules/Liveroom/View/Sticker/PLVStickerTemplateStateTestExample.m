//
//  PLVStickerTemplateStateTestExample.m
//  PolyvLiveScenesDemo
//
//  Created by AI Assistant on 2025/1/8.
//  Copyright © 2025 PLV. All rights reserved.
//

#import "PLVStickerTemplateStateTestExample.h"
#import "PLVStickerManager.h"
#import "PLVStickerTextModel.h"

@interface PLVStickerTemplateStateTestExample () <PLVStickerManagerDelegate>

@property (nonatomic, strong) PLVStickerManager *stickerManager;
@property (nonatomic, weak) UIView *parentView;

@end

@implementation PLVStickerTemplateStateTestExample

- (instancetype)initWithParentView:(UIView *)parentView {
    self = [super init];
    if (self) {
        _parentView = parentView;
        [self setupStickerManager];
        [self addTestStickers];
    }
    return self;
}

- (void)setupStickerManager {
    self.stickerManager = [[PLVStickerManager alloc] initWithParentView:self.parentView];
    self.stickerManager.delegate = self;
}

- (void)addTestStickers {
    // 添加测试用的文本贴纸
    PLVStickerTextModel *textModel = [PLVStickerTextModel defaultTextModelWithText:@"测试文本" templateType:PLVStickerTextTemplateType0];
    textModel.position = CGPointMake(100, 200);
    [self.stickerManager.stickerCanvas addTextStickerWithModel:textModel];
    
    // 添加测试用的图片贴纸
    UIImage *testImage = [self createTestImageWithColor:[UIColor blueColor] text:@"图片"];
    [self.stickerManager.stickerCanvas showCanvasWithImages:@[testImage]];
}

- (UIImage *)createTestImageWithColor:(UIColor *)color text:(NSString *)text {
    CGSize size = CGSizeMake(100, 100);
    UIGraphicsBeginImageContextWithOptions(size, NO, 0);
    
    // 绘制背景色
    [color setFill];
    UIRectFill(CGRectMake(0, 0, size.width, size.height));
    
    // 绘制文字
    NSDictionary *attributes = @{
        NSFontAttributeName: [UIFont systemFontOfSize:16],
        NSForegroundColorAttributeName: [UIColor whiteColor]
    };
    CGSize textSize = [text sizeWithAttributes:attributes];
    CGPoint textPoint = CGPointMake((size.width - textSize.width) / 2, (size.height - textSize.height) / 2);
    [text drawAtPoint:textPoint withAttributes:attributes];
    
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return image;
}

#pragma mark - Test Methods

- (void)testTemplateStateManagementBasic {
    NSLog(@"=== 测试基本的模版界面状态管理 ===");
    NSLog(@"步骤：");
    NSLog(@"1. 点击文本贴纸进入actionshow状态（模版界面显示）");
    NSLog(@"2. 在模版界面中选择不同的样式");
    NSLog(@"3. 点击图片贴纸");
    NSLog(@"预期结果：模版界面自动保存并消失，文本贴纸应用样式并回到normal状态，图片贴纸被选中");
}

- (void)testTemplateStateManagementWithTextEdit {
    NSLog(@"=== 测试文本编辑状态下的模版界面管理 ===");
    NSLog(@"步骤：");
    NSLog(@"1. 点击文本贴纸进入actionshow状态");
    NSLog(@"2. 点击编辑按钮进入textedit状态（模版界面显示）");
    NSLog(@"3. 修改文本内容和样式");
    NSLog(@"4. 点击图片贴纸");
    NSLog(@"预期结果：模版界面自动保存修改并消失，文本贴纸显示新内容和样式，图片贴纸被选中");
}

- (void)testNormalSwitchingWithoutTemplate {
    NSLog(@"=== 测试无模版界面时的正常切换 ===");
    NSLog(@"步骤：");
    NSLog(@"1. 确保文本贴纸处于normal状态（无模版界面显示）");
    NSLog(@"2. 点击图片贴纸");
    NSLog(@"预期结果：正常执行图片贴纸选中逻辑，无额外的模版界面处理");
}

- (void)testMultipleTextStickers {
    NSLog(@"=== 测试多个文本贴纸的情况 ===");
    NSLog(@"步骤：");
    NSLog(@"1. 添加多个文本贴纸");
    NSLog(@"2. 其中一个文本贴纸处于编辑状态（模版界面显示）");
    NSLog(@"3. 点击图片贴纸");
    NSLog(@"预期结果：模版界面自动保存并消失，所有文本贴纸回到normal状态，图片贴纸被选中");
    
    // 添加额外的文本贴纸用于测试
    PLVStickerTextModel *textModel2 = [PLVStickerTextModel defaultTextModelWithText:@"测试文本2" templateType:PLVStickerTextTemplateType1];
    textModel2.position = CGPointMake(250, 200);
    [self.stickerManager.stickerCanvas addTextStickerWithModel:textModel2];
}

- (void)runAllTests {
    NSLog(@"🚀 开始运行模版界面状态管理测试");
    NSLog(@"");
    
    [self testTemplateStateManagementBasic];
    NSLog(@"");
    
    [self testTemplateStateManagementWithTextEdit];
    NSLog(@"");
    
    [self testNormalSwitchingWithoutTemplate];
    NSLog(@"");
    
    [self testMultipleTextStickers];
    NSLog(@"");
    
    NSLog(@"✅ 所有测试用例已列出，请按照步骤手动测试并观察结果");
}

#pragma mark - PLVStickerManagerDelegate

- (void)stickerManagerDidEnterEditMode:(PLVStickerManager *)manager {
    NSLog(@"📝 贴图管理器进入编辑模式");
}

- (void)stickerManagerDidExitEditMode:(PLVStickerManager *)manager {
    NSLog(@"❌ 贴图管理器退出编辑模式");
}

@end

/*
 使用示例：
 
 // 在视图控制器中创建测试实例
 PLVStickerTemplateStateTestExample *stateTest = [[PLVStickerTemplateStateTestExample alloc] initWithParentView:self.view];
 
 // 运行所有测试
 [stateTest runAllTests];
 
 // 或者运行单个测试
 [stateTest testTemplateStateManagementBasic];
 [stateTest testTemplateStateManagementWithTextEdit];
 [stateTest testNormalSwitchingWithoutTemplate];
 [stateTest testMultipleTextStickers];
 
 // 观察控制台日志输出和UI变化，验证模版界面状态管理是否正确工作
 */
