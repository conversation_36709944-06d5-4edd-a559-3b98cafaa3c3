//
//  PLVStickerTextEditorView.m
//  PolyvLiveScenesDemo
//
//  Created by polyv on 2023/9/15.
//  Copyright © 2023 PLV. All rights reserved.
//

#import "PLVStickerTextEditorView.h"
#import <PLVFoundationSDK/PLVColorUtil.h>
#import "PLVSAUtils.h"
#import "PLVStickerEffectText.h"

// 最大文本长度限制
static NSInteger const kMaxTextLength = 8;
// 文本输入工具栏的高度
static CGFloat const kInputToolBarHeight = 62.0;

@interface PLVStickerTextEditorView () <UITextFieldDelegate>

@property (nonatomic, strong) UIView *inputToolBar;
@property (nonatomic, strong) UITextField *textField;
@property (nonatomic, strong) UIButton *doneButton;
@property (nonatomic, strong) UILabel *countLabel;
@property (nonatomic, strong) PLVStickerEffectText *previewEffectText;
@property (nonatomic, strong) PLVStickerTextModel *textModel;
@property (nonatomic, copy) NSString *initialText;
@property (nonatomic, assign) CGFloat keyboardHeight;

@end

@implementation PLVStickerTextEditorView

#pragma mark - Life Cycle

- (instancetype)initWithTextModel:(PLVStickerTextModel *)model height:(CGFloat)height {
    self = [super initWithSheetHeight:height];
    if (self) {
        _textModel = model;
        _initialText = model.editText ?: @"";
        _keyboardHeight = 0;
        
        self.contentView.backgroundColor = [UIColor clearColor];
        self.backgroundColor = [UIColor clearColor];
        
        [self setupUI];
        [self addKeyboardNotifications];
    }
    return self;
}

- (void)dealloc {
    [self removeKeyboardNotifications];
}

- (void)showInView:(UIView *)parentView {
    [super showInView:parentView];
    [self.textField becomeFirstResponder];
}

- (void)dismiss {
    [self.textField resignFirstResponder];
    [super dismiss];
}

#pragma mark - UI Setup

- (void)setupUI {
    [self.contentView addSubview:self.previewEffectText];
    
    [self.contentView addSubview:self.inputToolBar];
    [self.inputToolBar addSubview:self.textField];
    [self.inputToolBar addSubview:self.doneButton];
    
    // 为 countLabel 创建一个容器视图以实现内边距
    UIView *rightViewContainer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 50, 44)];
    self.countLabel.frame = CGRectMake(0, 0, 42, 44);
    [rightViewContainer addSubview:self.countLabel];
    
    self.textField.rightView = rightViewContainer;
    self.textField.rightViewMode = UITextFieldViewModeAlways;
    
    self.textField.text = self.initialText;
    [self updateCountLabel];
    
    __weak typeof(self) weakSelf = self;
    self.didCloseSheet = ^{
        if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector(textEditorViewDidCancel:)]) {
            [weakSelf.delegate textEditorViewDidCancel:weakSelf];
        }
    };
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    // inputToolBar 随键盘在 full-screen view 的底部移动
    CGFloat toolBarY = self.bounds.size.height - self.keyboardHeight - kInputToolBarHeight;
    self.inputToolBar.frame = CGRectMake(0, toolBarY, self.bounds.size.width, kInputToolBarHeight);

    // 布局预览标签，使其位于输入框上方的可见区域居中
    CGFloat previewAreaHeight = toolBarY;
    self.previewEffectText.bounds = CGRectMake(0, 0, self.textModel.size.width, self.textModel.size.height);
    self.previewEffectText.center = CGPointMake(self.bounds.size.width / 2, previewAreaHeight / 2);

    CGFloat margin = 16;
    CGFloat buttonWidth = 60;
    CGFloat spacing = 8;
    
    CGFloat textFieldWidth = self.inputToolBar.bounds.size.width - margin * 2 - buttonWidth - spacing;
    self.textField.frame = CGRectMake(margin, (kInputToolBarHeight - 44) / 2, textFieldWidth, 44);
    self.doneButton.frame = CGRectMake(CGRectGetMaxX(self.textField.frame) + spacing, (kInputToolBarHeight - 26) / 2, buttonWidth, 26);
}

#pragma mark - Actions

- (void)doneButtonAction {
    if (self.delegate && [self.delegate respondsToSelector:@selector(textEditorView:didFinishEditingWithText:)]) {
        [self.delegate textEditorView:self didFinishEditingWithText:self.textField.text];
    }
    [self dismiss];
}

- (void)updateCountLabel {
    NSInteger currentLength = self.textField.text.length;
    self.countLabel.text = [NSString stringWithFormat:@"%ld/%ld", (long)currentLength, (long)kMaxTextLength];
    
    self.countLabel.textColor = (currentLength > kMaxTextLength) ? [UIColor redColor] : [PLVColorUtil colorFromHexString:@"#999999"];
}

#pragma mark - UITextFieldDelegate

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    NSString *newText = [textField.text stringByReplacingCharactersInRange:range withString:string];
    if (newText.length > kMaxTextLength && ![string isEqualToString:@""]) {
        return NO;
    }
    return YES;
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    [self doneButtonAction];
    return NO;
}

- (void)textFieldDidChange:(UITextField *)textField {
    [self updateCountLabel];
    
    // 更新内部预览
    [self.previewEffectText updateText:textField.text];
    
    // 通过代理更新外部真实视图
    if (self.delegate && [self.delegate respondsToSelector:@selector(textEditorView:didUpdateText:)]) {
        [self.delegate textEditorView:self didUpdateText:textField.text];
    }
}

#pragma mark - Keyboard Notifications

- (void)addKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillShow:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillHide:) name:UIKeyboardWillHideNotification object:nil];
}

- (void)removeKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)keyboardWillShow:(NSNotification *)notification {
    NSDictionary *userInfo = notification.userInfo;
    CGRect keyboardFrame = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    NSTimeInterval animationDuration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve animationCurve = [userInfo[UIKeyboardAnimationCurveUserInfoKey] integerValue];
    
    CGFloat keyboardHeight = CGRectGetHeight(keyboardFrame);
    
    if (self.keyboardHeight != keyboardHeight) {
        self.keyboardHeight = keyboardHeight;
        
        [UIView animateWithDuration:animationDuration delay:0 options:(animationCurve << 16) animations:^{
            [self setNeedsLayout];
            [self layoutIfNeeded];
        } completion:nil];
    }
}

- (void)keyboardWillHide:(NSNotification *)notification {
    NSTimeInterval animationDuration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve animationCurve = [notification.userInfo[UIKeyboardAnimationCurveUserInfoKey] integerValue];
    
    self.keyboardHeight = 0;
    
    [UIView animateWithDuration:animationDuration delay:0 options:(animationCurve << 16) animations:^{
        [self setNeedsLayout];
        [self layoutIfNeeded];
    } completion:nil];
}

#pragma mark - Lazy Loading

- (PLVStickerEffectText *)previewEffectText{
    if (!_previewEffectText){
        _previewEffectText = [[PLVStickerEffectText alloc] initWithText:self.textModel.editText templateType:self.textModel.editTemplateType];
    }
    return _previewEffectText;
}

- (UIView *)inputToolBar {
    if (!_inputToolBar) {
        _inputToolBar = [[UIView alloc] init];
        _inputToolBar.backgroundColor = [PLVColorUtil colorFromHexString:@"#2B2C35" alpha:0.8];
        UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
        UIVisualEffectView *blurView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
        blurView.frame = _inputToolBar.bounds;
        blurView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        [_inputToolBar addSubview:blurView];
    }
    return _inputToolBar;
}

- (UITextField *)textField {
    if (!_textField) {
        _textField = [[UITextField alloc] init];
        _textField.font = [UIFont systemFontOfSize:14];
        _textField.textColor = [UIColor whiteColor];
        _textField.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.2];
        _textField.layer.cornerRadius = 22;
        _textField.layer.masksToBounds = YES;
        _textField.delegate = self;
        _textField.returnKeyType = UIReturnKeyDone;
        
        NSAttributedString *placeholder = [[NSAttributedString alloc] initWithString:@"关注主播赠送好礼" attributes:@{NSForegroundColorAttributeName: [UIColor colorWithWhite:1.0 alpha:0.6]}];
        _textField.attributedPlaceholder = placeholder;

        _textField.leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 16, 0)];
        _textField.leftViewMode = UITextFieldViewModeAlways;
        
        [_textField addTarget:self action:@selector(textFieldDidChange:) forControlEvents:UIControlEventEditingChanged];
    }
    return _textField;
}

- (UILabel *)countLabel {
    if (!_countLabel) {
        _countLabel = [[UILabel alloc] init];
        _countLabel.font = [UIFont systemFontOfSize:12];
        _countLabel.textColor = [PLVColorUtil colorFromHexString:@"#999999"];
        _countLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _countLabel;
}

- (UIButton *)doneButton {
    if (!_doneButton) {
        _doneButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_doneButton setTitle:@"完成" forState:UIControlStateNormal];
        [_doneButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _doneButton.titleLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];
        _doneButton.backgroundColor = [PLVColorUtil colorFromHexString:@"#409EFF"];
        _doneButton.layer.cornerRadius = 16;
        _doneButton.layer.masksToBounds = YES;
        
        [_doneButton addTarget:self action:@selector(doneButtonAction) forControlEvents:UIControlEventTouchUpInside];
    }
    return _doneButton;
}

@end
