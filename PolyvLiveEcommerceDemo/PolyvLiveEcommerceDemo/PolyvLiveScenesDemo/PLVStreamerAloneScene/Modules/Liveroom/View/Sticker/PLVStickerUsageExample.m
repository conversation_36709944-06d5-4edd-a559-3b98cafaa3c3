//
//  PLVStickerUsageExample.m
//  PolyvLiveScenesDemo
//
//  Created by AI Assistant on 2025/1/8.
//  Copyright © 2025 PLV. All rights reserved.
//

#import "PLVStickerUsageExample.h"
#import "PLVStickerManager.h"

@interface PLVStickerUsageExample () <PLVStickerManagerDelegate>

@property (nonatomic, strong) PLVStickerManager *stickerManager;
@property (nonatomic, weak) UIView *parentView;

@end

@implementation PLVStickerUsageExample

- (instancetype)initWithParentView:(UIView *)parentView {
    self = [super init];
    if (self) {
        _parentView = parentView;
        [self setupStickerManager];
    }
    return self;
}

- (void)setupStickerManager {
    self.stickerManager = [[PLVStickerManager alloc] initWithParentView:self.parentView];
    self.stickerManager.delegate = self;
}

#pragma mark - Public Methods

/// 显示贴图功能入口
- (void)showStickerFunction {
    // 这是贴图功能的主入口，会显示类型选择界面（文本/图片）
    [self.stickerManager showStickerTypeSelection];
}

/// 生成最终的贴图图像
- (UIImage *)generateFinalStickerImage {
    return [self.stickerManager generateStickerImage];
}

/// 清除所有贴图
- (void)clearAllStickers {
    [self.stickerManager clearAllStickers];
}

#pragma mark - PLVStickerManagerDelegate

- (void)stickerManagerDidEnterEditMode:(PLVStickerManager *)manager {
    NSLog(@"贴图管理器进入编辑模式");
    // 在这里可以处理进入编辑模式的UI变化
    // 例如：隐藏其他UI元素，显示编辑工具栏等
}

- (void)stickerManagerDidExitEditMode:(PLVStickerManager *)manager {
    NSLog(@"贴图管理器退出编辑模式");
    // 在这里可以处理退出编辑模式的UI变化
    // 例如：显示其他UI元素，隐藏编辑工具栏等
    
    // 可以在这里获取最终的贴图图像
    UIImage *finalImage = [self generateFinalStickerImage];
    if (finalImage) {
        NSLog(@"生成最终贴图图像成功");
        // 处理最终图像，例如保存或应用到视频流
    }
}

@end

/*
 使用示例：
 
 // 1. 在视图控制器中创建贴图功能
 PLVStickerUsageExample *stickerExample = [[PLVStickerUsageExample alloc] initWithParentView:self.view];
 
 // 2. 显示贴图功能
 [stickerExample showStickerFunction];
 
 // 3. 用户操作流程：
 //    a) 选择"文字"类型
 //    b) 选择文字模版
 //    c) 贴纸添加到画布，进入actionshow状态
 //    d) 点击贴纸或编辑按钮进入文本编辑
 //    e) 修改文本内容
 //    f) 点击完成回到actionshow状态
 //    g) 点击空白区域退出编辑模式
 
 // 4. 获取最终图像
 UIImage *finalImage = [stickerExample generateFinalStickerImage];
 
 // 5. 清除所有贴图
 [stickerExample clearAllStickers];
 */
