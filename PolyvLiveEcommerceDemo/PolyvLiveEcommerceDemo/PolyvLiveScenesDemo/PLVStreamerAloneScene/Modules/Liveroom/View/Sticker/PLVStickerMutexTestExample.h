//
//  PLVStickerMutexTestExample.h
//  PolyvLiveScenesDemo
//
//  Created by AI Assistant on 2025/1/8.
//  Copyright © 2025 PLV. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 贴纸焦点切换互斥逻辑测试示例
 * 
 * 这个类用于测试和验证贴纸焦点切换的互斥逻辑是否正确工作，包括：
 * 1. 图片贴纸之间的互斥选中
 * 2. 文本贴纸之间的互斥选中
 * 3. 图片贴纸与文本贴纸之间的互斥选中
 * 4. 空白区域点击重置所有贴纸状态
 */
@interface PLVStickerMutexTestExample : NSObject

/// 初始化方法
/// @param parentView 父视图，用于显示测试用的贴纸画布
- (instancetype)initWithParentView:(UIView *)parentView;

/// 测试图片贴纸互斥逻辑
- (void)testImageStickerMutex;

/// 测试文本贴纸互斥逻辑
- (void)testTextStickerMutex;

/// 测试图片与文本贴纸互斥逻辑
- (void)testCrossTypeMutex;

/// 测试空白区域重置逻辑
- (void)testBlankAreaReset;

@end

NS_ASSUME_NONNULL_BEGIN
