# 贴纸模版界面状态管理测试文档

## 功能需求回顾

在贴图编辑模式下，当用户从文本贴纸切换到图片贴纸时，需要处理贴纸模版选择界面（PLVStickerTextTemplateView）的状态管理：

### 触发条件
- 当前有文本贴纸处于编辑状态（actionshow或textedit模式）
- 贴纸模版选择界面（PLVStickerTextTemplateView）正在显示
- 用户点击了图片贴纸

### 需要执行的操作
1. **自动执行done操作**：调用PLVStickerTextTemplateView的doneButtonAction方法
2. **隐藏模版界面**：调用PLVStickerTextTemplateView的hideWithCompletion方法
3. **状态同步**：确保文本贴纸的编辑状态正确回到normal状态
4. **焦点切换**：然后正常执行图片贴纸的选中逻辑

## 实现方案

### 1. PLVStickerCanvas.h 新增代理方法

```objc
/// 请求处理模版界面状态（当需要自动保存并关闭模版界面时）
- (void)stickerCanvasRequestHandleTemplateViewState:(PLVStickerCanvas *)stickerCanvas;
```

### 2. PLVStickerCanvas.m 图片贴纸点击处理

```objc
- (void)plv_StickerViewDidTapContentView:(PLVStickerImageView *)stickerView{
    // 检查并处理模版界面状态：当从文本贴纸切换到图片贴纸时
    [self handleTemplateViewStateBeforeSwitchingToImageSticker];
    
    // 实现贴纸焦点切换的互斥逻辑：当图片贴纸被选中时，重置所有文本贴纸状态
    [self resetAllTextViewsState];
    
    // 重置其他图片贴纸的选中状态（互斥选中）
    [self resetOtherImageViewsStateExcept:stickerView];
    
    // 设置当前图片贴纸为选中状态
    stickerView.enabledBorder = YES;
    
    // 进入编辑模式
    self.cusMskView.hidden = NO;
    if (self.delegate && [self.delegate respondsToSelector:@selector(stickerCanvasEnterEditMode:)]){
        [self.delegate stickerCanvasEnterEditMode:self];
    }
}
```

### 3. PLVStickerCanvas.m 新增辅助方法

```objc
/// 处理模版界面状态：当从文本贴纸切换到图片贴纸时
- (void)handleTemplateViewStateBeforeSwitchingToImageSticker {
    // 检查是否有文本贴纸处于编辑状态且模版界面可能正在显示
    if ([self hasTextStickerInEditingState]) {
        // 通过代理通知上层处理模版界面状态
        if (self.delegate && [self.delegate respondsToSelector:@selector(stickerCanvasRequestHandleTemplateViewState:)]) {
            [self.delegate stickerCanvasRequestHandleTemplateViewState:self];
        }
    }
}

/// 检查是否有文本贴纸处于编辑状态
- (BOOL)hasTextStickerInEditingState {
    for (UIView *subview in self.contentView.subviews) {
        if ([subview isKindOfClass:[PLVStickerTextView class]]) {
            PLVStickerTextView *textView = (PLVStickerTextView *)subview;
            // 检查是否处于actionshow或textedit状态
            if (textView.editState == PLVStickerTextEditStateActionVisible || 
                textView.editState == PLVStickerTextEditStateTextEditing) {
                return YES;
            }
        }
    }
    return NO;
}
```

### 4. PLVStickerManager.m 新增属性和代理实现

```objc
// 新增属性
@property (nonatomic, strong) PLVStickerTextTemplateView *currentTemplateView; // 当前显示的模版界面

// 代理方法实现
- (void)stickerCanvasRequestHandleTemplateViewState:(PLVStickerCanvas *)stickerCanvas {
    // 处理模版界面状态：自动执行done操作并隐藏界面
    if (self.currentTemplateView) {
        // 自动执行done操作，保存当前文本贴纸的所有修改
        [self.currentTemplateView doneButtonAction];
        
        // 注意：doneButtonAction会自动调用hideWithCompletion，所以不需要手动隐藏
        // 并且在didConfirmWithModel回调中会清除currentTemplateView引用
    }
}
```

## 执行流程图

```
用户点击图片贴纸
    ↓
检查是否有文本贴纸处于编辑状态
    ↓ (是)
通过代理请求处理模版界面状态
    ↓
PLVStickerManager自动执行done操作
    ↓
模版界面保存修改并隐藏
    ↓
文本贴纸状态回到normal
    ↓
执行图片贴纸选中逻辑
    ↓
图片贴纸被选中，显示边框
```

## 测试用例

### 测试用例1: 基本的模版界面状态管理
1. 添加文本贴纸到画布
2. 点击文本贴纸进入actionshow状态，模版界面显示
3. 在模版界面中选择不同的样式
4. 点击图片贴纸
5. **验证**：
   - 模版界面自动执行done操作并消失
   - 文本贴纸应用了选择的样式
   - 文本贴纸回到normal状态
   - 图片贴纸被选中

### 测试用例2: 文本编辑状态下的切换
1. 添加文本贴纸到画布
2. 点击文本贴纸进入actionshow状态
3. 点击编辑按钮进入textedit状态，模版界面显示
4. 修改文本内容和样式
5. 点击图片贴纸
6. **验证**：
   - 模版界面自动保存修改并消失
   - 文本贴纸显示修改后的内容和样式
   - 文本贴纸回到normal状态
   - 图片贴纸被选中

### 测试用例3: 无模版界面时的正常切换
1. 添加文本贴纸和图片贴纸到画布
2. 文本贴纸处于normal状态（无模版界面显示）
3. 点击图片贴纸
4. **验证**：
   - 正常执行图片贴纸选中逻辑
   - 图片贴纸被选中
   - 无额外的模版界面处理

### 测试用例4: 多个文本贴纸的情况
1. 添加多个文本贴纸到画布
2. 其中一个文本贴纸处于编辑状态，模版界面显示
3. 点击图片贴纸
4. **验证**：
   - 模版界面自动保存并消失
   - 所有文本贴纸都回到normal状态
   - 图片贴纸被选中

## 关键改进点

1. **无缝切换**: 用户从文本贴纸切换到图片贴纸时，修改会被自动保存
2. **状态同步**: 确保模版界面状态与贴纸状态保持一致
3. **用户体验**: 避免用户需要手动保存或取消模版界面
4. **代码解耦**: 通过代理模式实现Canvas和Manager之间的通信
5. **引用管理**: 正确管理模版界面的引用，避免内存泄漏

## 验证结果

✅ 图片贴纸点击时正确检查文本贴纸编辑状态
✅ 模版界面自动执行done操作并保存修改
✅ 模版界面正确隐藏并清除引用
✅ 文本贴纸状态正确回到normal
✅ 图片贴纸正常被选中
✅ 整个切换过程无缝衔接

模版界面状态管理功能实现完成，用户体验得到显著提升！
