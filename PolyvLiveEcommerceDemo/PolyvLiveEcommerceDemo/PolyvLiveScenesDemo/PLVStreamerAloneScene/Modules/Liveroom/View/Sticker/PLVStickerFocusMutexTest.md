# 贴纸焦点切换互斥逻辑测试文档

## 功能需求回顾

实现贴纸焦点切换的互斥逻辑：
1. 当用户点击选中图片贴纸时，之前选中的文本贴纸应该自动回到normal状态
2. 确保文本贴纸从任何编辑状态（Selected、ActionVisible、TextEditing）都能正确回到Normal状态
3. 当文本贴纸被选中时，图片贴纸也应该取消选中状态
4. 实现贴纸间的互斥选中逻辑，确保同时只有一个贴纸处于编辑状态

## 实现方案

### 1. 图片贴纸点击处理 (plv_StickerViewDidTapContentView)

```objc
- (void)plv_StickerViewDidTapContentView:(PLVStickerImageView *)stickerView{
    // 实现贴纸焦点切换的互斥逻辑：当图片贴纸被选中时，重置所有文本贴纸状态
    [self resetAllTextViewsState];
    
    // 重置其他图片贴纸的选中状态（互斥选中）
    [self resetOtherImageViewsStateExcept:stickerView];
    
    // 设置当前图片贴纸为选中状态
    stickerView.enabledBorder = YES;
    
    // 进入编辑模式
    self.cusMskView.hidden = NO;
    if (self.delegate && [self.delegate respondsToSelector:@selector(stickerCanvasEnterEditMode:)]){
        [self.delegate stickerCanvasEnterEditMode:self];
    }
}
```

### 2. 文本贴纸点击处理 (plv_StickerTextViewDidTapContentView)

```objc
- (void)plv_StickerTextViewDidTapContentView:(PLVStickerTextView *)stickerTextView {
    // 实现贴纸焦点切换的互斥逻辑：当文本贴纸被选中时，重置所有图片贴纸状态
    [self resetAllImageViewsState];
    
    // 进入编辑模式
    self.cusMskView.hidden = NO;
    if (self.delegate && [self.delegate respondsToSelector:@selector(stickerCanvasEnterEditMode:)]){
        [self.delegate stickerCanvasEnterEditMode:self];
    }
}
```

### 3. 空白区域点击处理 (tapAction)

```objc
- (void)tapAction:(UITapGestureRecognizer *)tapGesture{
    // 如果当前处于文字贴纸添加或者编辑状态 （StickerTemplateView 显示中）不处理
    if (self.currentEditingTextView.editState >= PLVStickerTextEditStateActionVisible)
        return;
    
    // 重置所有文本贴纸状态
    [self resetAllTextViewsState];
    
    // 重置所有图片贴纸状态
    [self resetAllImageViewsState];
    
    // 关闭编辑模式
    if (self.delegate && [self.delegate respondsToSelector:@selector(stickerCanvasExitEditMode:)]){
        [self.delegate stickerCanvasExitEditMode:self];
    }
    self.cusMskView.hidden = YES;
}
```

### 4. 新增的辅助方法

#### 4.1 重置其他图片贴纸状态（保留当前选中）
```objc
- (void)resetOtherImageViewsStateExcept:(PLVStickerImageView *)exceptImageView {
    for (UIView *subview in self.contentView.subviews) {
        if ([subview isKindOfClass:[PLVStickerImageView class]] && subview != exceptImageView) {
            PLVStickerImageView *imageView = (PLVStickerImageView *)subview;
            imageView.enabledBorder = NO; // 取消边框显示，表示取消选中
        }
    }
}
```

#### 4.2 重置所有图片贴纸状态
```objc
- (void)resetAllImageViewsState {
    for (UIView *subview in self.contentView.subviews) {
        if ([subview isKindOfClass:[PLVStickerImageView class]]) {
            PLVStickerImageView *imageView = (PLVStickerImageView *)subview;
            imageView.enabledBorder = NO; // 取消边框显示，表示取消选中
        }
    }
}
```

## 互斥逻辑流程图

```
用户操作 -> 贴纸类型判断 -> 重置其他类型贴纸 -> 设置当前贴纸状态 -> 进入编辑模式

点击图片贴纸:
[图片贴纸被点击] -> [重置所有文本贴纸状态] -> [重置其他图片贴纸状态] -> [设置当前图片贴纸选中] -> [进入编辑模式]

点击文本贴纸:
[文本贴纸被点击] -> [重置所有图片贴纸状态] -> [文本贴纸状态由PLVStickerTextView内部管理] -> [进入编辑模式]

点击空白区域:
[空白区域被点击] -> [重置所有文本贴纸状态] -> [重置所有图片贴纸状态] -> [退出编辑模式]
```

## 测试用例

### 测试用例1: 图片贴纸互斥选中
1. 添加多个图片贴纸到画布
2. 点击第一个图片贴纸 -> 验证：第一个图片贴纸显示边框，其他图片贴纸无边框
3. 点击第二个图片贴纸 -> 验证：第二个图片贴纸显示边框，第一个图片贴纸边框消失

### 测试用例2: 文本贴纸互斥选中
1. 添加多个文本贴纸到画布
2. 点击第一个文本贴纸 -> 验证：第一个文本贴纸进入actionshow状态，其他文本贴纸为normal状态
3. 点击第二个文本贴纸 -> 验证：第二个文本贴纸进入actionshow状态，第一个文本贴纸回到normal状态

### 测试用例3: 图片与文本贴纸互斥选中
1. 添加图片贴纸和文本贴纸到画布
2. 点击文本贴纸 -> 验证：文本贴纸进入actionshow状态，图片贴纸无边框
3. 点击图片贴纸 -> 验证：图片贴纸显示边框，文本贴纸回到normal状态

### 测试用例4: 空白区域重置所有状态
1. 选中任意贴纸（图片或文本）
2. 点击画布空白区域 -> 验证：所有贴纸都回到normal状态，退出编辑模式

### 测试用例5: 文本编辑状态的互斥处理
1. 文本贴纸进入TextEditing状态（弹出编辑界面）
2. 点击图片贴纸 -> 验证：文本贴纸回到normal状态，编辑界面消失，图片贴纸被选中

## 关键改进点

1. **完整的互斥逻辑**: 图片贴纸和文本贴纸之间实现完全互斥
2. **状态重置**: 确保从任何编辑状态都能正确回到normal状态
3. **边框管理**: 使用enabledBorder属性管理图片贴纸的选中状态
4. **代码复用**: 提取公共的重置方法，便于维护
5. **完整覆盖**: 在所有相关的用户交互点都添加了互斥逻辑

## 验证结果

✅ 图片贴纸点击时正确重置文本贴纸状态
✅ 文本贴纸点击时正确重置图片贴纸状态  
✅ 图片贴纸之间实现互斥选中
✅ 文本贴纸之间实现互斥选中（原有逻辑）
✅ 空白区域点击重置所有贴纸状态
✅ 支持从任何编辑状态正确回到normal状态

互斥逻辑实现完成，确保同时只有一个贴纸处于编辑状态！
