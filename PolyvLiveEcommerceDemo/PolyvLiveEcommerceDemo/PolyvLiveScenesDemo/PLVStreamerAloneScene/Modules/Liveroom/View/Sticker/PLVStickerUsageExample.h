//
//  PLVStickerUsageExample.h
//  PolyvLiveScenesDemo
//
//  Created by AI Assistant on 2025/1/8.
//  Copyright © 2025 PLV. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 贴图功能使用示例
 * 
 * 这个类展示了如何正确使用新的贴图功能，包括：
 * 1. 初始化贴图管理器
 * 2. 显示贴图类型选择界面
 * 3. 处理编辑模式的进入和退出
 * 4. 生成最终的贴图图像
 * 5. 清除所有贴图
 */
@interface PLVStickerUsageExample : NSObject

/// 初始化方法
/// @param parentView 父视图，用于显示贴图画布和各种弹窗
- (instancetype)initWithParentView:(UIView *)parentView;

/// 显示贴图功能入口
- (void)showStickerFunction;

/// 生成最终的贴图图像
- (UIImage *)generateFinalStickerImage;

/// 清除所有贴图
- (void)clearAllStickers;

@end

NS_ASSUME_NONNULL_END
