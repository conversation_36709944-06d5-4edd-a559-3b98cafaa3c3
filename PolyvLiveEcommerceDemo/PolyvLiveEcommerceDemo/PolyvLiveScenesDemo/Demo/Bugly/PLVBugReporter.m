//
//  PLVBugReporter.m
//  PLVLiveScenesDemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/25.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVBugReporter.h"
#import <Bugly/Bugly.h>

@interface PLVBugReporter ()

@property (class, nonatomic, assign) BOOL on;

@end

@implementation PLVBugReporter

#pragma mark - Class Property

static BOOL _on = NO;

+ (BOOL)on {
  return _on;
}

+ (void)setOn:(BOOL)on {
    _on = on;
}

#pragma mark - Public

+ (void)openWithType:(PLVBuglyBundleType)type {
    if (![self isInnerTestWithType:type]) {
        return;
    }
    
    NSString *appId = [self appIdWithType:type];
    if (!appId) {
        return;
    }
    
    self.on = YES;
    
    BuglyConfig *config = [[BuglyConfig alloc] init];
    NSString *shortVersionString = [[[NSBundle mainBundle]infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    NSString *buildVersionString = [[[NSBundle mainBundle]infoDictionary] objectForKey:@"CFBundleVersion"];
    NSString *appVersion = [NSString stringWithFormat:@"%@(%@)", shortVersionString, buildVersionString];
    config.version = appVersion;
    [Bugly startWithAppId:appId config:config];
}

+ (void)setUserIdentifier:(NSString *)userId {
    if (self.on) {
        [Bugly setUserIdentifier:userId];
    }
}

#pragma mark - Private

+ (BOOL)isInnerTestWithType:(PLVBuglyBundleType)type {
    NSString* bundleID = [[NSBundle mainBundle] bundleIdentifier];
    if ([bundleID isEqualToString:@"polyv.PolyvLiveScenesDemo.test"]) {
        return type == PLVBuglyBundleTypeUniversal;
    } else if ([bundleID isEqualToString:@"polyv.PolyvLiveStreamerDemo.test"]) {
        return type == PLVBuglyBundleTypeStreamer;
    } else if ([bundleID isEqualToString:@"polyv.PolyvLiveWatchDemo.test"]) {
        return type == PLVBuglyBundleTypeWatch;
    }
    return NO;
}

+ (NSString *)appIdWithType:(PLVBuglyBundleType)type {
    NSString *appId = nil;
    switch (type) {
        case PLVBuglyBundleTypeUniversal: {
            appId = @"6712d3b416";
        } break;
        case PLVBuglyBundleTypeWatch: {
            appId = @"f365e2034d";
        } break;
        case PLVBuglyBundleTypeStreamer: {
            appId = @"cbe53403a9";
        } break;
        default: {
        } break;
    }
    return appId;
}

@end
