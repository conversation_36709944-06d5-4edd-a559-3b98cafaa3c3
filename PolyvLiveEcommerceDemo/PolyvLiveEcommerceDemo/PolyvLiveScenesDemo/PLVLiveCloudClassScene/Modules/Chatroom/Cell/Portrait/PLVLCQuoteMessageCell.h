//
//  PLVLCQuoteMessageCell.h
//  PLVLiveScenesDemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/1.
//  Copyright © 2020 PLV. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PLVLCMessageCell.h"

NS_ASSUME_NONNULL_BEGIN

/*
 云课堂场景，竖屏聊天室消息 cell
 支持引用消息
 */
@interface PLVLCQuoteMessageCell : PLVLCMessageCell

/// 生成回复消息的多属性文本
+ (NSMutableAttributedString *)contentAttributedStringWithMessage:(PLVQuoteMessage *)message;

@end

NS_ASSUME_NONNULL_END
